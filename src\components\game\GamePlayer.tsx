import React, { useRef } from 'react';
import { useFrame } from '@react-three/fiber';
import { Text } from '@react-three/drei';
import { Mesh, Group } from 'three';
import { Player } from '../../types/gameTypes';

interface GamePlayerProps {
  player: Player;
  position: { x: number; y: number; z: number };
  isCurrentPlayer: boolean;
}

export const GamePlayer: React.FC<GamePlayerProps> = ({
  player,
  position,
  isCurrentPlayer,
}) => {
  const meshRef = useRef<Mesh>(null);
  const ringRef = useRef<Mesh>(null);

  // Animate current player
  useFrame((state, delta) => {
    if (meshRef.current) {
      // Gentle bobbing animation
      meshRef.current.position.y = 2 + Math.sin(state.clock.elapsedTime * 3) * 0.2;

      // Rotate current player
      if (isCurrentPlayer) {
        meshRef.current.rotation.y += delta * 2;
      }
    }

    // Animate selection ring for current player
    if (ringRef.current && isCurrentPlayer) {
      ringRef.current.rotation.z += delta * 3;
      ringRef.current.scale.setScalar(1 + Math.sin(state.clock.elapsedTime * 4) * 0.1);
    }
  });

  // Calculate player height based on health
  const healthPercentage = player.health / 100;
  const playerHeight = 1 + healthPercentage * 0.5; // Scale between 1 and 1.5

  return (
    <group position={[position.x, 0, position.z]}>
      {/* Player Model */}
      <mesh
        ref={meshRef}
        position={[0, 2, 0]}
        castShadow
      >
        <capsuleGeometry args={[0.5, playerHeight, 4, 8]} />
        <meshStandardMaterial
          color={player.color}
          emissive={isCurrentPlayer ? player.color : '#000000'}
          emissiveIntensity={isCurrentPlayer ? 0.2 : 0}
          roughness={0.3}
          metalness={0.1}
        />
      </mesh>

      {/* Player Name */}
      <Text
        position={[0, 4, 0]}
        fontSize={0.4}
        color="#ffffff"
        anchorX="center"
        anchorY="middle"
        outlineWidth={0.02}
        outlineColor="#000000"
      >
        {player.name}
      </Text>

      {/* Health Bar */}
      <group position={[0, 3.5, 0]}>
        {/* Health Bar Background */}
        <mesh position={[0, 0, 0]}>
          <boxGeometry args={[2, 0.2, 0.1]} />
          <meshStandardMaterial color="#333333" />
        </mesh>

        {/* Health Bar Fill */}
        <mesh position={[-(1 - healthPercentage), 0, 0.05]}>
          <boxGeometry args={[2 * healthPercentage, 0.15, 0.05]} />
          <meshStandardMaterial
            color={healthPercentage > 0.5 ? '#2ecc71' : healthPercentage > 0.25 ? '#f1c40f' : '#e74c3c'}
          />
        </mesh>

        {/* Health Text */}
        <Text
          position={[0, 0.3, 0]}
          fontSize={0.25}
          color="#ffffff"
          anchorX="center"
          anchorY="middle"
          outlineWidth={0.01}
          outlineColor="#000000"
        >
          ❤️ {player.health}/100
        </Text>
      </group>

      {/* Keys Indicator */}
      <Text
        position={[0, 3, 0]}
        fontSize={0.3}
        color="#f1c40f"
        anchorX="center"
        anchorY="middle"
        outlineWidth={0.01}
        outlineColor="#000000"
      >
        🗝️ {player.keys}
      </Text>

      {/* Current Player Ring */}
      {isCurrentPlayer && (
        <mesh
          ref={ringRef}
          position={[0, 0.1, 0]}
          rotation={[Math.PI / 2, 0, 0]}
        >
          <torusGeometry args={[2, 0.1, 8, 16]} />
          <meshStandardMaterial
            color="#f39c12"
            emissive="#f39c12"
            emissiveIntensity={0.5}
          />
        </mesh>
      )}

      {/* Dead Player Indicator */}
      {!player.isAlive && (
        <>
          <Text
            position={[0, 5, 0]}
            fontSize={0.5}
            color="#e74c3c"
            anchorX="center"
            anchorY="middle"
          >
            X RESPAWNING
          </Text>

          {/* Respawn Effect */}
          <mesh position={[0, 2, 0]}>
            <sphereGeometry args={[1.5, 16, 16]} />
            <meshStandardMaterial
              color="#e74c3c"
              transparent
              opacity={0.3}
              emissive="#e74c3c"
              emissiveIntensity={0.2}
            />
          </mesh>
        </>
      )}
    </group>
  );
};
