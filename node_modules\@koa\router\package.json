{"name": "@koa/router", "description": "Router middleware for koa. Provides RESTful resource routing.", "version": "10.1.1", "author": "<PERSON> <<EMAIL>>", "bugs": {"url": "https://github.com/koajs/router/issues", "email": "<EMAIL>"}, "dependencies": {"debug": "^4.1.1", "http-errors": "^1.7.3", "koa-compose": "^4.1.0", "methods": "^1.1.2", "path-to-regexp": "^6.1.0"}, "devDependencies": {"@ladjs/env": "^1.0.0", "expect.js": "^0.3.1", "jsdoc-to-markdown": "^5.0.3", "koa": "^2.11.0", "mocha": "^7.0.1", "nyc": "^15.0.0", "should": "^13.2.3", "supertest": "^4.0.2", "wrk": "^1.2.0"}, "engines": {"node": ">= 8.0.0"}, "files": ["lib"], "homepage": "https://github.com/koajs/router", "keywords": ["koa", "middleware", "route", "router"], "license": "MIT", "main": "lib/router.js", "repository": {"type": "git", "url": "https://github.com/koajs/router.git"}, "scripts": {"docs": "NODE_ENV=test jsdoc2md -t ./lib/API_tpl.hbs --src ./lib/*.js  >| API.md", "test": "mocha test/**/*.js", "coverage": "nyc npm run test", "bench": "make -C bench"}}