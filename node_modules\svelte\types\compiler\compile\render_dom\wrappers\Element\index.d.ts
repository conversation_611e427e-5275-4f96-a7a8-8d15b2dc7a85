import Renderer from '../../Renderer';
import Element from '../../../nodes/Element';
import Wrapper from '../shared/Wrapper';
import Block from '../../Block';
import FragmentWrapper from '../Fragment';
import Attribute<PERSON>rapper from './Attribute';
import StyleAttri<PERSON>eWrapper from './StyleAttribute';
import SpreadAttributeWrapper from './SpreadAttribute';
import Binding from './Binding';
import { Identifier } from 'estree';
import EventHandler from './EventHandler';
interface BindingGroup {
    events: string[];
    bindings: Binding[];
}
export default class ElementWrapper extends Wrapper {
    node: Element;
    fragment: FragmentWrapper;
    attributes: Array<AttributeWrapper | StyleAttributeWrapper | SpreadAttributeWrapper>;
    bindings: Binding[];
    event_handlers: EventHandler[];
    class_dependencies: string[];
    dynamic_style_dependencies: Set<string>;
    has_dynamic_attribute: boolean;
    select_binding_dependencies?: Set<string>;
    has_dynamic_value: boolean;
    dynamic_value_condition: any;
    var: any;
    void: boolean;
    child_dynamic_element_block?: Block;
    child_dynamic_element?: ElementWrapper;
    element_data_name: any;
    constructor(renderer: Renderer, block: Block, parent: Wrapper, node: Element, strip_whitespace: boolean, next_sibling: Wrapper);
    render(block: Block, parent_node: Identifier, parent_nodes: Identifier): void;
    render_dynamic_element(block: Block, parent_node: Identifier, parent_nodes: Identifier): void;
    is_dom_node(): boolean;
    render_element(block: Block, parent_node: Identifier, parent_nodes: Identifier): void;
    can_use_textcontent(): boolean;
    get_render_statement(block: Block): (import("estree").ClassExpression & {
        start: number;
        end: number;
    }) | (import("estree").ArrayExpression & {
        start: number;
        end: number;
    }) | (import("estree").ArrowFunctionExpression & {
        start: number;
        end: number;
    }) | (import("estree").AssignmentExpression & {
        start: number;
        end: number;
    }) | (import("estree").AwaitExpression & {
        start: number;
        end: number;
    }) | (import("estree").BinaryExpression & {
        start: number;
        end: number;
    }) | (import("estree").SimpleCallExpression & {
        start: number;
        end: number;
    }) | (import("estree").NewExpression & {
        start: number;
        end: number;
    }) | (import("estree").ChainExpression & {
        start: number;
        end: number;
    }) | (import("estree").ConditionalExpression & {
        start: number;
        end: number;
    }) | (import("estree").FunctionExpression & {
        start: number;
        end: number;
    }) | (Identifier & {
        start: number;
        end: number;
    }) | (import("estree").ImportExpression & {
        start: number;
        end: number;
    }) | (import("estree").SimpleLiteral & {
        start: number;
        end: number;
    }) | (import("estree").RegExpLiteral & {
        start: number;
        end: number;
    }) | (import("estree").BigIntLiteral & {
        start: number;
        end: number;
    }) | (import("estree").LogicalExpression & {
        start: number;
        end: number;
    }) | (import("estree").MemberExpression & {
        start: number;
        end: number;
    }) | (import("estree").MetaProperty & {
        start: number;
        end: number;
    }) | (import("estree").ObjectExpression & {
        start: number;
        end: number;
    }) | (import("estree").SequenceExpression & {
        start: number;
        end: number;
    }) | (import("estree").TaggedTemplateExpression & {
        start: number;
        end: number;
    }) | (import("estree").TemplateLiteral & {
        start: number;
        end: number;
    }) | (import("estree").ThisExpression & {
        start: number;
        end: number;
    }) | (import("estree").UnaryExpression & {
        start: number;
        end: number;
    }) | (import("estree").UpdateExpression & {
        start: number;
        end: number;
    }) | (import("estree").YieldExpression & {
        start: number;
        end: number;
    });
    get_claim_statement(block: Block, nodes: Identifier): (import("estree").ClassExpression & {
        start: number;
        end: number;
    }) | (import("estree").ArrayExpression & {
        start: number;
        end: number;
    }) | (import("estree").ArrowFunctionExpression & {
        start: number;
        end: number;
    }) | (import("estree").AssignmentExpression & {
        start: number;
        end: number;
    }) | (import("estree").AwaitExpression & {
        start: number;
        end: number;
    }) | (import("estree").BinaryExpression & {
        start: number;
        end: number;
    }) | (import("estree").SimpleCallExpression & {
        start: number;
        end: number;
    }) | (import("estree").NewExpression & {
        start: number;
        end: number;
    }) | (import("estree").ChainExpression & {
        start: number;
        end: number;
    }) | (import("estree").ConditionalExpression & {
        start: number;
        end: number;
    }) | (import("estree").FunctionExpression & {
        start: number;
        end: number;
    }) | (Identifier & {
        start: number;
        end: number;
    }) | (import("estree").ImportExpression & {
        start: number;
        end: number;
    }) | (import("estree").SimpleLiteral & {
        start: number;
        end: number;
    }) | (import("estree").RegExpLiteral & {
        start: number;
        end: number;
    }) | (import("estree").BigIntLiteral & {
        start: number;
        end: number;
    }) | (import("estree").LogicalExpression & {
        start: number;
        end: number;
    }) | (import("estree").MemberExpression & {
        start: number;
        end: number;
    }) | (import("estree").MetaProperty & {
        start: number;
        end: number;
    }) | (import("estree").ObjectExpression & {
        start: number;
        end: number;
    }) | (import("estree").SequenceExpression & {
        start: number;
        end: number;
    }) | (import("estree").TaggedTemplateExpression & {
        start: number;
        end: number;
    }) | (import("estree").TemplateLiteral & {
        start: number;
        end: number;
    }) | (import("estree").ThisExpression & {
        start: number;
        end: number;
    }) | (import("estree").UnaryExpression & {
        start: number;
        end: number;
    }) | (import("estree").UpdateExpression & {
        start: number;
        end: number;
    }) | (import("estree").YieldExpression & {
        start: number;
        end: number;
    });
    add_directives_in_order(block: Block): void;
    add_bindings(block: Block, binding_group: BindingGroup): void;
    add_this_binding(block: Block, this_binding: Binding): void;
    add_attributes(block: Block): void;
    add_spread_attributes(block: Block): void;
    add_dynamic_element_attributes(block: Block): void;
    add_transitions(block: Block): void;
    add_animation(block: Block): void;
    add_classes(block: Block): void;
    add_styles(block: Block): void;
    add_manual_style_scoping(block: Block): void;
}
export {};
