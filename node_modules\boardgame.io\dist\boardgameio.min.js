!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).BoardgameIO={})}(this,(function(e){"use strict";let t=(e=21)=>{let t="",n=e;for(;n--;)t+="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict"[64*Math.random()|0];return t};function n(){}const r=e=>e;function a(e,t){for(const n in t)e[n]=t[n];return e}function o(e){return e()}function i(){return Object.create(null)}function s(e){e.forEach(o)}function l(e){return"function"==typeof e}function c(e,t){return e!=e?t==t:e!==t||e&&"object"==typeof e||"function"==typeof e}function u(e,...t){if(null==e)return n;const r=e.subscribe(...t);return r.unsubscribe?()=>r.unsubscribe():r}function d(e,t,n){e.$$.on_destroy.push(u(t,n))}function f(e,t,n,r){return e[1]&&r?a(n.ctx.slice(),e[1](r(t))):n.ctx}function p(e){const t={};for(const n in e)"$"!==n[0]&&(t[n]=e[n]);return t}function g(e){return null==e?"":e}const v="undefined"!=typeof window;let h=v?()=>window.performance.now():()=>Date.now(),m=v?e=>requestAnimationFrame(e):n;const y=new Set;function b(e){y.forEach(t=>{t.c(e)||(y.delete(t),t.f())}),0!==y.size&&m(b)}function $(e){let t;return 0===y.size&&m(b),{promise:new Promise(n=>{y.add(t={c:e,f:n})}),abort(){y.delete(t)}}}function x(e,t){e.appendChild(t)}function P(e,t,n){const r=w(e);if(!r.getElementById(t)){const e=A("style");e.id=t,e.textContent=n,E(r,e)}}function w(e){if(!e)return document;const t=e.getRootNode?e.getRootNode():e.ownerDocument;return t&&t.host?t:e.ownerDocument}function k(e){const t=A("style");return E(w(e),t),t.sheet}function E(e,t){x(e.head||e,t)}function O(e,t,n){e.insertBefore(t,n||null)}function I(e){e.parentNode.removeChild(e)}function _(e,t){for(let n=0;n<e.length;n+=1)e[n]&&e[n].d(t)}function A(e){return document.createElement(e)}function S(e){return document.createElementNS("http://www.w3.org/2000/svg",e)}function D(e){return document.createTextNode(e)}function M(){return D(" ")}function j(){return D("")}function N(e,t,n,r){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n,r)}function T(e){return function(t){return t.stopPropagation(),e.call(this,t)}}function C(e,t,n){null==n?e.removeAttribute(t):e.getAttribute(t)!==n&&e.setAttribute(t,n)}function G(e,t){t=""+t,e.wholeText!==t&&(e.data=t)}function z(e,t){e.value=null==t?"":t}function q(e,t){for(let n=0;n<e.options.length;n+=1){const r=e.options[n];if(r.__value===t)return void(r.selected=!0)}e.selectedIndex=-1}function V(e){const t=e.querySelector(":checked")||e.options[0];return t&&t.__value}function R(e,t,n){e.classList[n?"add":"remove"](t)}function B(e,t,{bubbles:n=!1,cancelable:r=!1}={}){const a=document.createEvent("CustomEvent");return a.initCustomEvent(e,n,r,t),a}const U=new Map;let F,L=0;function J(e,t,n,r,a,o,i,s=0){const l=16.666/r;let c="{\n";for(let e=0;e<=1;e+=l){const r=t+(n-t)*o(e);c+=100*e+`%{${i(r,1-r)}}\n`}const u=c+`100% {${i(n,1-n)}}\n}`,d=`__svelte_${function(e){let t=5381,n=e.length;for(;n--;)t=(t<<5)-t^e.charCodeAt(n);return t>>>0}(u)}_${s}`,f=w(e),{stylesheet:p,rules:g}=U.get(f)||function(e,t){const n={stylesheet:k(t),rules:{}};return U.set(e,n),n}(f,e);g[d]||(g[d]=!0,p.insertRule(`@keyframes ${d} ${u}`,p.cssRules.length));const v=e.style.animation||"";return e.style.animation=`${v?v+", ":""}${d} ${r}ms linear ${a}ms 1 both`,L+=1,d}function K(e,t){const n=(e.style.animation||"").split(", "),r=n.filter(t?e=>e.indexOf(t)<0:e=>-1===e.indexOf("__svelte")),a=n.length-r.length;a&&(e.style.animation=r.join(", "),L-=a,L||m(()=>{L||(U.forEach(e=>{const{stylesheet:t}=e;let n=t.cssRules.length;for(;n--;)t.deleteRule(n);e.rules={}}),U.clear())}))}function H(e){F=e}function W(){if(!F)throw new Error("Function called outside component initialization");return F}function Z(e){W().$$.on_destroy.push(e)}function Y(){const e=W();return(t,n,{cancelable:r=!1}={})=>{const a=e.$$.callbacks[t];if(a){const o=B(t,n,{cancelable:r});return a.slice().forEach(t=>{t.call(e,o)}),!o.defaultPrevented}return!0}}function X(e,t){return W().$$.context.set(e,t),t}function Q(e){return W().$$.context.get(e)}function ee(e,t){const n=e.$$.callbacks[t.type];n&&n.slice().forEach(e=>e.call(this,t))}const te=[],ne=[],re=[],ae=[],oe=Promise.resolve();let ie=!1;function se(e){re.push(e)}const le=new Set;let ce,ue=0;function de(){const e=F;do{for(;ue<te.length;){const e=te[ue];ue++,H(e),fe(e.$$)}for(H(null),te.length=0,ue=0;ne.length;)ne.pop()();for(let e=0;e<re.length;e+=1){const t=re[e];le.has(t)||(le.add(t),t())}re.length=0}while(te.length);for(;ae.length;)ae.pop()();ie=!1,le.clear(),H(e)}function fe(e){if(null!==e.fragment){e.update(),s(e.before_update);const t=e.dirty;e.dirty=[-1],e.fragment&&e.fragment.p(e.ctx,t),e.after_update.forEach(se)}}function pe(){return ce||(ce=Promise.resolve(),ce.then(()=>{ce=null})),ce}function ge(e,t,n){e.dispatchEvent(B(`${t?"intro":"outro"}${n}`))}const ve=new Set;let he;function me(){he={r:0,c:[],p:he}}function ye(){he.r||s(he.c),he=he.p}function be(e,t){e&&e.i&&(ve.delete(e),e.i(t))}function $e(e,t,n,r){if(e&&e.o){if(ve.has(e))return;ve.add(e),he.c.push(()=>{ve.delete(e),r&&(n&&e.d(1),r())}),e.o(t)}else r&&r()}const xe={duration:0};function Pe(e,t,a){let o,i,s=t(e,a),c=!1,u=0;function d(){o&&K(e,o)}function f(){const{delay:t=0,duration:a=300,easing:l=r,tick:f=n,css:p}=s||xe;p&&(o=J(e,0,1,a,t,l,p,u++)),f(0,1);const g=h()+t,v=g+a;i&&i.abort(),c=!0,se(()=>ge(e,!0,"start")),i=$(t=>{if(c){if(t>=v)return f(1,0),ge(e,!0,"end"),d(),c=!1;if(t>=g){const e=l((t-g)/a);f(e,1-e)}}return c})}let p=!1;return{start(){p||(p=!0,K(e),l(s)?(s=s(),pe().then(f)):f())},invalidate(){p=!1},end(){c&&(d(),c=!1)}}}function we(e,t,a){let o,i=t(e,a),c=!0;const u=he;function d(){const{delay:t=0,duration:a=300,easing:l=r,tick:d=n,css:f}=i||xe;f&&(o=J(e,1,0,a,t,l,f));const p=h()+t,g=p+a;se(()=>ge(e,!1,"start")),$(t=>{if(c){if(t>=g)return d(0,1),ge(e,!1,"end"),--u.r||s(u.c),!1;if(t>=p){const e=l((t-p)/a);d(1-e,e)}}return c})}return u.r+=1,l(i)?pe().then(()=>{i=i(),d()}):d(),{end(t){t&&i.tick&&i.tick(1,0),c&&(o&&K(e,o),c=!1)}}}function ke(e,t,a,o){let i=t(e,a),c=o?0:1,u=null,d=null,f=null;function p(){f&&K(e,f)}function g(e,t){const n=e.b-c;return t*=Math.abs(n),{a:c,b:e.b,d:n,duration:t,start:e.start,end:e.start+t,group:e.group}}function v(t){const{delay:a=0,duration:o=300,easing:l=r,tick:v=n,css:m}=i||xe,y={start:h()+a,b:t};t||(y.group=he,he.r+=1),u||d?d=y:(m&&(p(),f=J(e,c,t,o,a,l,m)),t&&v(0,1),u=g(y,o),se(()=>ge(e,t,"start")),$(t=>{if(d&&t>d.start&&(u=g(d,o),d=null,ge(e,u.b,"start"),m&&(p(),f=J(e,c,u.b,u.duration,0,l,i.css))),u)if(t>=u.end)v(c=u.b,1-c),ge(e,u.b,"end"),d||(u.b?p():--u.group.r||s(u.group.c)),u=null;else if(t>=u.start){const e=t-u.start;c=u.a+u.d*l(e/u.duration),v(c,1-c)}return!(!u&&!d)}))}return{run(e){l(i)?pe().then(()=>{i=i(),v(e)}):v(e)},end(){p(),u=d=null}}}function Ee(e,t){const n={},r={},a={$$scope:1};let o=e.length;for(;o--;){const i=e[o],s=t[o];if(s){for(const e in i)e in s||(r[e]=1);for(const e in s)a[e]||(n[e]=s[e],a[e]=1);e[o]=s}else for(const e in i)a[e]=1}for(const e in r)e in n||(n[e]=void 0);return n}function Oe(e){return"object"==typeof e&&null!==e?e:{}}function Ie(e){e&&e.c()}function _e(e,t,n,r){const{fragment:a,on_mount:i,on_destroy:c,after_update:u}=e.$$;a&&a.m(t,n),r||se(()=>{const t=i.map(o).filter(l);c?c.push(...t):s(t),e.$$.on_mount=[]}),u.forEach(se)}function Ae(e,t){const n=e.$$;null!==n.fragment&&(s(n.on_destroy),n.fragment&&n.fragment.d(t),n.on_destroy=n.fragment=null,n.ctx=[])}function Se(e,t){-1===e.$$.dirty[0]&&(te.push(e),ie||(ie=!0,oe.then(de)),e.$$.dirty.fill(0)),e.$$.dirty[t/31|0]|=1<<t%31}function De(e,t,r,a,o,l,c,u=[-1]){const d=F;H(e);const f=e.$$={fragment:null,ctx:null,props:l,update:n,not_equal:o,bound:i(),on_mount:[],on_destroy:[],on_disconnect:[],before_update:[],after_update:[],context:new Map(t.context||(d?d.$$.context:[])),callbacks:i(),dirty:u,skip_bound:!1,root:t.target||d.$$.root};c&&c(f.root);let p=!1;if(f.ctx=r?r(e,t.props||{},(t,n,...r)=>{const a=r.length?r[0]:n;return f.ctx&&o(f.ctx[t],f.ctx[t]=a)&&(!f.skip_bound&&f.bound[t]&&f.bound[t](a),p&&Se(e,t)),n}):[],f.update(),p=!0,s(f.before_update),f.fragment=!!a&&a(f.ctx),t.target){if(t.hydrate){const e=function(e){return Array.from(e.childNodes)}(t.target);f.fragment&&f.fragment.l(e),e.forEach(I)}else f.fragment&&f.fragment.c();t.intro&&be(e.$$.fragment),_e(e,t.target,t.anchor,t.customElement),de()}H(d)}class Me{$destroy(){Ae(this,1),this.$destroy=n}$on(e,t){const n=this.$$.callbacks[e]||(this.$$.callbacks[e]=[]);return n.push(t),()=>{const e=n.indexOf(t);-1!==e&&n.splice(e,1)}}$set(e){var t;this.$$set&&(t=e,0!==Object.keys(t).length)&&(this.$$.skip_bound=!0,this.$$set(e),this.$$.skip_bound=!1)}}function je(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ne(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Te(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ne(Object(n),!0).forEach((function(t){je(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ne(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Ce(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}var Ge="function"==typeof Symbol&&Symbol.observable||"@@observable",ze=function(){return Math.random().toString(36).substring(7).split("").join(".")},qe={INIT:"@@redux/INIT"+ze(),REPLACE:"@@redux/REPLACE"+ze(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+ze()}};function Ve(e){if("object"!=typeof e||null===e)return!1;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function Re(e,t,n){var r;if("function"==typeof t&&"function"==typeof n||"function"==typeof n&&"function"==typeof arguments[3])throw new Error(Ce(0));if("function"==typeof t&&void 0===n&&(n=t,t=void 0),void 0!==n){if("function"!=typeof n)throw new Error(Ce(1));return n(Re)(e,t)}if("function"!=typeof e)throw new Error(Ce(2));var a=e,o=t,i=[],s=i,l=!1;function c(){s===i&&(s=i.slice())}function u(){if(l)throw new Error(Ce(3));return o}function d(e){if("function"!=typeof e)throw new Error(Ce(4));if(l)throw new Error(Ce(5));var t=!0;return c(),s.push(e),function(){if(t){if(l)throw new Error(Ce(6));t=!1,c();var n=s.indexOf(e);s.splice(n,1),i=null}}}function f(e){if(!Ve(e))throw new Error(Ce(7));if(void 0===e.type)throw new Error(Ce(8));if(l)throw new Error(Ce(9));try{l=!0,o=a(o,e)}finally{l=!1}for(var t=i=s,n=0;n<t.length;n++){(0,t[n])()}return e}function p(e){if("function"!=typeof e)throw new Error(Ce(10));a=e,f({type:qe.REPLACE})}function g(){var e,t=d;return(e={subscribe:function(e){if("object"!=typeof e||null===e)throw new Error(Ce(11));function n(){e.next&&e.next(u())}return n(),{unsubscribe:t(n)}}})[Ge]=function(){return this},e}return f({type:qe.INIT}),(r={dispatch:f,subscribe:d,getState:u,replaceReducer:p})[Ge]=g,r}function Be(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return 0===t.length?function(e){return e}:1===t.length?t[0]:t.reduce((function(e,t){return function(){return e(t.apply(void 0,arguments))}}))}const Ue=(e,t,n,r)=>({type:"MAKE_MOVE",payload:{type:e,args:t,playerID:n,credentials:r}}),Fe=(e,t,n,r)=>({type:"GAME_EVENT",payload:{type:e,args:t,playerID:n,credentials:r}}),Le=(e,t,n,r)=>({type:"GAME_EVENT",payload:{type:e,args:t,playerID:n,credentials:r},automatic:!0}),Je=e=>({type:"SYNC",state:e.state,log:e.log,initialState:e.initialState,clientOnly:!0}),Ke=(e,t,n,r)=>({type:"PATCH",prevStateID:e,stateID:t,patch:n,deltalog:r,clientOnly:!0}),He=(e,t)=>({type:"UPDATE",state:e,deltalog:t,clientOnly:!0}),We=e=>({type:"RESET",state:e,clientOnly:!0}),Ze=(e,t)=>({type:"UNDO",payload:{type:null,args:null,playerID:e,credentials:t}}),Ye=(e,t)=>({type:"REDO",payload:{type:null,args:null,playerID:e,credentials:t}}),Xe=()=>({type:"STRIP_TRANSIENTS"});var Qe=Object.freeze({__proto__:null,makeMove:Ue,gameEvent:Fe,automaticGameEvent:Le,sync:Je,patch:Ke,update:He,reset:We,undo:Ze,redo:Ye,plugin:(e,t,n,r)=>({type:"PLUGIN",payload:{type:e,args:t,playerID:n,credentials:r}}),stripTransients:Xe});function et(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];throw Error("[Immer] minified error nr: "+e+(n.length?" "+n.map((function(e){return"'"+e+"'"})).join(","):"")+". Find the full error at: https://bit.ly/3cXEKWf")}function tt(e){return!!e&&!!e[Vt]}function nt(e){return!!e&&(function(e){if(!e||"object"!=typeof e)return!1;var t=Object.getPrototypeOf(e);if(null===t)return!0;var n=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return n===Object||"function"==typeof n&&Function.toString.call(n)===Rt}(e)||Array.isArray(e)||!!e[qt]||!!e.constructor[qt]||st(e)||lt(e))}function rt(e,t,n){void 0===n&&(n=!1),0===at(e)?(n?Object.keys:Bt)(e).forEach((function(r){n&&"symbol"==typeof r||t(r,e[r],e)})):e.forEach((function(n,r){return t(r,n,e)}))}function at(e){var t=e[Vt];return t?t.i>3?t.i-4:t.i:Array.isArray(e)?1:st(e)?2:lt(e)?3:0}function ot(e,t){return 2===at(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function it(e,t,n){var r=at(e);2===r?e.set(t,n):3===r?(e.delete(t),e.add(n)):e[t]=n}function st(e){return Tt&&e instanceof Map}function lt(e){return Ct&&e instanceof Set}function ct(e){return e.o||e.t}function ut(e){if(Array.isArray(e))return Array.prototype.slice.call(e);var t=Ut(e);delete t[Vt];for(var n=Bt(t),r=0;r<n.length;r++){var a=n[r],o=t[a];!1===o.writable&&(o.writable=!0,o.configurable=!0),(o.get||o.set)&&(t[a]={configurable:!0,writable:!0,enumerable:o.enumerable,value:e[a]})}return Object.create(Object.getPrototypeOf(e),t)}function dt(e,t){return void 0===t&&(t=!1),pt(e)||tt(e)||!nt(e)||(at(e)>1&&(e.set=e.add=e.clear=e.delete=ft),Object.freeze(e),t&&rt(e,(function(e,t){return dt(t,!0)}),!0)),e}function ft(){et(2)}function pt(e){return null==e||"object"!=typeof e||Object.isFrozen(e)}function gt(e){var t=Ft[e];return t||et(18,e),t}function vt(){return jt}function ht(e,t){t&&(gt("Patches"),e.u=[],e.s=[],e.v=t)}function mt(e){yt(e),e.p.forEach($t),e.p=null}function yt(e){e===jt&&(jt=e.l)}function bt(e){return jt={p:[],l:jt,h:e,m:!0,_:0}}function $t(e){var t=e[Vt];0===t.i||1===t.i?t.j():t.O=!0}function xt(e,t){t._=t.p.length;var n=t.p[0],r=void 0!==e&&e!==n;return t.h.g||gt("ES5").S(t,e,r),r?(n[Vt].P&&(mt(t),et(4)),nt(e)&&(e=Pt(t,e),t.l||kt(t,e)),t.u&&gt("Patches").M(n[Vt],e,t.u,t.s)):e=Pt(t,n,[]),mt(t),t.u&&t.v(t.u,t.s),e!==zt?e:void 0}function Pt(e,t,n){if(pt(t))return t;var r=t[Vt];if(!r)return rt(t,(function(a,o){return wt(e,r,t,a,o,n)}),!0),t;if(r.A!==e)return t;if(!r.P)return kt(e,r.t,!0),r.t;if(!r.I){r.I=!0,r.A._--;var a=4===r.i||5===r.i?r.o=ut(r.k):r.o;rt(3===r.i?new Set(a):a,(function(t,o){return wt(e,r,a,t,o,n)})),kt(e,a,!1),n&&e.u&&gt("Patches").R(r,n,e.u,e.s)}return r.o}function wt(e,t,n,r,a,o){if(tt(a)){var i=Pt(e,a,o&&t&&3!==t.i&&!ot(t.D,r)?o.concat(r):void 0);if(it(n,r,i),!tt(i))return;e.m=!1}if(nt(a)&&!pt(a)){if(!e.h.F&&e._<1)return;Pt(e,a),t&&t.A.l||kt(e,a)}}function kt(e,t,n){void 0===n&&(n=!1),e.h.F&&e.m&&dt(t,n)}function Et(e,t){var n=e[Vt];return(n?ct(n):e)[t]}function Ot(e,t){if(t in e)for(var n=Object.getPrototypeOf(e);n;){var r=Object.getOwnPropertyDescriptor(n,t);if(r)return r;n=Object.getPrototypeOf(n)}}function It(e){e.P||(e.P=!0,e.l&&It(e.l))}function _t(e){e.o||(e.o=ut(e.t))}function At(e,t,n){var r=st(t)?gt("MapSet").N(t,n):lt(t)?gt("MapSet").T(t,n):e.g?function(e,t){var n=Array.isArray(e),r={i:n?1:0,A:t?t.A:vt(),P:!1,I:!1,D:{},l:t,t:e,k:null,o:null,j:null,C:!1},a=r,o=Lt;n&&(a=[r],o=Jt);var i=Proxy.revocable(a,o),s=i.revoke,l=i.proxy;return r.k=l,r.j=s,l}(t,n):gt("ES5").J(t,n);return(n?n.A:vt()).p.push(r),r}function St(e){return tt(e)||et(22,e),function e(t){if(!nt(t))return t;var n,r=t[Vt],a=at(t);if(r){if(!r.P&&(r.i<4||!gt("ES5").K(r)))return r.t;r.I=!0,n=Dt(t,a),r.I=!1}else n=Dt(t,a);return rt(n,(function(t,a){r&&function(e,t){return 2===at(e)?e.get(t):e[t]}(r.t,t)===a||it(n,t,e(a))})),3===a?new Set(n):n}(e)}function Dt(e,t){switch(t){case 2:return new Map(e);case 3:return Array.from(e)}return ut(e)}var Mt,jt,Nt="undefined"!=typeof Symbol&&"symbol"==typeof Symbol("x"),Tt="undefined"!=typeof Map,Ct="undefined"!=typeof Set,Gt="undefined"!=typeof Proxy&&void 0!==Proxy.revocable&&"undefined"!=typeof Reflect,zt=Nt?Symbol.for("immer-nothing"):((Mt={})["immer-nothing"]=!0,Mt),qt=Nt?Symbol.for("immer-draftable"):"__$immer_draftable",Vt=Nt?Symbol.for("immer-state"):"__$immer_state",Rt=""+Object.prototype.constructor,Bt="undefined"!=typeof Reflect&&Reflect.ownKeys?Reflect.ownKeys:void 0!==Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:Object.getOwnPropertyNames,Ut=Object.getOwnPropertyDescriptors||function(e){var t={};return Bt(e).forEach((function(n){t[n]=Object.getOwnPropertyDescriptor(e,n)})),t},Ft={},Lt={get:function(e,t){if(t===Vt)return e;var n=ct(e);if(!ot(n,t))return function(e,t,n){var r,a=Ot(t,n);return a?"value"in a?a.value:null===(r=a.get)||void 0===r?void 0:r.call(e.k):void 0}(e,n,t);var r=n[t];return e.I||!nt(r)?r:r===Et(e.t,t)?(_t(e),e.o[t]=At(e.A.h,r,e)):r},has:function(e,t){return t in ct(e)},ownKeys:function(e){return Reflect.ownKeys(ct(e))},set:function(e,t,n){var r=Ot(ct(e),t);if(null==r?void 0:r.set)return r.set.call(e.k,n),!0;if(!e.P){var a=Et(ct(e),t),o=null==a?void 0:a[Vt];if(o&&o.t===n)return e.o[t]=n,e.D[t]=!1,!0;if(function(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t}(n,a)&&(void 0!==n||ot(e.t,t)))return!0;_t(e),It(e)}return e.o[t]===n&&"number"!=typeof n&&(void 0!==n||t in e.o)||(e.o[t]=n,e.D[t]=!0,!0)},deleteProperty:function(e,t){return void 0!==Et(e.t,t)||t in e.t?(e.D[t]=!1,_t(e),It(e)):delete e.D[t],e.o&&delete e.o[t],!0},getOwnPropertyDescriptor:function(e,t){var n=ct(e),r=Reflect.getOwnPropertyDescriptor(n,t);return r?{writable:!0,configurable:1!==e.i||"length"!==t,enumerable:r.enumerable,value:n[t]}:r},defineProperty:function(){et(11)},getPrototypeOf:function(e){return Object.getPrototypeOf(e.t)},setPrototypeOf:function(){et(12)}},Jt={};rt(Lt,(function(e,t){Jt[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}})),Jt.deleteProperty=function(e,t){return Lt.deleteProperty.call(this,e[0],t)},Jt.set=function(e,t,n){return Lt.set.call(this,e[0],t,n,e[0])};var Kt=new(function(){function e(e){var t=this;this.g=Gt,this.F=!0,this.produce=function(e,n,r){if("function"==typeof e&&"function"!=typeof n){var a=n;n=e;var o=t;return function(e){var t=this;void 0===e&&(e=a);for(var r=arguments.length,i=Array(r>1?r-1:0),s=1;s<r;s++)i[s-1]=arguments[s];return o.produce(e,(function(e){var r;return(r=n).call.apply(r,[t,e].concat(i))}))}}var i;if("function"!=typeof n&&et(6),void 0!==r&&"function"!=typeof r&&et(7),nt(e)){var s=bt(t),l=At(t,e,void 0),c=!0;try{i=n(l),c=!1}finally{c?mt(s):yt(s)}return"undefined"!=typeof Promise&&i instanceof Promise?i.then((function(e){return ht(s,r),xt(e,s)}),(function(e){throw mt(s),e})):(ht(s,r),xt(i,s))}if(!e||"object"!=typeof e){if((i=n(e))===zt)return;return void 0===i&&(i=e),t.F&&dt(i,!0),i}et(21,e)},this.produceWithPatches=function(e,n){return"function"==typeof e?function(n){for(var r=arguments.length,a=Array(r>1?r-1:0),o=1;o<r;o++)a[o-1]=arguments[o];return t.produceWithPatches(n,(function(t){return e.apply(void 0,[t].concat(a))}))}:[t.produce(e,n,(function(e,t){r=e,a=t})),r,a];var r,a},"boolean"==typeof(null==e?void 0:e.useProxies)&&this.setUseProxies(e.useProxies),"boolean"==typeof(null==e?void 0:e.autoFreeze)&&this.setAutoFreeze(e.autoFreeze)}var t=e.prototype;return t.createDraft=function(e){nt(e)||et(8),tt(e)&&(e=St(e));var t=bt(this),n=At(this,e,void 0);return n[Vt].C=!0,yt(t),n},t.finishDraft=function(e,t){var n=(e&&e[Vt]).A;return ht(n,t),xt(void 0,n)},t.setAutoFreeze=function(e){this.F=e},t.setUseProxies=function(e){e&&!Gt&&et(20),this.g=e},t.applyPatches=function(e,t){var n;for(n=t.length-1;n>=0;n--){var r=t[n];if(0===r.path.length&&"replace"===r.op){e=r.value;break}}var a=gt("Patches").$;return tt(e)?a(e,t):this.produce(e,(function(e){return a(e,t.slice(n+1))}))},e}()),Ht=Kt.produce;Kt.produceWithPatches.bind(Kt),Kt.setAutoFreeze.bind(Kt),Kt.setUseProxies.bind(Kt),Kt.applyPatches.bind(Kt),Kt.createDraft.bind(Kt),Kt.finishDraft.bind(Kt);const Wt={name:"plugin-immer",fnWrap:e=>(t,...n)=>{let r=!1;const a=Ht(t.G,a=>{const o=e({...t,G:a},...n);if("INVALID_MOVE"!==o)return o;r=!0});return r?"INVALID_MOVE":a}};class Zt{constructor(e){const t=function(){let e=4022871197;return function(t){const n=t.toString();for(let t=0;t<n.length;t++){e+=n.charCodeAt(t);let r=.02519603282416938*e;e=r>>>0,r-=e,r*=e,e=r>>>0,r-=e,e+=4294967296*r}return 2.3283064365386963e-10*(e>>>0)}}();this.c=1,this.s0=t(" "),this.s1=t(" "),this.s2=t(" "),this.s0-=t(e),this.s0<0&&(this.s0+=1),this.s1-=t(e),this.s1<0&&(this.s1+=1),this.s2-=t(e),this.s2<0&&(this.s2+=1)}next(){const e=2091639*this.s0+2.3283064365386963e-10*this.c;return this.s0=this.s1,this.s1=this.s2,this.s2=e-(this.c=Math.trunc(e))}}function Yt(e,t){return t.c=e.c,t.s0=e.s0,t.s1=e.s1,t.s2=e.s2,t}function Xt(e,t){const n=new Zt(e),r=n.next.bind(n);return t&&Yt(t,n),r.state=()=>Yt(n,{}),r}class Qt{constructor(e){this.state=e||{seed:"0"},this.used=!1}static seed(){return Date.now().toString(36).slice(-10)}isUsed(){return this.used}getState(){return this.state}_random(){this.used=!0;const e=this.state,t=Xt(e.prngstate?"":e.seed,e.prngstate),n=t();return this.state={...e,prngstate:t.state()},n}api(){const e=this._random.bind(this),t={D4:4,D6:6,D8:8,D10:10,D12:12,D20:20},n={};for(const r in t){const a=t[r];n[r]=t=>void 0===t?Math.floor(e()*a)+1:Array.from({length:t}).map(()=>Math.floor(e()*a)+1)}return{...n,Die:function(t=6,n){return void 0===n?Math.floor(e()*t)+1:Array.from({length:n}).map(()=>Math.floor(e()*t)+1)},Number:()=>e(),Shuffle:t=>{const n=[...t];let r=t.length,a=0;const o=Array.from({length:r});for(;r;){const t=Math.trunc(r*e());o[a++]=n[t],n[t]=n[--r]}return o},_private:this}}}const en={name:"random",noClient:({api:e})=>e._private.isUsed(),flush:({api:e})=>e._private.getState(),api:({data:e})=>new Qt(e).api(),setup:({game:e})=>{let{seed:t}=e;return void 0===t&&(t=Qt.seed()),{seed:t}},playerView:()=>{}};var tn,nn;!function(e){e.MOVE="MOVE",e.GAME_ON_END="GAME_ON_END",e.PHASE_ON_BEGIN="PHASE_ON_BEGIN",e.PHASE_ON_END="PHASE_ON_END",e.TURN_ON_BEGIN="TURN_ON_BEGIN",e.TURN_ON_MOVE="TURN_ON_MOVE",e.TURN_ON_END="TURN_ON_END"}(tn||(tn={})),function(e){e.CalledOutsideHook="Events must be called from moves or the `onBegin`, `onEnd`, and `onMove` hooks.\nThis error probably means you called an event from other game code, like an `endIf` trigger or one of the `turn.order` methods.",e.EndTurnInOnEnd="`endTurn` is disallowed in `onEnd` hooks — the turn is already ending.",e.MaxTurnEndings="Maximum number of turn endings exceeded for this update.\nThis likely means game code is triggering an infinite loop.",e.PhaseEventInOnEnd="`setPhase` & `endPhase` are disallowed in a phase’s `onEnd` hook — the phase is already ending.\nIf you’re trying to dynamically choose the next phase when a phase ends, use the phase’s `next` trigger.",e.StageEventInOnEnd="`setStage`, `endStage` & `setActivePlayers` are disallowed in `onEnd` hooks.",e.StageEventInPhaseBegin="`setStage`, `endStage` & `setActivePlayers` are disallowed in a phase’s `onBegin` hook.\nUse `setActivePlayers` in a `turn.onBegin` hook or declare stages with `turn.activePlayers` instead.",e.StageEventInTurnBegin="`setStage` & `endStage` are disallowed in `turn.onBegin`.\nUse `setActivePlayers` or declare stages with `turn.activePlayers` instead."}(nn||(nn={}));class rn{constructor(e,t,n){this.flow=e,this.playerID=n,this.dispatch=[],this.initialTurn=t.turn,this.updateTurnContext(t,void 0),this.maxEndedTurnsPerAction=100*t.numPlayers}api(){const e={_private:this};for(const t of this.flow.eventNames)e[t]=(...e)=>{this.dispatch.push({type:t,args:e,phase:this.currentPhase,turn:this.currentTurn,calledFrom:this.currentMethod,error:new Error("Events Plugin Error")})};return e}isUsed(){return this.dispatch.length>0}updateTurnContext(e,t){this.currentPhase=e.phase,this.currentTurn=e.turn,this.currentMethod=t}unsetCurrentMethod(){this.currentMethod=void 0}update(e){const t=e,n=({stack:e},n)=>({...t,plugins:{...t.plugins,events:{...t.plugins.events,data:{error:n+"\n"+e}}}});e:for(let t=0;t<this.dispatch.length;t++){const r=this.dispatch[t],a=r.turn!==e.ctx.turn;if(this.currentTurn-this.initialTurn>=this.maxEndedTurnsPerAction)return n(r.error,nn.MaxTurnEndings);if(void 0===r.calledFrom)return n(r.error,nn.CalledOutsideHook);if(e.ctx.gameover)break e;switch(r.type){case"endStage":case"setStage":case"setActivePlayers":switch(r.calledFrom){case tn.TURN_ON_END:case tn.PHASE_ON_END:return n(r.error,nn.StageEventInOnEnd);case tn.PHASE_ON_BEGIN:return n(r.error,nn.StageEventInPhaseBegin);case tn.TURN_ON_BEGIN:if("setActivePlayers"===r.type)break;return n(r.error,nn.StageEventInTurnBegin)}if(a)continue e;break;case"endTurn":if(r.calledFrom===tn.TURN_ON_END||r.calledFrom===tn.PHASE_ON_END)return n(r.error,nn.EndTurnInOnEnd);if(a)continue e;break;case"endPhase":case"setPhase":if(r.calledFrom===tn.PHASE_ON_END)return n(r.error,nn.PhaseEventInOnEnd);if(r.phase!==e.ctx.phase)continue e}const o=Le(r.type,r.args,this.playerID);e=this.flow.processEvent(e,o)}return e}}const an={name:"events",noClient:({api:e})=>e._private.isUsed(),isInvalid:({data:e})=>e.error||!1,fnWrap:(e,t)=>(n,...r)=>{const a=n.events;a&&a._private.updateTurnContext(n.ctx,t);const o=e(n,...r);return a&&a._private.unsetCurrentMethod(),o},dangerouslyFlushRawState:({state:e,api:t})=>t._private.update(e),api:({game:e,ctx:t,playerID:n})=>new rn(e.flow,t,n).api()};var on,sn,ln=Function.prototype,cn=Object.prototype,un=ln.toString,dn=cn.hasOwnProperty,fn=un.call(Object),pn=cn.toString,gn=(on=Object.getPrototypeOf,sn=Object,function(e){return on(sn(e))});var vn=function(e){if(!function(e){return!!e&&"object"==typeof e}(e)||"[object Object]"!=pn.call(e)||function(e){var t=!1;if(null!=e&&"function"!=typeof e.toString)try{t=!!(e+"")}catch(e){}return t}(e))return!1;var t=gn(e);if(null===t)return!0;var n=dn.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&un.call(n)==fn};function hn(e){((...e)=>{console.log(...e)})("INFO: "+e)}function mn(e){((...e)=>{console.error(...e)})("ERROR:",e)}const yn=[Wt,en,{name:"log",flush:()=>({}),api:({data:e})=>({setMetadata:t=>{e.metadata=t}}),setup:()=>({})},{name:"plugin-serializable",fnWrap:e=>(t,...n)=>{const r=e(t,...n);if(!function e(t){if(null==t||"boolean"==typeof t||"number"==typeof t||"string"==typeof t)return!0;if(!vn(t)&&!Array.isArray(t))return!1;for(const n in t)if(!e(t[n]))return!1;return!0}(r))throw new Error("Move state is not JSON-serialiazable.\nSee https://boardgame.io/documentation/#/?id=state for more information.");return r}}],bn=[...yn,an],$n=({plugins:e})=>Object.entries(e||{}).reduce((e,[t,{api:n}])=>(e[t]=n,e),{}),xn=(e,t,n)=>[...yn,...n,an].filter(e=>void 0!==e.fnWrap).reduce((e,{fnWrap:n})=>n(e,t),e),Pn=(e,t)=>([...bn,...t.game.plugins].filter(e=>void 0!==e.api).forEach(n=>{const r=n.name,a=e.plugins[r]||{data:{}},o=n.api({G:e.G,ctx:e.ctx,data:a.data,game:t.game,playerID:t.playerID});e={...e,plugins:{...e.plugins,[r]:{...a,api:o}}}}),e),wn=(e,t)=>{const n=((e,t)=>([...yn,...t.game.plugins,an].reverse().forEach(n=>{const r=n.name,a=e.plugins[r]||{data:{}};if(n.flush){const r=n.flush({G:e.G,ctx:e.ctx,game:t.game,api:a.api,data:a.data});e={...e,plugins:{...e.plugins,[n.name]:{data:r}}}}else if(n.dangerouslyFlushRawState){const o=(e=n.dangerouslyFlushRawState({state:e,game:t.game,api:a.api,data:a.data})).plugins[r].data;e={...e,plugins:{...e.plugins,[n.name]:{data:o}}}}}),e))(e,t),r=((e,t)=>[...bn,...t.game.plugins].filter(e=>void 0!==e.isInvalid).map(n=>{const{name:r}=n,a=e.plugins[r],o=n.isInvalid({G:e.G,ctx:e.ctx,game:t.game,data:a&&a.data});return!!o&&{plugin:r,message:o}}).find(e=>e)||!1)(n,t);if(!r)return[n];const{plugin:a,message:o}=r;return mn(`${a} plugin declared action invalid:\n${o}`),[e,r]},kn=({G:e,ctx:t,plugins:n={}},{game:r,playerID:a})=>([...bn,...r.plugins].forEach(({name:o,playerView:i})=>{if(!i)return;const{data:s}=n[o]||{data:{}},l=i({G:e,ctx:t,game:r,data:s,playerID:a});n={...n,[o]:{data:l}}}),n);function En(e,t=!1){e.moveLimit&&(t&&(e.minMoves=e.moveLimit),e.maxMoves=e.moveLimit,delete e.moveLimit)}function On(e,t){let n={},r=[],a=null,o={},i={};if(Array.isArray(t)){const e={};t.forEach(t=>e[t]=Sn.NULL),n=e}else{if(En(t),t.next&&(a=t.next),t.revert&&(r=[...e._prevActivePlayers,{activePlayers:e.activePlayers,_activePlayersMinMoves:e._activePlayersMinMoves,_activePlayersMaxMoves:e._activePlayersMaxMoves,_activePlayersNumMoves:e._activePlayersNumMoves}]),void 0!==t.currentPlayer&&In(n,o,i,e.currentPlayer,t.currentPlayer),void 0!==t.others)for(let r=0;r<e.playOrder.length;r++){const a=e.playOrder[r];a!==e.currentPlayer&&In(n,o,i,a,t.others)}if(void 0!==t.all)for(let r=0;r<e.playOrder.length;r++){In(n,o,i,e.playOrder[r],t.all)}if(t.value)for(const e in t.value)In(n,o,i,e,t.value[e]);if(t.minMoves)for(const e in n)void 0===o[e]&&(o[e]=t.minMoves);if(t.maxMoves)for(const e in n)void 0===i[e]&&(i[e]=t.maxMoves)}0===Object.keys(n).length&&(n=null),0===Object.keys(o).length&&(o=null),0===Object.keys(i).length&&(i=null);const s={};for(const e in n)s[e]=0;return{...e,activePlayers:n,_activePlayersMinMoves:o,_activePlayersMaxMoves:i,_activePlayersNumMoves:s,_prevActivePlayers:r,_nextActivePlayers:a}}function In(e,t,n,r,a){"object"==typeof a&&a!==Sn.NULL||(a={stage:a}),void 0!==a.stage&&(En(a),e[r]=a.stage,a.minMoves&&(t[r]=a.minMoves),a.maxMoves&&(n[r]=a.maxMoves))}function _n(e,t){return e[t]+""}const An={first:({ctx:e})=>0===e.turn?e.playOrderPos:(e.playOrderPos+1)%e.playOrder.length,next:({ctx:e})=>(e.playOrderPos+1)%e.playOrder.length},Sn={NULL:null};function Dn({moves:e,phases:t,endIf:n,onEnd:r,turn:a,events:o,plugins:i}){void 0===e&&(e={}),void 0===o&&(o={}),void 0===i&&(i=[]),void 0===t&&(t={}),n||(n=()=>{}),r||(r=({G:e})=>e),a||(a={});const s={...t};""in s&&mn("cannot specify phase with empty name"),s[""]={};const l={},c=new Set;let u=null;Object.keys(e).forEach(e=>c.add(e));const d=(e,t)=>{const n=xn(e,t,i);return e=>{const t=$n(e);return n({...t,G:e.G,ctx:e.ctx,playerID:e.playerID})}},f=e=>t=>{const n=$n(t);return e({...n,G:t.G,ctx:t.ctx})},p={onEnd:d(r,tn.GAME_ON_END),endIf:f(n)};for(const e in s){const t=s[e];if(!0===t.start&&(u=e),void 0!==t.moves)for(const n of Object.keys(t.moves))l[e+"."+n]=t.moves[n],c.add(n);void 0===t.endIf&&(t.endIf=()=>{}),void 0===t.onBegin&&(t.onBegin=({G:e})=>e),void 0===t.onEnd&&(t.onEnd=({G:e})=>e),void 0===t.turn&&(t.turn=a),void 0===t.turn.order&&(t.turn.order=An),void 0===t.turn.onBegin&&(t.turn.onBegin=({G:e})=>e),void 0===t.turn.onEnd&&(t.turn.onEnd=({G:e})=>e),void 0===t.turn.endIf&&(t.turn.endIf=()=>!1),void 0===t.turn.onMove&&(t.turn.onMove=({G:e})=>e),void 0===t.turn.stages&&(t.turn.stages={}),En(t.turn,!0);for(const n in t.turn.stages){const r=t.turn.stages[n].moves||{};for(const t of Object.keys(r)){l[e+"."+n+"."+t]=r[t],c.add(t)}}if(t.wrapped={onBegin:d(t.onBegin,tn.PHASE_ON_BEGIN),onEnd:d(t.onEnd,tn.PHASE_ON_END),endIf:f(t.endIf)},t.turn.wrapped={onMove:d(t.turn.onMove,tn.TURN_ON_MOVE),onBegin:d(t.turn.onBegin,tn.TURN_ON_BEGIN),onEnd:d(t.turn.onEnd,tn.TURN_ON_END),endIf:f(t.turn.endIf)},"function"!=typeof t.next){const{next:e}=t;t.next=()=>e||null}t.wrapped.next=f(t.next)}function g(e){return e.phase?s[e.phase]:s[""]}function v(e){return e}function h(e,t){const n=new Set,r=new Set;for(let a=0;a<t.length;a++){const{fn:o,arg:i,...s}=t[a];if(o===_){r.clear();const t=e.ctx.phase;if(n.has(t)){const t={...e.ctx,phase:null};return{...e,ctx:t}}n.add(t)}const l=[];if(e=o(e,{...s,arg:i,next:l}),o===I)break;const c=k(e);if(c){t.push({fn:I,arg:c,turn:e.ctx.turn,phase:e.ctx.phase,automatic:!0});continue}const u=E(e);if(u)t.push({fn:_,arg:u,turn:e.ctx.turn,phase:e.ctx.phase,automatic:!0});else{if([v,P,w].includes(o)){const n=O(e);if(n){t.push({fn:A,arg:n,turn:e.ctx.turn,phase:e.ctx.phase,automatic:!0});continue}}t.push(...l)}}return e}function m(e,{next:t}){return t.push({fn:y}),e}function y(e,{next:t}){let{G:n,ctx:r}=e;return n=g(r).wrapped.onBegin(e),t.push({fn:b}),{...e,G:n,ctx:r}}function b(e,{currentPlayer:t}){let{ctx:n}=e;const r=g(n);t?(n={...n,currentPlayer:t},r.turn.activePlayers&&(n=On(n,r.turn.activePlayers))):n=function(e,t){let{G:n,ctx:r}=e;const{numPlayers:a}=r,o={...$n(e),G:n,ctx:r},i=t.order;let s=[...Array.from({length:a})].map((e,t)=>t+"");void 0!==i.playOrder&&(s=i.playOrder(o));const l=i.first(o),c=typeof l;"number"!==c&&mn(`invalid value returned by turn.order.first — expected number got ${c} “${l}”.`);const u=_n(s,l);return r={...r,currentPlayer:u,playOrderPos:l,playOrder:s},r=On(r,t.activePlayers||{}),r}(e,r.turn);const a=n.turn+1;n={...n,turn:a,numMoves:0,_prevActivePlayers:[]};const o=r.turn.wrapped.onBegin({...e,ctx:n});return{...e,G:o,ctx:n,_undo:[],_redo:[]}}function $(e,{arg:t,next:n,phase:r}){const a=g({phase:r});let{ctx:o}=e;if(t&&t.next){if(!(t.next in s))return mn("invalid phase: "+t.next),e;o={...o,phase:t.next}}else o={...o,phase:a.wrapped.next(e)||null};return e={...e,ctx:o},n.push({fn:y}),e}function x(e,{arg:t,currentPlayer:n,next:r}){let{G:a,ctx:o}=e;const i=g(o),{endPhase:s,ctx:l}=function(e,t,n,r){const a=n.order;let{G:o,ctx:i}=e,s=i.playOrderPos,l=!1;if(r&&!0!==r)"object"!=typeof r&&mn("invalid argument to endTurn: "+r),Object.keys(r).forEach(e=>{switch(e){case"remove":t=_n(i.playOrder,s);break;case"next":s=i.playOrder.indexOf(r.next),t=r.next;break;default:mn("invalid argument to endTurn: "+e)}});else{const n={...$n(e),G:o,ctx:i},r=a.next(n),c=typeof r;void 0!==r&&"number"!==c&&mn(`invalid value returned by turn.order.next — expected number or undefined got ${c} “${r}”.`),void 0===r?l=!0:(s=r,t=_n(i.playOrder,s))}return i={...i,playOrderPos:s,currentPlayer:t},{endPhase:l,ctx:i}}(e,n,i.turn,t);return o=l,e={...e,G:a,ctx:o},s?r.push({fn:_,turn:o.turn,phase:o.phase}):r.push({fn:b,currentPlayer:o.currentPlayer}),e}function P(e,{arg:t,playerID:n}){if("string"!=typeof t&&t!==Sn.NULL||(t={stage:t}),"object"!=typeof t)return e;En(t);let{ctx:r}=e,{activePlayers:a,_activePlayersMinMoves:o,_activePlayersMaxMoves:i,_activePlayersNumMoves:s}=r;return void 0!==t.stage&&(null===a&&(a={}),a[n]=t.stage,s[n]=0,t.minMoves&&(null===o&&(o={}),o[n]=t.minMoves),t.maxMoves&&(null===i&&(i={}),i[n]=t.maxMoves)),r={...r,activePlayers:a,_activePlayersMinMoves:o,_activePlayersMaxMoves:i,_activePlayersNumMoves:s},{...e,ctx:r}}function w(e,{arg:t}){return{...e,ctx:On(e.ctx,t)}}function k(e){return p.endIf(e)}function E(e){return g(e.ctx).wrapped.endIf(e)}function O(e){const t=g(e.ctx),n=e.ctx.numMoves||0;return!!(t.turn.maxMoves&&n>=t.turn.maxMoves)||t.turn.wrapped.endIf(e)}function I(e,{arg:t,phase:n}){e=_(e,{phase:n}),void 0===t&&(t=!0),e={...e,ctx:{...e.ctx,gameover:t}};const r=p.onEnd(e);return{...e,G:r}}function _(e,{arg:t,next:n,turn:r,automatic:a}){e=A(e,{turn:r,force:!0,automatic:!0});const{phase:o,turn:i}=e.ctx;if(n&&n.push({fn:$,arg:t,phase:o}),null===o)return e;const s=g(e.ctx).wrapped.onEnd(e),l={...e.ctx,phase:null},c=Fe("endPhase",t),{_stateID:u}=e,d={action:c,_stateID:u,turn:i,phase:o};a&&(d.automatic=!0);const f=[...e.deltalog||[],d];return{...e,G:s,ctx:l,deltalog:f}}function A(e,{arg:t,next:n,turn:r,force:a,automatic:o,playerID:i}){if(r!==e.ctx.turn)return e;const{currentPlayer:s,numMoves:l,phase:c,turn:u}=e.ctx,d=g(e.ctx),f=l||0;if(!a&&d.turn.minMoves&&f<d.turn.minMoves)return hn(`cannot end turn before making ${d.turn.minMoves} moves`),e;const p=d.turn.wrapped.onEnd(e);n&&n.push({fn:x,arg:t,currentPlayer:s});let v={...e.ctx,activePlayers:null};if(t&&t.remove){i=i||s;const t=v.playOrder.filter(e=>e!=i),r=v.playOrderPos>t.length-1?0:v.playOrderPos;if(v={...v,playOrder:t,playOrderPos:r},0===t.length)return n.push({fn:_,turn:u,phase:c}),e}const h=Fe("endTurn",t),{_stateID:m}=e,y={action:h,_stateID:m,turn:u,phase:c};o&&(y.automatic=!0);const b=[...e.deltalog||[],y];return{...e,G:p,ctx:v,deltalog:b,_undo:[],_redo:[]}}function S(e,{arg:t,next:n,automatic:r,playerID:a}){a=a||e.ctx.currentPlayer;let{ctx:o,_stateID:i}=e,{activePlayers:s,_activePlayersNumMoves:l,_activePlayersMinMoves:c,_activePlayersMaxMoves:u,phase:d,turn:f}=o;const p=null!==s&&a in s,v=g(o);if(!t&&p){const e=v.turn.stages[s[a]];e&&e.next&&(t=e.next)}if(n&&n.push({fn:P,arg:t,playerID:a}),!p)return e;const h=l[a]||0;if(c&&c[a]&&h<c[a])return hn(`cannot end stage before making ${c[a]} moves`),e;s={...s},delete s[a],c&&(c={...c},delete c[a]),u&&(u={...u},delete u[a]),o=function(e){let{activePlayers:t,_activePlayersMinMoves:n,_activePlayersMaxMoves:r,_activePlayersNumMoves:a,_prevActivePlayers:o,_nextActivePlayers:i}=e;if(t&&0===Object.keys(t).length)if(i)e=On(e,i),({activePlayers:t,_activePlayersMinMoves:n,_activePlayersMaxMoves:r,_activePlayersNumMoves:a,_prevActivePlayers:o}=e);else if(o.length>0){const e=o.length-1;({activePlayers:t,_activePlayersMinMoves:n,_activePlayersMaxMoves:r,_activePlayersNumMoves:a}=o[e]),o=o.slice(0,e)}else t=null,n=null,r=null;return{...e,activePlayers:t,_activePlayersMinMoves:n,_activePlayersMaxMoves:r,_activePlayersNumMoves:a,_prevActivePlayers:o}}({...o,activePlayers:s,_activePlayersMinMoves:c,_activePlayersMaxMoves:u});const m={action:Fe("endStage",t),_stateID:i,turn:f,phase:d};r&&(m.automatic=!0);const y=[...e.deltalog||[],m];return{...e,ctx:o,deltalog:y}}function D(t,n,r){const a=g(t),o=a.turn.stages,{activePlayers:i}=t;if(i&&void 0!==i[r]&&i[r]!==Sn.NULL&&void 0!==o[i[r]]&&void 0!==o[i[r]].moves){const e=o[i[r]].moves;if(n in e)return e[n]}else if(a.moves){if(n in a.moves)return a.moves[n]}else if(n in e)return e[n];return null}const M={endStage:function(e,t){return h(e,[{fn:S,playerID:t}])},setStage:function(e,t,n){return h(e,[{fn:S,arg:n,playerID:t}])},endTurn:function(e,t,n){return h(e,[{fn:A,turn:e.ctx.turn,phase:e.ctx.phase,arg:n}])},pass:function(e,t,n){return h(e,[{fn:A,turn:e.ctx.turn,phase:e.ctx.phase,force:!0,arg:n}])},endPhase:function(e){return h(e,[{fn:_,phase:e.ctx.phase,turn:e.ctx.turn}])},setPhase:function(e,t,n){return h(e,[{fn:_,phase:e.ctx.phase,turn:e.ctx.turn,arg:{next:n}}])},endGame:function(e,t,n){return h(e,[{fn:I,turn:e.ctx.turn,phase:e.ctx.phase,arg:n}])},setActivePlayers:function(e,t,n){return h(e,[{fn:w,arg:n}])}},j=[];return!1!==o.endTurn&&j.push("endTurn"),!1!==o.pass&&j.push("pass"),!1!==o.endPhase&&j.push("endPhase"),!1!==o.setPhase&&j.push("setPhase"),!1!==o.endGame&&j.push("endGame"),!1!==o.setActivePlayers&&j.push("setActivePlayers"),!1!==o.endStage&&j.push("endStage"),!1!==o.setStage&&j.push("setStage"),{ctx:e=>({numPlayers:e,turn:0,currentPlayer:"0",playOrder:[...Array.from({length:e})].map((e,t)=>t+""),playOrderPos:0,phase:u,activePlayers:null}),init:e=>h(e,[{fn:m}]),isPlayerActive:function(e,t,n){return t.activePlayers?n in t.activePlayers:t.currentPlayer===n},eventHandlers:M,eventNames:Object.keys(M),enabledEventNames:j,moveMap:l,moveNames:[...c.values()],processMove:function(e,t){const{playerID:n,type:r}=t,{currentPlayer:a,activePlayers:o,_activePlayersMaxMoves:i}=e.ctx,s=D(e.ctx,r,n),l=!s||"function"==typeof s||!0!==s.noLimit;let{numMoves:c,_activePlayersNumMoves:u}=e.ctx;l&&(n===a&&c++,o&&u[n]++),e={...e,ctx:{...e.ctx,numMoves:c,_activePlayersNumMoves:u}},i&&u[n]>=i[n]&&(e=S(e,{playerID:n,automatic:!0}));const d=g(e.ctx).turn.wrapped.onMove({...e,playerID:n});return h(e={...e,G:d},[{fn:v}])},processEvent:function(e,t){const{type:n,playerID:r,args:a}=t.payload;return"function"!=typeof M[n]?e:M[n](e,r,...Array.isArray(a)?a:[a])},getMove:D}}function Mn(e){if(function(e){return void 0!==e.processMove}(e))return e;if(void 0===e.name&&(e.name="default"),void 0===e.deltaState&&(e.deltaState=!1),void 0===e.disableUndo&&(e.disableUndo=!1),void 0===e.setup&&(e.setup=()=>({})),void 0===e.moves&&(e.moves={}),void 0===e.playerView&&(e.playerView=({G:e})=>e),void 0===e.plugins&&(e.plugins=[]),e.plugins.forEach(e=>{if(void 0===e.name)throw new Error("Plugin missing name attribute");if(e.name.includes(" "))throw new Error(e.name+": Plugin name must not include spaces")}),e.name.includes(" "))throw new Error(e.name+": Game name must not include spaces");const t=Dn(e);return{...e,flow:t,moveNames:t.moveNames,pluginNames:e.plugins.map(e=>e.name),processMove:(n,r)=>{let a=t.getMove(n.ctx,r.type,r.playerID);var o;if((o=a)instanceof Object&&void 0!==o.move&&(a=a.move),a instanceof Function){const t=xn(a,tn.MOVE,e.plugins);let o=[];void 0!==r.args&&(o=Array.isArray(r.args)?r.args:[r.args]);return t({...$n(n),G:n.G,ctx:n.ctx,playerID:r.playerID},...o)}return mn("invalid move object: "+r.type),n.G}}}var jn,Nn;!function(e){e.UnauthorizedAction="update/unauthorized_action",e.MatchNotFound="update/match_not_found",e.PatchFailed="update/patch_failed"}(jn||(jn={})),function(e){e.StaleStateId="action/stale_state_id",e.UnavailableMove="action/unavailable_move",e.InvalidMove="action/invalid_move",e.InactivePlayer="action/inactive_player",e.GameOver="action/gameover",e.ActionDisabled="action/action_disabled",e.ActionInvalid="action/action_invalid",e.PluginActionInvalid="action/plugin_invalid"}(Nn||(Nn={}));var Tn="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function Cn(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function Gn(e,t){return e(t={exports:{}},t.exports),t.exports}var zn=Gn((function(e,t){function n(e){return e.replace(/~1/g,"/").replace(/~0/g,"~")}function r(e){return e.replace(/~/g,"~0").replace(/\//g,"~1")}Object.defineProperty(t,"__esModule",{value:!0}),t.Pointer=void 0;var a=function(){function e(e){void 0===e&&(e=[""]),this.tokens=e}return e.fromJSON=function(t){var r=t.split("/").map(n);if(""!==r[0])throw new Error("Invalid JSON Pointer: "+t);return new e(r)},e.prototype.toString=function(){return this.tokens.map(r).join("/")},e.prototype.evaluate=function(e){for(var t=null,n="",r=e,a=1,o=this.tokens.length;a<o;a++)t=r,"__proto__"!=(n=this.tokens[a])&&"constructor"!=n&&"prototype"!=n&&(r=(t||{})[n]);return{parent:t,key:n,value:r}},e.prototype.get=function(e){return this.evaluate(e).value},e.prototype.set=function(e,t){for(var n=e,r=1,a=this.tokens.length-1,o=this.tokens[r];r<a;r++)n=(n||{})[o];n&&(n[this.tokens[this.tokens.length-1]]=t)},e.prototype.push=function(e){this.tokens.push(e)},e.prototype.add=function(t){return new e(this.tokens.concat(String(t)))},e}();t.Pointer=a}));Cn(zn);zn.Pointer;var qn=Gn((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.clone=t.objectType=t.hasOwnProperty=void 0,t.hasOwnProperty=Object.prototype.hasOwnProperty,t.objectType=function(e){return void 0===e?"undefined":null===e?"null":Array.isArray(e)?"array":typeof e},t.clone=function e(n){if(null==(r=n)||"object"!=typeof r)return n;var r;if(n.constructor==Array){for(var a=n.length,o=new Array(a),i=0;i<a;i++)o[i]=e(n[i]);return o}if(n.constructor==Date)return new Date(+n);var s={};for(var l in n)t.hasOwnProperty.call(n,l)&&(s[l]=e(n[l]));return s}}));Cn(qn);qn.clone,qn.objectType;var Vn=Gn((function(e,t){function n(e,t){var n={};for(var r in e)qn.hasOwnProperty.call(e,r)&&void 0!==e[r]&&(n[r]=1);for(var a in t)qn.hasOwnProperty.call(t,a)&&void 0!==t[a]&&delete n[a];return Object.keys(n)}function r(e){for(var t=e.length,n={},r=0;r<t;r++){var a=e[r];for(var o in a)qn.hasOwnProperty.call(a,o)&&void 0!==a[o]&&(n[o]=(n[o]||0)+1)}for(var o in n)n[o]<t&&delete n[o];return Object.keys(n)}function a(e,t){return{operations:e.operations.concat(t),cost:e.cost+1}}function o(e,t,n,r){void 0===r&&(r=s);var o={"0,0":{operations:[],cost:0}};var i=isNaN(e.length)||e.length<=0?0:e.length,l=isNaN(t.length)||t.length<=0?0:t.length;return function n(i,s){var l=i+","+s,c=o[l];if(void 0===c){if(i>0&&s>0&&!r(e[i-1],t[s-1],new zn.Pointer).length)c=n(i-1,s-1);else{var u=[];if(i>0){var d=n(i-1,s),f={op:"remove",index:i-1};u.push(a(d,f))}if(s>0){var p=n(i,s-1),g={op:"add",index:i-1,value:t[s-1]};u.push(a(p,g))}if(i>0&&s>0){var v=n(i-1,s-1),h={op:"replace",index:i-1,original:e[i-1],value:t[s-1]};u.push(a(v,h))}c=u.sort((function(e,t){return e.cost-t.cost}))[0]}o[l]=c}return c}(i,l).operations.reduce((function(e,t){var a=e[0],o=e[1];if(function(e){return"add"===e.op}(t)){var s=t.index+1+o,l=s<i+o?String(s):"-",c={op:t.op,path:n.add(l).toString(),value:t.value};return[a.concat(c),o+1]}if(function(e){return"remove"===e.op}(t)){c={op:t.op,path:n.add(String(t.index+o)).toString()};return[a.concat(c),o-1]}var u=n.add(String(t.index+o)),d=r(t.original,t.value,u);return[a.concat.apply(a,d),o]}),[[],0])[0]}function i(e,t,a,o){void 0===o&&(o=s);var i=[];return n(e,t).forEach((function(e){i.push({op:"remove",path:a.add(e).toString()})})),n(t,e).forEach((function(e){i.push({op:"add",path:a.add(e).toString(),value:t[e]})})),r([e,t]).forEach((function(n){i.push.apply(i,o(e[n],t[n],a.add(n)))})),i}function s(e,t,n,r){if(void 0===r&&(r=s),e===t)return[];var a=qn.objectType(e),l=qn.objectType(t);return"array"==a&&"array"==l?o(e,t,n,r):"object"==a&&"object"==l?i(e,t,n,r):[{op:"replace",path:n.toString(),value:t}]}Object.defineProperty(t,"__esModule",{value:!0}),t.diffAny=t.diffObjects=t.diffArrays=t.intersection=t.subtract=t.isDestructive=void 0,t.isDestructive=function(e){var t=e.op;return"remove"===t||"replace"===t||"copy"===t||"move"===t},t.subtract=n,t.intersection=r,t.diffArrays=o,t.diffObjects=i,t.diffAny=s}));Cn(Vn);Vn.diffAny,Vn.diffObjects,Vn.diffArrays,Vn.intersection,Vn.subtract,Vn.isDestructive;var Rn=Gn((function(e,t){var n,r=Tn&&Tn.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0}),t.apply=t.InvalidOperationError=t.test=t.copy=t.move=t.replace=t.remove=t.add=t.TestError=t.MissingError=void 0;var a=function(e){function t(t){var n=e.call(this,"Value required at path: "+t)||this;return n.path=t,n.name="MissingError",n}return r(t,e),t}(Error);t.MissingError=a;var o=function(e){function t(t,n){var r=e.call(this,"Test failed: "+t+" != "+n)||this;return r.actual=t,r.expected=n,r.name="TestError",r}return r(t,e),t}(Error);function i(e,t,n){if(Array.isArray(e))if("-"==t)e.push(n);else{var r=parseInt(t,10);e.splice(r,0,n)}else e[t]=n}function s(e,t){if(Array.isArray(e)){var n=parseInt(t,10);e.splice(n,1)}else delete e[t]}function l(e,t){var n=zn.Pointer.fromJSON(t.path).evaluate(e);return void 0===n.parent?new a(t.path):(i(n.parent,n.key,qn.clone(t.value)),null)}function c(e,t){var n=zn.Pointer.fromJSON(t.path).evaluate(e);return void 0===n.value?new a(t.path):(s(n.parent,n.key),null)}function u(e,t){var n=zn.Pointer.fromJSON(t.path).evaluate(e);if(null===n.parent)return new a(t.path);if(Array.isArray(n.parent)){if(parseInt(n.key,10)>=n.parent.length)return new a(t.path)}else if(void 0===n.value)return new a(t.path);return n.parent[n.key]=t.value,null}function d(e,t){var n=zn.Pointer.fromJSON(t.from).evaluate(e);if(void 0===n.value)return new a(t.from);var r=zn.Pointer.fromJSON(t.path).evaluate(e);return void 0===r.parent?new a(t.path):(s(n.parent,n.key),i(r.parent,r.key,n.value),null)}function f(e,t){var n=zn.Pointer.fromJSON(t.from).evaluate(e);if(void 0===n.value)return new a(t.from);var r=zn.Pointer.fromJSON(t.path).evaluate(e);return void 0===r.parent?new a(t.path):(i(r.parent,r.key,qn.clone(n.value)),null)}function p(e,t){var n=zn.Pointer.fromJSON(t.path).evaluate(e);return Vn.diffAny(n.value,t.value,new zn.Pointer).length?new o(n.value,t.value):null}t.TestError=o,t.add=l,t.remove=c,t.replace=u,t.move=d,t.copy=f,t.test=p;var g=function(e){function t(t){var n=e.call(this,"Invalid operation: "+t.op)||this;return n.operation=t,n.name="InvalidOperationError",n}return r(t,e),t}(Error);t.InvalidOperationError=g,t.apply=function(e,t){switch(t.op){case"add":return l(e,t);case"remove":return c(e,t);case"replace":return u(e,t);case"move":return d(e,t);case"copy":return f(e,t);case"test":return p(e,t)}return new g(t)}}));Cn(Rn);Rn.apply,Rn.InvalidOperationError,Rn.test,Rn.copy,Rn.move,Rn.replace,Rn.remove,Rn.add,Rn.TestError,Rn.MissingError;var Bn=Gn((function(e,t){function n(e,t){var n=zn.Pointer.fromJSON(t).evaluate(e);if(void 0!==n)return{op:"test",path:t,value:n.value}}Object.defineProperty(t,"__esModule",{value:!0}),t.createTests=t.createPatch=t.applyPatch=void 0,t.applyPatch=function(e,t){return t.map((function(t){return Rn.apply(e,t)}))},t.createPatch=function(e,t,n){var r=new zn.Pointer;return(n?function(e){return function t(n,r,a){var o=e(n,r,a);return Array.isArray(o)?o:Vn.diffAny(n,r,a,t)}}(n):Vn.diffAny)(e,t,r)},t.createTests=function(e,t){var r=new Array;return t.filter(Vn.isDestructive).forEach((function(t){var a=n(e,t.path);if(a&&r.push(a),"from"in t){var o=n(e,t.from);o&&r.push(o)}})),r}}));Cn(Bn);Bn.createTests,Bn.createPatch;var Un=Bn.applyPatch;const Fn=e=>null!==e.payload.playerID&&void 0!==e.payload.playerID;function Ln(e,t){if(t.game.disableUndo)return e;const n={G:e.G,ctx:e.ctx,plugins:e.plugins,playerID:t.action.payload.playerID||e.ctx.currentPlayer};return"MAKE_MOVE"===t.action.type&&(n.moveType=t.action.payload.type),{...e,_undo:[...e._undo,n],_redo:[]}}function Jn(e,t,n){const r={action:t,_stateID:e._stateID,turn:e.ctx.turn,phase:e.ctx.phase},a=e.plugins.log.data.metadata;return void 0!==a&&(r.metadata=a),"object"==typeof n&&!0===n.redact?r.redact=!0:"object"==typeof n&&n.redact instanceof Function&&(r.redact=n.redact({G:e.G,ctx:e.ctx})),{...e,deltalog:[r]}}function Kn(e,t,n){const[r,a]=wn(e,n);return a?[r,Wn(t,Nn.PluginActionInvalid,a)]:[r]}function Hn(e){if(!e)return[null,void 0];const{transients:t,...n}=e;return[n,t]}function Wn(e,t,n){return{...e,transients:{error:{type:t,payload:n}}}}const Zn=e=>t=>n=>{const r=t(n);switch(n.type){case"STRIP_TRANSIENTS":return r;default:{const[,t]=Hn(e.getState());return void 0!==t?(e.dispatch({type:"STRIP_TRANSIENTS"}),{...r,transients:t}):r}}};function Yn({game:e,isClient:t}){return e=Mn(e),(n=null,r)=>{let[a]=Hn(n);switch(r.type){case"STRIP_TRANSIENTS":return a;case"GAME_EVENT":{if(a={...a,deltalog:[]},t)return a;if(void 0!==a.ctx.gameover)return mn("cannot call event after game end"),Wn(a,Nn.GameOver);if(Fn(r)&&!e.flow.isPlayerActive(a.G,a.ctx,r.payload.playerID))return mn("disallowed event: "+r.payload.type),Wn(a,Nn.InactivePlayer);a=Pn(a,{game:e,isClient:!1,playerID:r.payload.playerID});let n,o=e.flow.processEvent(a,r);return[o,n]=Kn(o,a,{game:e,isClient:!1}),n?n:(o=Ln(o,{game:e,action:r}),{...o,_stateID:a._stateID+1})}case"MAKE_MOVE":{const n=a={...a,deltalog:[]},o=e.flow.getMove(a.ctx,r.payload.type,r.payload.playerID||a.ctx.currentPlayer);if(null===o)return mn("disallowed move: "+r.payload.type),Wn(a,Nn.UnavailableMove);if(t&&!1===o.client)return a;if(void 0!==a.ctx.gameover)return mn("cannot make move after game end"),Wn(a,Nn.GameOver);if(Fn(r)&&!e.flow.isPlayerActive(a.G,a.ctx,r.payload.playerID))return mn("disallowed move: "+r.payload.type),Wn(a,Nn.InactivePlayer);a=Pn(a,{game:e,isClient:t,playerID:r.payload.playerID});const i=e.processMove(a,r.payload);if("INVALID_MOVE"===i)return mn(`invalid move: ${r.payload.type} args: ${r.payload.args}`),Wn(a,Nn.InvalidMove);const s={...a,G:i};if(t&&((e,t)=>[...bn,...t.game.plugins].filter(e=>void 0!==e.noClient).map(n=>{const r=n.name,a=e.plugins[r];return!!a&&n.noClient({G:e.G,ctx:e.ctx,game:t.game,api:a.api,data:a.data})}).includes(!0))(s,{game:e}))return a;if(a=s,t){let t;return[a,t]=Kn(a,n,{game:e,isClient:!0}),t||{...a,_stateID:a._stateID+1}}let l;return a=Jn(a,r,o),a=e.flow.processMove(a,r.payload),[a,l]=Kn(a,n,{game:e}),l?l:(a=Ln(a,{game:e,action:r}),{...a,_stateID:a._stateID+1})}case"RESET":case"UPDATE":case"SYNC":return r.state;case"UNDO":{if(a={...a,deltalog:[]},e.disableUndo)return mn("Undo is not enabled"),Wn(a,Nn.ActionDisabled);const{G:t,ctx:n,_undo:o,_redo:i,_stateID:s}=a;if(o.length<2)return mn("No moves to undo"),Wn(a,Nn.ActionInvalid);const l=o[o.length-1],c=o[o.length-2];if(Fn(r)&&r.payload.playerID!==l.playerID)return mn("Cannot undo other players' moves"),Wn(a,Nn.ActionInvalid);if(l.moveType){if(!((e,t,n)=>!function(e){return void 0!==e.undoable}(n)||(n.undoable instanceof Function?n.undoable({G:e,ctx:t}):n.undoable))(t,n,e.flow.getMove(c.ctx,l.moveType,l.playerID)))return mn("Move cannot be undone"),Wn(a,Nn.ActionInvalid)}return a=Jn(a,r),{...a,G:c.G,ctx:c.ctx,plugins:c.plugins,_stateID:s+1,_undo:o.slice(0,-1),_redo:[l,...i]}}case"REDO":{if(a={...a,deltalog:[]},e.disableUndo)return mn("Redo is not enabled"),Wn(a,Nn.ActionDisabled);const{_undo:t,_redo:n,_stateID:o}=a;if(0===n.length)return mn("No moves to redo"),Wn(a,Nn.ActionInvalid);const i=n[0];return Fn(r)&&r.payload.playerID!==i.playerID?(mn("Cannot redo other players' moves"),Wn(a,Nn.ActionInvalid)):(a=Jn(a,r),{...a,G:i.G,ctx:i.ctx,plugins:i.plugins,_stateID:o+1,_undo:[...t,i],_redo:n.slice(1)})}case"PLUGIN":return((e,t,n)=>(n.game.plugins.filter(e=>void 0!==e.action).filter(e=>e.name===t.payload.type).forEach(n=>{const r=n.name,a=e.plugins[r]||{data:{}},o=n.action(a.data,t.payload);e={...e,plugins:{...e.plugins,[r]:{...a,data:o}}}}),e))(a,r,{game:e});case"PATCH":{const e=a,t=JSON.parse(JSON.stringify(e)),n=Un(t,r.patch);return n.some(e=>null!==e)?(mn(`Patch ${JSON.stringify(r.patch)} apply failed`),Wn(e,jn.PatchFailed,n)):t}default:return a}}}function Xn({game:e,numPlayers:t,setupData:n}){t||(t=2);let r={G:{},ctx:(e=Mn(e)).flow.ctx(t),plugins:{}};r=((e,t)=>([...bn,...t.game.plugins].filter(e=>void 0!==e.setup).forEach(n=>{const r=n.name,a=n.setup({G:e.G,ctx:e.ctx,game:t.game});e={...e,plugins:{...e.plugins,[r]:{data:a}}}}),e))(r,{game:e}),r=Pn(r,{game:e,playerID:void 0});const a=$n(r);r.G=e.setup({...a,ctx:r.ctx},n);let o={...r,_undo:[],_redo:[],_stateID:0};return o=e.flow.init(o),[o]=wn(o,{game:e}),e.disableUndo||(o._undo=[{G:o.G,ctx:o.ctx,plugins:o.plugins}]),o}class Qn extends class{constructor({transportDataCallback:e,gameName:t,playerID:n,matchID:r,credentials:a,numPlayers:o}){this.connectionStatusCallback=()=>{},this.isConnected=!1,this.transportDataCallback=e,this.gameName=t||"default",this.playerID=n||null,this.matchID=r||"default",this.credentials=a,this.numPlayers=o||2}subscribeToConnectionStatus(e){this.connectionStatusCallback=e}setConnectionStatus(e){this.isConnected=e,this.connectionStatusCallback()}notifyClient(e){this.transportDataCallback(e)}}{connect(){}disconnect(){}sendAction(){}sendChatMessage(){}requestSync(){}updateCredentials(){}updateMatchID(){}updatePlayerID(){}}const er=e=>new Qn(e),tr=[];function nr(e,t=n){let r;const a=new Set;function o(t){if(c(e,t)&&(e=t,r)){const t=!tr.length;for(const t of a)t[1](),tr.push(t,e);if(t){for(let e=0;e<tr.length;e+=2)tr[e][0](tr[e+1]);tr.length=0}}}return{set:o,update:function(t){o(t(e))},subscribe:function(i,s=n){const l=[i,s];return a.add(l),1===a.size&&(r=t(o)||n),i(e),()=>{a.delete(l),0===a.size&&(r(),r=null)}}}}function rr(e){const t=e-1;return t*t*t+1}
/*! *****************************************************************************
  Copyright (c) Microsoft Corporation.

  Permission to use, copy, modify, and/or distribute this software for any
  purpose with or without fee is hereby granted.

  THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
  REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
  AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
  INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
  LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
  OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
  PERFORMANCE OF THIS SOFTWARE.
  ***************************************************************************** */function ar(e,{delay:t=0,duration:n=400,easing:r=rr,x:a=0,y:o=0,opacity:i=0}={}){const s=getComputedStyle(e),l=+s.opacity,c="none"===s.transform?"":s.transform,u=l*(1-i);return{delay:t,duration:n,easing:r,css:(e,t)=>`\n\t\t\ttransform: ${c} translate(${(1-e)*a}px, ${(1-e)*o}px);\n\t\t\topacity: ${l-u*t}`}}function or(e){var{fallback:t}=e,n=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(r=Object.getOwnPropertySymbols(e);a<r.length;a++)t.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]])}return n}(e,["fallback"]);const r=new Map,o=new Map;function i(e,r,o){return(i,s)=>(e.set(s.key,{rect:i.getBoundingClientRect()}),()=>{if(r.has(s.key)){const{rect:e}=r.get(s.key);return r.delete(s.key),function(e,t,r){const{delay:o=0,duration:i=(e=>30*Math.sqrt(e)),easing:s=rr}=a(a({},n),r),c=t.getBoundingClientRect(),u=e.left-c.left,d=e.top-c.top,f=e.width/c.width,p=e.height/c.height,g=Math.sqrt(u*u+d*d),v=getComputedStyle(t),h="none"===v.transform?"":v.transform,m=+v.opacity;return{delay:o,duration:l(i)?i(g):i,easing:s,css:(e,t)=>`\n\t\t\t\topacity: ${e*m};\n\t\t\t\ttransform-origin: top left;\n\t\t\t\ttransform: ${h} translate(${t*u}px,${t*d}px) scale(${e+(1-e)*f}, ${e+(1-e)*p});\n\t\t\t`}}(e,i,s)}return e.delete(s.key),t&&t(i,s,o)})}return[i(o,r,!1),i(r,o,!0)]}function ir(e){P(e,"svelte-c8tyih","svg.svelte-c8tyih{stroke:currentColor;fill:currentColor;stroke-width:0;width:100%;height:auto;max-height:100%}")}function sr(e){let t,n;return{c(){t=S("title"),n=D(e[0])},m(e,r){O(e,t,r),x(t,n)},p(e,t){1&t&&G(n,e[0])},d(e){e&&I(t)}}}function lr(e){let t,n,r,a=e[0]&&sr(e);const o=e[3].default,i=function(e,t,n,r){if(e){const a=f(e,t,n,r);return e[0](a)}}(o,e,e[2],null);return{c(){t=S("svg"),a&&a.c(),n=j(),i&&i.c(),C(t,"xmlns","http://www.w3.org/2000/svg"),C(t,"viewBox",e[1]),C(t,"class","svelte-c8tyih")},m(e,o){O(e,t,o),a&&a.m(t,null),x(t,n),i&&i.m(t,null),r=!0},p(e,[s]){e[0]?a?a.p(e,s):(a=sr(e),a.c(),a.m(t,n)):a&&(a.d(1),a=null),i&&i.p&&(!r||4&s)&&function(e,t,n,r,a,o){if(a){const i=f(t,n,r,o);e.p(i,a)}}(i,o,e,e[2],r?function(e,t,n,r){if(e[2]&&r){const a=e[2](r(n));if(void 0===t.dirty)return a;if("object"==typeof a){const e=[],n=Math.max(t.dirty.length,a.length);for(let r=0;r<n;r+=1)e[r]=t.dirty[r]|a[r];return e}return t.dirty|a}return t.dirty}(o,e[2],s,null):function(e){if(e.ctx.length>32){const t=[],n=e.ctx.length/32;for(let e=0;e<n;e++)t[e]=-1;return t}return-1}(e[2]),null),(!r||2&s)&&C(t,"viewBox",e[1])},i(e){r||(be(i,e),r=!0)},o(e){$e(i,e),r=!1},d(e){e&&I(t),a&&a.d(),i&&i.d(e)}}}function cr(e,t,n){let{$$slots:r={},$$scope:a}=t,{title:o=null}=t,{viewBox:i}=t;return e.$$set=e=>{"title"in e&&n(0,o=e.title),"viewBox"in e&&n(1,i=e.viewBox),"$$scope"in e&&n(2,a=e.$$scope)},[o,i,a,r]}class ur extends Me{constructor(e){super(),De(this,e,cr,lr,c,{title:0,viewBox:1},ir)}}function dr(e){let t;return{c(){t=S("path"),C(t,"d","M285.476 272.971L91.132 467.314c-9.373 9.373-24.569 9.373-33.941 0l-22.667-22.667c-9.357-9.357-9.375-24.522-.04-33.901L188.505 256 34.484 101.255c-9.335-9.379-9.317-24.544.04-33.901l22.667-22.667c9.373-9.373 24.569-9.373 33.941 0L285.475 239.03c9.373 9.372 9.373 24.568.001 33.941z")},m(e,n){O(e,t,n)},p:n,d(e){e&&I(t)}}}function fr(e){let t,n;const r=[{viewBox:"0 0 320 512"},e[0]];let o={$$slots:{default:[dr]},$$scope:{ctx:e}};for(let e=0;e<r.length;e+=1)o=a(o,r[e]);return t=new ur({props:o}),{c(){Ie(t.$$.fragment)},m(e,r){_e(t,e,r),n=!0},p(e,[n]){const a=1&n?Ee(r,[r[0],Oe(e[0])]):{};2&n&&(a.$$scope={dirty:n,ctx:e}),t.$set(a)},i(e){n||(be(t.$$.fragment,e),n=!0)},o(e){$e(t.$$.fragment,e),n=!1},d(e){Ae(t,e)}}}function pr(e,t,n){return e.$$set=e=>{n(0,t=a(a({},t),p(e)))},[t=p(t)]}class gr extends Me{constructor(e){super(),De(this,e,pr,fr,c,{})}}function vr(e){P(e,"svelte-1xg9v5h",".menu.svelte-1xg9v5h{display:flex;margin-top:43px;flex-direction:row-reverse;border:1px solid #ccc;border-radius:5px 5px 0 0;height:25px;line-height:25px;margin-right:-500px;transform-origin:bottom right;transform:rotate(-90deg) translate(0, -500px)}.menu-item.svelte-1xg9v5h{line-height:25px;cursor:pointer;border:0;background:#fefefe;color:#555;padding-left:15px;padding-right:15px;text-align:center}.menu-item.svelte-1xg9v5h:first-child{border-radius:0 5px 0 0}.menu-item.svelte-1xg9v5h:last-child{border-radius:5px 0 0 0}.menu-item.active.svelte-1xg9v5h{cursor:default;font-weight:bold;background:#ddd;color:#555}.menu-item.svelte-1xg9v5h:hover,.menu-item.svelte-1xg9v5h:focus{background:#eee;color:#555}")}function hr(e,t,n){const r=e.slice();return r[4]=t[n][0],r[5]=t[n][1].label,r}function mr(e){let t,n,r,a,o,i=e[5]+"";function s(){return e[3](e[4])}return{c(){t=A("button"),n=D(i),r=M(),C(t,"class","menu-item svelte-1xg9v5h"),R(t,"active",e[0]==e[4])},m(e,i){O(e,t,i),x(t,n),x(t,r),a||(o=N(t,"click",s),a=!0)},p(r,a){e=r,2&a&&i!==(i=e[5]+"")&&G(n,i),3&a&&R(t,"active",e[0]==e[4])},d(e){e&&I(t),a=!1,o()}}}function yr(e){let t,r=Object.entries(e[1]),a=[];for(let t=0;t<r.length;t+=1)a[t]=mr(hr(e,r,t));return{c(){t=A("nav");for(let e=0;e<a.length;e+=1)a[e].c();C(t,"class","menu svelte-1xg9v5h")},m(e,n){O(e,t,n);for(let e=0;e<a.length;e+=1)a[e].m(t,null)},p(e,[n]){if(7&n){let o;for(r=Object.entries(e[1]),o=0;o<r.length;o+=1){const i=hr(e,r,o);a[o]?a[o].p(i,n):(a[o]=mr(i),a[o].c(),a[o].m(t,null))}for(;o<a.length;o+=1)a[o].d(1);a.length=r.length}},i:n,o:n,d(e){e&&I(t),_(a,e)}}}function br(e,t,n){let{pane:r}=t,{panes:a}=t;const o=Y();return e.$$set=e=>{"pane"in e&&n(0,r=e.pane),"panes"in e&&n(1,a=e.panes)},[r,a,o,e=>o("change",e)]}class $r extends Me{constructor(e){super(),De(this,e,br,yr,c,{pane:0,panes:1},vr)}}var xr={};function Pr(e){P(e,"svelte-1vyml86",".container.svelte-1vyml86{display:inline-block;cursor:pointer;transform:translate(calc(0px - var(--li-identation)), -50%);position:absolute;top:50%;padding-right:100%}.arrow.svelte-1vyml86{transform-origin:25% 50%;position:relative;line-height:1.1em;font-size:0.75em;margin-left:0;transition:150ms;color:var(--arrow-sign);user-select:none;font-family:'Courier New', Courier, monospace}.expanded.svelte-1vyml86{transform:rotateZ(90deg) translateX(-3px)}")}function wr(e){let t,r,a,o;return{c(){t=A("div"),r=A("div"),r.textContent="▶",C(r,"class","arrow svelte-1vyml86"),R(r,"expanded",e[0]),C(t,"class","container svelte-1vyml86")},m(n,i){O(n,t,i),x(t,r),a||(o=N(t,"click",e[1]),a=!0)},p(e,[t]){1&t&&R(r,"expanded",e[0])},i:n,o:n,d(e){e&&I(t),a=!1,o()}}}function kr(e,t,n){let{expanded:r}=t;return e.$$set=e=>{"expanded"in e&&n(0,r=e.expanded)},[r,function(t){ee.call(this,e,t)}]}class Er extends Me{constructor(e){super(),De(this,e,kr,wr,c,{expanded:0},Pr)}}function Or(e){P(e,"svelte-1vlbacg","label.svelte-1vlbacg{display:inline-block;color:var(--label-color);padding:0}.spaced.svelte-1vlbacg{padding-right:var(--li-colon-space)}")}function Ir(e){let t,n,r,a,o,i;return{c(){t=A("label"),n=A("span"),r=D(e[0]),a=D(e[2]),C(t,"class","svelte-1vlbacg"),R(t,"spaced",e[1])},m(s,l){O(s,t,l),x(t,n),x(n,r),x(n,a),o||(i=N(t,"click",e[5]),o=!0)},p(e,n){1&n&&G(r,e[0]),4&n&&G(a,e[2]),2&n&&R(t,"spaced",e[1])},d(e){e&&I(t),o=!1,i()}}}function _r(e){let t,r=e[3]&&e[0]&&Ir(e);return{c(){r&&r.c(),t=j()},m(e,n){r&&r.m(e,n),O(e,t,n)},p(e,[n]){e[3]&&e[0]?r?r.p(e,n):(r=Ir(e),r.c(),r.m(t.parentNode,t)):r&&(r.d(1),r=null)},i:n,o:n,d(e){r&&r.d(e),e&&I(t)}}}function Ar(e,t,n){let r,{key:a,isParentExpanded:o,isParentArray:i=!1,colon:s=":"}=t;return e.$$set=e=>{"key"in e&&n(0,a=e.key),"isParentExpanded"in e&&n(1,o=e.isParentExpanded),"isParentArray"in e&&n(4,i=e.isParentArray),"colon"in e&&n(2,s=e.colon)},e.$$.update=()=>{19&e.$$.dirty&&n(3,r=o||!i||a!=+a)},[a,o,s,r,i,function(t){ee.call(this,e,t)}]}class Sr extends Me{constructor(e){super(),De(this,e,Ar,_r,c,{key:0,isParentExpanded:1,isParentArray:4,colon:2},Or)}}function Dr(e){P(e,"svelte-rwxv37","label.svelte-rwxv37{display:inline-block}.indent.svelte-rwxv37{padding-left:var(--li-identation)}.collapse.svelte-rwxv37{--li-display:inline;display:inline;font-style:italic}.comma.svelte-rwxv37{margin-left:-0.5em;margin-right:0.5em}label.svelte-rwxv37{position:relative}")}function Mr(e,t,n){const r=e.slice();return r[12]=t[n],r[20]=n,r}function jr(e){let t,n;return t=new Er({props:{expanded:e[0]}}),t.$on("click",e[15]),{c(){Ie(t.$$.fragment)},m(e,r){_e(t,e,r),n=!0},p(e,n){const r={};1&n&&(r.expanded=e[0]),t.$set(r)},i(e){n||(be(t.$$.fragment,e),n=!0)},o(e){$e(t.$$.fragment,e),n=!1},d(e){Ae(t,e)}}}function Nr(e){let t;return{c(){t=A("span"),t.textContent="…"},m(e,n){O(e,t,n)},p:n,i:n,o:n,d(e){e&&I(t)}}}function Tr(e){let t,n,r,a,o,i=e[13],s=[];for(let t=0;t<i.length;t+=1)s[t]=Gr(Mr(e,i,t));const l=e=>$e(s[e],1,1,()=>{s[e]=null});let c=e[13].length<e[7].length&&zr();return{c(){t=A("ul");for(let e=0;e<s.length;e+=1)s[e].c();n=M(),c&&c.c(),C(t,"class","svelte-rwxv37"),R(t,"collapse",!e[0])},m(i,l){O(i,t,l);for(let e=0;e<s.length;e+=1)s[e].m(t,null);x(t,n),c&&c.m(t,null),r=!0,a||(o=N(t,"click",e[16]),a=!0)},p(e,r){if(10129&r){let a;for(i=e[13],a=0;a<i.length;a+=1){const o=Mr(e,i,a);s[a]?(s[a].p(o,r),be(s[a],1)):(s[a]=Gr(o),s[a].c(),be(s[a],1),s[a].m(t,n))}for(me(),a=i.length;a<s.length;a+=1)l(a);ye()}e[13].length<e[7].length?c||(c=zr(),c.c(),c.m(t,null)):c&&(c.d(1),c=null),1&r&&R(t,"collapse",!e[0])},i(e){if(!r){for(let e=0;e<i.length;e+=1)be(s[e]);r=!0}},o(e){s=s.filter(Boolean);for(let e=0;e<s.length;e+=1)$e(s[e]);r=!1},d(e){e&&I(t),_(s,e),c&&c.d(),a=!1,o()}}}function Cr(e){let t;return{c(){t=A("span"),t.textContent=",",C(t,"class","comma svelte-rwxv37")},m(e,n){O(e,t,n)},d(e){e&&I(t)}}}function Gr(e){let t,n,r,a;t=new wa({props:{key:e[8](e[12]),isParentExpanded:e[0],isParentArray:e[4],value:e[0]?e[9](e[12]):e[10](e[12])}});let o=!e[0]&&e[20]<e[7].length-1&&Cr();return{c(){Ie(t.$$.fragment),n=M(),o&&o.c(),r=j()},m(e,i){_e(t,e,i),O(e,n,i),o&&o.m(e,i),O(e,r,i),a=!0},p(e,n){const a={};8448&n&&(a.key=e[8](e[12])),1&n&&(a.isParentExpanded=e[0]),16&n&&(a.isParentArray=e[4]),9729&n&&(a.value=e[0]?e[9](e[12]):e[10](e[12])),t.$set(a),!e[0]&&e[20]<e[7].length-1?o||(o=Cr(),o.c(),o.m(r.parentNode,r)):o&&(o.d(1),o=null)},i(e){a||(be(t.$$.fragment,e),a=!0)},o(e){$e(t.$$.fragment,e),a=!1},d(e){Ae(t,e),e&&I(n),o&&o.d(e),e&&I(r)}}}function zr(e){let t;return{c(){t=A("span"),t.textContent="…"},m(e,n){O(e,t,n)},d(e){e&&I(t)}}}function qr(e){let t,n,r,a,o,i,s,l,c,u,d,f,p,g,v,h,m,y,b=e[11]&&e[2]&&jr(e);a=new Sr({props:{key:e[12],colon:e[14].colon,isParentExpanded:e[2],isParentArray:e[3]}}),a.$on("click",e[15]);const $=[Tr,Nr],P=[];function w(e,t){return e[2]?0:1}return d=w(e),f=P[d]=$[d](e),{c(){t=A("li"),n=A("label"),b&&b.c(),r=M(),Ie(a.$$.fragment),o=M(),i=A("span"),s=A("span"),l=D(e[1]),c=D(e[5]),u=M(),f.c(),p=M(),g=A("span"),v=D(e[6]),C(n,"class","svelte-rwxv37"),C(t,"class","svelte-rwxv37"),R(t,"indent",e[2])},m(f,$){O(f,t,$),x(t,n),b&&b.m(n,null),x(n,r),_e(a,n,null),x(n,o),x(n,i),x(i,s),x(s,l),x(i,c),x(t,u),P[d].m(t,null),x(t,p),x(t,g),x(g,v),h=!0,m||(y=N(i,"click",e[15]),m=!0)},p(e,[o]){e[11]&&e[2]?b?(b.p(e,o),2052&o&&be(b,1)):(b=jr(e),b.c(),be(b,1),b.m(n,r)):b&&(me(),$e(b,1,1,()=>{b=null}),ye());const i={};4096&o&&(i.key=e[12]),4&o&&(i.isParentExpanded=e[2]),8&o&&(i.isParentArray=e[3]),a.$set(i),(!h||2&o)&&G(l,e[1]),(!h||32&o)&&G(c,e[5]);let s=d;d=w(e),d===s?P[d].p(e,o):(me(),$e(P[s],1,1,()=>{P[s]=null}),ye(),f=P[d],f?f.p(e,o):(f=P[d]=$[d](e),f.c()),be(f,1),f.m(t,p)),(!h||64&o)&&G(v,e[6]),4&o&&R(t,"indent",e[2])},i(e){h||(be(b),be(a.$$.fragment,e),be(f),h=!0)},o(e){$e(b),$e(a.$$.fragment,e),$e(f),h=!1},d(e){e&&I(t),b&&b.d(),Ae(a),P[d].d(),m=!1,y()}}}function Vr(e,t,n){let r,{key:a,keys:o,colon:i=":",label:s="",isParentExpanded:l,isParentArray:c,isArray:u=!1,bracketOpen:d,bracketClose:f}=t,{previewKeys:p=o}=t,{getKey:g=(e=>e)}=t,{getValue:v=(e=>e)}=t,{getPreviewValue:h=v}=t,{expanded:m=!1,expandable:y=!0}=t;const b=Q(xr);return X(xr,{...b,colon:i}),e.$$set=e=>{"key"in e&&n(12,a=e.key),"keys"in e&&n(17,o=e.keys),"colon"in e&&n(18,i=e.colon),"label"in e&&n(1,s=e.label),"isParentExpanded"in e&&n(2,l=e.isParentExpanded),"isParentArray"in e&&n(3,c=e.isParentArray),"isArray"in e&&n(4,u=e.isArray),"bracketOpen"in e&&n(5,d=e.bracketOpen),"bracketClose"in e&&n(6,f=e.bracketClose),"previewKeys"in e&&n(7,p=e.previewKeys),"getKey"in e&&n(8,g=e.getKey),"getValue"in e&&n(9,v=e.getValue),"getPreviewValue"in e&&n(10,h=e.getPreviewValue),"expanded"in e&&n(0,m=e.expanded),"expandable"in e&&n(11,y=e.expandable)},e.$$.update=()=>{4&e.$$.dirty&&(l||n(0,m=!1)),131201&e.$$.dirty&&n(13,r=m?o:p.slice(0,5))},[m,s,l,c,u,d,f,p,g,v,h,y,a,r,b,function(){n(0,m=!m)},function(){n(0,m=!0)},o,i]}class Rr extends Me{constructor(e){super(),De(this,e,Vr,qr,c,{key:12,keys:17,colon:18,label:1,isParentExpanded:2,isParentArray:3,isArray:4,bracketOpen:5,bracketClose:6,previewKeys:7,getKey:8,getValue:9,getPreviewValue:10,expanded:0,expandable:11},Dr)}}function Br(e){let t,n;return t=new Rr({props:{key:e[0],expanded:e[4],isParentExpanded:e[1],isParentArray:e[2],keys:e[5],previewKeys:e[5],getValue:e[6],label:e[3]+" ",bracketOpen:"{",bracketClose:"}"}}),{c(){Ie(t.$$.fragment)},m(e,r){_e(t,e,r),n=!0},p(e,[n]){const r={};1&n&&(r.key=e[0]),16&n&&(r.expanded=e[4]),2&n&&(r.isParentExpanded=e[1]),4&n&&(r.isParentArray=e[2]),32&n&&(r.keys=e[5]),32&n&&(r.previewKeys=e[5]),8&n&&(r.label=e[3]+" "),t.$set(r)},i(e){n||(be(t.$$.fragment,e),n=!0)},o(e){$e(t.$$.fragment,e),n=!1},d(e){Ae(t,e)}}}function Ur(e,t,n){let r,{key:a,value:o,isParentExpanded:i,isParentArray:s,nodeType:l}=t,{expanded:c=!0}=t;return e.$$set=e=>{"key"in e&&n(0,a=e.key),"value"in e&&n(7,o=e.value),"isParentExpanded"in e&&n(1,i=e.isParentExpanded),"isParentArray"in e&&n(2,s=e.isParentArray),"nodeType"in e&&n(3,l=e.nodeType),"expanded"in e&&n(4,c=e.expanded)},e.$$.update=()=>{128&e.$$.dirty&&n(5,r=Object.getOwnPropertyNames(o))},[a,i,s,l,c,r,function(e){return o[e]},o]}class Fr extends Me{constructor(e){super(),De(this,e,Ur,Br,c,{key:0,value:7,isParentExpanded:1,isParentArray:2,nodeType:3,expanded:4})}}function Lr(e){let t,n;return t=new Rr({props:{key:e[0],expanded:e[4],isParentExpanded:e[2],isParentArray:e[3],isArray:!0,keys:e[5],previewKeys:e[6],getValue:e[7],label:"Array("+e[1].length+")",bracketOpen:"[",bracketClose:"]"}}),{c(){Ie(t.$$.fragment)},m(e,r){_e(t,e,r),n=!0},p(e,[n]){const r={};1&n&&(r.key=e[0]),16&n&&(r.expanded=e[4]),4&n&&(r.isParentExpanded=e[2]),8&n&&(r.isParentArray=e[3]),32&n&&(r.keys=e[5]),64&n&&(r.previewKeys=e[6]),2&n&&(r.label="Array("+e[1].length+")"),t.$set(r)},i(e){n||(be(t.$$.fragment,e),n=!0)},o(e){$e(t.$$.fragment,e),n=!1},d(e){Ae(t,e)}}}function Jr(e,t,n){let r,a,{key:o,value:i,isParentExpanded:s,isParentArray:l}=t,{expanded:c=JSON.stringify(i).length<1024}=t;const u=new Set(["length"]);return e.$$set=e=>{"key"in e&&n(0,o=e.key),"value"in e&&n(1,i=e.value),"isParentExpanded"in e&&n(2,s=e.isParentExpanded),"isParentArray"in e&&n(3,l=e.isParentArray),"expanded"in e&&n(4,c=e.expanded)},e.$$.update=()=>{2&e.$$.dirty&&n(5,r=Object.getOwnPropertyNames(i)),32&e.$$.dirty&&n(6,a=r.filter(e=>!u.has(e)))},[o,i,s,l,c,r,a,function(e){return i[e]}]}class Kr extends Me{constructor(e){super(),De(this,e,Jr,Lr,c,{key:0,value:1,isParentExpanded:2,isParentArray:3,expanded:4})}}function Hr(e){let t,n;return t=new Rr({props:{key:e[0],isParentExpanded:e[1],isParentArray:e[2],keys:e[4],getKey:Wr,getValue:Zr,isArray:!0,label:e[3]+"("+e[4].length+")",bracketOpen:"{",bracketClose:"}"}}),{c(){Ie(t.$$.fragment)},m(e,r){_e(t,e,r),n=!0},p(e,[n]){const r={};1&n&&(r.key=e[0]),2&n&&(r.isParentExpanded=e[1]),4&n&&(r.isParentArray=e[2]),16&n&&(r.keys=e[4]),24&n&&(r.label=e[3]+"("+e[4].length+")"),t.$set(r)},i(e){n||(be(t.$$.fragment,e),n=!0)},o(e){$e(t.$$.fragment,e),n=!1},d(e){Ae(t,e)}}}function Wr(e){return String(e[0])}function Zr(e){return e[1]}function Yr(e,t,n){let{key:r,value:a,isParentExpanded:o,isParentArray:i,nodeType:s}=t,l=[];return e.$$set=e=>{"key"in e&&n(0,r=e.key),"value"in e&&n(5,a=e.value),"isParentExpanded"in e&&n(1,o=e.isParentExpanded),"isParentArray"in e&&n(2,i=e.isParentArray),"nodeType"in e&&n(3,s=e.nodeType)},e.$$.update=()=>{if(32&e.$$.dirty){let e=[],t=0;for(const n of a)e.push([t++,n]);n(4,l=e)}},[r,o,i,s,l,a]}class Xr extends Me{constructor(e){super(),De(this,e,Yr,Hr,c,{key:0,value:5,isParentExpanded:1,isParentArray:2,nodeType:3})}}class Qr{constructor(e,t){this.key=e,this.value=t}}function ea(e){let t,n;return t=new Rr({props:{key:e[0],isParentExpanded:e[1],isParentArray:e[2],keys:e[4],getKey:ta,getValue:na,label:e[3]+"("+e[4].length+")",colon:"",bracketOpen:"{",bracketClose:"}"}}),{c(){Ie(t.$$.fragment)},m(e,r){_e(t,e,r),n=!0},p(e,[n]){const r={};1&n&&(r.key=e[0]),2&n&&(r.isParentExpanded=e[1]),4&n&&(r.isParentArray=e[2]),16&n&&(r.keys=e[4]),24&n&&(r.label=e[3]+"("+e[4].length+")"),t.$set(r)},i(e){n||(be(t.$$.fragment,e),n=!0)},o(e){$e(t.$$.fragment,e),n=!1},d(e){Ae(t,e)}}}function ta(e){return e[0]}function na(e){return e[1]}function ra(e,t,n){let{key:r,value:a,isParentExpanded:o,isParentArray:i,nodeType:s}=t,l=[];return e.$$set=e=>{"key"in e&&n(0,r=e.key),"value"in e&&n(5,a=e.value),"isParentExpanded"in e&&n(1,o=e.isParentExpanded),"isParentArray"in e&&n(2,i=e.isParentArray),"nodeType"in e&&n(3,s=e.nodeType)},e.$$.update=()=>{if(32&e.$$.dirty){let e=[],t=0;for(const n of a)e.push([t++,new Qr(n[0],n[1])]);n(4,l=e)}},[r,o,i,s,l,a]}class aa extends Me{constructor(e){super(),De(this,e,ra,ea,c,{key:0,value:5,isParentExpanded:1,isParentArray:2,nodeType:3})}}function oa(e){let t,n;return t=new Rr({props:{expanded:e[4],isParentExpanded:e[2],isParentArray:e[3],key:e[2]?String(e[0]):e[1].key,keys:e[5],getValue:e[6],label:e[2]?"Entry ":"=> ",bracketOpen:"{",bracketClose:"}"}}),{c(){Ie(t.$$.fragment)},m(e,r){_e(t,e,r),n=!0},p(e,[n]){const r={};16&n&&(r.expanded=e[4]),4&n&&(r.isParentExpanded=e[2]),8&n&&(r.isParentArray=e[3]),7&n&&(r.key=e[2]?String(e[0]):e[1].key),4&n&&(r.label=e[2]?"Entry ":"=> "),t.$set(r)},i(e){n||(be(t.$$.fragment,e),n=!0)},o(e){$e(t.$$.fragment,e),n=!1},d(e){Ae(t,e)}}}function ia(e,t,n){let{key:r,value:a,isParentExpanded:o,isParentArray:i}=t,{expanded:s=!1}=t;return e.$$set=e=>{"key"in e&&n(0,r=e.key),"value"in e&&n(1,a=e.value),"isParentExpanded"in e&&n(2,o=e.isParentExpanded),"isParentArray"in e&&n(3,i=e.isParentArray),"expanded"in e&&n(4,s=e.expanded)},[r,a,o,i,s,["key","value"],function(e){return a[e]}]}class sa extends Me{constructor(e){super(),De(this,e,ia,oa,c,{key:0,value:1,isParentExpanded:2,isParentArray:3,expanded:4})}}function la(e){P(e,"svelte-3bjyvl","li.svelte-3bjyvl{user-select:text;word-wrap:break-word;word-break:break-all}.indent.svelte-3bjyvl{padding-left:var(--li-identation)}.String.svelte-3bjyvl{color:var(--string-color)}.Date.svelte-3bjyvl{color:var(--date-color)}.Number.svelte-3bjyvl{color:var(--number-color)}.Boolean.svelte-3bjyvl{color:var(--boolean-color)}.Null.svelte-3bjyvl{color:var(--null-color)}.Undefined.svelte-3bjyvl{color:var(--undefined-color)}.Function.svelte-3bjyvl{color:var(--function-color);font-style:italic}.Symbol.svelte-3bjyvl{color:var(--symbol-color)}")}function ca(e){let t,n,r,a,o,i,s,l=(e[2]?e[2](e[1]):e[1])+"";return n=new Sr({props:{key:e[0],colon:e[6],isParentExpanded:e[3],isParentArray:e[4]}}),{c(){t=A("li"),Ie(n.$$.fragment),r=M(),a=A("span"),o=D(l),C(a,"class",i=g(e[5])+" svelte-3bjyvl"),C(t,"class","svelte-3bjyvl"),R(t,"indent",e[3])},m(e,i){O(e,t,i),_e(n,t,null),x(t,r),x(t,a),x(a,o),s=!0},p(e,[r]){const c={};1&r&&(c.key=e[0]),8&r&&(c.isParentExpanded=e[3]),16&r&&(c.isParentArray=e[4]),n.$set(c),(!s||6&r)&&l!==(l=(e[2]?e[2](e[1]):e[1])+"")&&G(o,l),(!s||32&r&&i!==(i=g(e[5])+" svelte-3bjyvl"))&&C(a,"class",i),8&r&&R(t,"indent",e[3])},i(e){s||(be(n.$$.fragment,e),s=!0)},o(e){$e(n.$$.fragment,e),s=!1},d(e){e&&I(t),Ae(n)}}}function ua(e,t,n){let{key:r,value:a,valueGetter:o=null,isParentExpanded:i,isParentArray:s,nodeType:l}=t;const{colon:c}=Q(xr);return e.$$set=e=>{"key"in e&&n(0,r=e.key),"value"in e&&n(1,a=e.value),"valueGetter"in e&&n(2,o=e.valueGetter),"isParentExpanded"in e&&n(3,i=e.isParentExpanded),"isParentArray"in e&&n(4,s=e.isParentArray),"nodeType"in e&&n(5,l=e.nodeType)},[r,a,o,i,s,l,c]}class da extends Me{constructor(e){super(),De(this,e,ua,ca,c,{key:0,value:1,valueGetter:2,isParentExpanded:3,isParentArray:4,nodeType:5},la)}}function fa(e){P(e,"svelte-1ca3gb2","li.svelte-1ca3gb2{user-select:text;word-wrap:break-word;word-break:break-all}.indent.svelte-1ca3gb2{padding-left:var(--li-identation)}.collapse.svelte-1ca3gb2{--li-display:inline;display:inline;font-style:italic}")}function pa(e,t,n){const r=e.slice();return r[8]=t[n],r[10]=n,r}function ga(e){let t,n;return t=new Er({props:{expanded:e[0]}}),t.$on("click",e[7]),{c(){Ie(t.$$.fragment)},m(e,r){_e(t,e,r),n=!0},p(e,n){const r={};1&n&&(r.expanded=e[0]),t.$set(r)},i(e){n||(be(t.$$.fragment,e),n=!0)},o(e){$e(t.$$.fragment,e),n=!1},d(e){Ae(t,e)}}}function va(e){let t,n,r=e[0]&&ha(e);return{c(){t=A("ul"),r&&r.c(),C(t,"class","svelte-1ca3gb2"),R(t,"collapse",!e[0])},m(e,a){O(e,t,a),r&&r.m(t,null),n=!0},p(e,n){e[0]?r?(r.p(e,n),1&n&&be(r,1)):(r=ha(e),r.c(),be(r,1),r.m(t,null)):r&&(me(),$e(r,1,1,()=>{r=null}),ye()),1&n&&R(t,"collapse",!e[0])},i(e){n||(be(r),n=!0)},o(e){$e(r),n=!1},d(e){e&&I(t),r&&r.d()}}}function ha(e){let t,n,r,a,o,i,s;t=new wa({props:{key:"message",value:e[2].message}}),a=new Sr({props:{key:"stack",colon:":",isParentExpanded:e[3]}});let l=e[5],c=[];for(let t=0;t<l.length;t+=1)c[t]=ma(pa(e,l,t));return{c(){Ie(t.$$.fragment),n=M(),r=A("li"),Ie(a.$$.fragment),o=M(),i=A("span");for(let e=0;e<c.length;e+=1)c[e].c();C(r,"class","svelte-1ca3gb2")},m(e,l){_e(t,e,l),O(e,n,l),O(e,r,l),_e(a,r,null),x(r,o),x(r,i);for(let e=0;e<c.length;e+=1)c[e].m(i,null);s=!0},p(e,n){const r={};4&n&&(r.value=e[2].message),t.$set(r);const o={};if(8&n&&(o.isParentExpanded=e[3]),a.$set(o),32&n){let t;for(l=e[5],t=0;t<l.length;t+=1){const r=pa(e,l,t);c[t]?c[t].p(r,n):(c[t]=ma(r),c[t].c(),c[t].m(i,null))}for(;t<c.length;t+=1)c[t].d(1);c.length=l.length}},i(e){s||(be(t.$$.fragment,e),be(a.$$.fragment,e),s=!0)},o(e){$e(t.$$.fragment,e),$e(a.$$.fragment,e),s=!1},d(e){Ae(t,e),e&&I(n),e&&I(r),Ae(a),_(c,e)}}}function ma(e){let t,n,r,a=e[8]+"";return{c(){t=A("span"),n=D(a),r=A("br"),C(t,"class","svelte-1ca3gb2"),R(t,"indent",e[10]>0)},m(e,a){O(e,t,a),x(t,n),O(e,r,a)},p(e,t){32&t&&a!==(a=e[8]+"")&&G(n,a)},d(e){e&&I(t),e&&I(r)}}}function ya(e){let t,n,r,a,o,i,s,l,c,u,d,f=(e[0]?"":e[2].message)+"",p=e[3]&&ga(e);r=new Sr({props:{key:e[1],colon:e[6].colon,isParentExpanded:e[3],isParentArray:e[4]}});let g=e[3]&&va(e);return{c(){t=A("li"),p&&p.c(),n=M(),Ie(r.$$.fragment),a=M(),o=A("span"),i=D("Error: "),s=D(f),l=M(),g&&g.c(),C(t,"class","svelte-1ca3gb2"),R(t,"indent",e[3])},m(f,v){O(f,t,v),p&&p.m(t,null),x(t,n),_e(r,t,null),x(t,a),x(t,o),x(o,i),x(o,s),x(t,l),g&&g.m(t,null),c=!0,u||(d=N(o,"click",e[7]),u=!0)},p(e,[a]){e[3]?p?(p.p(e,a),8&a&&be(p,1)):(p=ga(e),p.c(),be(p,1),p.m(t,n)):p&&(me(),$e(p,1,1,()=>{p=null}),ye());const o={};2&a&&(o.key=e[1]),8&a&&(o.isParentExpanded=e[3]),16&a&&(o.isParentArray=e[4]),r.$set(o),(!c||5&a)&&f!==(f=(e[0]?"":e[2].message)+"")&&G(s,f),e[3]?g?(g.p(e,a),8&a&&be(g,1)):(g=va(e),g.c(),be(g,1),g.m(t,null)):g&&(me(),$e(g,1,1,()=>{g=null}),ye()),8&a&&R(t,"indent",e[3])},i(e){c||(be(p),be(r.$$.fragment,e),be(g),c=!0)},o(e){$e(p),$e(r.$$.fragment,e),$e(g),c=!1},d(e){e&&I(t),p&&p.d(),Ae(r),g&&g.d(),u=!1,d()}}}function ba(e,t,n){let r,{key:a,value:o,isParentExpanded:i,isParentArray:s}=t,{expanded:l=!1}=t;const c=Q(xr);return X(xr,{...c,colon:":"}),e.$$set=e=>{"key"in e&&n(1,a=e.key),"value"in e&&n(2,o=e.value),"isParentExpanded"in e&&n(3,i=e.isParentExpanded),"isParentArray"in e&&n(4,s=e.isParentArray),"expanded"in e&&n(0,l=e.expanded)},e.$$.update=()=>{4&e.$$.dirty&&n(5,r=o.stack.split("\n")),8&e.$$.dirty&&(i||n(0,l=!1))},[l,a,o,i,s,r,c,function(){n(0,l=!l)}]}class $a extends Me{constructor(e){super(),De(this,e,ba,ya,c,{key:1,value:2,isParentExpanded:3,isParentArray:4,expanded:0},fa)}}function xa(e){let t,n,r;var a=e[6];function o(e){return{props:{key:e[0],value:e[1],isParentExpanded:e[2],isParentArray:e[3],nodeType:e[4],valueGetter:e[5]}}}return a&&(t=new a(o(e))),{c(){t&&Ie(t.$$.fragment),n=j()},m(e,a){t&&_e(t,e,a),O(e,n,a),r=!0},p(e,[r]){const i={};if(1&r&&(i.key=e[0]),2&r&&(i.value=e[1]),4&r&&(i.isParentExpanded=e[2]),8&r&&(i.isParentArray=e[3]),16&r&&(i.nodeType=e[4]),32&r&&(i.valueGetter=e[5]),a!==(a=e[6])){if(t){me();const e=t;$e(e.$$.fragment,1,0,()=>{Ae(e,1)}),ye()}a?(t=new a(o(e)),Ie(t.$$.fragment),be(t.$$.fragment,1),_e(t,n.parentNode,n)):t=null}else a&&t.$set(i)},i(e){r||(t&&be(t.$$.fragment,e),r=!0)},o(e){t&&$e(t.$$.fragment,e),r=!1},d(e){e&&I(n),t&&Ae(t,e)}}}function Pa(e,t,n){let r,a,o,{key:i,value:s,isParentExpanded:l,isParentArray:c}=t;return e.$$set=e=>{"key"in e&&n(0,i=e.key),"value"in e&&n(1,s=e.value),"isParentExpanded"in e&&n(2,l=e.isParentExpanded),"isParentArray"in e&&n(3,c=e.isParentArray)},e.$$.update=()=>{2&e.$$.dirty&&n(4,r=function(e){const t=Object.prototype.toString.call(e).slice(8,-1);return"Object"===t?"function"==typeof e[Symbol.iterator]?"Iterable":e.constructor.name:t}(s)),16&e.$$.dirty&&n(6,a=function(e){switch(e){case"Object":return Fr;case"Error":return $a;case"Array":return Kr;case"Iterable":case"Map":case"Set":return"function"==typeof s.set?aa:Xr;case"MapEntry":return sa;default:return da}}(r)),16&e.$$.dirty&&n(5,o=function(e){switch(e){case"Object":case"Error":case"Array":case"Iterable":case"Map":case"Set":case"MapEntry":case"Number":return;case"String":return e=>`"${e}"`;case"Boolean":return e=>e?"true":"false";case"Date":return e=>e.toISOString();case"Null":return()=>"null";case"Undefined":return()=>"undefined";case"Function":case"Symbol":return e=>e.toString();default:return()=>`<${e}>`}}(r))},[i,s,l,c,r,o,a]}class wa extends Me{constructor(e){super(),De(this,e,Pa,xa,c,{key:0,value:1,isParentExpanded:2,isParentArray:3})}}function ka(e){P(e,"svelte-773n60","ul.svelte-773n60{--string-color:var(--json-tree-string-color, #cb3f41);--symbol-color:var(--json-tree-symbol-color, #cb3f41);--boolean-color:var(--json-tree-boolean-color, #112aa7);--function-color:var(--json-tree-function-color, #112aa7);--number-color:var(--json-tree-number-color, #3029cf);--label-color:var(--json-tree-label-color, #871d8f);--arrow-color:var(--json-tree-arrow-color, #727272);--null-color:var(--json-tree-null-color, #8d8d8d);--undefined-color:var(--json-tree-undefined-color, #8d8d8d);--date-color:var(--json-tree-date-color, #8d8d8d);--li-identation:var(--json-tree-li-indentation, 1em);--li-line-height:var(--json-tree-li-line-height, 1.3);--li-colon-space:0.3em;font-size:var(--json-tree-font-size, 12px);font-family:var(--json-tree-font-family, 'Courier New', Courier, monospace)}ul.svelte-773n60 li{line-height:var(--li-line-height);display:var(--li-display, list-item);list-style:none}ul.svelte-773n60,ul.svelte-773n60 ul{padding:0;margin:0}")}function Ea(e){let t,n,r;return n=new wa({props:{key:e[0],value:e[1],isParentExpanded:!0,isParentArray:!1}}),{c(){t=A("ul"),Ie(n.$$.fragment),C(t,"class","svelte-773n60")},m(e,a){O(e,t,a),_e(n,t,null),r=!0},p(e,[t]){const r={};1&t&&(r.key=e[0]),2&t&&(r.value=e[1]),n.$set(r)},i(e){r||(be(n.$$.fragment,e),r=!0)},o(e){$e(n.$$.fragment,e),r=!1},d(e){e&&I(t),Ae(n)}}}function Oa(e,t,n){X(xr,{});let{key:r="",value:a}=t;return e.$$set=e=>{"key"in e&&n(0,r=e.key),"value"in e&&n(1,a=e.value)},[r,a]}class Ia extends Me{constructor(e){super(),De(this,e,Oa,Ea,c,{key:0,value:1},ka)}}function _a(e){P(e,"svelte-jvfq3i",".svelte-jvfq3i{box-sizing:border-box}section.switcher.svelte-jvfq3i{position:sticky;bottom:0;transform:translateY(20px);margin:40px -20px 0;border-top:1px solid #999;padding:20px;background:#fff}label.svelte-jvfq3i{display:flex;align-items:baseline;gap:5px;font-weight:bold}select.svelte-jvfq3i{min-width:140px}")}function Aa(e,t,n){const r=e.slice();return r[7]=t[n],r[9]=n,r}function Sa(e){let t,n,r,a,o,i,l=e[1],c=[];for(let t=0;t<l.length;t+=1)c[t]=Da(Aa(e,l,t));return{c(){t=A("section"),n=A("label"),r=D("Client\n      \n      "),a=A("select");for(let e=0;e<c.length;e+=1)c[e].c();C(a,"id",ja),C(a,"class","svelte-jvfq3i"),void 0===e[2]&&se(()=>e[6].call(a)),C(n,"class","svelte-jvfq3i"),C(t,"class","switcher svelte-jvfq3i")},m(s,l){O(s,t,l),x(t,n),x(n,r),x(n,a);for(let e=0;e<c.length;e+=1)c[e].m(a,null);q(a,e[2]),o||(i=[N(a,"change",e[3]),N(a,"change",e[6])],o=!0)},p(e,t){if(2&t){let n;for(l=e[1],n=0;n<l.length;n+=1){const r=Aa(e,l,n);c[n]?c[n].p(r,t):(c[n]=Da(r),c[n].c(),c[n].m(a,null))}for(;n<c.length;n+=1)c[n].d(1);c.length=l.length}4&t&&q(a,e[2])},d(e){e&&I(t),_(c,e),o=!1,s(i)}}}function Da(e){let t,n,r,a,o,i,s,l,c,u,d=JSON.stringify(e[7].playerID)+"",f=JSON.stringify(e[7].matchID)+"",p=e[7].game.name+"";return{c(){t=A("option"),n=D(e[9]),r=D(" —\n            playerID: "),a=D(d),o=D(",\n            matchID: "),i=D(f),s=D("\n            ("),l=D(p),c=D(")\n          "),t.__value=u=e[9],t.value=t.__value,C(t,"class","svelte-jvfq3i")},m(e,u){O(e,t,u),x(t,n),x(t,r),x(t,a),x(t,o),x(t,i),x(t,s),x(t,l),x(t,c)},p(e,t){2&t&&d!==(d=JSON.stringify(e[7].playerID)+"")&&G(a,d),2&t&&f!==(f=JSON.stringify(e[7].matchID)+"")&&G(i,f),2&t&&p!==(p=e[7].game.name+"")&&G(l,p)},d(e){e&&I(t)}}}function Ma(e){let t,r=e[1].length>1&&Sa(e);return{c(){r&&r.c(),t=j()},m(e,n){r&&r.m(e,n),O(e,t,n)},p(e,[n]){e[1].length>1?r?r.p(e,n):(r=Sa(e),r.c(),r.m(t.parentNode,t)):r&&(r.d(1),r=null)},i:n,o:n,d(e){r&&r.d(e),e&&I(t)}}}const ja="bgio-debug-select-client";function Na(e,t,r){let a,o,i,s,l=n,c=()=>(l(),l=u(d,e=>r(5,s=e)),d);e.$$.on_destroy.push(()=>l());let{clientManager:d}=t;c();return e.$$set=e=>{"clientManager"in e&&c(r(0,d=e.clientManager))},e.$$.update=()=>{32&e.$$.dirty&&r(4,({client:a,debuggableClients:o}=s),a,(r(1,o),r(5,s))),18&e.$$.dirty&&r(2,i=o.indexOf(a))},[d,o,i,e=>{const t=o[e.target.value];d.switchToClient(t);const n=document.getElementById(ja);n&&n.focus()},a,s,function(){i=V(this),r(2,i),r(1,o),r(4,a),r(5,s)}]}class Ta extends Me{constructor(e){super(),De(this,e,Na,Ma,c,{clientManager:0},_a)}}function Ca(e){P(e,"svelte-1vfj1mn",".key.svelte-1vfj1mn.svelte-1vfj1mn{display:flex;flex-direction:row;align-items:center}button.svelte-1vfj1mn.svelte-1vfj1mn{cursor:pointer;min-width:10px;padding-left:5px;padding-right:5px;height:20px;line-height:20px;text-align:center;border:1px solid #ccc;box-shadow:1px 1px 1px #888;background:#eee;color:#444}button.svelte-1vfj1mn.svelte-1vfj1mn:hover{background:#ddd}.key.active.svelte-1vfj1mn button.svelte-1vfj1mn{background:#ddd;border:1px solid #999;box-shadow:none}label.svelte-1vfj1mn.svelte-1vfj1mn{margin-left:10px}")}function Ga(e){let t,n,r,a,o,i=`(shortcut: ${e[0]})`;return{c(){t=A("label"),n=D(e[1]),r=M(),a=A("span"),o=D(i),C(a,"class","screen-reader-only"),C(t,"for",e[5]),C(t,"class","svelte-1vfj1mn")},m(e,i){O(e,t,i),x(t,n),x(t,r),x(t,a),x(a,o)},p(e,t){2&t&&G(n,e[1]),1&t&&i!==(i=`(shortcut: ${e[0]})`)&&G(o,i)},d(e){e&&I(t)}}}function za(e){let t,r,a,o,i,l,c=e[1]&&Ga(e);return{c(){t=A("div"),r=A("button"),a=D(e[0]),o=M(),c&&c.c(),C(r,"id",e[5]),r.disabled=e[2],C(r,"class","svelte-1vfj1mn"),C(t,"class","key svelte-1vfj1mn"),R(t,"active",e[3])},m(n,s){O(n,t,s),x(t,r),x(r,a),x(t,o),c&&c.m(t,null),i||(l=[N(window,"keydown",e[7]),N(r,"click",e[6])],i=!0)},p(e,[n]){1&n&&G(a,e[0]),4&n&&(r.disabled=e[2]),e[1]?c?c.p(e,n):(c=Ga(e),c.c(),c.m(t,null)):c&&(c.d(1),c=null),8&n&&R(t,"active",e[3])},i:n,o:n,d(e){e&&I(t),c&&c.d(),i=!1,s(l)}}}function qa(e,t,n){let r,{value:a}=t,{onPress:o=null}=t,{label:i=null}=t,{disable:s=!1}=t;const{disableHotkeys:l}=Q("hotkeys");d(e,l,e=>n(9,r=e));let c=!1,u="key-"+a;function f(){n(3,c=!1)}function p(){n(3,c=!0),setTimeout(f,200),o&&setTimeout(o,1)}return e.$$set=e=>{"value"in e&&n(0,a=e.value),"onPress"in e&&n(8,o=e.onPress),"label"in e&&n(1,i=e.label),"disable"in e&&n(2,s=e.disable)},[a,i,s,c,l,u,p,function(e){r||s||e.ctrlKey||e.metaKey||e.key!=a||(e.preventDefault(),p())},o]}class Va extends Me{constructor(e){super(),De(this,e,qa,za,c,{value:0,onPress:8,label:1,disable:2},Ca)}}function Ra(e){P(e,"svelte-1mppqmp",".move.svelte-1mppqmp{display:flex;flex-direction:row;cursor:pointer;margin-left:10px;color:#666}.move.svelte-1mppqmp:hover{color:#333}.move.active.svelte-1mppqmp{color:#111;font-weight:bold}.arg-field.svelte-1mppqmp{outline:none;font-family:monospace}")}function Ba(e){let t,r,a,o,i,c,u,d,f,p,g;return{c(){t=A("div"),r=A("span"),a=D(e[2]),o=M(),i=A("span"),i.textContent="(",c=M(),u=A("span"),d=M(),f=A("span"),f.textContent=")",C(u,"class","arg-field svelte-1mppqmp"),C(u,"contenteditable",""),C(t,"class","move svelte-1mppqmp"),R(t,"active",e[3])},m(n,s){O(n,t,s),x(t,r),x(r,a),x(t,o),x(t,i),x(t,c),x(t,u),e[6](u),x(t,d),x(t,f),p||(g=[N(u,"focus",(function(){l(e[0])&&e[0].apply(this,arguments)})),N(u,"blur",(function(){l(e[1])&&e[1].apply(this,arguments)})),N(u,"keypress",T(Ua)),N(u,"keydown",e[5]),N(t,"click",(function(){l(e[0])&&e[0].apply(this,arguments)}))],p=!0)},p(n,[r]){e=n,4&r&&G(a,e[2]),8&r&&R(t,"active",e[3])},i:n,o:n,d(n){n&&I(t),e[6](null),p=!1,s(g)}}}const Ua=()=>{};function Fa(e,t,n){let r,{Activate:a}=t,{Deactivate:o}=t,{name:i}=t,{active:s}=t;const l=Y();return function(e){W().$$.after_update.push(e)}(()=>{s?r.focus():r.blur()}),e.$$set=e=>{"Activate"in e&&n(0,a=e.Activate),"Deactivate"in e&&n(1,o=e.Deactivate),"name"in e&&n(2,i=e.name),"active"in e&&n(3,s=e.active)},[a,o,i,s,r,function(e){"Enter"==e.key&&(e.preventDefault(),function(){try{const e=r.innerText;let t=new Function(`return [${e}]`)();l("submit",t)}catch(e){l("error",e)}n(4,r.innerText="",r)}()),"Escape"==e.key&&(e.preventDefault(),o())},function(e){ne[e?"unshift":"push"](()=>{r=e,n(4,r)})}]}class La extends Me{constructor(e){super(),De(this,e,Fa,Ba,c,{Activate:0,Deactivate:1,name:2,active:3},Ra)}}function Ja(e){P(e,"svelte-smqssc",".move-error.svelte-smqssc{color:#a00;font-weight:bold}.wrapper.svelte-smqssc{display:flex;flex-direction:row;align-items:center}")}function Ka(e){let t,n;return{c(){t=A("span"),n=D(e[2]),C(t,"class","move-error svelte-smqssc")},m(e,r){O(e,t,r),x(t,n)},p(e,t){4&t&&G(n,e[2])},d(e){e&&I(t)}}}function Ha(e){let t,n,r,a,o,i,s;r=new Va({props:{value:e[0],onPress:e[4]}}),o=new La({props:{Activate:e[4],Deactivate:e[5],name:e[1],active:e[3]}}),o.$on("submit",e[6]),o.$on("error",e[7]);let l=e[2]&&Ka(e);return{c(){t=A("div"),n=A("div"),Ie(r.$$.fragment),a=M(),Ie(o.$$.fragment),i=M(),l&&l.c(),C(n,"class","wrapper svelte-smqssc")},m(e,c){O(e,t,c),x(t,n),_e(r,n,null),x(n,a),_e(o,n,null),x(t,i),l&&l.m(t,null),s=!0},p(e,[n]){const a={};1&n&&(a.value=e[0]),r.$set(a);const i={};2&n&&(i.name=e[1]),8&n&&(i.active=e[3]),o.$set(i),e[2]?l?l.p(e,n):(l=Ka(e),l.c(),l.m(t,null)):l&&(l.d(1),l=null)},i(e){s||(be(r.$$.fragment,e),be(o.$$.fragment,e),s=!0)},o(e){$e(r.$$.fragment,e),$e(o.$$.fragment,e),s=!1},d(e){e&&I(t),Ae(r),Ae(o),l&&l.d()}}}function Wa(e,t,n){let{shortcut:r}=t,{name:a}=t,{fn:o}=t;const{disableHotkeys:i}=Q("hotkeys");let s="",l=!1;function c(){i.set(!1),n(2,s=""),n(3,l=!1)}return e.$$set=e=>{"shortcut"in e&&n(0,r=e.shortcut),"name"in e&&n(1,a=e.name),"fn"in e&&n(8,o=e.fn)},[r,a,s,l,function(){i.set(!0),n(3,l=!0)},c,function(e){n(2,s=""),c(),o.apply(this,e.detail)},function(e){n(2,s=e.detail),mn(e.detail)},o]}class Za extends Me{constructor(e){super(),De(this,e,Wa,Ha,c,{shortcut:0,name:1,fn:8},Ja)}}
/*! (c) 2020 Andrea Giammarchi */const{parse:Ya,stringify:Xa}=JSON,{keys:Qa}=Object,eo=String,to={},no=(e,t)=>t,ro=e=>e instanceof eo?eo(e):e,ao=(e,t)=>"string"==typeof t?new eo(t):t,oo=(e,t,n,r)=>{const a=[];for(let o=Qa(n),{length:i}=o,s=0;s<i;s++){const i=o[s],l=n[i];if(l instanceof eo){const o=e[l];"object"!=typeof o||t.has(o)?n[i]=r.call(n,i,o):(t.add(o),n[i]=to,a.push({k:i,a:[e,t,o,r]}))}else n[i]!==to&&(n[i]=r.call(n,i,l))}for(let{length:e}=a,t=0;t<e;t++){const{k:e,a:o}=a[t];n[e]=r.call(n,e,oo.apply(null,o))}return n},io=(e,t,n)=>{const r=eo(t.push(n)-1);return e.set(n,r),r},so=(e,t)=>{const n=Ya(e,ao).map(ro),r=n[0],a=t||no,o="object"==typeof r&&r?oo(n,new Set,r,a):r;return a.call({"":o},"",o)},lo=(e,t,n)=>{const r=t&&"object"==typeof t?(e,n)=>""===e||-1<t.indexOf(e)?n:void 0:t||no,a=new Map,o=[],i=[];let s=+io(a,o,r.call({"":e},"",e)),l=!s;for(;s<o.length;)l=!0,i[s]=Xa(o[s++],c,n);return"["+i.join(",")+"]";function c(e,t){if(l)return l=!l,t;const n=r.call(this,e,t);switch(typeof n){case"object":if(null===n)return n;case"string":return a.get(n)||io(a,o,n)}return n}};function co(e){P(e,"svelte-9hauj9","ul.svelte-9hauj9{padding-left:0}li.svelte-9hauj9{list-style:none;margin:0;margin-bottom:5px}")}function uo(e){let t,n,r,a,o,i,s,l,c,u,d,f,p;return r=new Va({props:{value:"1",onPress:e[0].reset,label:"reset"}}),i=new Va({props:{value:"2",onPress:e[2],label:"save"}}),c=new Va({props:{value:"3",onPress:e[3],label:"restore"}}),f=new Va({props:{value:".",onPress:e[1],label:"hide"}}),{c(){t=A("ul"),n=A("li"),Ie(r.$$.fragment),a=M(),o=A("li"),Ie(i.$$.fragment),s=M(),l=A("li"),Ie(c.$$.fragment),u=M(),d=A("li"),Ie(f.$$.fragment),C(n,"class","svelte-9hauj9"),C(o,"class","svelte-9hauj9"),C(l,"class","svelte-9hauj9"),C(d,"class","svelte-9hauj9"),C(t,"id","debug-controls"),C(t,"class","controls svelte-9hauj9")},m(e,g){O(e,t,g),x(t,n),_e(r,n,null),x(t,a),x(t,o),_e(i,o,null),x(t,s),x(t,l),_e(c,l,null),x(t,u),x(t,d),_e(f,d,null),p=!0},p(e,[t]){const n={};1&t&&(n.onPress=e[0].reset),r.$set(n);const a={};2&t&&(a.onPress=e[1]),f.$set(a)},i(e){p||(be(r.$$.fragment,e),be(i.$$.fragment,e),be(c.$$.fragment,e),be(f.$$.fragment,e),p=!0)},o(e){$e(r.$$.fragment,e),$e(i.$$.fragment,e),$e(c.$$.fragment,e),$e(f.$$.fragment,e),p=!1},d(e){e&&I(t),Ae(r),Ae(i),Ae(c),Ae(f)}}}function fo(e,t,n){let{client:r}=t,{ToggleVisibility:a}=t;return e.$$set=e=>{"client"in e&&n(0,r=e.client),"ToggleVisibility"in e&&n(1,a=e.ToggleVisibility)},[r,a,function(){const e=r.getState(),t=lo({...e,_undo:[],_redo:[],deltalog:[]});window.localStorage.setItem("gamestate",t),window.localStorage.setItem("initialState",lo(r.initialState))},function(){const e=window.localStorage.getItem("gamestate"),t=window.localStorage.getItem("initialState");if(null!==e&&null!==t){const n=so(e),a=so(t);r.store.dispatch(Je({state:n,initialState:a}))}}]}class po extends Me{constructor(e){super(),De(this,e,fo,uo,c,{client:0,ToggleVisibility:1},co)}}function go(e){P(e,"svelte-19aan9p",".player-box.svelte-19aan9p{display:flex;flex-direction:row}.player.svelte-19aan9p{cursor:pointer;text-align:center;width:30px;height:30px;line-height:30px;background:#eee;border:3px solid #fefefe;box-sizing:content-box;padding:0}.player.current.svelte-19aan9p{background:#555;color:#eee;font-weight:bold}.player.active.svelte-19aan9p{border:3px solid #ff7f50}")}function vo(e,t,n){const r=e.slice();return r[7]=t[n],r}function ho(e){let t,n,r,a,o,i,s=e[7]+"";function l(){return e[5](e[7])}return{c(){t=A("button"),n=D(s),r=M(),C(t,"class","player svelte-19aan9p"),C(t,"aria-label",a=e[4](e[7])),R(t,"current",e[7]==e[0].currentPlayer),R(t,"active",e[7]==e[1])},m(e,a){O(e,t,a),x(t,n),x(t,r),o||(i=N(t,"click",l),o=!0)},p(r,o){e=r,4&o&&s!==(s=e[7]+"")&&G(n,s),4&o&&a!==(a=e[4](e[7]))&&C(t,"aria-label",a),5&o&&R(t,"current",e[7]==e[0].currentPlayer),6&o&&R(t,"active",e[7]==e[1])},d(e){e&&I(t),o=!1,i()}}}function mo(e){let t,r=e[2],a=[];for(let t=0;t<r.length;t+=1)a[t]=ho(vo(e,r,t));return{c(){t=A("div");for(let e=0;e<a.length;e+=1)a[e].c();C(t,"class","player-box svelte-19aan9p")},m(e,n){O(e,t,n);for(let e=0;e<a.length;e+=1)a[e].m(t,null)},p(e,[n]){if(31&n){let o;for(r=e[2],o=0;o<r.length;o+=1){const i=vo(e,r,o);a[o]?a[o].p(i,n):(a[o]=ho(i),a[o].c(),a[o].m(t,null))}for(;o<a.length;o+=1)a[o].d(1);a.length=r.length}},i:n,o:n,d(e){e&&I(t),_(a,e)}}}function yo(e,t,n){let{ctx:r}=t,{playerID:a}=t;const o=Y();function i(e){o("change",e==a?{playerID:null}:{playerID:e})}let s;return e.$$set=e=>{"ctx"in e&&n(0,r=e.ctx),"playerID"in e&&n(1,a=e.playerID)},e.$$.update=()=>{1&e.$$.dirty&&n(2,s=r?[...Array(r.numPlayers).keys()].map(e=>e.toString()):[])},[r,a,s,i,function(e){const t=[];e==r.currentPlayer&&t.push("current"),e==a&&t.push("active");let n="Player "+e;return t.length&&(n+=` (${t.join(", ")})`),n},e=>i(e)]}class bo extends Me{constructor(e){super(),De(this,e,yo,mo,c,{ctx:0,playerID:1},go)}}function $o(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function xo(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return $o(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?$o(e,t):void 0}}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,a=function(){};return{s:a,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,i=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return i=e.done,e},e:function(e){s=!0,o=e},f:function(){try{i||null==n.return||n.return()}finally{if(s)throw o}}}}function Po(e){P(e,"svelte-146sq5f",".tree.svelte-146sq5f{--json-tree-font-family:monospace;--json-tree-font-size:14px;--json-tree-null-color:#757575}.label.svelte-146sq5f{margin-bottom:0;text-transform:none}h3.svelte-146sq5f{text-transform:uppercase}ul.svelte-146sq5f{padding-left:0}li.svelte-146sq5f{list-style:none;margin:0;margin-bottom:5px}")}function wo(e,t,n){const r=e.slice();return r[11]=t[n][0],r[12]=t[n][1],r}function ko(e){let t,n,r,a;return n=new Za({props:{shortcut:e[8][e[11]],fn:e[12],name:e[11]}}),{c(){t=A("li"),Ie(n.$$.fragment),r=M(),C(t,"class","svelte-146sq5f")},m(e,o){O(e,t,o),_e(n,t,null),x(t,r),a=!0},p(e,t){const r={};16&t&&(r.shortcut=e[8][e[11]]),16&t&&(r.fn=e[12]),16&t&&(r.name=e[11]),n.$set(r)},i(e){a||(be(n.$$.fragment,e),a=!0)},o(e){$e(n.$$.fragment,e),a=!1},d(e){e&&I(t),Ae(n)}}}function Eo(e){let t,n,r;return n=new Za({props:{name:"endStage",shortcut:7,fn:e[5].endStage}}),{c(){t=A("li"),Ie(n.$$.fragment),C(t,"class","svelte-146sq5f")},m(e,a){O(e,t,a),_e(n,t,null),r=!0},p(e,t){const r={};32&t&&(r.fn=e[5].endStage),n.$set(r)},i(e){r||(be(n.$$.fragment,e),r=!0)},o(e){$e(n.$$.fragment,e),r=!1},d(e){e&&I(t),Ae(n)}}}function Oo(e){let t,n,r;return n=new Za({props:{name:"endTurn",shortcut:8,fn:e[5].endTurn}}),{c(){t=A("li"),Ie(n.$$.fragment),C(t,"class","svelte-146sq5f")},m(e,a){O(e,t,a),_e(n,t,null),r=!0},p(e,t){const r={};32&t&&(r.fn=e[5].endTurn),n.$set(r)},i(e){r||(be(n.$$.fragment,e),r=!0)},o(e){$e(n.$$.fragment,e),r=!1},d(e){e&&I(t),Ae(n)}}}function Io(e){let t,n,r;return n=new Za({props:{name:"endPhase",shortcut:9,fn:e[5].endPhase}}),{c(){t=A("li"),Ie(n.$$.fragment),C(t,"class","svelte-146sq5f")},m(e,a){O(e,t,a),_e(n,t,null),r=!0},p(e,t){const r={};32&t&&(r.fn=e[5].endPhase),n.$set(r)},i(e){r||(be(n.$$.fragment,e),r=!0)},o(e){$e(n.$$.fragment,e),r=!1},d(e){e&&I(t),Ae(n)}}}function _o(e){let t,n,r,a,o,i,s,l,c,u,d,f,p,g,v,h,m,y,b,$,P,w,k,E,S,D,j,N,T,G,z,q,V,R;a=new po({props:{client:e[0],ToggleVisibility:e[2]}}),c=new bo({props:{ctx:e[6],playerID:e[3]}}),c.$on("change",e[9]);let B=Object.entries(e[4]),U=[];for(let t=0;t<B.length;t+=1)U[t]=ko(wo(e,B,t));const F=e=>$e(U[e],1,1,()=>{U[e]=null});let L=e[6].activePlayers&&e[5].endStage&&Eo(e),J=e[5].endTurn&&Oo(e),K=e[6].phase&&e[5].endPhase&&Io(e);return D=new Ia({props:{value:e[7]}}),z=new Ia({props:{value:Ao(e[6])}}),V=new Ta({props:{clientManager:e[1]}}),{c(){t=A("section"),n=A("h3"),n.textContent="Controls",r=M(),Ie(a.$$.fragment),o=M(),i=A("section"),s=A("h3"),s.textContent="Players",l=M(),Ie(c.$$.fragment),u=M(),d=A("section"),f=A("h3"),f.textContent="Moves",p=M(),g=A("ul");for(let e=0;e<U.length;e+=1)U[e].c();v=M(),h=A("section"),m=A("h3"),m.textContent="Events",y=M(),b=A("ul"),L&&L.c(),$=M(),J&&J.c(),P=M(),K&&K.c(),w=M(),k=A("section"),E=A("h3"),E.textContent="G",S=M(),Ie(D.$$.fragment),j=M(),N=A("section"),T=A("h3"),T.textContent="ctx",G=M(),Ie(z.$$.fragment),q=M(),Ie(V.$$.fragment),C(n,"class","svelte-146sq5f"),C(s,"class","svelte-146sq5f"),C(f,"class","svelte-146sq5f"),C(g,"class","svelte-146sq5f"),C(m,"class","svelte-146sq5f"),C(b,"class","svelte-146sq5f"),C(E,"class","label svelte-146sq5f"),C(k,"class","tree svelte-146sq5f"),C(T,"class","label svelte-146sq5f"),C(N,"class","tree svelte-146sq5f")},m(e,I){O(e,t,I),x(t,n),x(t,r),_e(a,t,null),O(e,o,I),O(e,i,I),x(i,s),x(i,l),_e(c,i,null),O(e,u,I),O(e,d,I),x(d,f),x(d,p),x(d,g);for(let e=0;e<U.length;e+=1)U[e].m(g,null);O(e,v,I),O(e,h,I),x(h,m),x(h,y),x(h,b),L&&L.m(b,null),x(b,$),J&&J.m(b,null),x(b,P),K&&K.m(b,null),O(e,w,I),O(e,k,I),x(k,E),x(k,S),_e(D,k,null),O(e,j,I),O(e,N,I),x(N,T),x(N,G),_e(z,N,null),O(e,q,I),_e(V,e,I),R=!0},p(e,[t]){const n={};1&t&&(n.client=e[0]),4&t&&(n.ToggleVisibility=e[2]),a.$set(n);const r={};if(64&t&&(r.ctx=e[6]),8&t&&(r.playerID=e[3]),c.$set(r),272&t){let n;for(B=Object.entries(e[4]),n=0;n<B.length;n+=1){const r=wo(e,B,n);U[n]?(U[n].p(r,t),be(U[n],1)):(U[n]=ko(r),U[n].c(),be(U[n],1),U[n].m(g,null))}for(me(),n=B.length;n<U.length;n+=1)F(n);ye()}e[6].activePlayers&&e[5].endStage?L?(L.p(e,t),96&t&&be(L,1)):(L=Eo(e),L.c(),be(L,1),L.m(b,$)):L&&(me(),$e(L,1,1,()=>{L=null}),ye()),e[5].endTurn?J?(J.p(e,t),32&t&&be(J,1)):(J=Oo(e),J.c(),be(J,1),J.m(b,P)):J&&(me(),$e(J,1,1,()=>{J=null}),ye()),e[6].phase&&e[5].endPhase?K?(K.p(e,t),96&t&&be(K,1)):(K=Io(e),K.c(),be(K,1),K.m(b,null)):K&&(me(),$e(K,1,1,()=>{K=null}),ye());const o={};128&t&&(o.value=e[7]),D.$set(o);const i={};64&t&&(i.value=Ao(e[6])),z.$set(i);const s={};2&t&&(s.clientManager=e[1]),V.$set(s)},i(e){if(!R){be(a.$$.fragment,e),be(c.$$.fragment,e);for(let e=0;e<B.length;e+=1)be(U[e]);be(L),be(J),be(K),be(D.$$.fragment,e),be(z.$$.fragment,e),be(V.$$.fragment,e),R=!0}},o(e){$e(a.$$.fragment,e),$e(c.$$.fragment,e),U=U.filter(Boolean);for(let e=0;e<U.length;e+=1)$e(U[e]);$e(L),$e(J),$e(K),$e(D.$$.fragment,e),$e(z.$$.fragment,e),$e(V.$$.fragment,e),R=!1},d(e){e&&I(t),Ae(a),e&&I(o),e&&I(i),Ae(c),e&&I(u),e&&I(d),_(U,e),e&&I(v),e&&I(h),L&&L.d(),J&&J.d(),K&&K.d(),e&&I(w),e&&I(k),Ae(D),e&&I(j),e&&I(N),Ae(z),e&&I(q),Ae(V,e)}}}function Ao(e){let t={};for(const n in e)n.startsWith("_")||(t[n]=e[n]);return t}function So(e,t,n){let{client:r}=t,{clientManager:a}=t,{ToggleVisibility:o}=t;const i=function(e,t){var n,r={},a={},o=xo(t);try{for(o.s();!(n=o.n()).done;){a[n.value]=!0}}catch(e){o.e(e)}finally{o.f()}var i=a,s=!0;for(var l in e){var c=l[0];if(i[c]){s=!1;break}i[c]=!0,r[l]=c}if(s)return r;i=a;var u=97;for(var d in r={},e){for(var f=String.fromCharCode(u);i[f];)u++,f=String.fromCharCode(u);i[f]=!0,r[d]=f}return r}(r.moves,"mlia");let{playerID:s,moves:l,events:c}=r,u={},d={};Z(r.subscribe(e=>{e&&n(7,({G:d,ctx:u}=e),d,n(6,u)),n(3,({playerID:s,moves:l,events:c}=r),s,n(4,l),n(5,c))}));return e.$$set=e=>{"client"in e&&n(0,r=e.client),"clientManager"in e&&n(1,a=e.clientManager),"ToggleVisibility"in e&&n(2,o=e.ToggleVisibility)},[r,a,o,s,l,c,u,d,i,e=>a.switchPlayerID(e.detail.playerID)]}class Do extends Me{constructor(e){super(),De(this,e,So,_o,c,{client:0,clientManager:1,ToggleVisibility:2},Po)}}function Mo(e){P(e,"svelte-13qih23",".item.svelte-13qih23.svelte-13qih23{padding:10px}.item.svelte-13qih23.svelte-13qih23:not(:first-child){border-top:1px dashed #aaa}.item.svelte-13qih23 div.svelte-13qih23{float:right;text-align:right}")}function jo(e){let t,r,a,o,i,s,l=JSON.stringify(e[1])+"";return{c(){t=A("div"),r=A("strong"),a=D(e[0]),o=M(),i=A("div"),s=D(l),C(i,"class","svelte-13qih23"),C(t,"class","item svelte-13qih23")},m(e,n){O(e,t,n),x(t,r),x(r,a),x(t,o),x(t,i),x(i,s)},p(e,[t]){1&t&&G(a,e[0]),2&t&&l!==(l=JSON.stringify(e[1])+"")&&G(s,l)},i:n,o:n,d(e){e&&I(t)}}}function No(e,t,n){let{name:r}=t,{value:a}=t;return e.$$set=e=>{"name"in e&&n(0,r=e.name),"value"in e&&n(1,a=e.value)},[r,a]}class To extends Me{constructor(e){super(),De(this,e,No,jo,c,{name:0,value:1},Mo)}}function Co(e){P(e,"svelte-1yzq5o8",".gameinfo.svelte-1yzq5o8{padding:10px}")}function Go(e){let t,n;return t=new To({props:{name:"isConnected",value:e[1].isConnected}}),{c(){Ie(t.$$.fragment)},m(e,r){_e(t,e,r),n=!0},p(e,n){const r={};2&n&&(r.value=e[1].isConnected),t.$set(r)},i(e){n||(be(t.$$.fragment,e),n=!0)},o(e){$e(t.$$.fragment,e),n=!1},d(e){Ae(t,e)}}}function zo(e){let t,n,r,a,o,i,s,l;n=new To({props:{name:"matchID",value:e[0].matchID}}),a=new To({props:{name:"playerID",value:e[0].playerID}}),i=new To({props:{name:"isActive",value:e[1].isActive}});let c=e[0].multiplayer&&Go(e);return{c(){t=A("section"),Ie(n.$$.fragment),r=M(),Ie(a.$$.fragment),o=M(),Ie(i.$$.fragment),s=M(),c&&c.c(),C(t,"class","gameinfo svelte-1yzq5o8")},m(e,u){O(e,t,u),_e(n,t,null),x(t,r),_e(a,t,null),x(t,o),_e(i,t,null),x(t,s),c&&c.m(t,null),l=!0},p(e,[r]){const o={};1&r&&(o.value=e[0].matchID),n.$set(o);const s={};1&r&&(s.value=e[0].playerID),a.$set(s);const l={};2&r&&(l.value=e[1].isActive),i.$set(l),e[0].multiplayer?c?(c.p(e,r),1&r&&be(c,1)):(c=Go(e),c.c(),be(c,1),c.m(t,null)):c&&(me(),$e(c,1,1,()=>{c=null}),ye())},i(e){l||(be(n.$$.fragment,e),be(a.$$.fragment,e),be(i.$$.fragment,e),be(c),l=!0)},o(e){$e(n.$$.fragment,e),$e(a.$$.fragment,e),$e(i.$$.fragment,e),$e(c),l=!1},d(e){e&&I(t),Ae(n),Ae(a),Ae(i),c&&c.d()}}}function qo(e,t,r){let a,o=n,i=()=>(o(),o=u(s,e=>r(1,a=e)),s);e.$$.on_destroy.push(()=>o());let{client:s}=t;i();let{clientManager:l}=t,{ToggleVisibility:c}=t;return e.$$set=e=>{"client"in e&&i(r(0,s=e.client)),"clientManager"in e&&r(2,l=e.clientManager),"ToggleVisibility"in e&&r(3,c=e.ToggleVisibility)},[s,a,l,c]}class Vo extends Me{constructor(e){super(),De(this,e,qo,zo,c,{client:0,clientManager:2,ToggleVisibility:3},Co)}}function Ro(e){P(e,"svelte-6eza86",".turn-marker.svelte-6eza86{display:flex;justify-content:center;align-items:center;grid-column:1;background:#555;color:#eee;text-align:center;font-weight:bold;border:1px solid #888}")}function Bo(e){let t,r;return{c(){t=A("div"),r=D(e[0]),C(t,"class","turn-marker svelte-6eza86"),C(t,"style",e[1])},m(e,n){O(e,t,n),x(t,r)},p(e,[t]){1&t&&G(r,e[0])},i:n,o:n,d(e){e&&I(t)}}}function Uo(e,t,n){let{turn:r}=t,{numEvents:a}=t;const o="grid-row: span "+a;return e.$$set=e=>{"turn"in e&&n(0,r=e.turn),"numEvents"in e&&n(2,a=e.numEvents)},[r,o,a]}class Fo extends Me{constructor(e){super(),De(this,e,Uo,Bo,c,{turn:0,numEvents:2},Ro)}}function Lo(e){P(e,"svelte-1t4xap",".phase-marker.svelte-1t4xap{grid-column:3;background:#555;border:1px solid #888;color:#eee;text-align:center;font-weight:bold;padding-top:10px;padding-bottom:10px;text-orientation:sideways;writing-mode:vertical-rl;line-height:30px;width:100%}")}function Jo(e){let t,r,a=(e[0]||"")+"";return{c(){t=A("div"),r=D(a),C(t,"class","phase-marker svelte-1t4xap"),C(t,"style",e[1])},m(e,n){O(e,t,n),x(t,r)},p(e,[t]){1&t&&a!==(a=(e[0]||"")+"")&&G(r,a)},i:n,o:n,d(e){e&&I(t)}}}function Ko(e,t,n){let{phase:r}=t,{numEvents:a}=t;const o="grid-row: span "+a;return e.$$set=e=>{"phase"in e&&n(0,r=e.phase),"numEvents"in e&&n(2,a=e.numEvents)},[r,o,a]}class Ho extends Me{constructor(e){super(),De(this,e,Ko,Jo,c,{phase:0,numEvents:2},Lo)}}function Wo(e){let t;return{c(){t=A("div"),t.textContent=""+e[0]},m(e,n){O(e,t,n)},p:n,i:n,o:n,d(e){e&&I(t)}}}function Zo(e,t,n){let{metadata:r}=t;const a=void 0!==r?JSON.stringify(r,null,4):"";return e.$$set=e=>{"metadata"in e&&n(1,r=e.metadata)},[a,r]}class Yo extends Me{constructor(e){super(),De(this,e,Zo,Wo,c,{metadata:1})}}function Xo(e){P(e,"svelte-vajd9z",".log-event.svelte-vajd9z{grid-column:2;cursor:pointer;overflow:hidden;display:flex;flex-direction:column;justify-content:center;background:#fff;border:1px dotted #ccc;border-left:5px solid #ccc;padding:5px;text-align:center;color:#666;font-size:14px;min-height:25px;line-height:25px}.log-event.svelte-vajd9z:hover,.log-event.svelte-vajd9z:focus{border-style:solid;background:#eee}.log-event.pinned.svelte-vajd9z{border-style:solid;background:#eee;opacity:1}.args.svelte-vajd9z{text-align:left;white-space:pre-wrap}.player0.svelte-vajd9z{border-left-color:#ff851b}.player1.svelte-vajd9z{border-left-color:#7fdbff}.player2.svelte-vajd9z{border-left-color:#0074d9}.player3.svelte-vajd9z{border-left-color:#39cccc}.player4.svelte-vajd9z{border-left-color:#3d9970}.player5.svelte-vajd9z{border-left-color:#2ecc40}.player6.svelte-vajd9z{border-left-color:#01ff70}.player7.svelte-vajd9z{border-left-color:#ffdc00}.player8.svelte-vajd9z{border-left-color:#001f3f}.player9.svelte-vajd9z{border-left-color:#ff4136}.player10.svelte-vajd9z{border-left-color:#85144b}.player11.svelte-vajd9z{border-left-color:#f012be}.player12.svelte-vajd9z{border-left-color:#b10dc9}.player13.svelte-vajd9z{border-left-color:#111111}.player14.svelte-vajd9z{border-left-color:#aaaaaa}.player15.svelte-vajd9z{border-left-color:#dddddd}")}function Qo(e){let t,n;return t=new Yo({props:{metadata:e[2]}}),{c(){Ie(t.$$.fragment)},m(e,r){_e(t,e,r),n=!0},p(e,n){const r={};4&n&&(r.metadata=e[2]),t.$set(r)},i(e){n||(be(t.$$.fragment,e),n=!0)},o(e){$e(t.$$.fragment,e),n=!1},d(e){Ae(t,e)}}}function ei(e){let t,n,r;var a=e[3];function o(e){return{props:{metadata:e[2]}}}return a&&(t=new a(o(e))),{c(){t&&Ie(t.$$.fragment),n=j()},m(e,a){t&&_e(t,e,a),O(e,n,a),r=!0},p(e,r){const i={};if(4&r&&(i.metadata=e[2]),a!==(a=e[3])){if(t){me();const e=t;$e(e.$$.fragment,1,0,()=>{Ae(e,1)}),ye()}a?(t=new a(o(e)),Ie(t.$$.fragment),be(t.$$.fragment,1),_e(t,n.parentNode,n)):t=null}else a&&t.$set(i)},i(e){r||(t&&be(t.$$.fragment,e),r=!0)},o(e){t&&$e(t.$$.fragment,e),r=!1},d(e){e&&I(n),t&&Ae(t,e)}}}function ti(e){let t,n,r,a,o,i,l,c,u,d,f,p,g;const v=[ei,Qo],h=[];function m(e,t){return e[3]?0:1}return c=m(e),u=h[c]=v[c](e),{c(){t=A("button"),n=A("div"),r=D(e[4]),a=D("("),o=D(e[6]),i=D(")"),l=M(),u.c(),C(n,"class","args svelte-vajd9z"),C(t,"class",d="log-event player"+e[7]+" svelte-vajd9z"),R(t,"pinned",e[1])},m(s,u){O(s,t,u),x(t,n),x(n,r),x(n,a),x(n,o),x(n,i),x(t,l),h[c].m(t,null),f=!0,p||(g=[N(t,"click",e[9]),N(t,"mouseenter",e[10]),N(t,"focus",e[11]),N(t,"mouseleave",e[12]),N(t,"blur",e[13])],p=!0)},p(e,[n]){(!f||16&n)&&G(r,e[4]);let a=c;c=m(e),c===a?h[c].p(e,n):(me(),$e(h[a],1,1,()=>{h[a]=null}),ye(),u=h[c],u?u.p(e,n):(u=h[c]=v[c](e),u.c()),be(u,1),u.m(t,null)),2&n&&R(t,"pinned",e[1])},i(e){f||(be(u),f=!0)},o(e){$e(u),f=!1},d(e){e&&I(t),h[c].d(),p=!1,s(g)}}}function ni(e,t,n){let{logIndex:r}=t,{action:a}=t,{pinned:o}=t,{metadata:i}=t,{metadataComponent:s}=t;const l=Y(),c=a.payload.args,u=Array.isArray(c)?c.map(e=>JSON.stringify(e,null,2)).join(","):JSON.stringify(c,null,2)||"",d=a.payload.playerID;let f;switch(a.type){case"UNDO":f="undo";break;case"REDO":f="redo";case"GAME_EVENT":case"MAKE_MOVE":default:f=a.payload.type}return e.$$set=e=>{"logIndex"in e&&n(0,r=e.logIndex),"action"in e&&n(8,a=e.action),"pinned"in e&&n(1,o=e.pinned),"metadata"in e&&n(2,i=e.metadata),"metadataComponent"in e&&n(3,s=e.metadataComponent)},[r,o,i,s,f,l,u,d,a,()=>l("click",{logIndex:r}),()=>l("mouseenter",{logIndex:r}),()=>l("mouseenter",{logIndex:r}),()=>l("mouseleave"),()=>l("mouseleave")]}class ri extends Me{constructor(e){super(),De(this,e,ni,ti,c,{logIndex:0,action:8,pinned:1,metadata:2,metadataComponent:3},Xo)}}function ai(e){let t;return{c(){t=S("path"),C(t,"d","M504 256c0 137-111 248-248 248S8 393 8 256 119 8 256 8s248 111 248 248zM212 140v116h-70.9c-10.7 0-16.1 13-8.5 20.5l114.9 114.3c4.7 4.7 12.2 4.7 16.9 0l114.9-114.3c7.6-7.6 2.2-20.5-8.5-20.5H300V140c0-6.6-5.4-12-12-12h-64c-6.6 0-12 5.4-12 12z")},m(e,n){O(e,t,n)},p:n,d(e){e&&I(t)}}}function oi(e){let t,n;const r=[{viewBox:"0 0 512 512"},e[0]];let o={$$slots:{default:[ai]},$$scope:{ctx:e}};for(let e=0;e<r.length;e+=1)o=a(o,r[e]);return t=new ur({props:o}),{c(){Ie(t.$$.fragment)},m(e,r){_e(t,e,r),n=!0},p(e,[n]){const a=1&n?Ee(r,[r[0],Oe(e[0])]):{};2&n&&(a.$$scope={dirty:n,ctx:e}),t.$set(a)},i(e){n||(be(t.$$.fragment,e),n=!0)},o(e){$e(t.$$.fragment,e),n=!1},d(e){Ae(t,e)}}}function ii(e,t,n){return e.$$set=e=>{n(0,t=a(a({},t),p(e)))},[t=p(t)]}class si extends Me{constructor(e){super(),De(this,e,ii,oi,c,{})}}function li(e){P(e,"svelte-1a7time","div.svelte-1a7time{white-space:nowrap;text-overflow:ellipsis;overflow:hidden;max-width:500px}")}function ci(e){let t,r;return{c(){t=A("div"),r=D(e[0]),C(t,"alt",e[0]),C(t,"class","svelte-1a7time")},m(e,n){O(e,t,n),x(t,r)},p(e,[n]){1&n&&G(r,e[0]),1&n&&C(t,"alt",e[0])},i:n,o:n,d(e){e&&I(t)}}}function ui(e,t,n){let r,{action:a}=t;return e.$$set=e=>{"action"in e&&n(1,a=e.action)},e.$$.update=()=>{if(2&e.$$.dirty){const{type:e,args:t}=a.payload,o=(t||[]).join(",");n(0,r=`${e}(${o})`)}},[r,a]}class di extends Me{constructor(e){super(),De(this,e,ui,ci,c,{action:1},li)}}function fi(e){P(e,"svelte-ztcwsu","table.svelte-ztcwsu.svelte-ztcwsu{font-size:12px;border-collapse:collapse;border:1px solid #ddd;padding:0}tr.svelte-ztcwsu.svelte-ztcwsu{cursor:pointer}tr.svelte-ztcwsu:hover td.svelte-ztcwsu{background:#eee}tr.selected.svelte-ztcwsu td.svelte-ztcwsu{background:#eee}td.svelte-ztcwsu.svelte-ztcwsu{padding:10px;height:10px;line-height:10px;font-size:12px;border:none}th.svelte-ztcwsu.svelte-ztcwsu{background:#888;color:#fff;padding:10px;text-align:center}")}function pi(e,t,n){const r=e.slice();return r[10]=t[n],r[12]=n,r}function gi(e){let t,n,r,a,o,i,l,c,u,d,f,p,g,v=e[10].value+"",h=e[10].visits+"";function m(){return e[6](e[10],e[12])}function y(){return e[7](e[12])}function b(){return e[8](e[10],e[12])}return u=new di({props:{action:e[10].parentAction}}),{c(){t=A("tr"),n=A("td"),r=D(v),a=M(),o=A("td"),i=D(h),l=M(),c=A("td"),Ie(u.$$.fragment),d=M(),C(n,"class","svelte-ztcwsu"),C(o,"class","svelte-ztcwsu"),C(c,"class","svelte-ztcwsu"),C(t,"class","svelte-ztcwsu"),R(t,"clickable",e[1].length>0),R(t,"selected",e[12]===e[0])},m(e,s){O(e,t,s),x(t,n),x(n,r),x(t,a),x(t,o),x(o,i),x(t,l),x(t,c),_e(u,c,null),x(t,d),f=!0,p||(g=[N(t,"click",m),N(t,"mouseout",y),N(t,"mouseover",b)],p=!0)},p(n,a){e=n,(!f||2&a)&&v!==(v=e[10].value+"")&&G(r,v),(!f||2&a)&&h!==(h=e[10].visits+"")&&G(i,h);const o={};2&a&&(o.action=e[10].parentAction),u.$set(o),2&a&&R(t,"clickable",e[1].length>0),1&a&&R(t,"selected",e[12]===e[0])},i(e){f||(be(u.$$.fragment,e),f=!0)},o(e){$e(u.$$.fragment,e),f=!1},d(e){e&&I(t),Ae(u),p=!1,s(g)}}}function vi(e){let t,n,r,a,o,i=e[1],s=[];for(let t=0;t<i.length;t+=1)s[t]=gi(pi(e,i,t));const l=e=>$e(s[e],1,1,()=>{s[e]=null});return{c(){t=A("table"),n=A("thead"),n.innerHTML='<th class="svelte-ztcwsu">Value</th> \n    <th class="svelte-ztcwsu">Visits</th> \n    <th class="svelte-ztcwsu">Action</th>',r=M(),a=A("tbody");for(let e=0;e<s.length;e+=1)s[e].c();C(t,"class","svelte-ztcwsu")},m(e,i){O(e,t,i),x(t,n),x(t,r),x(t,a);for(let e=0;e<s.length;e+=1)s[e].m(a,null);o=!0},p(e,[t]){if(15&t){let n;for(i=e[1],n=0;n<i.length;n+=1){const r=pi(e,i,n);s[n]?(s[n].p(r,t),be(s[n],1)):(s[n]=gi(r),s[n].c(),be(s[n],1),s[n].m(a,null))}for(me(),n=i.length;n<s.length;n+=1)l(n);ye()}},i(e){if(!o){for(let e=0;e<i.length;e+=1)be(s[e]);o=!0}},o(e){s=s.filter(Boolean);for(let e=0;e<s.length;e+=1)$e(s[e]);o=!1},d(e){e&&I(t),_(s,e)}}}function hi(e,t,n){let{root:r}=t,{selectedIndex:a=null}=t;const o=Y();let i=[],s=[];function l(e,t){o("select",{node:e,selectedIndex:t})}function c(e,t){null===a&&o("preview",{node:e})}return e.$$set=e=>{"root"in e&&n(4,r=e.root),"selectedIndex"in e&&n(0,a=e.selectedIndex)},e.$$.update=()=>{if(48&e.$$.dirty){let e=r;for(n(5,i=[]);e.parent;){const t=e.parent,{type:n,args:r}=e.parentAction.payload,a=`${n}(${(r||[]).join(",")})`;i.push({parent:t,arrowText:a}),e=t}i.reverse(),n(1,s=[...r.children].sort((e,t)=>e.visits<t.visits?1:-1).slice(0,50))}},[a,s,l,c,r,i,(e,t)=>l(e,t),e=>c(null),(e,t)=>c(e)]}class mi extends Me{constructor(e){super(),De(this,e,hi,vi,c,{root:4,selectedIndex:0},fi)}}function yi(e){P(e,"svelte-1f0amz4",".visualizer.svelte-1f0amz4{display:flex;flex-direction:column;align-items:center;padding:50px}.preview.svelte-1f0amz4{opacity:0.5}.icon.svelte-1f0amz4{color:#777;width:32px;height:32px;margin-bottom:20px}")}function bi(e,t,n){const r=e.slice();return r[9]=t[n].node,r[10]=t[n].selectedIndex,r[12]=n,r}function $i(e){let t,n;return t=new mi({props:{root:e[9],selectedIndex:e[10]}}),t.$on("select",(function(...t){return e[7](e[12],...t)})),{c(){Ie(t.$$.fragment)},m(e,r){_e(t,e,r),n=!0},p(n,r){e=n;const a={};1&r&&(a.root=e[9]),1&r&&(a.selectedIndex=e[10]),t.$set(a)},i(e){n||(be(t.$$.fragment,e),n=!0)},o(e){$e(t.$$.fragment,e),n=!1},d(e){Ae(t,e)}}}function xi(e){let t,n;return t=new mi({props:{root:e[9]}}),t.$on("select",(function(...t){return e[5](e[12],...t)})),t.$on("preview",(function(...t){return e[6](e[12],...t)})),{c(){Ie(t.$$.fragment)},m(e,r){_e(t,e,r),n=!0},p(n,r){e=n;const a={};1&r&&(a.root=e[9]),t.$set(a)},i(e){n||(be(t.$$.fragment,e),n=!0)},o(e){$e(t.$$.fragment,e),n=!1},d(e){Ae(t,e)}}}function Pi(e){let t,n,r,a,o,i=0!==e[12]&&function(e){let t,n,r;return n=new si({}),{c(){t=A("div"),Ie(n.$$.fragment),C(t,"class","icon svelte-1f0amz4")},m(e,a){O(e,t,a),_e(n,t,null),r=!0},i(e){r||(be(n.$$.fragment,e),r=!0)},o(e){$e(n.$$.fragment,e),r=!1},d(e){e&&I(t),Ae(n)}}}();const s=[xi,$i],l=[];function c(e,t){return e[12]===e[0].length-1?0:1}return r=c(e),a=l[r]=s[r](e),{c(){i&&i.c(),t=M(),n=A("section"),a.c()},m(e,a){i&&i.m(e,a),O(e,t,a),O(e,n,a),l[r].m(n,null),o=!0},p(e,t){let o=r;r=c(e),r===o?l[r].p(e,t):(me(),$e(l[o],1,1,()=>{l[o]=null}),ye(),a=l[r],a?a.p(e,t):(a=l[r]=s[r](e),a.c()),be(a,1),a.m(n,null))},i(e){o||(be(i),be(a),o=!0)},o(e){$e(i),$e(a),o=!1},d(e){i&&i.d(e),e&&I(t),e&&I(n),l[r].d()}}}function wi(e){let t,n,r,a,o,i;return n=new si({}),o=new mi({props:{root:e[1]}}),{c(){t=A("div"),Ie(n.$$.fragment),r=M(),a=A("section"),Ie(o.$$.fragment),C(t,"class","icon svelte-1f0amz4"),C(a,"class","preview svelte-1f0amz4")},m(e,s){O(e,t,s),_e(n,t,null),O(e,r,s),O(e,a,s),_e(o,a,null),i=!0},p(e,t){const n={};2&t&&(n.root=e[1]),o.$set(n)},i(e){i||(be(n.$$.fragment,e),be(o.$$.fragment,e),i=!0)},o(e){$e(n.$$.fragment,e),$e(o.$$.fragment,e),i=!1},d(e){e&&I(t),Ae(n),e&&I(r),e&&I(a),Ae(o)}}}function ki(e){let t,n,r,a=e[0],o=[];for(let t=0;t<a.length;t+=1)o[t]=Pi(bi(e,a,t));const i=e=>$e(o[e],1,1,()=>{o[e]=null});let s=e[1]&&wi(e);return{c(){t=A("div");for(let e=0;e<o.length;e+=1)o[e].c();n=M(),s&&s.c(),C(t,"class","visualizer svelte-1f0amz4")},m(e,a){O(e,t,a);for(let e=0;e<o.length;e+=1)o[e].m(t,null);x(t,n),s&&s.m(t,null),r=!0},p(e,[r]){if(13&r){let s;for(a=e[0],s=0;s<a.length;s+=1){const i=bi(e,a,s);o[s]?(o[s].p(i,r),be(o[s],1)):(o[s]=Pi(i),o[s].c(),be(o[s],1),o[s].m(t,n))}for(me(),s=a.length;s<o.length;s+=1)i(s);ye()}e[1]?s?(s.p(e,r),2&r&&be(s,1)):(s=wi(e),s.c(),be(s,1),s.m(t,null)):s&&(me(),$e(s,1,1,()=>{s=null}),ye())},i(e){if(!r){for(let e=0;e<a.length;e+=1)be(o[e]);be(s),r=!0}},o(e){o=o.filter(Boolean);for(let e=0;e<o.length;e+=1)$e(o[e]);$e(s),r=!1},d(e){e&&I(t),_(o,e),s&&s.d()}}}function Ei(e,t,n){let{metadata:r}=t,a=[],o=null;function i({node:e,selectedIndex:t},r){n(1,o=null),n(0,a[r].selectedIndex=t,a),n(0,a=[...a.slice(0,r+1),{node:e}])}function s({node:e},t){n(1,o=e)}return e.$$set=e=>{"metadata"in e&&n(4,r=e.metadata)},e.$$.update=()=>{16&e.$$.dirty&&n(0,a=[{node:r}])},[a,o,i,s,r,(e,t)=>i(t.detail,e),(e,t)=>s(t.detail),(e,t)=>i(t.detail,e)]}class Oi extends Me{constructor(e){super(),De(this,e,Ei,ki,c,{metadata:4},yi)}}function Ii(e){P(e,"svelte-1pq5e4b",".gamelog.svelte-1pq5e4b{display:grid;grid-template-columns:30px 1fr 30px;grid-auto-rows:auto;grid-auto-flow:column}")}function _i(e,t,n){const r=e.slice();return r[16]=t[n].phase,r[18]=n,r}function Ai(e,t,n){const r=e.slice();return r[19]=t[n].action,r[20]=t[n].metadata,r[18]=n,r}function Si(e,t,n){const r=e.slice();return r[22]=t[n].turn,r[18]=n,r}function Di(e){let t,n;return t=new Fo({props:{turn:e[22],numEvents:e[3][e[18]]}}),{c(){Ie(t.$$.fragment)},m(e,r){_e(t,e,r),n=!0},p(e,n){const r={};2&n&&(r.turn=e[22]),8&n&&(r.numEvents=e[3][e[18]]),t.$set(r)},i(e){n||(be(t.$$.fragment,e),n=!0)},o(e){$e(t.$$.fragment,e),n=!1},d(e){Ae(t,e)}}}function Mi(e){let t,n,r=e[18]in e[3]&&Di(e);return{c(){r&&r.c(),t=j()},m(e,a){r&&r.m(e,a),O(e,t,a),n=!0},p(e,n){e[18]in e[3]?r?(r.p(e,n),8&n&&be(r,1)):(r=Di(e),r.c(),be(r,1),r.m(t.parentNode,t)):r&&(me(),$e(r,1,1,()=>{r=null}),ye())},i(e){n||(be(r),n=!0)},o(e){$e(r),n=!1},d(e){r&&r.d(e),e&&I(t)}}}function ji(e){let t,n;return t=new ri({props:{pinned:e[18]===e[2],logIndex:e[18],action:e[19],metadata:e[20]}}),t.$on("click",e[5]),t.$on("mouseenter",e[6]),t.$on("mouseleave",e[7]),{c(){Ie(t.$$.fragment)},m(e,r){_e(t,e,r),n=!0},p(e,n){const r={};4&n&&(r.pinned=e[18]===e[2]),2&n&&(r.action=e[19]),2&n&&(r.metadata=e[20]),t.$set(r)},i(e){n||(be(t.$$.fragment,e),n=!0)},o(e){$e(t.$$.fragment,e),n=!1},d(e){Ae(t,e)}}}function Ni(e){let t,n;return t=new Ho({props:{phase:e[16],numEvents:e[4][e[18]]}}),{c(){Ie(t.$$.fragment)},m(e,r){_e(t,e,r),n=!0},p(e,n){const r={};2&n&&(r.phase=e[16]),16&n&&(r.numEvents=e[4][e[18]]),t.$set(r)},i(e){n||(be(t.$$.fragment,e),n=!0)},o(e){$e(t.$$.fragment,e),n=!1},d(e){Ae(t,e)}}}function Ti(e){let t,n,r=e[18]in e[4]&&Ni(e);return{c(){r&&r.c(),t=j()},m(e,a){r&&r.m(e,a),O(e,t,a),n=!0},p(e,n){e[18]in e[4]?r?(r.p(e,n),16&n&&be(r,1)):(r=Ni(e),r.c(),be(r,1),r.m(t.parentNode,t)):r&&(me(),$e(r,1,1,()=>{r=null}),ye())},i(e){n||(be(r),n=!0)},o(e){$e(r),n=!1},d(e){r&&r.d(e),e&&I(t)}}}function Ci(e){let t,n,r,a,o,i,s=e[1],l=[];for(let t=0;t<s.length;t+=1)l[t]=Mi(Si(e,s,t));const c=e=>$e(l[e],1,1,()=>{l[e]=null});let u=e[1],d=[];for(let t=0;t<u.length;t+=1)d[t]=ji(Ai(e,u,t));const f=e=>$e(d[e],1,1,()=>{d[e]=null});let p=e[1],g=[];for(let t=0;t<p.length;t+=1)g[t]=Ti(_i(e,p,t));const v=e=>$e(g[e],1,1,()=>{g[e]=null});return{c(){t=A("div");for(let e=0;e<l.length;e+=1)l[e].c();n=M();for(let e=0;e<d.length;e+=1)d[e].c();r=M();for(let e=0;e<g.length;e+=1)g[e].c();C(t,"class","gamelog svelte-1pq5e4b"),R(t,"pinned",e[2])},m(s,c){O(s,t,c);for(let e=0;e<l.length;e+=1)l[e].m(t,null);x(t,n);for(let e=0;e<d.length;e+=1)d[e].m(t,null);x(t,r);for(let e=0;e<g.length;e+=1)g[e].m(t,null);a=!0,o||(i=N(window,"keydown",e[8]),o=!0)},p(e,[a]){if(10&a){let r;for(s=e[1],r=0;r<s.length;r+=1){const o=Si(e,s,r);l[r]?(l[r].p(o,a),be(l[r],1)):(l[r]=Mi(o),l[r].c(),be(l[r],1),l[r].m(t,n))}for(me(),r=s.length;r<l.length;r+=1)c(r);ye()}if(230&a){let n;for(u=e[1],n=0;n<u.length;n+=1){const o=Ai(e,u,n);d[n]?(d[n].p(o,a),be(d[n],1)):(d[n]=ji(o),d[n].c(),be(d[n],1),d[n].m(t,r))}for(me(),n=u.length;n<d.length;n+=1)f(n);ye()}if(18&a){let n;for(p=e[1],n=0;n<p.length;n+=1){const r=_i(e,p,n);g[n]?(g[n].p(r,a),be(g[n],1)):(g[n]=Ti(r),g[n].c(),be(g[n],1),g[n].m(t,null))}for(me(),n=p.length;n<g.length;n+=1)v(n);ye()}4&a&&R(t,"pinned",e[2])},i(e){if(!a){for(let e=0;e<s.length;e+=1)be(l[e]);for(let e=0;e<u.length;e+=1)be(d[e]);for(let e=0;e<p.length;e+=1)be(g[e]);a=!0}},o(e){l=l.filter(Boolean);for(let e=0;e<l.length;e+=1)$e(l[e]);d=d.filter(Boolean);for(let e=0;e<d.length;e+=1)$e(d[e]);g=g.filter(Boolean);for(let e=0;e<g.length;e+=1)$e(g[e]);a=!1},d(e){e&&I(t),_(l,e),_(d,e),_(g,e),o=!1,i()}}}function Gi(e,t,r){let a,o=n,i=()=>(o(),o=u(s,e=>r(10,a=e)),s);e.$$.on_destroy.push(()=>o());let{client:s}=t;i();const{secondaryPane:l}=Q("secondaryPane"),c=Yn({game:s.game}),d=s.getInitialState();let f,{log:p}=a,g=null;function v(e){let t=d;for(let n=0;n<p.length;n++){const{action:r,automatic:a}=p[n];if(!a){if(t=c(t,r),0==e)break;e--}}return{G:t.G,ctx:t.ctx,plugins:t.plugins}}function h(){r(2,g=null),s.overrideGameState(null),l.set(null)}Z(h);let m={},y={};return e.$$set=e=>{"client"in e&&i(r(0,s=e.client))},e.$$.update=()=>{if(1538&e.$$.dirty){r(9,p=a.log),r(1,f=p.filter(e=>!e.automatic));let e=0,t=0;r(3,m={}),r(4,y={});for(let n=0;n<f.length;n++){const{action:a,payload:o,turn:i,phase:s}=f[n];t++,e++,n!=f.length-1&&f[n+1].turn==i||(r(3,m[n]=t,m),t=0),n!=f.length-1&&f[n+1].phase==s||(r(4,y[n]=e,y),e=0)}}},[s,f,g,m,y,function(e){const{logIndex:t}=e.detail,n=v(t),a=p.filter(e=>!e.automatic);if(s.overrideGameState(n),g==t)r(2,g=null),l.set(null);else{r(2,g=t);const{metadata:e}=a[t].action.payload;e&&l.set({component:Oi,metadata:e})}},function(e){const{logIndex:t}=e.detail;if(null===g){const e=v(t);s.overrideGameState(e)}},function(){null===g&&s.overrideGameState(null)},function(e){27==e.keyCode&&h()},p,a]}class zi extends Me{constructor(e){super(),De(this,e,Gi,Ci,c,{client:0},Ii)}}function qi(e){P(e,"svelte-1fu900w","label.svelte-1fu900w{color:#666}.option.svelte-1fu900w{margin-bottom:20px}.value.svelte-1fu900w{font-weight:bold;color:#000}input[type='checkbox'].svelte-1fu900w{vertical-align:middle}")}function Vi(e,t,n){const r=e.slice();return r[6]=t[n][0],r[7]=t[n][1],r[8]=t,r[9]=n,r}function Ri(e){let t,n,r,a;function o(){e[5].call(t,e[6])}return{c(){t=A("input"),C(t,"id",n=e[3](e[6])),C(t,"type","checkbox"),C(t,"class","svelte-1fu900w")},m(n,i){O(n,t,i),t.checked=e[1][e[6]],r||(a=[N(t,"change",o),N(t,"change",e[2])],r=!0)},p(r,a){e=r,1&a&&n!==(n=e[3](e[6]))&&C(t,"id",n),3&a&&(t.checked=e[1][e[6]])},d(e){e&&I(t),r=!1,s(a)}}}function Bi(e){let t,n,r,a,o,i,l,c,u,d=e[1][e[6]]+"";function f(){e[4].call(a,e[6])}return{c(){t=A("span"),n=D(d),r=M(),a=A("input"),C(t,"class","value svelte-1fu900w"),C(a,"id",o=e[3](e[6])),C(a,"type","range"),C(a,"min",i=e[7].range.min),C(a,"max",l=e[7].range.max)},m(o,i){O(o,t,i),x(t,n),O(o,r,i),O(o,a,i),z(a,e[1][e[6]]),c||(u=[N(a,"change",f),N(a,"input",f),N(a,"change",e[2])],c=!0)},p(t,r){e=t,3&r&&d!==(d=e[1][e[6]]+"")&&G(n,d),1&r&&o!==(o=e[3](e[6]))&&C(a,"id",o),1&r&&i!==(i=e[7].range.min)&&C(a,"min",i),1&r&&l!==(l=e[7].range.max)&&C(a,"max",l),3&r&&z(a,e[1][e[6]])},d(e){e&&I(t),e&&I(r),e&&I(a),c=!1,s(u)}}}function Ui(e){let t,n,r,a,o,i,s=e[6]+"";function l(e,t){return e[7].range?Bi:"boolean"==typeof e[7].value?Ri:void 0}let c=l(e),u=c&&c(e);return{c(){t=A("div"),n=A("label"),r=D(s),o=M(),u&&u.c(),i=M(),C(n,"for",a=e[3](e[6])),C(n,"class","svelte-1fu900w"),C(t,"class","option svelte-1fu900w")},m(e,a){O(e,t,a),x(t,n),x(n,r),x(t,o),u&&u.m(t,null),x(t,i)},p(e,o){1&o&&s!==(s=e[6]+"")&&G(r,s),1&o&&a!==(a=e[3](e[6]))&&C(n,"for",a),c===(c=l(e))&&u?u.p(e,o):(u&&u.d(1),u=c&&c(e),u&&(u.c(),u.m(t,i)))},d(e){e&&I(t),u&&u.d()}}}function Fi(e){let t,r=Object.entries(e[0].opts()),a=[];for(let t=0;t<r.length;t+=1)a[t]=Ui(Vi(e,r,t));return{c(){for(let e=0;e<a.length;e+=1)a[e].c();t=j()},m(e,n){for(let t=0;t<a.length;t+=1)a[t].m(e,n);O(e,t,n)},p(e,[n]){if(15&n){let o;for(r=Object.entries(e[0].opts()),o=0;o<r.length;o+=1){const i=Vi(e,r,o);a[o]?a[o].p(i,n):(a[o]=Ui(i),a[o].c(),a[o].m(t.parentNode,t))}for(;o<a.length;o+=1)a[o].d(1);a.length=r.length}},i:n,o:n,d(e){_(a,e),e&&I(t)}}}function Li(e,t,n){let{bot:r}=t,a={};for(let[e,t]of Object.entries(r.opts()))a[e]=t.value;return e.$$set=e=>{"bot"in e&&n(0,r=e.bot)},[r,a,function(){for(let[e,t]of Object.entries(a))r.setOpt(e,t)},e=>"ai-option-"+e,function(e){var t;a[e]=""===(t=this.value)?null:+t,n(1,a)},function(e){a[e]=this.checked,n(1,a)}]}class Ji extends Me{constructor(e){super(),De(this,e,Li,Fi,c,{bot:0},qi)}}!function(e,t){if(!e.setImmediate){var n,r,a,o,i,s=1,l={},c=!1,u=e.document,d=Object.getPrototypeOf&&Object.getPrototypeOf(e);d=d&&d.setTimeout?d:e,"[object process]"==={}.toString.call(e.process)?n=function(e){process.nextTick((function(){p(e)}))}:!function(){if(e.postMessage&&!e.importScripts){var t=!0,n=e.onmessage;return e.onmessage=function(){t=!1},e.postMessage("","*"),e.onmessage=n,t}}()?e.MessageChannel?((a=new MessageChannel).port1.onmessage=function(e){p(e.data)},n=function(e){a.port2.postMessage(e)}):u&&"onreadystatechange"in u.createElement("script")?(r=u.documentElement,n=function(e){var t=u.createElement("script");t.onreadystatechange=function(){p(e),t.onreadystatechange=null,r.removeChild(t),t=null},r.appendChild(t)}):n=function(e){setTimeout(p,0,e)}:(o="setImmediate$"+Math.random()+"$",i=function(t){t.source===e&&"string"==typeof t.data&&0===t.data.indexOf(o)&&p(+t.data.slice(o.length))},e.addEventListener?e.addEventListener("message",i,!1):e.attachEvent("onmessage",i),n=function(t){e.postMessage(o+t,"*")}),d.setImmediate=function(e){"function"!=typeof e&&(e=new Function(""+e));for(var t=new Array(arguments.length-1),r=0;r<t.length;r++)t[r]=arguments[r+1];var a={callback:e,args:t};return l[s]=a,n(s),s++},d.clearImmediate=f}function f(e){delete l[e]}function p(e){if(c)setTimeout(p,0,e);else{var t=l[e];if(t){c=!0;try{!function(e){var t=e.callback,n=e.args;switch(n.length){case 0:t();break;case 1:t(n[0]);break;case 2:t(n[0],n[1]);break;case 3:t(n[0],n[1],n[2]);break;default:t.apply(void 0,n)}}(t)}finally{f(e),c=!1}}}}}("undefined"==typeof self?Tn:self);class Ki{constructor({enumerate:e,seed:t}){this.enumerateFn=e,this.seed=t,this.iterationCounter=0,this._opts={}}addOpt({key:e,range:t,initial:n}){this._opts[e]={range:t,value:n}}getOpt(e){return this._opts[e].value}setOpt(e,t){e in this._opts&&(this._opts[e].value=t)}opts(){return this._opts}enumerate(e,t,n){return this.enumerateFn(e,t,n).map(e=>"payload"in e?e:"move"in e?Ue(e.move,e.args,n):"event"in e?Fe(e.event,e.args,n):void 0)}random(e){let t;if(void 0!==this.seed){const e=Xt(this.prngstate?"":this.seed,this.prngstate);t=e(),this.prngstate=e.state()}else t=Math.random();if(e){if(Array.isArray(e)){return e[Math.floor(t*e.length)]}return Math.floor(t*e)}return t}}class Hi extends Ki{constructor({enumerate:e,seed:t,objectives:n,game:r,iterations:a,playoutDepth:o,iterationCallback:i}){super({enumerate:e,seed:t}),void 0===n&&(n=()=>({})),this.objectives=n,this.iterationCallback=i||(()=>{}),this.reducer=Yn({game:r}),this.iterations=a,this.playoutDepth=o,this.addOpt({key:"async",initial:!1}),this.addOpt({key:"iterations",initial:"number"==typeof a?a:1e3,range:{min:1,max:2e3}}),this.addOpt({key:"playoutDepth",initial:"number"==typeof o?o:50,range:{min:1,max:100}})}createNode({state:e,parentAction:t,parent:n,playerID:r}){const{G:a,ctx:o}=e;let i=[],s=[];if(void 0!==r)i=this.enumerate(a,o,r),s=this.objectives(a,o,r);else if(o.activePlayers)for(const e in o.activePlayers)i.push(...this.enumerate(a,o,e)),s.push(this.objectives(a,o,e));else i=this.enumerate(a,o,o.currentPlayer),s=this.objectives(a,o,o.currentPlayer);return{state:e,parent:n,parentAction:t,actions:i,objectives:s,children:[],visits:0,value:0}}select(e){if(e.actions.length>0)return e;if(0===e.children.length)return e;let t=null,n=0;for(const r of e.children){const a=r.visits+Number.EPSILON,o=r.value/a+Math.sqrt(2*Math.log(e.visits)/a);(null==t||o>n)&&(n=o,t=r)}return this.select(t)}expand(e){const t=e.actions;if(0===t.length||void 0!==e.state.ctx.gameover)return e;const n=this.random(t.length),r=t[n];e.actions.splice(n,1);const a=this.reducer(e.state,r),o=this.createNode({state:a,parentAction:r,parent:e});return e.children.push(o),o}playout({state:e}){let t=this.getOpt("playoutDepth");"function"==typeof this.playoutDepth&&(t=this.playoutDepth(e.G,e.ctx));for(let n=0;n<t&&void 0===e.ctx.gameover;n++){const{G:t,ctx:n}=e;let r=n.currentPlayer;n.activePlayers&&(r=Object.keys(n.activePlayers)[0]);const a=this.enumerate(t,n,r),o=this.objectives(t,n,r),i=Object.keys(o).reduce((e,r)=>{const a=o[r];return a.checker(t,n)?e+a.weight:e},0);if(i>0)return{score:i};if(!a||0===a.length)return;const s=this.random(a.length);e=this.reducer(e,a[s])}return e.ctx.gameover}backpropagate(e,t={}){e.visits++,void 0!==t.score&&(e.value+=t.score),!0===t.draw&&(e.value+=.5),e.parentAction&&t.winner===e.parentAction.payload.playerID&&e.value++,e.parent&&this.backpropagate(e.parent,t)}play(e,t){const n=this.createNode({state:e,playerID:t});let r=this.getOpt("iterations");"function"==typeof this.iterations&&(r=this.iterations(e.G,e.ctx));const a=()=>{let e=null;for(const t of n.children)(null==e||t.visits>e.visits)&&(e=t);return{action:e&&e.parentAction,metadata:n}};return new Promise(e=>{const t=()=>{for(let e=0;e<25&&this.iterationCounter<r;e++){const e=this.select(n),t=this.expand(e),r=this.playout(t);this.backpropagate(t,r),this.iterationCounter++}this.iterationCallback({iterationCounter:this.iterationCounter,numIterations:r,metadata:n})};if(this.iterationCounter=0,this.getOpt("async")){const n=()=>{this.iterationCounter<r?(t(),setImmediate(n)):e(a())};n()}else{for(;this.iterationCounter<r;)t();e(a())}})}}class Wi extends Ki{play({G:e,ctx:t},n){const r=this.enumerate(e,t,n);return Promise.resolve({action:this.random(r)})}}async function Zi(e,t){const n=e.store.getState();let r=n.ctx.currentPlayer;n.ctx.activePlayers&&(r=Object.keys(n.ctx.activePlayers)[0]);const{action:a,metadata:o}=await t.play(n,r);if(a){const t={...a,payload:{...a.payload,metadata:o}};return e.store.dispatch(t),t}}function Yi(e){P(e,"svelte-fn09gm","ul.svelte-fn09gm{padding-left:0}li.svelte-fn09gm{list-style:none;margin:0;margin-bottom:5px}h3.svelte-fn09gm{text-transform:uppercase}label.svelte-fn09gm{color:#666}input[type='checkbox'].svelte-fn09gm{vertical-align:middle}")}function Xi(e,t,n){const r=e.slice();return r[7]=t[n],r}function Qi(e){let t,r,a;return{c(){t=A("p"),t.textContent="No bots available.",r=M(),a=A("p"),a.innerHTML='Follow the instructions\n        <a href="https://boardgame.io/documentation/#/tutorial?id=bots" target="_blank">here</a>\n        to set up bots.'},m(e,n){O(e,t,n),O(e,r,n),O(e,a,n)},p:n,i:n,o:n,d(e){e&&I(t),e&&I(r),e&&I(a)}}}function es(e){let t;return{c(){t=A("p"),t.textContent="The bot debugger is only available in singleplayer mode."},m(e,n){O(e,t,n)},p:n,i:n,o:n,d(e){e&&I(t)}}}function ts(e){let t,n,r,a,o,i,l,c,u,d,f,p,g,v,h,m,y,b,$,P,w,k,E,S=Object.keys(e[7].opts()).length;i=new Va({props:{value:"1",onPress:e[13],label:"reset"}}),u=new Va({props:{value:"2",onPress:e[11],label:"play"}}),p=new Va({props:{value:"3",onPress:e[12],label:"simulate"}});let D=Object.keys(e[8]),T=[];for(let t=0;t<D.length;t+=1)T[t]=ns(Xi(e,D,t));let G=S&&rs(e),z=(e[5]||e[3])&&as(e);return{c(){t=A("section"),n=A("h3"),n.textContent="Controls",r=M(),a=A("ul"),o=A("li"),Ie(i.$$.fragment),l=M(),c=A("li"),Ie(u.$$.fragment),d=M(),f=A("li"),Ie(p.$$.fragment),g=M(),v=A("section"),h=A("h3"),h.textContent="Bot",m=M(),y=A("select");for(let e=0;e<T.length;e+=1)T[e].c();b=M(),G&&G.c(),$=M(),z&&z.c(),P=j(),C(n,"class","svelte-fn09gm"),C(o,"class","svelte-fn09gm"),C(c,"class","svelte-fn09gm"),C(f,"class","svelte-fn09gm"),C(a,"class","svelte-fn09gm"),C(h,"class","svelte-fn09gm"),void 0===e[4]&&se(()=>e[17].call(y))},m(s,I){O(s,t,I),x(t,n),x(t,r),x(t,a),x(a,o),_e(i,o,null),x(a,l),x(a,c),_e(u,c,null),x(a,d),x(a,f),_e(p,f,null),O(s,g,I),O(s,v,I),x(v,h),x(v,m),x(v,y);for(let e=0;e<T.length;e+=1)T[e].m(y,null);q(y,e[4]),O(s,b,I),G&&G.m(s,I),O(s,$,I),z&&z.m(s,I),O(s,P,I),w=!0,k||(E=[N(y,"change",e[17]),N(y,"change",e[10])],k=!0)},p(e,t){if(256&t){let n;for(D=Object.keys(e[8]),n=0;n<D.length;n+=1){const r=Xi(e,D,n);T[n]?T[n].p(r,t):(T[n]=ns(r),T[n].c(),T[n].m(y,null))}for(;n<T.length;n+=1)T[n].d(1);T.length=D.length}272&t&&q(y,e[4]),128&t&&(S=Object.keys(e[7].opts()).length),S?G?(G.p(e,t),128&t&&be(G,1)):(G=rs(e),G.c(),be(G,1),G.m($.parentNode,$)):G&&(me(),$e(G,1,1,()=>{G=null}),ye()),e[5]||e[3]?z?z.p(e,t):(z=as(e),z.c(),z.m(P.parentNode,P)):z&&(z.d(1),z=null)},i(e){w||(be(i.$$.fragment,e),be(u.$$.fragment,e),be(p.$$.fragment,e),be(G),w=!0)},o(e){$e(i.$$.fragment,e),$e(u.$$.fragment,e),$e(p.$$.fragment,e),$e(G),w=!1},d(e){e&&I(t),Ae(i),Ae(u),Ae(p),e&&I(g),e&&I(v),_(T,e),e&&I(b),G&&G.d(e),e&&I($),z&&z.d(e),e&&I(P),k=!1,s(E)}}}function ns(e){let t,r,a,o=e[7]+"";return{c(){t=A("option"),r=D(o),t.__value=a=e[7],t.value=t.__value},m(e,n){O(e,t,n),x(t,r)},p:n,d(e){e&&I(t)}}}function rs(e){let t,n,r,a,o,i,l,c,u,d,f;return c=new Ji({props:{bot:e[7]}}),{c(){t=A("section"),n=A("h3"),n.textContent="Options",r=M(),a=A("label"),a.textContent="debug",o=M(),i=A("input"),l=M(),Ie(c.$$.fragment),C(n,"class","svelte-fn09gm"),C(a,"for","ai-option-debug"),C(a,"class","svelte-fn09gm"),C(i,"id","ai-option-debug"),C(i,"type","checkbox"),C(i,"class","svelte-fn09gm")},m(s,p){O(s,t,p),x(t,n),x(t,r),x(t,a),x(t,o),x(t,i),i.checked=e[1],x(t,l),_e(c,t,null),u=!0,d||(f=[N(i,"change",e[18]),N(i,"change",e[9])],d=!0)},p(e,t){2&t&&(i.checked=e[1]);const n={};128&t&&(n.bot=e[7]),c.$set(n)},i(e){u||(be(c.$$.fragment,e),u=!0)},o(e){$e(c.$$.fragment,e),u=!1},d(e){e&&I(t),Ae(c),d=!1,s(f)}}}function as(e){let t,n,r,a,o=e[2]&&e[2]<1&&os(e),i=e[5]&&is(e);return{c(){t=A("section"),n=A("h3"),n.textContent="Result",r=M(),o&&o.c(),a=M(),i&&i.c(),C(n,"class","svelte-fn09gm")},m(e,s){O(e,t,s),x(t,n),x(t,r),o&&o.m(t,null),x(t,a),i&&i.m(t,null)},p(e,n){e[2]&&e[2]<1?o?o.p(e,n):(o=os(e),o.c(),o.m(t,a)):o&&(o.d(1),o=null),e[5]?i?i.p(e,n):(i=is(e),i.c(),i.m(t,null)):i&&(i.d(1),i=null)},d(e){e&&I(t),o&&o.d(),i&&i.d()}}}function os(e){let t;return{c(){t=A("progress"),t.value=e[2]},m(e,n){O(e,t,n)},p(e,n){4&n&&(t.value=e[2])},d(e){e&&I(t)}}}function is(e){let t,n,r,a,o,i,s,l,c=JSON.stringify(e[6])+"";return{c(){t=A("ul"),n=A("li"),r=D("Action: "),a=D(e[5]),o=M(),i=A("li"),s=D("Args: "),l=D(c),C(n,"class","svelte-fn09gm"),C(i,"class","svelte-fn09gm"),C(t,"class","svelte-fn09gm")},m(e,c){O(e,t,c),x(t,n),x(n,r),x(n,a),x(t,o),x(t,i),x(i,s),x(i,l)},p(e,t){32&t&&G(a,e[5]),64&t&&c!==(c=JSON.stringify(e[6])+"")&&G(l,c)},d(e){e&&I(t)}}}function ss(e){let t,n,r,a,o,i;const s=[ts,es,Qi],l=[];function c(e,t){return e[0].game.ai&&!e[0].multiplayer?0:e[0].multiplayer?1:2}return n=c(e),r=l[n]=s[n](e),{c(){t=A("section"),r.c()},m(r,s){O(r,t,s),l[n].m(t,null),a=!0,o||(i=N(window,"keydown",e[14]),o=!0)},p(e,[a]){let o=n;n=c(e),n===o?l[n].p(e,a):(me(),$e(l[o],1,1,()=>{l[o]=null}),ye(),r=l[n],r?r.p(e,a):(r=l[n]=s[n](e),r.c()),be(r,1),r.m(t,null))},i(e){a||(be(r),a=!0)},o(e){$e(r),a=!1},d(e){e&&I(t),l[n].d(),o=!1,i()}}}function ls(e,t,n){let{client:r}=t,{clientManager:a}=t,{ToggleVisibility:o}=t;const{secondaryPane:i}=Q("secondaryPane"),s={MCTS:Hi,Random:Wi};let l=!1,c=null,u=0,d=null;const f=({iterationCounter:e,numIterations:t,metadata:r})=>{n(3,u=e),n(2,c=e/t),d=r,l&&d&&i.set({component:Oi,metadata:d})};let p,g,v,h;function m(){r.overrideGameState(null),i.set(null),n(1,l=!1)}return r.game.ai&&(p=new Hi({game:r.game,enumerate:r.game.ai.enumerate,iterationCallback:f}),p.setOpt("async",!0)),Z(m),e.$$set=e=>{"client"in e&&n(0,r=e.client),"clientManager"in e&&n(15,a=e.clientManager),"ToggleVisibility"in e&&n(16,o=e.ToggleVisibility)},[r,l,c,u,g,v,h,p,s,function(){l&&d?i.set({component:Oi,metadata:d}):i.set(null)},function(){n(7,p=new(0,s[g])({game:r.game,enumerate:r.game.ai.enumerate,iterationCallback:f})),p.setOpt("async",!0),n(5,v=null),d=null,i.set(null),n(3,u=0)},async function(){n(5,v=null),d=null,n(3,u=0);const e=await Zi(r,p);e&&(n(5,v=e.payload.type),n(6,h=e.payload.args))},function(e=1e4,t=100){return n(5,v=null),d=null,n(3,u=0),(async()=>{for(let n=0;n<e;n++){if(!await Zi(r,p))break;await new Promise(e=>setTimeout(e,t))}})()},function(){r.reset(),n(5,v=null),d=null,n(3,u=0),m()},function(e){27==e.keyCode&&m()},a,o,function(){g=V(this),n(4,g),n(8,s)},function(){l=this.checked,n(1,l)}]}class cs extends Me{constructor(e){super(),De(this,e,ls,ss,c,{client:0,clientManager:15,ToggleVisibility:16},Yi)}}function us(e){P(e,"svelte-8ymctk",".debug-panel.svelte-8ymctk.svelte-8ymctk{position:fixed;color:#555;font-family:monospace;right:0;top:0;height:100%;font-size:14px;opacity:0.9;z-index:99999}.panel.svelte-8ymctk.svelte-8ymctk{display:flex;position:relative;flex-direction:row;height:100%}.visibility-toggle.svelte-8ymctk.svelte-8ymctk{position:absolute;box-sizing:border-box;top:7px;border:1px solid #ccc;border-radius:5px;width:48px;height:48px;padding:8px;background:white;color:#555;box-shadow:0 0 5px rgba(0, 0, 0, 0.2)}.visibility-toggle.svelte-8ymctk.svelte-8ymctk:hover,.visibility-toggle.svelte-8ymctk.svelte-8ymctk:focus{background:#eee}.opener.svelte-8ymctk.svelte-8ymctk{right:10px}.closer.svelte-8ymctk.svelte-8ymctk{left:-326px}@keyframes svelte-8ymctk-rotateFromZero{from{transform:rotateZ(0deg)}to{transform:rotateZ(180deg)}}.icon.svelte-8ymctk.svelte-8ymctk{display:flex;height:100%;animation:svelte-8ymctk-rotateFromZero 0.4s cubic-bezier(0.68, -0.55, 0.27, 1.55) 0s 1\n      normal forwards}.closer.svelte-8ymctk .icon.svelte-8ymctk{animation-direction:reverse}.pane.svelte-8ymctk.svelte-8ymctk{flex-grow:2;overflow-x:hidden;overflow-y:scroll;background:#fefefe;padding:20px;border-left:1px solid #ccc;box-shadow:-1px 0 5px rgba(0, 0, 0, 0.2);box-sizing:border-box;width:280px}.secondary-pane.svelte-8ymctk.svelte-8ymctk{background:#fefefe;overflow-y:scroll}.debug-panel.svelte-8ymctk button,.debug-panel.svelte-8ymctk select{cursor:pointer;font-size:14px;font-family:monospace}.debug-panel.svelte-8ymctk select{background:#eee;border:1px solid #bbb;color:#555;padding:3px;border-radius:3px}.debug-panel.svelte-8ymctk section{margin-bottom:20px}.debug-panel.svelte-8ymctk .screen-reader-only{clip:rect(0 0 0 0);clip-path:inset(50%);height:1px;overflow:hidden;position:absolute;white-space:nowrap;width:1px}")}function ds(e){let t,r,a,o,i,s,l,c,u,d=e[10]&&function(e){let t,r,a,o,i,s,l,c;return a=new gr({}),{c(){t=A("button"),r=A("span"),Ie(a.$$.fragment),C(r,"class","icon svelte-8ymctk"),C(r,"aria-hidden","true"),C(t,"class","visibility-toggle closer svelte-8ymctk"),C(t,"title","Hide Debug Panel")},m(n,o){O(n,t,o),x(t,r),_e(a,r,null),s=!0,l||(c=N(t,"click",e[9]),l=!0)},p:n,i(n){s||(be(a.$$.fragment,n),se(()=>{i&&i.end(1),o=Pe(t,e[14],{key:"toggle"}),o.start()}),s=!0)},o(n){$e(a.$$.fragment,n),o&&o.invalidate(),i=we(t,e[13],{key:"toggle"}),s=!1},d(e){e&&I(t),Ae(a),e&&i&&i.end(),l=!1,c()}}}(e);a=new $r({props:{panes:e[6],pane:e[2]}}),a.$on("change",e[8]);var f=e[6][e[2]].component;function p(e){return{props:{client:e[4],clientManager:e[0],ToggleVisibility:e[9]}}}f&&(s=new f(p(e)));let g=e[5]&&ps(e);return{c(){t=A("div"),d&&d.c(),r=M(),Ie(a.$$.fragment),o=M(),i=A("div"),s&&Ie(s.$$.fragment),l=M(),g&&g.c(),C(i,"class","pane svelte-8ymctk"),C(i,"role","region"),C(i,"aria-label",e[2]),C(i,"tabindex","-1"),C(t,"class","panel svelte-8ymctk")},m(n,c){O(n,t,c),d&&d.m(t,null),x(t,r),_e(a,t,null),x(t,o),x(t,i),s&&_e(s,i,null),e[16](i),x(t,l),g&&g.m(t,null),u=!0},p(n,r){(e=n)[10]&&d.p(e,r);const o={};4&r&&(o.pane=e[2]),a.$set(o);const l={};if(16&r&&(l.client=e[4]),1&r&&(l.clientManager=e[0]),f!==(f=e[6][e[2]].component)){if(s){me();const e=s;$e(e.$$.fragment,1,0,()=>{Ae(e,1)}),ye()}f?(s=new f(p(e)),Ie(s.$$.fragment),be(s.$$.fragment,1),_e(s,i,null)):s=null}else f&&s.$set(l);(!u||4&r)&&C(i,"aria-label",e[2]),e[5]?g?(g.p(e,r),32&r&&be(g,1)):(g=ps(e),g.c(),be(g,1),g.m(t,null)):g&&(me(),$e(g,1,1,()=>{g=null}),ye())},i(n){u||(be(d),be(a.$$.fragment,n),s&&be(s.$$.fragment,n),be(g),se(()=>{c||(c=ke(t,ar,{x:400,...e[12]},!0)),c.run(1)}),u=!0)},o(n){$e(d),$e(a.$$.fragment,n),s&&$e(s.$$.fragment,n),$e(g),c||(c=ke(t,ar,{x:400,...e[12]},!1)),c.run(0),u=!1},d(n){n&&I(t),d&&d.d(),Ae(a),s&&Ae(s),e[16](null),g&&g.d(),n&&c&&c.end()}}}function fs(e){let t,r,a=e[10]&&function(e){let t,r,a,o,i,s,l,c;return a=new gr({}),{c(){t=A("button"),r=A("span"),Ie(a.$$.fragment),C(r,"class","icon svelte-8ymctk"),C(r,"aria-hidden","true"),C(t,"class","visibility-toggle opener svelte-8ymctk"),C(t,"title","Show Debug Panel")},m(n,o){O(n,t,o),x(t,r),_e(a,r,null),s=!0,l||(c=N(t,"click",e[9]),l=!0)},p:n,i(n){s||(be(a.$$.fragment,n),se(()=>{i&&i.end(1),o=Pe(t,e[14],{key:"toggle"}),o.start()}),s=!0)},o(n){$e(a.$$.fragment,n),o&&o.invalidate(),i=we(t,e[13],{key:"toggle"}),s=!1},d(e){e&&I(t),Ae(a),e&&i&&i.end(),l=!1,c()}}}(e);return{c(){a&&a.c(),t=j()},m(e,n){a&&a.m(e,n),O(e,t,n),r=!0},p(e,t){e[10]&&a.p(e,t)},i(e){r||(be(a),r=!0)},o(e){$e(a),r=!1},d(e){a&&a.d(e),e&&I(t)}}}function ps(e){let t,n,r;var a=e[5].component;function o(e){return{props:{metadata:e[5].metadata}}}return a&&(n=new a(o(e))),{c(){t=A("div"),n&&Ie(n.$$.fragment),C(t,"class","secondary-pane svelte-8ymctk")},m(e,a){O(e,t,a),n&&_e(n,t,null),r=!0},p(e,r){const i={};if(32&r&&(i.metadata=e[5].metadata),a!==(a=e[5].component)){if(n){me();const e=n;$e(e.$$.fragment,1,0,()=>{Ae(e,1)}),ye()}a?(n=new a(o(e)),Ie(n.$$.fragment),be(n.$$.fragment,1),_e(n,t,null)):n=null}else a&&n.$set(i)},i(e){r||(n&&be(n.$$.fragment,e),r=!0)},o(e){n&&$e(n.$$.fragment,e),r=!1},d(e){e&&I(t),n&&Ae(n)}}}function gs(e){let t,n,r,a,o,i;const s=[fs,ds],l=[];function c(e,t){return e[3]?1:0}return n=c(e),r=l[n]=s[n](e),{c(){t=A("section"),r.c(),C(t,"aria-label","boardgame.io Debug Panel"),C(t,"class","debug-panel svelte-8ymctk")},m(r,s){O(r,t,s),l[n].m(t,null),a=!0,o||(i=N(window,"keypress",e[11]),o=!0)},p(e,[a]){let o=n;n=c(e),n===o?l[n].p(e,a):(me(),$e(l[o],1,1,()=>{l[o]=null}),ye(),r=l[n],r?r.p(e,a):(r=l[n]=s[n](e),r.c()),be(r,1),r.m(t,null))},i(e){a||(be(r),a=!0)},o(e){$e(r),a=!1},d(e){e&&I(t),l[n].d(),o=!1,i()}}}function vs(e,t,r){let a,o,i,s=n,l=()=>(s(),s=u(c,e=>r(15,o=e)),c);e.$$.on_destroy.push(()=>s());let{clientManager:c}=t;l();const f={main:{label:"Main",shortcut:"m",component:Do},log:{label:"Log",shortcut:"l",component:zi},info:{label:"Info",shortcut:"i",component:Vo},ai:{label:"AI",shortcut:"a",component:cs}},p=nr(!1),g=nr(null);let v;d(e,g,e=>r(5,i=e)),X("hotkeys",{disableHotkeys:p}),X("secondaryPane",{secondaryPane:g});let h="main";function m(){r(3,b=!b)}const y=o.client.debugOpt;let b=!y||!y.collapseOnLoad;const $=!y||!y.hideToggleButton;const x={duration:150,easing:rr},[P,w]=or(x);return e.$$set=e=>{"clientManager"in e&&l(r(0,c=e.clientManager))},e.$$.update=()=>{32768&e.$$.dirty&&r(4,a=o.client)},[c,v,h,b,a,i,f,g,function(e){r(2,h=e.detail),v.focus()},m,$,function(e){"."!=e.key?b&&Object.entries(f).forEach(([t,{shortcut:n}])=>{e.key==n&&r(2,h=t)}):m()},x,P,w,o,function(e){ne[e?"unshift":"push"](()=>{v=e,r(1,v)})}]}class hs extends Me{constructor(e){super(),De(this,e,vs,gs,c,{clientManager:0},us)}}const ms=new class{constructor(){this.debugPanel=null,this.currentClient=null,this.clients=new Map,this.subscribers=new Map}register(e){this.clients.set(e,e),this.mountDebug(e),this.notifySubscribers()}unregister(e){if(this.clients.delete(e),this.currentClient===e){this.unmountDebug();for(const[e]of this.clients){if(this.debugPanel)break;this.mountDebug(e)}}this.notifySubscribers()}subscribe(e){const t=Symbol();return this.subscribers.set(t,e),e(this.getState()),()=>{this.subscribers.delete(t)}}switchPlayerID(e){if(this.currentClient.multiplayer)for(const[t]of this.clients)if(t.playerID===e&&!1!==t.debugOpt&&t.multiplayer===this.currentClient.multiplayer)return void this.switchToClient(t);this.currentClient.updatePlayerID(e),this.notifySubscribers()}switchToClient(e){e!==this.currentClient&&(this.unmountDebug(),this.mountDebug(e),this.notifySubscribers())}notifySubscribers(){const e=this.getState();this.subscribers.forEach(t=>{t(e)})}getState(){return{client:this.currentClient,debuggableClients:this.getDebuggableClients()}}getDebuggableClients(){return[...this.clients.values()].filter(e=>!1!==e.debugOpt)}mountDebug(e){if(!1===e.debugOpt||null!==this.debugPanel||"undefined"==typeof document)return;let t,n=document.body;t=hs,e.debugOpt&&!0!==e.debugOpt&&(t=e.debugOpt.impl||t,n=e.debugOpt.target||n),t&&(this.currentClient=e,this.debugPanel=new t({target:n,props:{clientManager:this}}))}unmountDebug(){this.debugPanel.$destroy(),this.debugPanel=null,this.currentClient=null}};function ys(e,t,n){if(!n&&null==e){e=t.getState().ctx.currentPlayer}return e}function bs(e,t,n,r,a,o){const i={};for(const s of t)i[s]=(...t)=>{const i=Qe[e](s,t,ys(r,n,o),a);n.dispatch(i)};return i}const $s=bs.bind(null,"makeMove"),xs=bs.bind(null,"gameEvent"),Ps=bs.bind(null,"plugin");class ws{constructor({game:e,debug:n,numPlayers:r,multiplayer:a,matchID:o,playerID:i,credentials:s,enhancer:l}){this.game=Mn(e),this.playerID=i,this.matchID=o||"default",this.credentials=s,this.multiplayer=a,this.debugOpt=n,this.manager=ms,this.gameStateOverride=null,this.subscribers={},this._running=!1,this.reducer=Yn({game:this.game,isClient:void 0!==a}),this.initialState=null,a||(this.initialState=Xn({game:this.game,numPlayers:r})),this.reset=()=>{this.store.dispatch(We(this.initialState))},this.undo=()=>{const e=Ze(ys(this.playerID,this.store,this.multiplayer),this.credentials);this.store.dispatch(e)},this.redo=()=>{const e=Ye(ys(this.playerID,this.store,this.multiplayer),this.credentials);this.store.dispatch(e)},this.log=[];const c=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){return function(){var n=e.apply(void 0,arguments),r=function(){throw new Error(Ce(15))},a={getState:n.getState,dispatch:function(){return r.apply(void 0,arguments)}},o=t.map((function(e){return e(a)}));return r=Be.apply(void 0,o)(n.dispatch),Te(Te({},n),{},{dispatch:r})}}}(Zn,()=>e=>t=>{const n=e(t);return this.notifySubscribers(),n},e=>t=>n=>{const r=e.getState(),a=t(n);return"clientOnly"in n||"STRIP_TRANSIENTS"===n.type||this.transport.sendAction(r,n),a},e=>t=>n=>{const r=t(n),a=e.getState();switch(n.type){case"MAKE_MOVE":case"GAME_EVENT":case"UNDO":case"REDO":{const e=a.deltalog;this.log=[...this.log,...e];break}case"RESET":this.log=[];break;case"PATCH":case"UPDATE":{let e=-1;this.log.length>0&&(e=this.log[this.log.length-1]._stateID);let t=n.deltalog||[];t=t.filter(t=>t._stateID>e),this.log=[...this.log,...t];break}case"SYNC":this.initialState=n.initialState,this.log=n.log||[]}return r});l=void 0!==l?Be(c,l):c,this.store=Re(this.reducer,this.initialState,l),a||(a=er),this.transport=a({transportDataCallback:e=>this.receiveTransportData(e),gameKey:e,game:this.game,matchID:o,playerID:i,credentials:s,gameName:this.game.name,numPlayers:r}),this.createDispatchers(),this.chatMessages=[],this.sendChatMessage=e=>{this.transport.sendChatMessage(this.matchID,{id:t(7),sender:this.playerID,payload:e})}}receiveMatchData(e){this.matchData=e,this.notifySubscribers()}receiveChatMessage(e){this.chatMessages=[...this.chatMessages,e],this.notifySubscribers()}receiveTransportData(e){const[t]=e.args;if(t===this.matchID)switch(e.type){case"sync":{const[,t]=e.args,n=Je(t);this.receiveMatchData(t.filteredMetadata),this.store.dispatch(n);break}case"update":{const[,t,n]=e.args,r=this.store.getState();if(t._stateID>=r._stateID){const e=He(t,n);this.store.dispatch(e)}break}case"patch":{const[,t,n,r,a]=e.args,o=this.store.getState()._stateID;if(t!==o)break;const i=Ke(t,n,r,a);this.store.dispatch(i),this.store.getState()._stateID===o&&this.transport.requestSync();break}case"matchData":{const[,t]=e.args;this.receiveMatchData(t);break}case"chat":{const[,t]=e.args;this.receiveChatMessage(t);break}}}notifySubscribers(){Object.values(this.subscribers).forEach(e=>e(this.getState()))}overrideGameState(e){this.gameStateOverride=e,this.notifySubscribers()}start(){this.transport.connect(),this._running=!0,this.manager.register(this)}stop(){this.transport.disconnect(),this._running=!1,this.manager.unregister(this)}subscribe(e){const t=Object.keys(this.subscribers).length;return this.subscribers[t]=e,this.transport.subscribeToConnectionStatus(()=>this.notifySubscribers()),!this._running&&this.multiplayer||e(this.getState()),()=>{delete this.subscribers[t]}}getInitialState(){return this.initialState}getState(){let e=this.store.getState();if(null!==this.gameStateOverride&&(e=this.gameStateOverride),null===e)return e;let t=!0;const n=this.game.flow.isPlayerActive(e.G,e.ctx,this.playerID);return this.multiplayer&&!n&&(t=!1),this.multiplayer||null===this.playerID||void 0===this.playerID||n||(t=!1),void 0!==e.ctx.gameover&&(t=!1),this.multiplayer||(e={...e,G:this.game.playerView({G:e.G,ctx:e.ctx,playerID:this.playerID}),plugins:kn(e,this)}),{...e,log:this.log,isActive:t,isConnected:this.transport.isConnected}}createDispatchers(){this.moves=$s(this.game.moveNames,this.store,this.playerID,this.credentials,this.multiplayer),this.events=xs(this.game.flow.enabledEventNames,this.store,this.playerID,this.credentials,this.multiplayer),this.plugins=Ps(this.game.pluginNames,this.store,this.playerID,this.credentials,this.multiplayer)}updatePlayerID(e){this.playerID=e,this.createDispatchers(),this.transport.updatePlayerID(e),this.notifySubscribers()}updateMatchID(e){this.matchID=e,this.createDispatchers(),this.transport.updateMatchID(e),this.notifySubscribers()}updateCredentials(e){this.credentials=e,this.createDispatchers(),this.transport.updateCredentials(e),this.notifySubscribers()}}const ks=(e,t)=>{if(!e||"string"!=typeof e)throw new Error(`Expected ${t} string, got "${e}".`)},Es=e=>ks(e,"game name"),Os=e=>ks(e,"match ID"),Is=(e,t)=>{if(!e)throw new Error(`Expected body, got “${e}”.`);for(const n in t){const r=t[n],a=Array.isArray(r)?r:[r],o=e[n];if(!a.includes(typeof o)){const e=a.join("|");throw new TypeError(`Expected body.${n} to be of type ${e}, got “${o}”.`)}}};class _s extends Error{constructor(e,t){super(e),this.details=t}}e.Client=function(e){return new ws(e)},e.LobbyClient=class{constructor({server:e=""}={}){this.server=e.replace(/\/$/,"")}async request(e,t){const n=await fetch(this.server+e,t);if(!n.ok){let e;try{e=await n.clone().json()}catch{try{e=await n.text()}catch(t){e=t.message}}throw new _s("HTTP status "+n.status,e)}return n.json()}async post(e,t){let n={method:"post",body:JSON.stringify(t.body),headers:{"Content-Type":"application/json"}};return t.init&&(n={...n,...t.init,headers:{...n.headers,...t.init.headers}}),this.request(e,n)}async listGames(e){return this.request("/games",e)}async listMatches(e,t,n){Es(e);let r="";if(t){const e=[],{isGameover:n,updatedBefore:a,updatedAfter:o}=t;void 0!==n&&e.push("isGameover="+n),a&&e.push("updatedBefore="+a),o&&e.push("updatedAfter="+o),e.length>0&&(r="?"+e.join("&"))}return this.request(`/games/${e}${r}`,n)}async getMatch(e,t,n){return Es(e),Os(t),this.request(`/games/${e}/${t}`,n)}async createMatch(e,t,n){return Es(e),Is(t,{numPlayers:"number"}),this.post(`/games/${e}/create`,{body:t,init:n})}async joinMatch(e,t,n,r){return Es(e),Os(t),Is(n,{playerID:["string","undefined"],playerName:"string"}),this.post(`/games/${e}/${t}/join`,{body:n,init:r})}async leaveMatch(e,t,n,r){Es(e),Os(t),Is(n,{playerID:"string",credentials:"string"}),await this.post(`/games/${e}/${t}/leave`,{body:n,init:r})}async updatePlayer(e,t,n,r){Es(e),Os(t),Is(n,{playerID:"string",credentials:"string"}),await this.post(`/games/${e}/${t}/update`,{body:n,init:r})}async playAgain(e,t,n,r){return Es(e),Os(t),Is(n,{playerID:"string",credentials:"string"}),this.post(`/games/${e}/${t}/playAgain`,{body:n,init:r})}},e.LobbyClientError=_s,Object.defineProperty(e,"__esModule",{value:!0})}));
