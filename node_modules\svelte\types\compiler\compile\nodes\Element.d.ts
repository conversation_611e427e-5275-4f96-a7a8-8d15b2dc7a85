import Node from './shared/Node';
import Attribute from './Attribute';
import Binding from './Binding';
import EventHandler from './EventHandler';
import Transition from './Transition';
import Animation from './Animation';
import Action from './Action';
import Class from './Class';
import StyleDirective from './StyleDirective';
import Let from './Let';
import TemplateScope from './shared/TemplateScope';
import { INode } from './interfaces';
import Component from '../Component';
import Expression from './shared/Expression';
export default class Element extends Node {
    type: 'Element';
    name: string;
    scope: TemplateScope;
    attributes: Attribute[];
    actions: Action[];
    bindings: Binding[];
    classes: Class[];
    styles: StyleDirective[];
    handlers: EventHandler[];
    lets: Let[];
    intro?: Transition;
    outro?: Transition;
    animation?: Animation;
    children: INode[];
    namespace: string;
    needs_manual_style_scoping: boolean;
    tag_expr: Expression;
    contains_a11y_label: boolean;
    get is_dynamic_element(): boolean;
    constructor(component: Component, parent: Node, scope: TemplateScope, info: any);
    validate(): void;
    validate_attributes(): void;
    validate_attributes_a11y(): void;
    validate_special_cases(): void;
    validate_bindings_foreign(): void;
    validate_bindings(): void;
    validate_content(): void;
    validate_event_handlers(): void;
    is_media_node(): boolean;
    add_css_class(): void;
    get slot_template_name(): string;
    optimise(): void;
}
