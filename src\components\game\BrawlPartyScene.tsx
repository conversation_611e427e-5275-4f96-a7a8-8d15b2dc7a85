import React, { useRef } from 'react';
import { useFrame } from '@react-three/fiber';
import { OrbitControls, Text } from '@react-three/drei';
import { Group } from 'three';
import { GameState, Player, TileType } from '../../types/gameTypes';
import { Ctx } from 'boardgame.io';
import { GameTile } from './GameTile';
import { GamePlayer } from './GamePlayer';

interface BrawlPartySceneProps {
  G: GameState;
  ctx: Ctx;
  moves: any;
  playerID: string | null;
  isActive: boolean;
}

export const BrawlPartyScene: React.FC<BrawlPartySceneProps> = ({
  G,
  ctx,
  moves,
  playerID,
  isActive,
}) => {
  const groupRef = useRef<Group>(null);

  // Rotate the entire board slowly
  useFrame((state, delta) => {
    if (groupRef.current) {
      groupRef.current.rotation.y += delta * 0.1;
    }
  });

  return (
    <>
      {/* Enhanced Lighting */}
      <ambientLight intensity={0.6} />
      <directionalLight
        position={[20, 30, 10]}
        intensity={1.2}
        castShadow
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
        shadow-camera-far={100}
        shadow-camera-left={-50}
        shadow-camera-right={50}
        shadow-camera-top={50}
        shadow-camera-bottom={-50}
      />
      <pointLight position={[0, 25, 0]} intensity={0.8} color="#f39c12" />
      <pointLight position={[15, 15, 15]} intensity={0.4} color="#3498db" />
      <pointLight position={[-15, 15, -15]} intensity={0.4} color="#e74c3c" />

      {/* Camera Controls */}
      <OrbitControls
        enablePan={true}
        enableZoom={true}
        enableRotate={true}
        minDistance={15}
        maxDistance={50}
        minPolarAngle={0}
        maxPolarAngle={Math.PI / 2}
      />



      {/* Game Board */}
      <group ref={groupRef}>
        {/* Main Platform - Enlarged to accommodate all tiles */}
        <mesh position={[0, -1.5, 0]} receiveShadow>
          <cylinderGeometry args={[35, 35, 3, 64]} />
          <meshStandardMaterial
            color="#2c3e50"
            roughness={0.8}
            metalness={0.2}
          />
        </mesh>

        {/* Secondary Platform - Middle level */}
        <mesh position={[0, -0.8, 0]} receiveShadow>
          <cylinderGeometry args={[25, 25, 1.5, 48]} />
          <meshStandardMaterial
            color="#34495e"
            roughness={0.7}
            metalness={0.3}
          />
        </mesh>

        {/* Inner Platform - Top level */}
        <mesh position={[0, -0.2, 0]} receiveShadow>
          <cylinderGeometry args={[15, 15, 0.8, 32]} />
          <meshStandardMaterial
            color="#3d566e"
            roughness={0.6}
            metalness={0.4}
          />
        </mesh>

        {/* Decorative connecting pillars */}
        {Array.from({ length: 8 }, (_, i) => {
          const angle = (i / 8) * Math.PI * 2;
          const x = Math.cos(angle) * 18;
          const z = Math.sin(angle) * 18;
          return (
            <mesh key={i} position={[x, -1, z]} receiveShadow>
              <cylinderGeometry args={[0.8, 0.8, 2, 8]} />
              <meshStandardMaterial
                color="#4a6741"
                roughness={0.9}
                metalness={0.1}
              />
            </mesh>
          );
        })}

        {/* Outer decorative ring */}
        <mesh position={[0, 0.2, 0]} receiveShadow>
          <torusGeometry args={[30, 1.5, 16, 32]} />
          <meshStandardMaterial
            color="#5d4e75"
            roughness={0.6}
            metalness={0.4}
          />
        </mesh>

        {/* Inner decorative ring */}
        <mesh position={[0, 0.1, 0]} receiveShadow>
          <torusGeometry args={[12, 0.8, 16, 32]} />
          <meshStandardMaterial
            color="#34495e"
            roughness={0.6}
            metalness={0.4}
          />
        </mesh>

        {/* Connecting bridges between levels */}
        {Array.from({ length: 4 }, (_, i) => {
          const angle = (i / 4) * Math.PI * 2;
          const x = Math.cos(angle) * 22;
          const z = Math.sin(angle) * 22;
          return (
            <group key={`bridge-${i}`}>
              {/* Bridge platform */}
              <mesh position={[x, -0.5, z]} receiveShadow>
                <boxGeometry args={[4, 0.3, 1.5]} />
                <meshStandardMaterial
                  color="#5a6c57"
                  roughness={0.8}
                  metalness={0.2}
                />
              </mesh>
              {/* Bridge supports */}
              <mesh position={[x - 1.5, -1, z]} receiveShadow>
                <cylinderGeometry args={[0.2, 0.2, 1, 8]} />
                <meshStandardMaterial
                  color="#4a5a47"
                  roughness={0.9}
                  metalness={0.1}
                />
              </mesh>
              <mesh position={[x + 1.5, -1, z]} receiveShadow>
                <cylinderGeometry args={[0.2, 0.2, 1, 8]} />
                <meshStandardMaterial
                  color="#4a5a47"
                  roughness={0.9}
                  metalness={0.1}
                />
              </mesh>
            </group>
          );
        })}

        {/* Game Tiles */}
        {G.tiles.map((tile) => (
          <GameTile
            key={tile.id}
            tile={tile}
            players={Object.values(G.players).filter(
              (player: Player) => player.position === tile.id
            )}
            isActive={isActive}
            moves={moves}
            playerID={playerID}
          />
        ))}

        {/* Game Players */}
        {Object.values(G.players).map((player: Player) => {
          const tile = G.tiles.find(t => t.id === player.position);
          if (!tile) return null;

          return (
            <GamePlayer
              key={player.ID}
              player={player}
              position={tile.position}
              isCurrentPlayer={ctx.currentPlayer === player.ID}
            />
          );
        })}
      </group>



      {/* Game Over */}
      {ctx.gameover && (
        <Text
          position={[0, 12, 0]}
          fontSize={1.5}
          color="#e74c3c"
          anchorX="center"
          anchorY="middle"
        >
          🎉 {G.players[ctx.gameover.winner]?.name} Wins!
        </Text>
      )}
    </>
  );
};
