import React, { useRef } from 'react';
import { useFrame } from '@react-three/fiber';
import { OrbitControls, Text } from '@react-three/drei';
import { Group } from 'three';
import { GameState, Player, TileType } from '../../types/gameTypes';
import { Ctx } from 'boardgame.io';
import { GameTile } from './GameTile';
import { GamePlayer } from './GamePlayer';

interface BrawlPartySceneProps {
  G: GameState;
  ctx: Ctx;
  moves: any;
  playerID: string | null;
  isActive: boolean;
}

export const BrawlPartyScene: React.FC<BrawlPartySceneProps> = ({
  G,
  ctx,
  moves,
  playerID,
  isActive,
}) => {
  const groupRef = useRef<Group>(null);

  // Rotate the entire board slowly
  useFrame((state, delta) => {
    if (groupRef.current) {
      groupRef.current.rotation.y += delta * 0.1;
    }
  });

  return (
    <>
      {/* Enhanced Lighting */}
      <ambientLight intensity={0.6} />
      <directionalLight
        position={[20, 30, 10]}
        intensity={1.2}
        castShadow
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
        shadow-camera-far={100}
        shadow-camera-left={-50}
        shadow-camera-right={50}
        shadow-camera-top={50}
        shadow-camera-bottom={-50}
      />
      <pointLight position={[0, 25, 0]} intensity={0.8} color="#f39c12" />
      <pointLight position={[15, 15, 15]} intensity={0.4} color="#3498db" />
      <pointLight position={[-15, 15, -15]} intensity={0.4} color="#e74c3c" />

      {/* Camera Controls */}
      <OrbitControls
        enablePan={true}
        enableZoom={true}
        enableRotate={true}
        minDistance={15}
        maxDistance={50}
        minPolarAngle={0}
        maxPolarAngle={Math.PI / 2}
      />



      {/* Game Board */}
      <group ref={groupRef}>
        {/* Center Platform */}
        <mesh position={[0, -1, 0]} receiveShadow>
          <cylinderGeometry args={[25, 25, 2, 64]} />
          <meshStandardMaterial
            color="#2c3e50"
            roughness={0.8}
            metalness={0.2}
          />
        </mesh>

        {/* Inner decorative ring */}
        <mesh position={[0, -0.5, 0]} receiveShadow>
          <torusGeometry args={[12, 1, 16, 32]} />
          <meshStandardMaterial
            color="#34495e"
            roughness={0.6}
            metalness={0.4}
          />
        </mesh>

        {/* Game Tiles */}
        {G.tiles.map((tile) => (
          <GameTile
            key={tile.id}
            tile={tile}
            players={Object.values(G.players).filter(
              (player: Player) => player.position === tile.id
            )}
            isActive={isActive}
            moves={moves}
            playerID={playerID}
          />
        ))}

        {/* Game Players */}
        {Object.values(G.players).map((player: Player) => {
          const tile = G.tiles.find(t => t.id === player.position);
          if (!tile) return null;

          return (
            <GamePlayer
              key={player.ID}
              player={player}
              position={tile.position}
              isCurrentPlayer={ctx.currentPlayer === player.ID}
            />
          );
        })}
      </group>



      {/* Game Over */}
      {ctx.gameover && (
        <Text
          position={[0, 12, 0]}
          fontSize={1.5}
          color="#e74c3c"
          anchorX="center"
          anchorY="middle"
        >
          🎉 {G.players[ctx.gameover.winner]?.name} Wins!
        </Text>
      )}
    </>
  );
};
