import React, { useRef } from 'react';
import { useFrame } from '@react-three/fiber';
import { OrbitControls, Text } from '@react-three/drei';
import { Group } from 'three';
import { GameState, Player, TileType } from '../../types/gameTypes';
import { Ctx } from 'boardgame.io';
import { GameTile } from './GameTile';
import { GamePlayer } from './GamePlayer';

interface BrawlPartySceneProps {
  G: GameState;
  ctx: Ctx;
  moves: any;
  playerID: string | null;
  isActive: boolean;
}

export const BrawlPartyScene: React.FC<BrawlPartySceneProps> = ({
  G,
  ctx,
  moves,
  playerID,
  isActive,
}) => {
  const groupRef = useRef<Group>(null);

  // Rotate the entire board slowly
  useFrame((state, delta) => {
    if (groupRef.current) {
      groupRef.current.rotation.y += delta * 0.1;
    }
  });

  return (
    <>
      {/* Desert Lighting */}
      <ambientLight intensity={0.4} color="#FFF8DC" />
      <directionalLight
        position={[30, 40, 20]}
        intensity={1.8}
        color="#FFE4B5"
        castShadow
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
        shadow-camera-far={150}
        shadow-camera-left={-60}
        shadow-camera-right={60}
        shadow-camera-top={60}
        shadow-camera-bottom={-60}
      />
      <pointLight position={[0, 30, 0]} intensity={0.6} color="#F4A460" />
      <hemisphereLight args={['#FFE4B5', '#DEB887', 0.3]} />

      {/* Desert atmosphere - subtle warm glow */}
      <pointLight position={[20, 15, 20]} intensity={0.3} color="#FF8C00" distance={80} />
      <pointLight position={[-20, 15, -20]} intensity={0.3} color="#FF8C00" distance={80} />

      {/* Camera Controls */}
      <OrbitControls
        enablePan={true}
        enableZoom={true}
        enableRotate={true}
        minDistance={15}
        maxDistance={50}
        minPolarAngle={0}
        maxPolarAngle={Math.PI / 2}
      />



      {/* Desert Game Board */}
      <group ref={groupRef}>
        {/* Main Desert Ground - Large sandy terrain */}
        <mesh position={[0, -2, 0]} receiveShadow>
          <cylinderGeometry args={[50, 50, 4, 64]} />
          <meshStandardMaterial
            color="#F4A460"
            roughness={0.9}
            metalness={0.0}
          />
        </mesh>

        {/* Secondary Desert Layer - Undulating terrain */}
        <mesh position={[0, -1.2, 0]} receiveShadow>
          <cylinderGeometry args={[40, 40, 2, 48]} />
          <meshStandardMaterial
            color="#DEB887"
            roughness={0.95}
            metalness={0.0}
          />
        </mesh>

        {/* Inner Desert Area - Slightly elevated */}
        <mesh position={[0, -0.5, 0]} receiveShadow>
          <cylinderGeometry args={[30, 30, 1, 32]} />
          <meshStandardMaterial
            color="#D2B48C"
            roughness={0.9}
            metalness={0.0}
          />
        </mesh>

        {/* Desert Rocks - Scattered around the terrain, avoiding tile paths */}
        {Array.from({ length: 15 }, (_, i) => {
          const angle = (i / 15) * Math.PI * 2 + Math.random() * 0.8;
          // Place rocks either very close to center or far from tile path
          const radius = Math.random() > 0.6 ? 5 + Math.random() * 8 : 38 + Math.random() * 10;
          const x = Math.cos(angle) * radius;
          const z = Math.sin(angle) * radius;
          const scale = 0.4 + Math.random() * 1.2;
          return (
            <mesh key={`rock-${i}`} position={[x, -0.8 + scale * 0.4, z]} receiveShadow castShadow>
              <sphereGeometry args={[scale, 8, 6]} />
              <meshStandardMaterial
                color="#8B7355"
                roughness={0.95}
                metalness={0.0}
              />
            </mesh>
          );
        })}

        {/* Desert Cacti - Various sizes, positioned to avoid tile paths */}
        {Array.from({ length: 10 }, (_, i) => {
          const angle = (i / 10) * Math.PI * 2 + Math.random() * 0.6;
          // Position cacti between tile path and outer edge
          const radius = 32 + Math.random() * 12;
          const x = Math.cos(angle) * radius;
          const z = Math.sin(angle) * radius;
          const height = 2 + Math.random() * 3;
          return (
            <group key={`cactus-${i}`} position={[x, 0, z]}>
              {/* Main cactus body */}
              <mesh position={[0, height / 2, 0]} receiveShadow castShadow>
                <cylinderGeometry args={[0.3, 0.4, height, 8]} />
                <meshStandardMaterial
                  color="#228B22"
                  roughness={0.8}
                  metalness={0.0}
                />
              </mesh>
              {/* Cactus arms */}
              {Math.random() > 0.5 && (
                <mesh position={[0.6, height * 0.7, 0]} rotation={[0, 0, Math.PI / 2]} receiveShadow castShadow>
                  <cylinderGeometry args={[0.2, 0.25, 1.5, 6]} />
                  <meshStandardMaterial
                    color="#228B22"
                    roughness={0.8}
                    metalness={0.0}
                  />
                </mesh>
              )}
            </group>
          );
        })}

        {/* Palm Trees - Strategically placed */}
        {Array.from({ length: 6 }, (_, i) => {
          const angle = (i / 6) * Math.PI * 2;
          const radius = 35 + Math.random() * 8;
          const x = Math.cos(angle) * radius;
          const z = Math.sin(angle) * radius;
          const trunkHeight = 4 + Math.random() * 2;
          return (
            <group key={`palm-${i}`} position={[x, 0, z]}>
              {/* Palm trunk */}
              <mesh position={[0, trunkHeight / 2, 0]} receiveShadow castShadow>
                <cylinderGeometry args={[0.4, 0.6, trunkHeight, 8]} />
                <meshStandardMaterial
                  color="#8B4513"
                  roughness={0.9}
                  metalness={0.0}
                />
              </mesh>
              {/* Palm fronds */}
              {Array.from({ length: 6 }, (_, j) => {
                const frondAngle = (j / 6) * Math.PI * 2;
                const frondX = Math.cos(frondAngle) * 2;
                const frondZ = Math.sin(frondAngle) * 2;
                return (
                  <mesh
                    key={`frond-${j}`}
                    position={[frondX, trunkHeight + 0.5, frondZ]}
                    rotation={[Math.PI / 6, frondAngle, 0]}
                    receiveShadow
                    castShadow
                  >
                    <boxGeometry args={[0.2, 3, 0.8]} />
                    <meshStandardMaterial
                      color="#228B22"
                      roughness={0.7}
                      metalness={0.0}
                    />
                  </mesh>
                );
              })}
            </group>
          );
        })}

        {/* Sand Dunes - Add terrain variation */}
        {Array.from({ length: 8 }, (_, i) => {
          const angle = (i / 8) * Math.PI * 2 + Math.random() * 0.5;
          const radius = 45 + Math.random() * 8;
          const x = Math.cos(angle) * radius;
          const z = Math.sin(angle) * radius;
          const scale = 2 + Math.random() * 3;
          return (
            <mesh key={`dune-${i}`} position={[x, -1.5, z]} receiveShadow>
              <sphereGeometry args={[scale, 16, 8]} />
              <meshStandardMaterial
                color="#DEB887"
                roughness={0.9}
                metalness={0.0}
              />
            </mesh>
          );
        })}

        {/* Central Oasis */}
        <group position={[0, 0, 0]}>
          {/* Oasis water */}
          <mesh position={[0, -0.3, 0]} receiveShadow>
            <cylinderGeometry args={[8, 8, 0.4, 32]} />
            <meshStandardMaterial
              color="#4682B4"
              roughness={0.1}
              metalness={0.3}
              transparent
              opacity={0.8}
            />
          </mesh>

          {/* Oasis vegetation ring */}
          {Array.from({ length: 16 }, (_, i) => {
            const angle = (i / 16) * Math.PI * 2;
            const radius = 9 + Math.random() * 2;
            const x = Math.cos(angle) * radius;
            const z = Math.sin(angle) * radius;
            return (
              <mesh key={`vegetation-${i}`} position={[x, 0, z]} receiveShadow castShadow>
                <sphereGeometry args={[0.4 + Math.random() * 0.6, 8, 6]} />
                <meshStandardMaterial
                  color="#32CD32"
                  roughness={0.8}
                  metalness={0.0}
                />
              </mesh>
            );
          })}

          {/* Small oasis palm trees */}
          {Array.from({ length: 3 }, (_, i) => {
            const angle = (i / 3) * Math.PI * 2;
            const radius = 11;
            const x = Math.cos(angle) * radius;
            const z = Math.sin(angle) * radius;
            const trunkHeight = 3 + Math.random() * 1;
            return (
              <group key={`oasis-palm-${i}`} position={[x, 0, z]}>
                {/* Palm trunk */}
                <mesh position={[0, trunkHeight / 2, 0]} receiveShadow castShadow>
                  <cylinderGeometry args={[0.3, 0.4, trunkHeight, 8]} />
                  <meshStandardMaterial
                    color="#8B4513"
                    roughness={0.9}
                    metalness={0.0}
                  />
                </mesh>
                {/* Palm fronds */}
                {Array.from({ length: 5 }, (_, j) => {
                  const frondAngle = (j / 5) * Math.PI * 2;
                  const frondX = Math.cos(frondAngle) * 1.5;
                  const frondZ = Math.sin(frondAngle) * 1.5;
                  return (
                    <mesh
                      key={`oasis-frond-${j}`}
                      position={[frondX, trunkHeight + 0.3, frondZ]}
                      rotation={[Math.PI / 6, frondAngle, 0]}
                      receiveShadow
                      castShadow
                    >
                      <boxGeometry args={[0.15, 2, 0.6]} />
                      <meshStandardMaterial
                        color="#228B22"
                        roughness={0.7}
                        metalness={0.0}
                      />
                    </mesh>
                  );
                })}
              </group>
            );
          })}
        </group>

        {/* Game Tiles */}
        {G.tiles.map((tile) => (
          <GameTile
            key={tile.id}
            tile={tile}
            players={Object.values(G.players).filter(
              (player: Player) => player.position === tile.id
            )}
            isActive={isActive}
            moves={moves}
            playerID={playerID}
          />
        ))}

        {/* Game Players */}
        {Object.values(G.players).map((player: Player) => {
          const tile = G.tiles.find(t => t.id === player.position);
          if (!tile) return null;

          return (
            <GamePlayer
              key={player.ID}
              player={player}
              position={tile.position}
              isCurrentPlayer={ctx.currentPlayer === player.ID}
            />
          );
        })}
      </group>



      {/* Game Over */}
      {ctx.gameover && (
        <Text
          position={[0, 12, 0]}
          fontSize={1.5}
          color="#e74c3c"
          anchorX="center"
          anchorY="middle"
        >
          🎉 {G.players[ctx.gameover.winner]?.name} Wins!
        </Text>
      )}
    </>
  );
};
