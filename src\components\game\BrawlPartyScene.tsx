import React, { useRef } from 'react';
import { useFrame } from '@react-three/fiber';
import { OrbitControls, Text } from '@react-three/drei';
import { Group } from 'three';
import { GameState, Player, TileType } from '../../types/gameTypes';
import { Ctx } from 'boardgame.io';
import { GameTile } from './GameTile';
import { GamePlayer } from './GamePlayer';

interface BrawlPartySceneProps {
  G: GameState;
  ctx: Ctx;
  moves: any;
  playerID: string | null;
  isActive: boolean;
}

export const BrawlPartyScene: React.FC<BrawlPartySceneProps> = ({
  G,
  ctx,
  moves,
  playerID,
  isActive,
}) => {
  const groupRef = useRef<Group>(null);

  // Rotate the entire board slowly
  useFrame((state, delta) => {
    if (groupRef.current) {
      groupRef.current.rotation.y += delta * 0.1;
    }
  });

  return (
    <>
      {/* Lighting */}
      <ambientLight intensity={0.4} />
      <directionalLight
        position={[10, 10, 5]}
        intensity={1}
        castShadow
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
      />
      <pointLight position={[0, 20, 0]} intensity={0.5} />

      {/* Camera Controls */}
      <OrbitControls
        enablePan={true}
        enableZoom={true}
        enableRotate={true}
        minDistance={15}
        maxDistance={50}
        minPolarAngle={0}
        maxPolarAngle={Math.PI / 2}
      />

      {/* Game Title */}
      <Text
        position={[0, 15, 0]}
        fontSize={2}
        color="#ffffff"
        anchorX="center"
        anchorY="middle"
      >
        🎲 Brawl Party AI
      </Text>

      {/* Game Board */}
      <group ref={groupRef}>
        {/* Center Platform */}
        <mesh position={[0, -1, 0]} receiveShadow>
          <cylinderGeometry args={[15, 15, 2, 32]} />
          <meshStandardMaterial color="#2c3e50" />
        </mesh>

        {/* Game Tiles */}
        {G.tiles.map((tile) => (
          <GameTile
            key={tile.id}
            tile={tile}
            players={Object.values(G.players).filter(
              (player: Player) => player.position === tile.id
            )}
            isActive={isActive}
            moves={moves}
            playerID={playerID}
          />
        ))}

        {/* Game Players */}
        {Object.values(G.players).map((player: Player) => {
          const tile = G.tiles.find(t => t.id === player.position);
          if (!tile) return null;

          return (
            <GamePlayer
              key={player.ID}
              player={player}
              position={tile.position}
              isCurrentPlayer={ctx.currentPlayer === player.ID}
            />
          );
        })}
      </group>

      {/* Current Turn Indicator */}
      {!ctx.gameover && (
        <Text
          position={[0, 12, 0]}
          fontSize={1}
          color="#f39c12"
          anchorX="center"
          anchorY="middle"
        >
          {G.players[ctx.currentPlayer]?.name}'s Turn
        </Text>
      )}

      {/* Game Over */}
      {ctx.gameover && (
        <Text
          position={[0, 12, 0]}
          fontSize={1.5}
          color="#e74c3c"
          anchorX="center"
          anchorY="middle"
        >
          🎉 {G.players[ctx.gameover.winner]?.name} Wins!
        </Text>
      )}
    </>
  );
};
