# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

### [6.15.5](https://github.com/millsp/ts-toolbelt/compare/v6.15.1...v6.15.5) (2020-08-13)


### Bug Fixes

* **class:** incorrect base class definition ([249fdf6](https://github.com/millsp/ts-toolbelt/commit/249fdf6f2283a7ed496f408fc878ee0fa7a606d2))
* **modifiers:** do not go over built-in props ([96424cf](https://github.com/millsp/ts-toolbelt/commit/96424cf9f00d46bbf9330fdcefca37b29e9e53d9))


### Others

* **release:** 6.15.4 ([fea6147](https://github.com/millsp/ts-toolbelt/commit/fea6147d105db5d898523c21552ba4499ce32523))
* readme ([0a245a3](https://github.com/millsp/ts-toolbelt/commit/0a245a320def6bc0926fc56501209a0259304f79))
* **release:** 6.15.3 ([44b2a1a](https://github.com/millsp/ts-toolbelt/commit/44b2a1abda5c00969686ef48bb4243d3f3b63dd8))
* cleanup ([e95ee11](https://github.com/millsp/ts-toolbelt/commit/e95ee1146854753ea4d9df092b13bcf68e3e2859))
* **modifiers:** add function to merge ([6729d7f](https://github.com/millsp/ts-toolbelt/commit/6729d7f3e8b5d3562c37ebdbc0792d98a44d626d))
* cleanup ([8e1fe39](https://github.com/millsp/ts-toolbelt/commit/8e1fe3961bac8504877274308a2380a973dc2460))
* **release:** 6.15.2 ([d1268a7](https://github.com/millsp/ts-toolbelt/commit/d1268a7d8b08dea718c602b24b16ef0313f1a97a))
* cleanup ([35c09ff](https://github.com/millsp/ts-toolbelt/commit/35c09ffe098486db7189524d7f8cb65bf9afda54))
* readme ([ccdf90b](https://github.com/millsp/ts-toolbelt/commit/ccdf90bf558f82716362758ccd57188f089a985a))

### [6.15.4](https://github.com/millsp/ts-toolbelt/compare/v6.15.1...v6.15.4) (2020-08-11)


### Bug Fixes

* **modifiers:** do not go over built-in props ([96424cf](https://github.com/millsp/ts-toolbelt/commit/96424cf9f00d46bbf9330fdcefca37b29e9e53d9))


### Others

* readme ([0a245a3](https://github.com/millsp/ts-toolbelt/commit/0a245a320def6bc0926fc56501209a0259304f79))
* **release:** 6.15.3 ([44b2a1a](https://github.com/millsp/ts-toolbelt/commit/44b2a1abda5c00969686ef48bb4243d3f3b63dd8))
* cleanup ([e95ee11](https://github.com/millsp/ts-toolbelt/commit/e95ee1146854753ea4d9df092b13bcf68e3e2859))
* **modifiers:** add function to merge ([6729d7f](https://github.com/millsp/ts-toolbelt/commit/6729d7f3e8b5d3562c37ebdbc0792d98a44d626d))
* cleanup ([8e1fe39](https://github.com/millsp/ts-toolbelt/commit/8e1fe3961bac8504877274308a2380a973dc2460))
* **release:** 6.15.2 ([d1268a7](https://github.com/millsp/ts-toolbelt/commit/d1268a7d8b08dea718c602b24b16ef0313f1a97a))
* cleanup ([35c09ff](https://github.com/millsp/ts-toolbelt/commit/35c09ffe098486db7189524d7f8cb65bf9afda54))
* readme ([ccdf90b](https://github.com/millsp/ts-toolbelt/commit/ccdf90bf558f82716362758ccd57188f089a985a))

### [6.15.3](https://github.com/millsp/ts-toolbelt/compare/v6.15.1...v6.15.3) (2020-08-11)


### Bug Fixes

* **modifiers:** do not go over built-in props ([96424cf](https://github.com/millsp/ts-toolbelt/commit/96424cf9f00d46bbf9330fdcefca37b29e9e53d9))


### Others

* cleanup ([e95ee11](https://github.com/millsp/ts-toolbelt/commit/e95ee1146854753ea4d9df092b13bcf68e3e2859))
* **modifiers:** add function to merge ([6729d7f](https://github.com/millsp/ts-toolbelt/commit/6729d7f3e8b5d3562c37ebdbc0792d98a44d626d))
* cleanup ([8e1fe39](https://github.com/millsp/ts-toolbelt/commit/8e1fe3961bac8504877274308a2380a973dc2460))
* **release:** 6.15.2 ([d1268a7](https://github.com/millsp/ts-toolbelt/commit/d1268a7d8b08dea718c602b24b16ef0313f1a97a))
* cleanup ([35c09ff](https://github.com/millsp/ts-toolbelt/commit/35c09ffe098486db7189524d7f8cb65bf9afda54))
* readme ([ccdf90b](https://github.com/millsp/ts-toolbelt/commit/ccdf90bf558f82716362758ccd57188f089a985a))

### [6.15.2](https://github.com/millsp/ts-toolbelt/compare/v6.15.1...v6.15.2) (2020-08-11)


### Others

* cleanup ([35c09ff](https://github.com/millsp/ts-toolbelt/commit/35c09ffe098486db7189524d7f8cb65bf9afda54))
* readme ([ccdf90b](https://github.com/millsp/ts-toolbelt/commit/ccdf90bf558f82716362758ccd57188f089a985a))

### [6.15.1](https://github.com/millsp/ts-toolbelt/compare/v6.15.0...v6.15.1) (2020-08-08)


### Bug Fixes

* docs ([c493887](https://github.com/millsp/ts-toolbelt/commit/c493887ef1dd2e6648a4107932f002ea400010e0))
* more expressive errors ([93bebed](https://github.com/millsp/ts-toolbelt/commit/93bebed6d44377837a40ef8928b821b3cfa400b8))


### Others

* **pathvalid:** fix ([056e42f](https://github.com/millsp/ts-toolbelt/commit/056e42ff0fe95fd378f56598dae4757219c204cc))
* cleanup ([5f1461b](https://github.com/millsp/ts-toolbelt/commit/5f1461bfffdc4da7d60a2875dea0590acc340801))

## [6.15.0](https://github.com/millsp/ts-toolbelt/compare/v6.13.31...v6.15.0) (2020-08-05)


### Features

* **nomerge, patch, merge:** allow exclude types from merging ([804d140](https://github.com/millsp/ts-toolbelt/commit/804d140d63e93119febb9da100de6eba4c72f655))


### Bug Fixes

* **merge, patch:** prefer lodash-style merging ([71dda67](https://github.com/millsp/ts-toolbelt/commit/71dda67baeced11f1b3528a1a7416a83bcf7cadc))
* **modfiers:** modifier utils don't hide properties anymore [#134](https://github.com/millsp/ts-toolbelt/issues/134) ([ca04ca6](https://github.com/millsp/ts-toolbelt/commit/ca04ca6fba7ab40fee25abec2c68e9365064efda))
* **modifiers, patch, merge:** swallowing list entries when not list ([f5a91b3](https://github.com/millsp/ts-toolbelt/commit/f5a91b3b5893744fe57e448090b955a88d38b43e))
* **nomerge:** forgot to propagate the option ([4505673](https://github.com/millsp/ts-toolbelt/commit/45056737ed780ce949c74e8c8168c6dcd1fea06a))
* **release:** do not tag if not release ([d51fc4c](https://github.com/millsp/ts-toolbelt/commit/d51fc4c21486f379632290a3fd8c0ca267ab8cfa))
* **uncurry:** now works after optimizations ([ab49de4](https://github.com/millsp/ts-toolbelt/commit/ab49de4f5eb457cd0ad7a8a3307b2a60da975af6))


### Others

* **release:** 6.14.0 ([65bed13](https://github.com/millsp/ts-toolbelt/commit/65bed13d5332c2e5b4bd09b4dfab1fb09d81552c))
* cleanup ([66dd2ed](https://github.com/millsp/ts-toolbelt/commit/66dd2edd8b8d7f7e7224ccf72720957f3ede337f))
* **all:** lib-wide optimizations ([211de26](https://github.com/millsp/ts-toolbelt/commit/211de26da358601056dd922d63f832433d7a693b))
* **curry:** optimize ([f21312a](https://github.com/millsp/ts-toolbelt/commit/f21312a8cbf1a562887e92263b36e6fc8b1e1d2c))
* **merge, patch:** simplyfy statement ([f86944f](https://github.com/millsp/ts-toolbelt/commit/f86944ff00b970d7e2da48abbff43e58bdf29b99))
* **merge, patch, objectof:** improve speed of merge and patch ([94f41ff](https://github.com/millsp/ts-toolbelt/commit/94f41ff83c2b84ca468c44525b3475aef42517eb))
* **modifiers:** expanded tests for modifier utils ([5e4de4b](https://github.com/millsp/ts-toolbelt/commit/5e4de4b4cc8ed689e07b83d92f9939eb446eeef9))
* **patch, merge:** more tests on generics ([4a33041](https://github.com/millsp/ts-toolbelt/commit/4a33041c433342e103bc6d0192f03f738a0f1ae3))
* **patch, merge:** prevent unnecessary distribution ([7f5cd5d](https://github.com/millsp/ts-toolbelt/commit/7f5cd5df0594797fee0827df785028b82167e275))
* **release:** 6.13.32 ([fb05cbe](https://github.com/millsp/ts-toolbelt/commit/fb05cbeca0754131082b56b5a59d753b23a88f4b))
* **release:** 6.13.33 ([e077940](https://github.com/millsp/ts-toolbelt/commit/e0779409a11fd1cf50d1330a77accadf45a6ad3a))
* **release:** 6.13.34 ([c0d9ceb](https://github.com/millsp/ts-toolbelt/commit/c0d9ceb03a71e4ca9a51f6077662cb754a586aa2))
* **release:** 6.13.35 ([943408a](https://github.com/millsp/ts-toolbelt/commit/943408a70a27e8874122bb151a18b88311ad4e6f))
* **release:** 6.13.36 ([6ffddf5](https://github.com/millsp/ts-toolbelt/commit/6ffddf5b4fc467f7eabb0b219f30721bde886f27))
* **release:** 6.13.37 ([41859fd](https://github.com/millsp/ts-toolbelt/commit/41859fd7385886da9aab0824cb7281d8dd78c100))
* **release:** 6.13.38 ([5e20d12](https://github.com/millsp/ts-toolbelt/commit/5e20d128c904e43f3adeadf3f71df1fcb05f90ca))
* **release:** 6.13.39 ([f130dae](https://github.com/millsp/ts-toolbelt/commit/f130dae6665064502e4113118205babd59888ff5))
* added funding ([c783b7a](https://github.com/millsp/ts-toolbelt/commit/c783b7a2995528ce62749422c77bf249d1113e1a))
* update notes ([8b9d49b](https://github.com/millsp/ts-toolbelt/commit/8b9d49b191ada31dd6d966440443143dbeeeddd7))

## [6.14.0](https://github.com/millsp/ts-toolbelt/compare/v6.13.31...v6.14.0) (2020-08-05)


### Features

* **nomerge, patch, merge:** allow exclude types from merging ([804d140](https://github.com/millsp/ts-toolbelt/commit/804d140d63e93119febb9da100de6eba4c72f655))


### Bug Fixes

* **merge, patch:** prefer lodash-style merging ([71dda67](https://github.com/millsp/ts-toolbelt/commit/71dda67baeced11f1b3528a1a7416a83bcf7cadc))
* **modfiers:** modifier utils don't hide properties anymore [#134](https://github.com/millsp/ts-toolbelt/issues/134) ([ca04ca6](https://github.com/millsp/ts-toolbelt/commit/ca04ca6fba7ab40fee25abec2c68e9365064efda))
* **modifiers, patch, merge:** swallowing list entries when not list ([f5a91b3](https://github.com/millsp/ts-toolbelt/commit/f5a91b3b5893744fe57e448090b955a88d38b43e))
* **release:** do not tag if not release ([d51fc4c](https://github.com/millsp/ts-toolbelt/commit/d51fc4c21486f379632290a3fd8c0ca267ab8cfa))
* **uncurry:** now works after optimizations ([ab49de4](https://github.com/millsp/ts-toolbelt/commit/ab49de4f5eb457cd0ad7a8a3307b2a60da975af6))


### Others

* cleanup ([66dd2ed](https://github.com/millsp/ts-toolbelt/commit/66dd2edd8b8d7f7e7224ccf72720957f3ede337f))
* **all:** lib-wide optimizations ([211de26](https://github.com/millsp/ts-toolbelt/commit/211de26da358601056dd922d63f832433d7a693b))
* **curry:** optimize ([f21312a](https://github.com/millsp/ts-toolbelt/commit/f21312a8cbf1a562887e92263b36e6fc8b1e1d2c))
* **merge, patch:** simplyfy statement ([f86944f](https://github.com/millsp/ts-toolbelt/commit/f86944ff00b970d7e2da48abbff43e58bdf29b99))
* **merge, patch, objectof:** improve speed of merge and patch ([94f41ff](https://github.com/millsp/ts-toolbelt/commit/94f41ff83c2b84ca468c44525b3475aef42517eb))
* **modifiers:** expanded tests for modifier utils ([5e4de4b](https://github.com/millsp/ts-toolbelt/commit/5e4de4b4cc8ed689e07b83d92f9939eb446eeef9))
* **patch, merge:** more tests on generics ([4a33041](https://github.com/millsp/ts-toolbelt/commit/4a33041c433342e103bc6d0192f03f738a0f1ae3))
* **patch, merge:** prevent unnecessary distribution ([7f5cd5d](https://github.com/millsp/ts-toolbelt/commit/7f5cd5df0594797fee0827df785028b82167e275))
* **release:** 6.13.32 ([fb05cbe](https://github.com/millsp/ts-toolbelt/commit/fb05cbeca0754131082b56b5a59d753b23a88f4b))
* **release:** 6.13.33 ([e077940](https://github.com/millsp/ts-toolbelt/commit/e0779409a11fd1cf50d1330a77accadf45a6ad3a))
* **release:** 6.13.34 ([c0d9ceb](https://github.com/millsp/ts-toolbelt/commit/c0d9ceb03a71e4ca9a51f6077662cb754a586aa2))
* **release:** 6.13.35 ([943408a](https://github.com/millsp/ts-toolbelt/commit/943408a70a27e8874122bb151a18b88311ad4e6f))
* **release:** 6.13.36 ([6ffddf5](https://github.com/millsp/ts-toolbelt/commit/6ffddf5b4fc467f7eabb0b219f30721bde886f27))
* **release:** 6.13.37 ([41859fd](https://github.com/millsp/ts-toolbelt/commit/41859fd7385886da9aab0824cb7281d8dd78c100))
* **release:** 6.13.38 ([5e20d12](https://github.com/millsp/ts-toolbelt/commit/5e20d128c904e43f3adeadf3f71df1fcb05f90ca))
* **release:** 6.13.39 ([f130dae](https://github.com/millsp/ts-toolbelt/commit/f130dae6665064502e4113118205babd59888ff5))
* added funding ([c783b7a](https://github.com/millsp/ts-toolbelt/commit/c783b7a2995528ce62749422c77bf249d1113e1a))
* update notes ([8b9d49b](https://github.com/millsp/ts-toolbelt/commit/8b9d49b191ada31dd6d966440443143dbeeeddd7))

### [6.13.39](https://github.com/millsp/ts-toolbelt/compare/v6.13.31...v6.13.39) (2020-08-02)


### Bug Fixes

* **merge, patch:** prefer lodash-style merging ([71dda67](https://github.com/millsp/ts-toolbelt/commit/71dda67baeced11f1b3528a1a7416a83bcf7cadc))
* **modfiers:** modifier utils don't hide properties anymore [#134](https://github.com/millsp/ts-toolbelt/issues/134) ([ca04ca6](https://github.com/millsp/ts-toolbelt/commit/ca04ca6fba7ab40fee25abec2c68e9365064efda))
* **modifiers, patch, merge:** swallowing list entries when not list ([f5a91b3](https://github.com/millsp/ts-toolbelt/commit/f5a91b3b5893744fe57e448090b955a88d38b43e))
* **release:** do not tag if not release ([d51fc4c](https://github.com/millsp/ts-toolbelt/commit/d51fc4c21486f379632290a3fd8c0ca267ab8cfa))
* **uncurry:** now works after optimizations ([ab49de4](https://github.com/millsp/ts-toolbelt/commit/ab49de4f5eb457cd0ad7a8a3307b2a60da975af6))


### Others

* **all:** lib-wide optimizations ([211de26](https://github.com/millsp/ts-toolbelt/commit/211de26da358601056dd922d63f832433d7a693b))
* **curry:** optimize ([f21312a](https://github.com/millsp/ts-toolbelt/commit/f21312a8cbf1a562887e92263b36e6fc8b1e1d2c))
* **merge, patch:** simplyfy statement ([f86944f](https://github.com/millsp/ts-toolbelt/commit/f86944ff00b970d7e2da48abbff43e58bdf29b99))
* **merge, patch, objectof:** improve speed of merge and patch ([94f41ff](https://github.com/millsp/ts-toolbelt/commit/94f41ff83c2b84ca468c44525b3475aef42517eb))
* **modifiers:** expanded tests for modifier utils ([5e4de4b](https://github.com/millsp/ts-toolbelt/commit/5e4de4b4cc8ed689e07b83d92f9939eb446eeef9))
* **patch, merge:** more tests on generics ([4a33041](https://github.com/millsp/ts-toolbelt/commit/4a33041c433342e103bc6d0192f03f738a0f1ae3))
* **patch, merge:** prevent unnecessary distribution ([7f5cd5d](https://github.com/millsp/ts-toolbelt/commit/7f5cd5df0594797fee0827df785028b82167e275))
* **release:** 6.13.32 ([fb05cbe](https://github.com/millsp/ts-toolbelt/commit/fb05cbeca0754131082b56b5a59d753b23a88f4b))
* **release:** 6.13.33 ([e077940](https://github.com/millsp/ts-toolbelt/commit/e0779409a11fd1cf50d1330a77accadf45a6ad3a))
* **release:** 6.13.34 ([c0d9ceb](https://github.com/millsp/ts-toolbelt/commit/c0d9ceb03a71e4ca9a51f6077662cb754a586aa2))
* **release:** 6.13.35 ([943408a](https://github.com/millsp/ts-toolbelt/commit/943408a70a27e8874122bb151a18b88311ad4e6f))
* **release:** 6.13.36 ([6ffddf5](https://github.com/millsp/ts-toolbelt/commit/6ffddf5b4fc467f7eabb0b219f30721bde886f27))
* **release:** 6.13.37 ([41859fd](https://github.com/millsp/ts-toolbelt/commit/41859fd7385886da9aab0824cb7281d8dd78c100))
* **release:** 6.13.38 ([5e20d12](https://github.com/millsp/ts-toolbelt/commit/5e20d128c904e43f3adeadf3f71df1fcb05f90ca))
* added funding ([c783b7a](https://github.com/millsp/ts-toolbelt/commit/c783b7a2995528ce62749422c77bf249d1113e1a))
* update notes ([8b9d49b](https://github.com/millsp/ts-toolbelt/commit/8b9d49b191ada31dd6d966440443143dbeeeddd7))

### [6.13.38](https://github.com/millsp/ts-toolbelt/compare/v6.13.31...v6.13.38) (2020-08-02)


### Bug Fixes

* **merge, patch:** prefer lodash-style merging ([71dda67](https://github.com/millsp/ts-toolbelt/commit/71dda67baeced11f1b3528a1a7416a83bcf7cadc))
* **modfiers:** modifier utils don't hide properties anymore [#134](https://github.com/millsp/ts-toolbelt/issues/134) ([ca04ca6](https://github.com/millsp/ts-toolbelt/commit/ca04ca6fba7ab40fee25abec2c68e9365064efda))
* **modifiers, patch, merge:** swallowing list entries when not list ([f5a91b3](https://github.com/millsp/ts-toolbelt/commit/f5a91b3b5893744fe57e448090b955a88d38b43e))
* **release:** do not tag if not release ([d51fc4c](https://github.com/millsp/ts-toolbelt/commit/d51fc4c21486f379632290a3fd8c0ca267ab8cfa))


### Others

* **all:** lib-wide optimizations ([211de26](https://github.com/millsp/ts-toolbelt/commit/211de26da358601056dd922d63f832433d7a693b))
* **curry:** optimize ([f21312a](https://github.com/millsp/ts-toolbelt/commit/f21312a8cbf1a562887e92263b36e6fc8b1e1d2c))
* update notes ([8b9d49b](https://github.com/millsp/ts-toolbelt/commit/8b9d49b191ada31dd6d966440443143dbeeeddd7))
* **merge, patch:** simplyfy statement ([f86944f](https://github.com/millsp/ts-toolbelt/commit/f86944ff00b970d7e2da48abbff43e58bdf29b99))
* **merge, patch, objectof:** improve speed of merge and patch ([94f41ff](https://github.com/millsp/ts-toolbelt/commit/94f41ff83c2b84ca468c44525b3475aef42517eb))
* **modifiers:** expanded tests for modifier utils ([5e4de4b](https://github.com/millsp/ts-toolbelt/commit/5e4de4b4cc8ed689e07b83d92f9939eb446eeef9))
* **patch, merge:** more tests on generics ([4a33041](https://github.com/millsp/ts-toolbelt/commit/4a33041c433342e103bc6d0192f03f738a0f1ae3))
* **patch, merge:** prevent unnecessary distribution ([7f5cd5d](https://github.com/millsp/ts-toolbelt/commit/7f5cd5df0594797fee0827df785028b82167e275))
* **release:** 6.13.32 ([fb05cbe](https://github.com/millsp/ts-toolbelt/commit/fb05cbeca0754131082b56b5a59d753b23a88f4b))
* **release:** 6.13.33 ([e077940](https://github.com/millsp/ts-toolbelt/commit/e0779409a11fd1cf50d1330a77accadf45a6ad3a))
* **release:** 6.13.34 ([c0d9ceb](https://github.com/millsp/ts-toolbelt/commit/c0d9ceb03a71e4ca9a51f6077662cb754a586aa2))
* **release:** 6.13.35 ([943408a](https://github.com/millsp/ts-toolbelt/commit/943408a70a27e8874122bb151a18b88311ad4e6f))
* **release:** 6.13.36 ([6ffddf5](https://github.com/millsp/ts-toolbelt/commit/6ffddf5b4fc467f7eabb0b219f30721bde886f27))
* **release:** 6.13.37 ([41859fd](https://github.com/millsp/ts-toolbelt/commit/41859fd7385886da9aab0824cb7281d8dd78c100))
* added funding ([c783b7a](https://github.com/millsp/ts-toolbelt/commit/c783b7a2995528ce62749422c77bf249d1113e1a))

### [6.13.37](https://github.com/millsp/ts-toolbelt/compare/v6.13.31...v6.13.37) (2020-07-31)


### Bug Fixes

* **merge, patch:** prefer lodash-style merging ([71dda67](https://github.com/millsp/ts-toolbelt/commit/71dda67baeced11f1b3528a1a7416a83bcf7cadc))
* **modfiers:** modifier utils don't hide properties anymore [#134](https://github.com/millsp/ts-toolbelt/issues/134) ([ca04ca6](https://github.com/millsp/ts-toolbelt/commit/ca04ca6fba7ab40fee25abec2c68e9365064efda))
* **modifiers, patch, merge:** swallowing list entries when not list ([f5a91b3](https://github.com/millsp/ts-toolbelt/commit/f5a91b3b5893744fe57e448090b955a88d38b43e))
* **release:** do not tag if not release ([d51fc4c](https://github.com/millsp/ts-toolbelt/commit/d51fc4c21486f379632290a3fd8c0ca267ab8cfa))


### Others

* **merge, patch:** simplyfy statement ([f86944f](https://github.com/millsp/ts-toolbelt/commit/f86944ff00b970d7e2da48abbff43e58bdf29b99))
* **merge, patch, objectof:** improve speed of merge and patch ([94f41ff](https://github.com/millsp/ts-toolbelt/commit/94f41ff83c2b84ca468c44525b3475aef42517eb))
* **modifiers:** expanded tests for modifier utils ([5e4de4b](https://github.com/millsp/ts-toolbelt/commit/5e4de4b4cc8ed689e07b83d92f9939eb446eeef9))
* **patch, merge:** more tests on generics ([4a33041](https://github.com/millsp/ts-toolbelt/commit/4a33041c433342e103bc6d0192f03f738a0f1ae3))
* **patch, merge:** prevent unnecessary distribution ([7f5cd5d](https://github.com/millsp/ts-toolbelt/commit/7f5cd5df0594797fee0827df785028b82167e275))
* **release:** 6.13.32 ([fb05cbe](https://github.com/millsp/ts-toolbelt/commit/fb05cbeca0754131082b56b5a59d753b23a88f4b))
* **release:** 6.13.33 ([e077940](https://github.com/millsp/ts-toolbelt/commit/e0779409a11fd1cf50d1330a77accadf45a6ad3a))
* **release:** 6.13.34 ([c0d9ceb](https://github.com/millsp/ts-toolbelt/commit/c0d9ceb03a71e4ca9a51f6077662cb754a586aa2))
* **release:** 6.13.35 ([943408a](https://github.com/millsp/ts-toolbelt/commit/943408a70a27e8874122bb151a18b88311ad4e6f))
* **release:** 6.13.36 ([6ffddf5](https://github.com/millsp/ts-toolbelt/commit/6ffddf5b4fc467f7eabb0b219f30721bde886f27))
* added funding ([c783b7a](https://github.com/millsp/ts-toolbelt/commit/c783b7a2995528ce62749422c77bf249d1113e1a))

### [6.13.36](https://github.com/millsp/ts-toolbelt/compare/v6.13.31...v6.13.36) (2020-07-30)


### Bug Fixes

* **merge, patch:** prefer lodash-style merging ([71dda67](https://github.com/millsp/ts-toolbelt/commit/71dda67baeced11f1b3528a1a7416a83bcf7cadc))
* **modfiers:** modifier utils don't hide properties anymore [#134](https://github.com/millsp/ts-toolbelt/issues/134) ([ca04ca6](https://github.com/millsp/ts-toolbelt/commit/ca04ca6fba7ab40fee25abec2c68e9365064efda))
* **release:** do not tag if not release ([d51fc4c](https://github.com/millsp/ts-toolbelt/commit/d51fc4c21486f379632290a3fd8c0ca267ab8cfa))


### Others

* **merge, patch, objectof:** improve speed of merge and patch ([94f41ff](https://github.com/millsp/ts-toolbelt/commit/94f41ff83c2b84ca468c44525b3475aef42517eb))
* **modifiers:** expanded tests for modifier utils ([5e4de4b](https://github.com/millsp/ts-toolbelt/commit/5e4de4b4cc8ed689e07b83d92f9939eb446eeef9))
* **release:** 6.13.35 ([943408a](https://github.com/millsp/ts-toolbelt/commit/943408a70a27e8874122bb151a18b88311ad4e6f))
* added funding ([c783b7a](https://github.com/millsp/ts-toolbelt/commit/c783b7a2995528ce62749422c77bf249d1113e1a))
* **merge, patch:** simplyfy statement ([f86944f](https://github.com/millsp/ts-toolbelt/commit/f86944ff00b970d7e2da48abbff43e58bdf29b99))
* **patch, merge:** more tests on generics ([4a33041](https://github.com/millsp/ts-toolbelt/commit/4a33041c433342e103bc6d0192f03f738a0f1ae3))
* **patch, merge:** prevent unnecessary distribution ([7f5cd5d](https://github.com/millsp/ts-toolbelt/commit/7f5cd5df0594797fee0827df785028b82167e275))
* **release:** 6.13.32 ([fb05cbe](https://github.com/millsp/ts-toolbelt/commit/fb05cbeca0754131082b56b5a59d753b23a88f4b))
* **release:** 6.13.33 ([e077940](https://github.com/millsp/ts-toolbelt/commit/e0779409a11fd1cf50d1330a77accadf45a6ad3a))
* **release:** 6.13.34 ([c0d9ceb](https://github.com/millsp/ts-toolbelt/commit/c0d9ceb03a71e4ca9a51f6077662cb754a586aa2))

### [6.13.35](https://github.com/millsp/ts-toolbelt/compare/v6.13.31...v6.13.35) (2020-07-29)


### Bug Fixes

* **merge, patch:** prefer lodash-style merging ([71dda67](https://github.com/millsp/ts-toolbelt/commit/71dda67baeced11f1b3528a1a7416a83bcf7cadc))
* **release:** do not tag if not release ([d51fc4c](https://github.com/millsp/ts-toolbelt/commit/d51fc4c21486f379632290a3fd8c0ca267ab8cfa))


### Others

* added funding ([c783b7a](https://github.com/millsp/ts-toolbelt/commit/c783b7a2995528ce62749422c77bf249d1113e1a))
* **merge, patch:** simplyfy statement ([f86944f](https://github.com/millsp/ts-toolbelt/commit/f86944ff00b970d7e2da48abbff43e58bdf29b99))
* **merge, patch, objectof:** improve speed of merge and patch ([94f41ff](https://github.com/millsp/ts-toolbelt/commit/94f41ff83c2b84ca468c44525b3475aef42517eb))
* **patch, merge:** more tests on generics ([4a33041](https://github.com/millsp/ts-toolbelt/commit/4a33041c433342e103bc6d0192f03f738a0f1ae3))
* **patch, merge:** prevent unnecessary distribution ([7f5cd5d](https://github.com/millsp/ts-toolbelt/commit/7f5cd5df0594797fee0827df785028b82167e275))
* **release:** 6.13.32 ([fb05cbe](https://github.com/millsp/ts-toolbelt/commit/fb05cbeca0754131082b56b5a59d753b23a88f4b))
* **release:** 6.13.33 ([e077940](https://github.com/millsp/ts-toolbelt/commit/e0779409a11fd1cf50d1330a77accadf45a6ad3a))
* **release:** 6.13.34 ([c0d9ceb](https://github.com/millsp/ts-toolbelt/commit/c0d9ceb03a71e4ca9a51f6077662cb754a586aa2))

### [6.13.34](https://github.com/millsp/ts-toolbelt/compare/v6.13.31...v6.13.34) (2020-07-27)


### Bug Fixes

* **merge, patch:** prefer lodash-style merging ([71dda67](https://github.com/millsp/ts-toolbelt/commit/71dda67baeced11f1b3528a1a7416a83bcf7cadc))
* **release:** do not tag if not release ([d51fc4c](https://github.com/millsp/ts-toolbelt/commit/d51fc4c21486f379632290a3fd8c0ca267ab8cfa))


### Others

* **merge, patch:** simplyfy statement ([f86944f](https://github.com/millsp/ts-toolbelt/commit/f86944ff00b970d7e2da48abbff43e58bdf29b99))
* **merge, patch, objectof:** improve speed of merge and patch ([94f41ff](https://github.com/millsp/ts-toolbelt/commit/94f41ff83c2b84ca468c44525b3475aef42517eb))
* **patch, merge:** more tests on generics ([4a33041](https://github.com/millsp/ts-toolbelt/commit/4a33041c433342e103bc6d0192f03f738a0f1ae3))
* **patch, merge:** prevent unnecessary distribution ([7f5cd5d](https://github.com/millsp/ts-toolbelt/commit/7f5cd5df0594797fee0827df785028b82167e275))
* **release:** 6.13.32 ([fb05cbe](https://github.com/millsp/ts-toolbelt/commit/fb05cbeca0754131082b56b5a59d753b23a88f4b))
* **release:** 6.13.33 ([e077940](https://github.com/millsp/ts-toolbelt/commit/e0779409a11fd1cf50d1330a77accadf45a6ad3a))

### [6.13.33](https://github.com/millsp/ts-toolbelt/compare/v6.13.31...v6.13.33) (2020-07-26)


### Bug Fixes

* **release:** do not tag if not release ([d51fc4c](https://github.com/millsp/ts-toolbelt/commit/d51fc4c21486f379632290a3fd8c0ca267ab8cfa))


### Others

* **merge, patch:** simplyfy statement ([f86944f](https://github.com/millsp/ts-toolbelt/commit/f86944ff00b970d7e2da48abbff43e58bdf29b99))
* **merge, patch, objectof:** improve speed of merge and patch ([94f41ff](https://github.com/millsp/ts-toolbelt/commit/94f41ff83c2b84ca468c44525b3475aef42517eb))
* **patch, merge:** more tests on generics ([4a33041](https://github.com/millsp/ts-toolbelt/commit/4a33041c433342e103bc6d0192f03f738a0f1ae3))
* **release:** 6.13.32 ([fb05cbe](https://github.com/millsp/ts-toolbelt/commit/fb05cbeca0754131082b56b5a59d753b23a88f4b))

### [6.13.32](https://github.com/millsp/ts-toolbelt/compare/v6.13.31...v6.13.32) (2020-07-26)


### Bug Fixes

* **release:** do not tag if not release ([d51fc4c](https://github.com/millsp/ts-toolbelt/commit/d51fc4c21486f379632290a3fd8c0ca267ab8cfa))

### [6.13.31](https://github.com/millsp/ts-toolbelt/compare/v6.13.0...v6.13.31) (2020-07-26)


### Bug Fixes

* **assign:** add merging style option ([335eb32](https://github.com/millsp/ts-toolbelt/commit/335eb3214b39ace98fb58940f6304dae717f6c76))
* **build:** error on unused variable ([3fdfe4d](https://github.com/millsp/ts-toolbelt/commit/3fdfe4d6a87a6ae19899adea9f68c1bce49d940b))
* **changelog:** avoid conflicts for merges of changelog ([dea41b1](https://github.com/millsp/ts-toolbelt/commit/dea41b185a9b466b472938574f904122fe2c0ca3))
* **exports:** revert changes ([f977829](https://github.com/millsp/ts-toolbelt/commit/f977829c85bbca5d5442dd711f327518e277c1f4))
* **listof:** bug on 3.7 index ([5c20eac](https://github.com/millsp/ts-toolbelt/commit/5c20eacf701d53d8144f96471f9b9a6b5a59aae0))
* **listof:** handle infinite index objects ([7ca73e7](https://github.com/millsp/ts-toolbelt/commit/7ca73e7c10c94579cfeeff20912d6e227116fe5d))
* **listof:** swap number out with never ([fdc5f68](https://github.com/millsp/ts-toolbelt/commit/fdc5f689f306511d3255636187ef887872c681c2))
* **map:** circular ref Map -> Patch, ListOf -> Map, Patch -> ListOf ([d8c4973](https://github.com/millsp/ts-toolbelt/commit/d8c49739ab006f4da528ae394bd7090369fa4035))
* **merge:** distribution ([dc303b3](https://github.com/millsp/ts-toolbelt/commit/dc303b353ae9bd14c29d8a3426db489c698a6553))
* **merge:** fields that exist in `O` and not `O1` become `never` ([a60ae40](https://github.com/millsp/ts-toolbelt/commit/a60ae400ef89bcce2ae0e5df18450a4dd1be6cb8))
* **merge:** undo breaking change, postpone till v7 ([0c52771](https://github.com/millsp/ts-toolbelt/commit/0c52771a5963fefb7192fdb9dca72ce246f78b48))
* **merge, patch:** distribution not happening ([0e78804](https://github.com/millsp/ts-toolbelt/commit/0e788045d7c9e0e9d597a0994b4c91c143110846))
* **merge, patch:** merge exactly like lodash ([2d864e8](https://github.com/millsp/ts-toolbelt/commit/2d864e889c12bfd05b39e3c90c2fb6e9ddf47c51))
* **merge, patch:** metadata loss due to use of infer ([f12d766](https://github.com/millsp/ts-toolbelt/commit/f12d7665af406a1580328946472a6bf3978d5a63))
* **merge, patch:** prevent mapped type from deep resolving itself ([9d0425e](https://github.com/millsp/ts-toolbelt/commit/9d0425e01d678a0e2008709ecebc87adb9f63988))
* **merge, patch:** various fixes ([c3d5242](https://github.com/millsp/ts-toolbelt/commit/c3d5242bd7669a00172a2b6fa05363a870ef858b))
* **patch:** distribution ([f918471](https://github.com/millsp/ts-toolbelt/commit/f918471c1342c12102dff887d3c0e17502773724))
* **release:** delete unused tags ([7ccaf1e](https://github.com/millsp/ts-toolbelt/commit/7ccaf1e02f8861d0c0c913c56cae11f2b7466bde))
* metadata loss for utils using patch ([2f46c93](https://github.com/millsp/ts-toolbelt/commit/2f46c932ff850aca5c09962334ee8cdbcdd258c7))
* unused parameters ([9e09c85](https://github.com/millsp/ts-toolbelt/commit/9e09c854884a5a1044e2dc2ea1b70fbeb32b61af))
* **object:** revert beacause of breakage on deps ([8131b97](https://github.com/millsp/ts-toolbelt/commit/8131b976c5b24249103a7350d35e28e7056b172a))


### Others

* **release:** 6.13.30 ([3e51d09](https://github.com/millsp/ts-toolbelt/commit/3e51d09594e5beccd6a9d7197ca8f6f4d39d84ee))
* cleanup ([fe867c7](https://github.com/millsp/ts-toolbelt/commit/fe867c75d0aac1ad133e964dfd45ff61b0f46958))
* test re-enable dt ([f49c679](https://github.com/millsp/ts-toolbelt/commit/f49c6798967ed5e3765a94335999cdf13d5c2c77))
* **all:** micro optimize ([b30331e](https://github.com/millsp/ts-toolbelt/commit/b30331eb96df16be0e8195945b03571a9a80131b))
* **iteration:** reshape and clean iteration mechanisms ([c11fa44](https://github.com/millsp/ts-toolbelt/commit/c11fa4441654065db6848a813ad0519ca0128e04))
* **listof:** add missing tests ([4e4b969](https://github.com/millsp/ts-toolbelt/commit/4e4b9692788033044bd9317f1b58726134be781a))
* **listof:** simplify ([d5fc8cf](https://github.com/millsp/ts-toolbelt/commit/d5fc8cf5aeb8cb984b6fb91bf4f5a105a458d6b7))
* **patch, merge:** step the process in separate functions ([e53a5de](https://github.com/millsp/ts-toolbelt/commit/e53a5ded70d0a6428afb6dff05d0016caecf6df5))
* **release:** 6.13.13 ([538a2e9](https://github.com/millsp/ts-toolbelt/commit/538a2e91835ee551c50dd0a5380d4e49f068462b))
* **release:** 6.13.14 ([5d5aadb](https://github.com/millsp/ts-toolbelt/commit/5d5aadb9fd15b08fcf7f3892ec04a0edeab6fddb))
* **release:** 6.13.15 ([2528449](https://github.com/millsp/ts-toolbelt/commit/2528449063e21195b283068559f5a25825029fce))
* **release:** 6.13.16 ([e9f2ac1](https://github.com/millsp/ts-toolbelt/commit/e9f2ac1e5cfbc5e2a96f6d930e5b8f4569c670c7))
* **release:** 6.13.17 ([aafc9a5](https://github.com/millsp/ts-toolbelt/commit/aafc9a5586314a87a95c57bdc34fe7344a56601a))
* **release:** 6.13.18 ([f43763a](https://github.com/millsp/ts-toolbelt/commit/f43763a27858b0e8d27f4986ecae988686b6038b))
* **release:** 6.13.19 ([8f1b39b](https://github.com/millsp/ts-toolbelt/commit/8f1b39b294cf1b8e4b47ef9fca8d4b58fb774596))
* **release:** 6.13.20 ([f1b2003](https://github.com/millsp/ts-toolbelt/commit/f1b2003fc92d9a887e20fd17b5a2b286cf52d422))
* **release:** 6.13.21 ([39b95df](https://github.com/millsp/ts-toolbelt/commit/39b95dff7371b7a324a3052f5c4e9355ff7e6c57))
* **release:** 6.13.22 ([ee8f155](https://github.com/millsp/ts-toolbelt/commit/ee8f155a5d455ce02519250d39d7cc70f5983806))
* **release:** 6.13.23 ([eb967a2](https://github.com/millsp/ts-toolbelt/commit/eb967a273de2e175ecb0e15af11675c654899290))
* **release:** 6.13.24 ([efc3325](https://github.com/millsp/ts-toolbelt/commit/efc3325548bc3f8d69fff9e8b3a5fa1d88eaf7f4))
* **release:** 6.13.25 ([b9d138e](https://github.com/millsp/ts-toolbelt/commit/b9d138e6d2e49dcc1a97c47ca59623333eb4feda))
* **release:** 6.13.26 ([7f30483](https://github.com/millsp/ts-toolbelt/commit/7f30483973ed32f461152eed2b7b20af435bb1b7))
* **release:** 6.13.27 ([c666786](https://github.com/millsp/ts-toolbelt/commit/c666786a9e07995578b5bbb5360972751c3546d2))
* **release:** 6.13.28 ([94b2b6d](https://github.com/millsp/ts-toolbelt/commit/94b2b6d8ccbbc0754c9ce9ba32ce6540a3971d3e))
* **release:** 6.13.29 ([86377bf](https://github.com/millsp/ts-toolbelt/commit/86377bfc464bdeec4a9ee88c5ee208990408417c))
* cleanup ([e6ecd4f](https://github.com/millsp/ts-toolbelt/commit/e6ecd4fbc68ad74b91df9bb8996a0812af062e7d))
* cleanup ([280899b](https://github.com/millsp/ts-toolbelt/commit/280899b3b70e98ad3569805a7ed4ff184b574802))
* cleanup ([a21ba4f](https://github.com/millsp/ts-toolbelt/commit/a21ba4fed027f3b1147a16877df6ec63e430dd23))
* cleanup ([d3163b3](https://github.com/millsp/ts-toolbelt/commit/d3163b3b3da2d177ae0593b4837595fe72c1442a))
* cleanup ([24c3d00](https://github.com/millsp/ts-toolbelt/commit/24c3d0098aafe985a15ddcf03d2d62e5106176c7))
* cleanup ([0a6af7b](https://github.com/millsp/ts-toolbelt/commit/0a6af7b30bee303e9978f4d6e7438e10e79d4938))
* cleanup ([637cbe0](https://github.com/millsp/ts-toolbelt/commit/637cbe0bc28a16f0424a35183c05d80a0431ba69))
* disable dt tests ([4475d58](https://github.com/millsp/ts-toolbelt/commit/4475d58661bf44448acac89c386a5d57842f6983))
* notes ([04d5484](https://github.com/millsp/ts-toolbelt/commit/04d54843d39913c32a9009971f44ab322ddf8303))
* set ts version ([c8d1b36](https://github.com/millsp/ts-toolbelt/commit/c8d1b369a03aaf9893b4252238a1759d9adf045b))
* test ([0433807](https://github.com/millsp/ts-toolbelt/commit/0433807eeb5911adbe11fc0623706c1fc370afde))
* **release:** 6.13.12 ([a5bd316](https://github.com/millsp/ts-toolbelt/commit/a5bd316d35e2c497e25c4c2016bec1bc90e3851f))
* cleanup ([92fd7d0](https://github.com/millsp/ts-toolbelt/commit/92fd7d08735fad5ea11f14b93ec8cc46b700e30a))
* **release:** 6.13.11 ([8df7c68](https://github.com/millsp/ts-toolbelt/commit/8df7c68e7595ccfe3a29ef85cfc381034d8b11d6))
* cleanup ([7c346cb](https://github.com/millsp/ts-toolbelt/commit/7c346cbdb7ffe6ec4f625701d732b524988cb8b4))
* prepare legacy of v7 ([8f3dc02](https://github.com/millsp/ts-toolbelt/commit/8f3dc0283b3d7cb5f5251a8f0bfe9f4a50333d48))
* readme ([7664c4f](https://github.com/millsp/ts-toolbelt/commit/7664c4fc64e84657d7046cb47fab0dcc7f7bbea9))
* **release:** 6.13.10 ([3004a93](https://github.com/millsp/ts-toolbelt/commit/3004a93f1e0a645d90f0f232bd9aae7b706ec803))
* readme ([469f4ca](https://github.com/millsp/ts-toolbelt/commit/469f4ca914a8f0a1b9948d859d3cc87ba7cbc97e))
* **release:** 6.13.3 ([c66d72e](https://github.com/millsp/ts-toolbelt/commit/c66d72e6dba2b0ccb7b6ce8d85d1090fadb7b920))
* **release:** 6.13.4 ([2c3c1f4](https://github.com/millsp/ts-toolbelt/commit/2c3c1f47a0a30ae654afdc41774327b35d45398a))
* **release:** 6.13.9 ([900ac99](https://github.com/millsp/ts-toolbelt/commit/900ac999cbffe44efb5ae52b2e9f4b32f97116a5))
* readme ([df9d038](https://github.com/millsp/ts-toolbelt/commit/df9d03807a56b200d527b3a8ac63ae62e6a17de1))
* readme ([3e1e2df](https://github.com/millsp/ts-toolbelt/commit/3e1e2df66dde86acb83d408317476a36bdbc1885))
* **objectof:** downgrade tasks ([18dc456](https://github.com/millsp/ts-toolbelt/commit/18dc45657e9fe1450affd9c6f7477b0c5cfb33b2))
* **release:** 6.13.5 ([f7b8463](https://github.com/millsp/ts-toolbelt/commit/f7b8463b1f4229f764431a71e6362928edfc6c59))
* **release:** 6.13.6 ([b8e2dcf](https://github.com/millsp/ts-toolbelt/commit/b8e2dcf850b7845197ea239fdb881718aebf1bbc))
* **release:** 6.13.8 ([43542e4](https://github.com/millsp/ts-toolbelt/commit/43542e4d3cef4eed146ff89184380e608ef83867))
* cleanup ([2ae7b51](https://github.com/millsp/ts-toolbelt/commit/2ae7b5178d8ec07de9aeca646b3b91567ca04f4e))
* multi clean ([fc8085d](https://github.com/millsp/ts-toolbelt/commit/fc8085d861164b729b7f1c2c2d4855f15b06e801))
* readme ([2a15c13](https://github.com/millsp/ts-toolbelt/commit/2a15c13bffb3b8abe004110a013101a16b51502e))
* **release:** 6.13.7 ([94fad02](https://github.com/millsp/ts-toolbelt/commit/94fad024f6288c4bb940f06bab8e8b52871dbe2c))
* cleanup ([97351f9](https://github.com/millsp/ts-toolbelt/commit/97351f99bd34554965648ff605c53f79cdc2cf23))
* cleanup old techniques ([e7699ca](https://github.com/millsp/ts-toolbelt/commit/e7699ca9c6d9b55eb42bcd359118e042d416ce84))
* readme ([33f52fe](https://github.com/millsp/ts-toolbelt/commit/33f52fef2b2186e7e5ab2efd44735ce10369ee53))
* readme ([bbd2619](https://github.com/millsp/ts-toolbelt/commit/bbd26192e5482645668eba8b59edbb89601cca7d))
* readme ([224756e](https://github.com/millsp/ts-toolbelt/commit/224756e012f913df0dc1e5924272c3371e7d776d))
* revert and comment ([faf2b05](https://github.com/millsp/ts-toolbelt/commit/faf2b059feaedb2ccab3942bed5c87bdfa6ab3e1))
* **release:** 6.13.1 ([5af91aa](https://github.com/millsp/ts-toolbelt/commit/5af91aac238e3242f31c64e51ddc98dd15119390))
* **release:** 6.13.2 ([71c8504](https://github.com/millsp/ts-toolbelt/commit/71c850488d90020a1cc0f9806455cef0dcd6ab11))
* readme ([02acbc0](https://github.com/millsp/ts-toolbelt/commit/02acbc08f55712042583d55987043db70b761e25))

### [6.13.17](https://github.com/millsp/ts-toolbelt/compare/v6.13.16...v6.13.17) (2020-07-21)


### Others

* cleanup ([d3163b3](https://github.com/millsp/ts-toolbelt/commit/d3163b3b3da2d177ae0593b4837595fe72c1442a))

### [6.13.16](https://github.com/millsp/ts-toolbelt/compare/v6.13.15...v6.13.16) (2020-07-21)


### Bug Fixes

* **merge:** undo breaking change, postpone till v7 ([0c52771](https://github.com/millsp/ts-toolbelt/commit/0c52771a5963fefb7192fdb9dca72ce246f78b48))
* **merge, patch:** prevent mapped type from deep resolving itself ([9d0425e](https://github.com/millsp/ts-toolbelt/commit/9d0425e01d678a0e2008709ecebc87adb9f63988))

### [6.13.15](https://github.com/millsp/ts-toolbelt/compare/v6.13.14...v6.13.15) (2020-07-20)


### Others

* cleanup ([24c3d00](https://github.com/millsp/ts-toolbelt/commit/24c3d0098aafe985a15ddcf03d2d62e5106176c7))
* cleanup ([0a6af7b](https://github.com/millsp/ts-toolbelt/commit/0a6af7b30bee303e9978f4d6e7438e10e79d4938))

### [6.13.14](https://github.com/millsp/ts-toolbelt/compare/v6.13.13...v6.13.14) (2020-07-20)


### Bug Fixes

* **assign:** add merging style option ([335eb32](https://github.com/millsp/ts-toolbelt/commit/335eb3214b39ace98fb58940f6304dae717f6c76))


### Others

* cleanup ([fe867c7](https://github.com/millsp/ts-toolbelt/commit/fe867c75d0aac1ad133e964dfd45ff61b0f46958))

### [6.13.13](https://github.com/millsp/ts-toolbelt/compare/v6.13.12...v6.13.13) (2020-07-20)


### Others

* cleanup ([637cbe0](https://github.com/millsp/ts-toolbelt/commit/637cbe0bc28a16f0424a35183c05d80a0431ba69))

### [6.13.12](https://github.com/millsp/ts-toolbelt/compare/v6.13.11...v6.13.12) (2020-07-20)


### Bug Fixes

* **merge, patch:** distribution not happening ([0e78804](https://github.com/millsp/ts-toolbelt/commit/0e788045d7c9e0e9d597a0994b4c91c143110846))
* **merge, patch:** various fixes ([c3d5242](https://github.com/millsp/ts-toolbelt/commit/c3d5242bd7669a00172a2b6fa05363a870ef858b))


### Others

* cleanup ([92fd7d0](https://github.com/millsp/ts-toolbelt/commit/92fd7d08735fad5ea11f14b93ec8cc46b700e30a))

### [6.13.11](https://github.com/millsp/ts-toolbelt/compare/v6.13.10...v6.13.11) (2020-07-16)


### Others

* readme ([7664c4f](https://github.com/millsp/ts-toolbelt/commit/7664c4fc64e84657d7046cb47fab0dcc7f7bbea9))

### [6.13.10](https://github.com/millsp/ts-toolbelt/compare/v6.13.9...v6.13.10) (2020-07-16)


### Others

* readme ([469f4ca](https://github.com/millsp/ts-toolbelt/commit/469f4ca914a8f0a1b9948d859d3cc87ba7cbc97e))

### [6.13.9](https://github.com/millsp/ts-toolbelt/compare/v6.13.8...v6.13.9) (2020-07-16)


### Others

* readme ([df9d038](https://github.com/millsp/ts-toolbelt/commit/df9d03807a56b200d527b3a8ac63ae62e6a17de1))
* readme ([3e1e2df](https://github.com/millsp/ts-toolbelt/commit/3e1e2df66dde86acb83d408317476a36bdbc1885))

### [6.13.8](https://github.com/millsp/ts-toolbelt/compare/v6.13.7...v6.13.8) (2020-07-16)


### Others

* readme ([2a15c13](https://github.com/millsp/ts-toolbelt/commit/2a15c13bffb3b8abe004110a013101a16b51502e))

### [6.13.7](https://github.com/millsp/ts-toolbelt/compare/v6.13.6...v6.13.7) (2020-07-16)


### Others

* cleanup ([7c346cb](https://github.com/millsp/ts-toolbelt/commit/7c346cbdb7ffe6ec4f625701d732b524988cb8b4))
* readme ([33f52fe](https://github.com/millsp/ts-toolbelt/commit/33f52fef2b2186e7e5ab2efd44735ce10369ee53))

### [6.13.6](https://github.com/millsp/ts-toolbelt/compare/v6.13.5...v6.13.6) (2020-07-15)


### Bug Fixes

* **object:** revert beacause of breakage on deps ([8131b97](https://github.com/millsp/ts-toolbelt/commit/8131b976c5b24249103a7350d35e28e7056b172a))


### Others

* prepare legacy of v7 ([8f3dc02](https://github.com/millsp/ts-toolbelt/commit/8f3dc0283b3d7cb5f5251a8f0bfe9f4a50333d48))
* revert and comment ([faf2b05](https://github.com/millsp/ts-toolbelt/commit/faf2b059feaedb2ccab3942bed5c87bdfa6ab3e1))

### [6.13.5](https://github.com/millsp/ts-toolbelt/compare/v6.13.4...v6.13.5) (2020-07-15)


### Others

* **objectof:** downgrade tasks ([18dc456](https://github.com/millsp/ts-toolbelt/commit/18dc45657e9fe1450affd9c6f7477b0c5cfb33b2))
* cleanup ([97351f9](https://github.com/millsp/ts-toolbelt/commit/97351f99bd34554965648ff605c53f79cdc2cf23))

### [6.13.4](https://github.com/millsp/ts-toolbelt/compare/v6.13.3...v6.13.4) (2020-07-15)


### Others

* cleanup old techniques ([e7699ca](https://github.com/millsp/ts-toolbelt/commit/e7699ca9c6d9b55eb42bcd359118e042d416ce84))

### [6.13.3](https://github.com/millsp/ts-toolbelt/compare/v6.13.2...v6.13.3) (2020-07-15)


### Others

* cleanup ([2ae7b51](https://github.com/millsp/ts-toolbelt/commit/2ae7b5178d8ec07de9aeca646b3b91567ca04f4e))
* multi clean ([fc8085d](https://github.com/millsp/ts-toolbelt/commit/fc8085d861164b729b7f1c2c2d4855f15b06e801))

### [6.13.2](https://github.com/millsp/ts-toolbelt/compare/v6.13.1...v6.13.2) (2020-07-15)


### Bug Fixes

* **merge:** fields that exist in `O` and not `O1` become `never` ([a60ae40](https://github.com/millsp/ts-toolbelt/commit/a60ae400ef89bcce2ae0e5df18450a4dd1be6cb8))

### [6.13.1](https://github.com/millsp/ts-toolbelt/compare/v6.13.0...v6.13.1) (2020-07-15)


### Others

* readme ([bbd2619](https://github.com/millsp/ts-toolbelt/commit/bbd26192e5482645668eba8b59edbb89601cca7d))
* readme ([02acbc0](https://github.com/millsp/ts-toolbelt/commit/02acbc08f55712042583d55987043db70b761e25))
* readme ([224756e](https://github.com/millsp/ts-toolbelt/commit/224756e012f913df0dc1e5924272c3371e7d776d))

## [6.13.0](https://github.com/millsp/ts-toolbelt/compare/v6.12.2...v6.13.0) (2020-07-14)


### Features

* **mergeall, patchall:** library wide cleanup for new features ([7f38e59](https://github.com/millsp/ts-toolbelt/commit/7f38e5985f7f95a3469cba1d3964fd0fe9c27384))

### [6.12.2](https://github.com/millsp/ts-toolbelt/compare/v6.12.1...v6.12.2) (2020-07-13)


### Others

* update nick ([0ede6bf](https://github.com/millsp/ts-toolbelt/commit/0ede6bfcfbd938c37b90ef67ae86ab3910206476))

### [6.12.1](https://github.com/millsp/ts-toolbelt/compare/v6.12.0...v6.12.1) (2020-07-11)


### Bug Fixes

* **partial:** add missing entry for list ([c754027](https://github.com/millsp/ts-toolbelt/commit/c7540274f56776906d347ef597451191d4cea89a))

## [6.12.0](https://github.com/millsp/ts-toolbelt/compare/v6.10.16...v6.12.0) (2020-07-11)


### Features

* **object:** added Partial alias for OptionalPart ([42108ff](https://github.com/millsp/ts-toolbelt/commit/42108ffb52f7561feaced7a7bd0d619a17f6660b))


### Bug Fixes

* **object:** export order ([879edb6](https://github.com/millsp/ts-toolbelt/commit/879edb6ed90f88b9ae6a3c2e8878ae1be48e0c88))
* **partial:** format of JSDoc example ([f1d6fc8](https://github.com/millsp/ts-toolbelt/commit/f1d6fc876a3ca62580eadd27be8d69c533b8794f))


### Others

* cleanup ([593bb53](https://github.com/millsp/ts-toolbelt/commit/593bb53b38754dbcdc9bc57f66890b983c4c31bd))
* **cleanup:** remove comment ([a62cc89](https://github.com/millsp/ts-toolbelt/commit/a62cc89b31ef25a45600a604dd9157db6d36d5df))
* **object:** add test for new Partial type ([befa7b8](https://github.com/millsp/ts-toolbelt/commit/befa7b873cd2aa0d693f899d47ecb42866993e46))
* **partial:** separate bundle of tests for Partial ([286c3e5](https://github.com/millsp/ts-toolbelt/commit/286c3e5bd1f902d5027ea4b845a49b28dff3a2dc))
* **partial:** simplify ([e500c85](https://github.com/millsp/ts-toolbelt/commit/e500c85a02c34548497cd36409a3595338e51345))
* **release:** 6.10.0 ([73adcd6](https://github.com/millsp/ts-toolbelt/commit/73adcd6deff69a0be64646e73eb963a64338dce4))

### [6.10.16](https://github.com/millsp/ts-toolbelt/compare/v6.10.15...v6.10.16) (2020-07-10)


### Others

* **equals:** simplify ([5f1c014](https://github.com/millsp/ts-toolbelt/commit/5f1c0147b965102572370265cddaa1b9ed50bebb))
* **mergeup:** cleanup ([3f72e35](https://github.com/millsp/ts-toolbelt/commit/3f72e3539aa96148e0eca1876f2f7d62b633d00f))

### [6.10.15](https://github.com/millsp/ts-toolbelt/compare/v6.10.14...v6.10.15) (2020-07-10)


### Bug Fixes

* **mergeup:** do not merge built-in objects ([8b6e93e](https://github.com/millsp/ts-toolbelt/commit/8b6e93e50d3e9d983c624e6c9e64b0068747ff05))


### Others

* cleanup ([a6e69aa](https://github.com/millsp/ts-toolbelt/commit/a6e69aa8101834c37ba073a09a7d6181c40c29fb))

### [6.10.14](https://github.com/millsp/ts-toolbelt/compare/v6.10.13...v6.10.14) (2020-07-10)


### Others

* **compute:** introduce basic compute raw ([9f65db5](https://github.com/millsp/ts-toolbelt/commit/9f65db50c7398c41e0d97508de6a5df1479533b5))

### [6.10.13](https://github.com/millsp/ts-toolbelt/compare/v6.10.12...v6.10.13) (2020-07-09)


### Others

* **compute:** remove recursive infer ([70a0097](https://github.com/millsp/ts-toolbelt/commit/70a00973a07520959210219fcaba2a782d76f491))

### [6.10.12](https://github.com/millsp/ts-toolbelt/compare/v6.10.11...v6.10.12) (2020-07-09)


### Bug Fixes

* utilities output not recognized ([767ac7a](https://github.com/millsp/ts-toolbelt/commit/767ac7aab868673760a52845ede0f4ea0bfad485))
* **compute:** fixes [#127](https://github.com/millsp/ts-toolbelt/issues/127) ([a3376d7](https://github.com/millsp/ts-toolbelt/commit/a3376d7884aab9e7c706762c8a327685667158f9))
* **mergeup:** flat also handles lists ([c961a57](https://github.com/millsp/ts-toolbelt/commit/c961a572c20d0f9ab0a71f38f3e40dc7c6c68358))

### [6.10.11](https://github.com/millsp/ts-toolbelt/compare/v6.10.10...v6.10.11) (2020-07-09)


### Others

* **mergeup:** add flags to be enabled ([99b77b1](https://github.com/millsp/ts-toolbelt/commit/99b77b1c6ec68379cb0c3a03ce7aeb914334aa26))

### [6.10.10](https://github.com/millsp/ts-toolbelt/compare/v6.10.9...v6.10.10) (2020-07-09)


### Bug Fixes

* **builtinobject:** array is a lang feature more than a built-in object ([12cacde](https://github.com/millsp/ts-toolbelt/commit/12cacdeba870df53db8c20e187efa4803364c1af))

### [6.10.9](https://github.com/millsp/ts-toolbelt/compare/v6.10.8...v6.10.9) (2020-07-09)


### Bug Fixes

* namespace pollution ([f3e18c0](https://github.com/millsp/ts-toolbelt/commit/f3e18c0b22c633829f989bac4cfcfd7d039773bd))
* typo ([ffb7b0c](https://github.com/millsp/ts-toolbelt/commit/ffb7b0c092613db785a42164e0b490e01b79a733))

### [6.10.8](https://github.com/millsp/ts-toolbelt/compare/v6.10.7...v6.10.8) (2020-07-09)


### Bug Fixes

* **pobject.p:** temporary fix revert ([c11874b](https://github.com/millsp/ts-toolbelt/commit/c11874b88fd8d4a63a85228573f326363e49bf55))


### Others

* restore dt tests ([2155645](https://github.com/millsp/ts-toolbelt/commit/2155645c6b12409b1d2d574b613cedb6f5883f05))

### [6.10.7](https://github.com/millsp/ts-toolbelt/compare/v6.10.6...v6.10.7) (2020-07-09)


### Others

* **assign, compact:** clearer descriptions ([ec36045](https://github.com/millsp/ts-toolbelt/commit/ec360455484a5b3dda41cf30e490fa01d5609493))

### [6.10.6](https://github.com/millsp/ts-toolbelt/compare/v6.10.5...v6.10.6) (2020-07-09)


### Bug Fixes

* **compute:** export new compute ([c4bab8e](https://github.com/millsp/ts-toolbelt/commit/c4bab8ee72d5be55d09fb064a0ee7832e8cd822e))

### [6.10.5](https://github.com/millsp/ts-toolbelt/compare/v6.10.4...v6.10.5) (2020-07-09)


### Others

* **scripts:** temporarily disable dt tests ([ce1336d](https://github.com/millsp/ts-toolbelt/commit/ce1336dbd70303d4af630868358f34dfe4293a97))
* cleanup ([ba11d1e](https://github.com/millsp/ts-toolbelt/commit/ba11d1e58ac989451f9434206a5f906227047a51))

### [6.10.4](https://github.com/millsp/ts-toolbelt/compare/v6.10.3...v6.10.4) (2020-07-09)


### Bug Fixes

* **compute:** depth option ([557fc61](https://github.com/millsp/ts-toolbelt/commit/557fc61b3558366aae207d7cfaaabeb34fa51fad))

### [6.10.3](https://github.com/millsp/ts-toolbelt/compare/v6.10.2...v6.10.3) (2020-07-09)


### Bug Fixes

* **compute:** can compute circular refs ([6416ac3](https://github.com/millsp/ts-toolbelt/commit/6416ac31de6726c92af2e967ebb613cc991ecf4e))


### Others

* cleanup ([d147991](https://github.com/millsp/ts-toolbelt/commit/d147991ff7e8fe15c55368601c00b12a931bc6e5))
* cleanup ([db00785](https://github.com/millsp/ts-toolbelt/commit/db007858434343f2ee7e8516c926ca724754acb4))

### [6.10.2](https://github.com/millsp/ts-toolbelt/compare/v6.10.1...v6.10.2) (2020-07-09)


### Bug Fixes

* **contains:** make implements the new contains utility ([eb27ac7](https://github.com/millsp/ts-toolbelt/commit/eb27ac754b509911c4aa7e77a7842467558c40f2))


### Others

* cleanup ([89b90bc](https://github.com/millsp/ts-toolbelt/commit/89b90bc01782c05547e9996fd1cb9b265182475c))
* cleanup ([d2d0cd0](https://github.com/millsp/ts-toolbelt/commit/d2d0cd05e8b3fccbe4911eb5229cf73699da12ff))
* cleanup ([6e0e19a](https://github.com/millsp/ts-toolbelt/commit/6e0e19a447f13d8a8b29d2af8b9e09ae6cfb9888))

### [6.10.1](https://github.com/millsp/ts-toolbelt/compare/v6.10.0...v6.10.1) (2020-07-08)


### Others

* **match:** more details ([b0f6245](https://github.com/millsp/ts-toolbelt/commit/b0f6245f21806bcef4dfadf255b9aa8f949c2fe5))

## [6.10.0](https://github.com/millsp/ts-toolbelt/compare/v6.9.9...v6.10.0) (2020-07-08)


### Features

* **object:** added Partial alias for OptionalPart ([42108ff](https://github.com/millsp/ts-toolbelt/commit/42108ffb52f7561feaced7a7bd0d619a17f6660b))
* **contains:** new comparison operator ([6f2c393](https://github.com/millsp/ts-toolbelt/commit/6f2c3933163e2b55e22c7d5fa4ddc43730ee2317))
* **contains:** new comparison operator ([5ea1ab7](https://github.com/millsp/ts-toolbelt/commit/5ea1ab77e25c2ff1859e3ca7570dbdac43974ba0))


### Bug Fixes

* **object:** export order ([879edb6](https://github.com/millsp/ts-toolbelt/commit/879edb6ed90f88b9ae6a3c2e8878ae1be48e0c88))
* version ([3617865](https://github.com/millsp/ts-toolbelt/commit/36178654732305f28ca21dc20b0cf6baaafc5ae1))
* **misc.builtinobject:** missing readonly types ([f8ae740](https://github.com/millsp/ts-toolbelt/commit/f8ae740e7b04e2010cbf3625c6d43daeb82ca2c3))
* **contains:** missing entry ([18c7ba4](https://github.com/millsp/ts-toolbelt/commit/18c7ba4665b65c282db0ec22e2b8bf42c261786a))


### Others

* **curry:** remove drop ([80d5020](https://github.com/millsp/ts-toolbelt/commit/80d50203eeb3b9531a385cb03e0183d99551e648))
* **curry:** use undristributed concat ([1c8ff5c](https://github.com/millsp/ts-toolbelt/commit/1c8ff5cf2feb3ce0b7d0fbfc43be8d20f5b73287))
* **linit:** disable debug ([390c1a3](https://github.com/millsp/ts-toolbelt/commit/390c1a3954c8e5b5d9540c0f084c625734ff1f36))
* **lint:** debug ([8275a23](https://github.com/millsp/ts-toolbelt/commit/8275a2302b42465109196ed023e835f340ed1a8d))
* **lint:** debug ([2fbf51d](https://github.com/millsp/ts-toolbelt/commit/2fbf51d2692efced910b36999c6c46ed961726a6))
* **object:** add test for new Partial type ([befa7b8](https://github.com/millsp/ts-toolbelt/commit/befa7b873cd2aa0d693f899d47ecb42866993e46))
* **release:** 6.9.1 ([748fe7c](https://github.com/millsp/ts-toolbelt/commit/748fe7c080628dc11475adf6319a6840aa483f78))
* **release:** 6.9.2 ([6772e72](https://github.com/millsp/ts-toolbelt/commit/6772e7214d31f3bd284762bf3dfd1a9d2864bbf5))
* **release:** 6.9.3 ([804e6bf](https://github.com/millsp/ts-toolbelt/commit/804e6bfdeadeaf9a1dd67fae5e9982875c3fe96c))
* **release:** 6.9.4 ([0d5f604](https://github.com/millsp/ts-toolbelt/commit/0d5f604d377f5fa5810280f6d719c581609d8897))
* **release:** 6.9.5 ([fe48b70](https://github.com/millsp/ts-toolbelt/commit/fe48b70ce35cbae8745a50639b76bfe5f17b726b))
* **release:** 6.9.7 ([4f3a036](https://github.com/millsp/ts-toolbelt/commit/4f3a036f5fbc04037febdbdb64cf095e57f8bd19))
* **release:** 6.9.8 ([5f8735b](https://github.com/millsp/ts-toolbelt/commit/5f8735ba59935a10e2014b460411019894973d22))
* **release:** 6.9.9 ([9054f95](https://github.com/millsp/ts-toolbelt/commit/9054f958a39179a2ea7ca6d5c422bc06e2a8e047))
* **scripts:** cleanup ([712411e](https://github.com/millsp/ts-toolbelt/commit/712411e20141eaa299902f0a112ea51b1fa29c2a))
* readme ([3f3bd1e](https://github.com/millsp/ts-toolbelt/commit/3f3bd1e5eaa304ef40225411f22d679709ea0922))
* update deps ([46fc56a](https://github.com/millsp/ts-toolbelt/commit/46fc56a0b0e1a9042931b053808847500e0f5968))
* **docs:** update ([64c1f26](https://github.com/millsp/ts-toolbelt/commit/64c1f263acefb6ea602d58716816c908b8f8ed73))

### [6.9.9](https://github.com/millsp/ts-toolbelt/compare/v6.9.8...v6.9.9) (2020-06-18)


### Others

* **curry:** use undristributed concat ([1c8ff5c](https://github.com/millsp/ts-toolbelt/commit/1c8ff5cf2feb3ce0b7d0fbfc43be8d20f5b73287))

### [6.9.8](https://github.com/millsp/ts-toolbelt/compare/v6.9.7...v6.9.8) (2020-06-18)


### Others

* **curry:** remove drop ([80d5020](https://github.com/millsp/ts-toolbelt/commit/80d50203eeb3b9531a385cb03e0183d99551e648))

### [6.9.7](https://github.com/millsp/ts-toolbelt/compare/v6.9.4...v6.9.7) (2020-06-18)


### Bug Fixes

* version ([3617865](https://github.com/millsp/ts-toolbelt/commit/36178654732305f28ca21dc20b0cf6baaafc5ae1))
* **misc.builtinobject:** missing readonly types ([f8ae740](https://github.com/millsp/ts-toolbelt/commit/f8ae740e7b04e2010cbf3625c6d43daeb82ca2c3))


### Others

* **release:** 6.9.5 ([fe48b70](https://github.com/millsp/ts-toolbelt/commit/fe48b70ce35cbae8745a50639b76bfe5f17b726b))

### [6.9.5](https://github.com/millsp/ts-toolbelt/compare/v6.9.4...v6.9.5) (2020-06-18)


### Bug Fixes

* **misc.builtinobject:** missing readonly types ([f8ae740](https://github.com/millsp/ts-toolbelt/commit/f8ae740e7b04e2010cbf3625c6d43daeb82ca2c3))

### [6.9.4](https://github.com/millsp/ts-toolbelt/compare/v6.9.3...v6.9.4) (2020-05-25)


### Others

* **linit:** disable debug ([390c1a3](https://github.com/millsp/ts-toolbelt/commit/390c1a3954c8e5b5d9540c0f084c625734ff1f36))

### [6.9.3](https://github.com/millsp/ts-toolbelt/compare/v6.9.2...v6.9.3) (2020-05-25)


### Others

* **lint:** debug ([8275a23](https://github.com/millsp/ts-toolbelt/commit/8275a2302b42465109196ed023e835f340ed1a8d))

### [6.9.2](https://github.com/millsp/ts-toolbelt/compare/v6.9.1...v6.9.2) (2020-05-25)


### Others

* **lint:** debug ([2fbf51d](https://github.com/millsp/ts-toolbelt/commit/2fbf51d2692efced910b36999c6c46ed961726a6))

### [6.9.1](https://github.com/millsp/ts-toolbelt/compare/v6.9.0...v6.9.1) (2020-05-25)


### Others

* **scripts:** cleanup ([712411e](https://github.com/millsp/ts-toolbelt/commit/712411e20141eaa299902f0a112ea51b1fa29c2a))
* readme ([3f3bd1e](https://github.com/millsp/ts-toolbelt/commit/3f3bd1e5eaa304ef40225411f22d679709ea0922))
* update deps ([46fc56a](https://github.com/millsp/ts-toolbelt/commit/46fc56a0b0e1a9042931b053808847500e0f5968))

## [6.9.0](https://github.com/millsp/ts-toolbelt/compare/v6.8.9...v6.9.0) (2020-05-21)


### Features

* **a.promise:** a promise type that unwraps ([ba505d4](https://github.com/millsp/ts-toolbelt/commit/ba505d41cb0f53cd0dbc6ae29191b981a7afb0b5))


### Bug Fixes

* **a.promisable:** integrate with Promise ([b14c2fc](https://github.com/millsp/ts-toolbelt/commit/b14c2fc8a52f2bab5baafbc5c32dc41ea5407850))
* **a.promise:** imports ([5054a0b](https://github.com/millsp/ts-toolbelt/commit/5054a0b57e2178092d8c29dd52de55d239ad00a9))


### Others

* cleanup ([5802d6d](https://github.com/millsp/ts-toolbelt/commit/5802d6d6ec2101867d06e38e8fcba92488bdc928))

### [6.8.9](https://github.com/millsp/ts-toolbelt/compare/v6.8.8...v6.8.9) (2020-05-19)


### Others

* readme ([fe3fbdf](https://github.com/millsp/ts-toolbelt/commit/fe3fbdf6477832f7579a953baaaeab6208a1fa1e))

### [6.8.8](https://github.com/millsp/ts-toolbelt/compare/v6.8.7...v6.8.8) (2020-05-19)


### Others

* readme ([6d0719a](https://github.com/millsp/ts-toolbelt/commit/6d0719a9f36ca5e18f48a4bda2f7673eabf73be8))
* readme ([ff89867](https://github.com/millsp/ts-toolbelt/commit/ff898676be13a81ba7efaf08cc0952aeaf856553))

### [6.8.7](https://github.com/millsp/ts-toolbelt/compare/v6.8.6...v6.8.7) (2020-05-19)


### Others

* readme ([578fc5f](https://github.com/millsp/ts-toolbelt/commit/578fc5fcb879185ebc0cc07aa18e441c59a037ee))

### [6.8.6](https://github.com/millsp/ts-toolbelt/compare/v6.8.5...v6.8.6) (2020-05-19)


### Others

* readme ([bc34aa7](https://github.com/millsp/ts-toolbelt/commit/bc34aa7cf003cd8156e654b71f4a54a3552af4ea))

### [6.8.5](https://github.com/millsp/ts-toolbelt/compare/v6.8.4...v6.8.5) (2020-05-19)


### Bug Fixes

* **isliteral:** [#115](https://github.com/millsp/ts-toolbelt/issues/115) ([0cc5c6e](https://github.com/millsp/ts-toolbelt/commit/0cc5c6ef736bb8d94a5fcdc94973f0c423d0c271))


### Others

* **release:** do not release for bug fixes ([7016004](https://github.com/millsp/ts-toolbelt/commit/7016004431c19bffd2900acb8832b0433e475ca5))

### [6.8.4](https://github.com/millsp/ts-toolbelt/compare/v6.8.3...v6.8.4) (2020-05-18)


### Bug Fixes

* **nullables:** [#113](https://github.com/millsp/ts-toolbelt/issues/113) ([e30aa29](https://github.com/millsp/ts-toolbelt/commit/e30aa29164116ef3df3ea1d801b1506152eb7ed1))

### [6.8.3](https://github.com/millsp/ts-toolbelt/compare/v6.8.2...v6.8.3) (2020-05-18)

### [6.8.2](https://github.com/millsp/ts-toolbelt/compare/v6.8.1...v6.8.2) (2020-05-18)


### Others

* readme ([41afdb0](https://github.com/millsp/ts-toolbelt/commit/41afdb0330f574e7eaa17ec34b50ddca36c77a33))

### [6.8.1](https://github.com/millsp/ts-toolbelt/compare/v6.8.0...v6.8.1) (2020-05-18)


### Others

* cleanup ([f4eeb01](https://github.com/millsp/ts-toolbelt/commit/f4eeb0133787b8eb31a50f8e6baf1dc61b357296))
* readme ([58b311e](https://github.com/millsp/ts-toolbelt/commit/58b311ee0e6e3456a2d97f376c53331e08cfe9c8))

## [6.8.0](https://github.com/millsp/ts-toolbelt/compare/v6.7.7...v6.8.0) (2020-05-18)


### Features

* **isliteral:** [#111](https://github.com/millsp/ts-toolbelt/issues/111) ([83a32e6](https://github.com/millsp/ts-toolbelt/commit/83a32e67e751f75b8adb2ff7f195b0f139a01074))


### Others

* cleanup ([ce9fb34](https://github.com/millsp/ts-toolbelt/commit/ce9fb344564d716a9eba01c1acce3df33da3fab4))
* cleanup ([011fb62](https://github.com/millsp/ts-toolbelt/commit/011fb62493054ed3f1e75a74654f6ed13daad10c))

### [6.7.7](https://github.com/millsp/ts-toolbelt/compare/v6.7.6...v6.7.7) (2020-05-14)


### Bug Fixes

* **o.mergeup:** regression intersected types eval to never ([2dc31ae](https://github.com/millsp/ts-toolbelt/commit/2dc31aeed03d1289f52ae50411eecbcdde2d4e4c))

### [6.7.6](https://github.com/millsp/ts-toolbelt/compare/v6.7.5...v6.7.6) (2020-05-14)


### Bug Fixes

* **o.listof:** handle unlimited objects ([64800df](https://github.com/millsp/ts-toolbelt/commit/64800df2bc572eaf84982bf2c27f808826be8e55))
* **o.mergeup:** speed improvement, support ramda/lodash, better accuracy ([6499a7d](https://github.com/millsp/ts-toolbelt/commit/6499a7dd147001e7aeaf1ebb9d80755f80892884))


### Others

* cleanup ([2808df2](https://github.com/millsp/ts-toolbelt/commit/2808df20d28ebaac1d77429d6fc5980f65aa4d03))
* **o.mergeup:** cleanup ([78bd623](https://github.com/millsp/ts-toolbelt/commit/78bd6230e2f0252aa4eed2986eaa7be08efd306e))
* **o.mergeup:** cleanup ([679a8df](https://github.com/millsp/ts-toolbelt/commit/679a8df43f9159ebbb978a64434d9caf81989112))

### [6.7.5](https://github.com/millsp/ts-toolbelt/compare/v6.7.4...v6.7.5) (2020-05-14)


### Bug Fixes

* **a.type:** remove optional ([10550a9](https://github.com/millsp/ts-toolbelt/commit/10550a902281ade3026bd4eb0ea7837fa8202095))

### [6.7.4](https://github.com/millsp/ts-toolbelt/compare/v6.7.3...v6.7.4) (2020-05-11)


### Bug Fixes

* **pipe, compose:** remove unlimited arguments ([95a66b1](https://github.com/millsp/ts-toolbelt/commit/95a66b147c75c7691f55458c6b3980c38832c973))

### [6.7.3](https://github.com/millsp/ts-toolbelt/compare/v6.7.2...v6.7.3) (2020-05-11)


### Others

* readme ([32642cc](https://github.com/millsp/ts-toolbelt/commit/32642cc1d08a41325e0726f93022d97c975e03f5))
* readme ([cb9b585](https://github.com/millsp/ts-toolbelt/commit/cb9b585156fe13796fcb1adb5ead7075103d41c8))

### [6.7.2](https://github.com/millsp/ts-toolbelt/compare/v6.7.1...v6.7.2) (2020-05-08)


### Bug Fixes

* **build:** restore output ([1aa3e32](https://github.com/millsp/ts-toolbelt/commit/1aa3e32513dea134234de562445f8394b8f9a028))

### [6.7.1](https://github.com/millsp/ts-toolbelt/compare/v6.7.0...v6.7.1) (2020-05-08)


### Bug Fixes

* **build:** added lib to build types ([5797d70](https://github.com/millsp/ts-toolbelt/commit/5797d7018857f88a12cd90cea490389fd7c806f2))

## [6.7.0](https://github.com/millsp/ts-toolbelt/compare/v6.6.2...v6.7.0) (2020-05-08)


### Features

* **builtinobject:** types for standard built-in objs ([fc6762c](https://github.com/millsp/ts-toolbelt/commit/fc6762cf47997eb968f49d75d77723d2c6bcdc35))


### Bug Fixes

* **compilation:** set esnext ([9deb23b](https://github.com/millsp/ts-toolbelt/commit/9deb23bc2bf8594e006846b4e7bc8947361a8277))


### Others

* **cleanup:** remove unused files ([937c269](https://github.com/millsp/ts-toolbelt/commit/937c2690c562df83a469d25241a0185db73d2d43))

### [6.6.2](https://github.com/millsp/ts-toolbelt/compare/v6.6.1...v6.6.2) (2020-05-02)


### Bug Fixes

* **a.type:** simplify, remove symbol ([bbc62f3](https://github.com/millsp/ts-toolbelt/commit/bbc62f3ee34b72ec7c6229f8fba07a7d420c6e1c))


### Others

* **l.unnest:** useless distribution ([cda0554](https://github.com/millsp/ts-toolbelt/commit/cda05548fb74e481135ee47dc198121ad09d2d68))
* **release:** fix amd and symbol ([666a508](https://github.com/millsp/ts-toolbelt/commit/666a508733516e6328bdb3675daa62231e6285d7))

### [6.6.1](https://github.com/millsp/ts-toolbelt/compare/v6.6.0...v6.6.1) (2020-05-02)


### Bug Fixes

* error with target and symbol ([cfd4f22](https://github.com/millsp/ts-toolbelt/commit/cfd4f22a417c92b59ac0a1412db01431f587041e))

## [6.6.0](https://github.com/millsp/ts-toolbelt/compare/v6.5.1...v6.6.0) (2020-05-02)


### Features

* **l.flatten, l.unnest:** strict and limit options ([bf1b021](https://github.com/millsp/ts-toolbelt/commit/bf1b02181030aa1ab1f3a692605bc046a0b46c83))

### [6.5.1](https://github.com/millsp/ts-toolbelt/compare/v6.5.0...v6.5.1) (2020-04-18)


### Bug Fixes

* **atleast:** edge case optional union objects ([ae92869](https://github.com/millsp/ts-toolbelt/commit/ae9286971a8cfe32813d2ba37181c3f10f6e756e))

## [6.5.0](https://github.com/millsp/ts-toolbelt/compare/v6.4.2...v6.5.0) (2020-04-17)


### Features

* **atleast:** mark fields to be atleast required ([33f2adf](https://github.com/millsp/ts-toolbelt/commit/33f2adf717d045d1cf1c457b5f57f9c1adcb7685))


### Bug Fixes

* **atleast:** simplify ([2ac92f0](https://github.com/millsp/ts-toolbelt/commit/2ac92f06079ce3ae2742af79e6b81091a28a154b))


### Others

* update `any` to `Key` ([654cecb](https://github.com/millsp/ts-toolbelt/commit/654cecb88e981bf63c6669052d8d819dd9458387))
* **number:** missing map information ([3324276](https://github.com/millsp/ts-toolbelt/commit/33242762febf8f7f7b3e2695d82ec9cdf8649c5b))

### [6.4.2](https://github.com/millsp/ts-toolbelt/compare/v6.4.1...v6.4.2) (2020-04-10)


### Bug Fixes

* **iteration, maps:** optimized bigger iteration maps ([6ec2bdc](https://github.com/millsp/ts-toolbelt/commit/6ec2bdc062507c2d257d38e1f0826731e6418bf3))

### [6.4.1](https://github.com/millsp/ts-toolbelt/compare/v6.4.0...v6.4.1) (2020-04-10)


### Bug Fixes

* **ts-3.5:** compilation ([0dd5671](https://github.com/millsp/ts-toolbelt/commit/0dd5671170a4c31d13b8d0606ca2b4fdbea39b1e))


### Others

* **todo:** make cleanup to be done ([42f6051](https://github.com/millsp/ts-toolbelt/commit/42f605181272a6f3c3289b5f4d9aa8e19de9ec3c))

## [6.4.0](https://github.com/millsp/ts-toolbelt/compare/v6.3.13...v6.4.0) (2020-04-10)


### Features

* **iteration:** allow to take custom maps ([c68c6d8](https://github.com/millsp/ts-toolbelt/commit/c68c6d805c692de124d921df9d3e7f3045edd6cd))
* **number:** number maps (ie. math for pixels) ([5297410](https://github.com/millsp/ts-toolbelt/commit/5297410af7f52084f56f0da2a1f60d9487c579e5))

### [6.3.13](https://github.com/millsp/ts-toolbelt/compare/v6.3.12...v6.3.13) (2020-04-10)


### Bug Fixes

* **distribution, number:** distribution on numbers ([41b2a89](https://github.com/millsp/ts-toolbelt/commit/41b2a89b6d06983d0169b6a6f0d6eb310c7707e5))

### [6.3.12](https://github.com/millsp/ts-toolbelt/compare/v6.3.11...v6.3.12) (2020-04-04)


### Bug Fixes

* **repeat:** possibly non-terminating condition ([3b37596](https://github.com/millsp/ts-toolbelt/commit/3b37596648086de4b43823b38caddee22769fa71))

### [6.3.11](https://github.com/millsp/ts-toolbelt/compare/v6.3.10...v6.3.11) (2020-04-02)


### Others

* readme ([bd35739](https://github.com/millsp/ts-toolbelt/commit/bd357392e4064377473ed6437a8c39e32a4b629a))
* readme ([256adef](https://github.com/millsp/ts-toolbelt/commit/256adef135bee64b78c42b900840865b35606eaf))

### [6.3.10](https://github.com/millsp/ts-toolbelt/compare/v6.3.9...v6.3.10) (2020-04-02)


### Bug Fixes

* **scripts:** mkdir alreay exists ([f46e100](https://github.com/millsp/ts-toolbelt/commit/f46e100444ed4e4948c4bd41858a799fced9d713))


### Others

* **community.includesdeep:** first community entry [#99](https://github.com/millsp/ts-toolbelt/issues/99) ([8527025](https://github.com/millsp/ts-toolbelt/commit/8527025782b6206482f17f94a341e050be430b1c))

### [6.3.9](https://github.com/millsp/ts-toolbelt/compare/v6.3.8...v6.3.9) (2020-04-01)


### Others

* readme ([b6ed7c0](https://github.com/millsp/ts-toolbelt/commit/b6ed7c0d7f49fd6714a6788161e1890e4864f98a))
* **curry:** rollback ([7077391](https://github.com/millsp/ts-toolbelt/commit/70773915f7f7446595487695cdcaa11010b6afdc))

### [6.3.8](https://github.com/millsp/ts-toolbelt/compare/v6.3.7...v6.3.8) (2020-04-01)


### Bug Fixes

* **ts-3.9:** bugs caught by ts 3.9 ([f853aef](https://github.com/millsp/ts-toolbelt/commit/f853aef6315f5e48397b5b895013ec383251847c))

### [6.3.7](https://github.com/millsp/ts-toolbelt/compare/v6.3.6...v6.3.7) (2020-04-01)


### Bug Fixes

* **release:** bugfixes also are releases ([9881b4a](https://github.com/millsp/ts-toolbelt/commit/9881b4a7ccf850d3ee19f70c3eefbe8dbb0b058a))


### Others

* **curry:** reduce memory footprint ([45b7b81](https://github.com/millsp/ts-toolbelt/commit/45b7b819c731d7f836ba00cf527b062b7bd3b054))

### [6.3.6](https://github.com/millsp/ts-toolbelt/compare/v6.3.5...v6.3.6) (2020-03-10)


### Bug Fixes

* **equals:** fix broken null unions [#97](https://github.com/millsp/ts-toolbelt/issues/97) ([91aef20](https://github.com/millsp/ts-toolbelt/commit/91aef20a67da686fda397051f467b00a43db6591))

### [6.3.5](https://github.com/millsp/ts-toolbelt/compare/v6.3.4...v6.3.5) (2020-02-26)


### Bug Fixes

* **mergeup:** revert faulty optimization ([336f026](https://github.com/millsp/ts-toolbelt/commit/336f0266d2983ab45d2c802fc850065c84fdfa0d))

### [6.3.4](https://github.com/millsp/ts-toolbelt/compare/v6.3.3...v6.3.4) (2020-02-26)


### Others

* **mergeup:** removed useless mapped loop ([8d72919](https://github.com/millsp/ts-toolbelt/commit/8d729190d8bd7065c0bc9d113350b7522d3bf1bf))

### [6.3.3](https://github.com/millsp/ts-toolbelt/compare/v6.3.2...v6.3.3) (2020-02-26)


### Bug Fixes

* fixes type intersections [#95](https://github.com/millsp/ts-toolbelt/issues/95) [#96](https://github.com/millsp/ts-toolbelt/issues/96) ([aff74d8](https://github.com/millsp/ts-toolbelt/commit/aff74d83310ef01e74045a9fbd575c35366a287a))


### Others

* indent ([336cb06](https://github.com/millsp/ts-toolbelt/commit/336cb06a816529c3777d2d61056bf94fed7547f7))

### [6.3.2](https://github.com/millsp/ts-toolbelt/compare/v6.3.1...v6.3.2) (2020-02-23)


### Others

* **type:** improvements suggested in [#94](https://github.com/millsp/ts-toolbelt/issues/94) ([c1ccf3d](https://github.com/millsp/ts-toolbelt/commit/c1ccf3d4c465e6315e4b536e8c51b46cba7e3d2b))

### [6.3.1](https://github.com/millsp/ts-toolbelt/compare/v6.3.0...v6.3.1) (2020-02-20)


### Bug Fixes

* **path:** fixes [#93](https://github.com/millsp/ts-toolbelt/issues/93) ([cdf8069](https://github.com/millsp/ts-toolbelt/commit/cdf8069a1958461bec4dc684b68f789826eb4c37))


### Others

* **path:** missing option ([a5dfe12](https://github.com/millsp/ts-toolbelt/commit/a5dfe122803ddf2460cc1d04d19c43415c8c5175))

## [6.3.0](https://github.com/millsp/ts-toolbelt/compare/v6.2.0...v6.3.0) (2020-02-16)


### Features

* **compact:** multiple merging at once ([460d9ea](https://github.com/millsp/ts-toolbelt/commit/460d9eaca50187f194a8174d91044da3357c9ba5))

## [6.2.0](https://github.com/millsp/ts-toolbelt/compare/v6.1.13...v6.2.0) (2020-02-13)


### Features

* **assign:** deep option ([f19fed0](https://github.com/millsp/ts-toolbelt/commit/f19fed09570b38734ab6e4c6c8a364a1f259a7a3))

### [6.1.13](https://github.com/millsp/ts-toolbelt/compare/v6.1.12...v6.1.13) (2020-01-31)


### Bug Fixes

* **l.objectof:** not handling const tuples ([7dd198c](https://github.com/millsp/ts-toolbelt/commit/7dd198c4fa44af8fcfa21f5bb8362f7fc0742e3d))


### Others

* cleanup ([f88c256](https://github.com/millsp/ts-toolbelt/commit/f88c2563e8a4cdf80cba440c53bd5af78cf22a26))

### [6.1.12](https://github.com/millsp/ts-toolbelt/compare/v6.1.11...v6.1.12) (2020-01-31)


### Bug Fixes

* **l.unionize:** was never tested ([f4eb8d3](https://github.com/millsp/ts-toolbelt/commit/f4eb8d31e928f3aedc4914d966d84bd7aa70ad90))


### Others

* cleanup ([77ad5f6](https://github.com/millsp/ts-toolbelt/commit/77ad5f6b02c3f6bc51c05524984a0e5dc3df0c37))

### [6.1.11](https://github.com/millsp/ts-toolbelt/compare/v6.1.10...v6.1.11) (2020-01-30)


### Others

* fix edge ([2a60c37](https://github.com/millsp/ts-toolbelt/commit/2a60c37fe88cd4c909cb2fc5317992ed466b067b))

### [6.1.10](https://github.com/millsp/ts-toolbelt/compare/v6.1.8...v6.1.10) (2020-01-30)


### Others

* deploy test ([55364f4](https://github.com/millsp/ts-toolbelt/commit/55364f47b66a54faa724d7b8dcbe728ee2fe40cf))

### [6.1.8](https://github.com/millsp/ts-toolbelt/compare/v6.1.7...v6.1.8) (2020-01-30)


### Others

* fix config ([76ad1e7](https://github.com/millsp/ts-toolbelt/commit/76ad1e70f4bf7923e6ce19a0332b37d39cf516ad))

### [6.1.7](https://github.com/millsp/ts-toolbelt/compare/v6.1.6...v6.1.7) (2020-01-23)


### Bug Fixes

* **o.path:** unnecessary distribution ([e64e7ad](https://github.com/millsp/ts-toolbelt/commit/e64e7ada991736b16bad09ae411549bce506d876))

### [6.1.6](https://github.com/millsp/ts-toolbelt/compare/v6.1.5...v6.1.6) (2020-01-09)


### Others

* pathvalid ([e73dda8](https://github.com/millsp/ts-toolbelt/commit/e73dda838569e3c07c8b14658e30b2a4936c4111))
* **o.pathvalid:** simpler impl ([811892a](https://github.com/millsp/ts-toolbelt/commit/811892a29ed1c182975224242409889db39b9794))

### [6.1.5](https://github.com/millsp/ts-toolbelt/compare/v6.1.4...v6.1.5) (2020-01-01)


### Bug Fixes

* broken tests ([08738eb](https://github.com/millsp/ts-toolbelt/commit/08738eb385730f3b47fa78110136f1addf2f3487))


### Others

* cleanup ([cfaaac7](https://github.com/millsp/ts-toolbelt/commit/cfaaac787d3d897f5ae7f3e09a492098dffa3e8b))
* delete `HasAll`, upgrade `Has` ([9b2dfe8](https://github.com/millsp/ts-toolbelt/commit/9b2dfe8d777dde87550da690aa147f0f9090a582))
* update ([954f240](https://github.com/millsp/ts-toolbelt/commit/954f240373299cc0d02ab47cebc2360e7674e6df))
* update ([adff960](https://github.com/millsp/ts-toolbelt/commit/adff960e74d39f91c671648144116df4ceeafcd2))
* **o.at:** clean horrible impl ([f9dbb46](https://github.com/millsp/ts-toolbelt/commit/f9dbb4618dbe79fbcd1ad0363d6c77bd3cc63530))

### [6.1.4](https://github.com/millsp/ts-toolbelt/compare/v6.1.3...v6.1.4) (2019-12-31)


### Bug Fixes

* types altering expressions [#84](https://github.com/millsp/ts-toolbelt/issues/84) ([e1bf10f](https://github.com/millsp/ts-toolbelt/commit/e1bf10f3ea6a354da2cd6d4eb45960aa1cd2df26))

### [6.1.3](https://github.com/millsp/ts-toolbelt/compare/v6.1.2...v6.1.3) (2019-12-30)


### Bug Fixes

* **l.update:** handle variant lists ([1d53b1a](https://github.com/millsp/ts-toolbelt/commit/1d53b1a937cb87914f93b0ca1befd21c9e668440))


### Others

* cleanup ([ff598b3](https://github.com/millsp/ts-toolbelt/commit/ff598b39eb6d67697cee7029674dfd1faa879fe0))

### [6.1.2](https://github.com/millsp/ts-toolbelt/compare/v6.1.1...v6.1.2) (2019-12-30)


### Bug Fixes

* **o.p.update:** not creating fields ([1772c9a](https://github.com/millsp/ts-toolbelt/commit/1772c9ace3619a0c3f42b1ae92080188614a5d20))

### [6.1.1](https://github.com/millsp/ts-toolbelt/compare/v6.1.0...v6.1.1) (2019-12-30)


### Bug Fixes

* typo ([6281878](https://github.com/millsp/ts-toolbelt/commit/6281878af7ca9016c918001f32bd663ea4771901))

## [6.1.0](https://github.com/millsp/ts-toolbelt/compare/v6.0.7...v6.1.0) (2019-12-30)


### Features

* **o.p:** add support for arrays (excluding Update) ([2395201](https://github.com/millsp/ts-toolbelt/commit/239520137d69ea9f3ac24494d8ee3af9efbcaeb3)), closes [millsp/ts-toolbelt#80](https://github.com/millsp/ts-toolbelt/issues/80)


### Others

* change comment format ([eb3cc99](https://github.com/millsp/ts-toolbelt/commit/eb3cc99eacc8139690bdb9e25d7f3392e3f46e83))
* cleanup ([40a5b29](https://github.com/millsp/ts-toolbelt/commit/40a5b298d3c8eb27955ed1cfcd2b09f992bcd123))
* cleanup ([bd2b4ea](https://github.com/millsp/ts-toolbelt/commit/bd2b4eaf1f640a4110a1eee2c15eaadb674872a5))
* cleanup ([84dca82](https://github.com/millsp/ts-toolbelt/commit/84dca828c8f2351da21dc54968a20fe145770db3))

### [6.0.7](https://github.com/millsp/ts-toolbelt/compare/v7.0.0...v6.0.7) (2019-12-30)


### Others

* version ([d892bd0](https://github.com/millsp/ts-toolbelt/commit/d892bd05ba613c906a266d164d5824f5650e8134))

## [7.0.0](https://github.com/millsp/ts-toolbelt/compare/v6.0.4...v7.0.0) (2019-12-30)


### ⚠ BREAKING CHANGES

* **distribution:** This will affect users using O.P.Update to modify paths
which contain more than one value.

### Features

* added Primitive ([c85c4db](https://github.com/millsp/ts-toolbelt/commit/c85c4db2962e0fa4e19c564a1c4306e3ce1bb529))
* **o.p:** add O.P.Record ([4335679](https://github.com/millsp/ts-toolbelt/commit/4335679a976977ef8b036a062dd2df0fcd73b344))


### Bug Fixes

* **distribution:** fix O.P.Update distribution ([1ccf432](https://github.com/millsp/ts-toolbelt/commit/1ccf432b9635a77c350e48520b4e20d53f12e625))
* **l.update:** not working on empty lists ([fd16879](https://github.com/millsp/ts-toolbelt/commit/fd168796f5ddd6b019f14c1cccf2e00526668719))


### Others

* added diagnostics for tests ([0e1d6fa](https://github.com/millsp/ts-toolbelt/commit/0e1d6fa3f9acffeaa9542b19c0013af3586c215b))
* ramda wip ([e745031](https://github.com/millsp/ts-toolbelt/commit/e74503174d65cae6208d7cec606e68cea0fa059e))

### [6.0.4](https://github.com/millsp/ts-toolbelt/compare/v6.0.3...v6.0.4) (2019-12-29)


### Bug Fixes

* **l.update:** allow to update non-existing fields ([22e6568](https://github.com/millsp/ts-toolbelt/commit/22e6568d22b01df8122459af854b3196078b8911))
* **list, keys:** list util now handle number keys ([4642629](https://github.com/millsp/ts-toolbelt/commit/4642629346abab764ac61f613276e29cb4c7c6a2))
* **o.either:** fix distribution ([ae1ab94](https://github.com/millsp/ts-toolbelt/commit/ae1ab940d1e9c02e60bb3f53b7f77990796a71ba))


### Others

* **a.numberof:** add internal feature ([ebcd34b](https://github.com/millsp/ts-toolbelt/commit/ebcd34bf1290e3a397c20bee2729e8c7f3a8b15e))

### [6.0.3](https://github.com/millsp/ts-toolbelt/compare/v6.0.2...v6.0.3) (2019-12-29)


### Others

* fix default param problem ([25cabf8](https://github.com/millsp/ts-toolbelt/commit/25cabf83364dfb7acd9e2a4a49d95d60c2371659))
* readme ([5c8cf07](https://github.com/millsp/ts-toolbelt/commit/5c8cf074681b6db2307298b2d7bd2c32a738f76c))

### [6.0.2](https://github.com/millsp/ts-toolbelt/compare/v6.0.1...v6.0.2) (2019-12-29)


### Bug Fixes

* excessive dristribution ([b81a65f](https://github.com/millsp/ts-toolbelt/commit/b81a65fc82f372e436207cc75f7e629db7d91b07))


### Others

* cleanup ([7b7dbc9](https://github.com/millsp/ts-toolbelt/commit/7b7dbc90f34ca9d91ab0cc2ce3d400b6eda82700))
* cleanup ([30fe596](https://github.com/millsp/ts-toolbelt/commit/30fe59626cb300049ad2b5ccba3c4e42972123d9))
* cleanup ([5b24ab6](https://github.com/millsp/ts-toolbelt/commit/5b24ab6bba0268f4c9dbfbe7b548084fa1f280d8))
* complete missing docs ([d86c7b1](https://github.com/millsp/ts-toolbelt/commit/d86c7b1d8ca0a2659543ea5da1e05d48a670655f))
* distribution ([7fa1a0f](https://github.com/millsp/ts-toolbelt/commit/7fa1a0f5f19bb72173c8723bb60b33dcd3ace61a))
* distribution optimization ([1165f2c](https://github.com/millsp/ts-toolbelt/commit/1165f2c37e01d2f6426bc7ac5bdbbf2a059cd6ba))
* export non-distrib types ([f58f8b1](https://github.com/millsp/ts-toolbelt/commit/f58f8b183df3ebf6f9394cabd473cfb5a1fb5d9a))
* remove unsused alias ([acf83d1](https://github.com/millsp/ts-toolbelt/commit/acf83d110de855b2e1601800099f8752955d1f96))
* simplify indexed conditions ([3af8f83](https://github.com/millsp/ts-toolbelt/commit/3af8f836301e954f52bcfe2e23156970d20b39ed))
* update notes ([ead64c3](https://github.com/millsp/ts-toolbelt/commit/ead64c33ea1756047f30532f3a345746cb5bb9b7))
* update notes ([dad7925](https://github.com/millsp/ts-toolbelt/commit/dad79253b0061ab5c4ef8ece79a4fb18f5b1fd47))

### [6.0.1](https://github.com/millsp/ts-toolbelt/compare/v6.0.0...v6.0.1) (2019-12-22)


### Bug Fixes

* typo ([78d8d84](https://github.com/millsp/ts-toolbelt/commit/78d8d842350aa1c0e06ffb3c14cb6e94d2c8a9c8))

## [6.0.0](https://github.com/millsp/ts-toolbelt/compare/v4.14.6...v6.0.0) (2019-12-22)


### ⚠ BREAKING CHANGES

* **api:** 

### Features

* **l.shortest:** find shortest list ([abafc34](https://github.com/millsp/ts-toolbelt/commit/abafc34ae8fae5b7ba6897990a96533d0831e044))
* **o.p.update:** use abritrary paths (creates on the fly) ([1904812](https://github.com/millsp/ts-toolbelt/commit/190481231abf7a348fae989dba2a361d3df3e39e))
* **recursives:** allowing distribution ([bd1c891](https://github.com/millsp/ts-toolbelt/commit/bd1c8914265d2206998348537dedbf9147fb1c50))
* mapped types now all distribute ([680efd9](https://github.com/millsp/ts-toolbelt/commit/680efd988ef837d8d39fda09251a2a9eee2f3b08))


### Bug Fixes

* **distribution:** keys types can now distribute ([617f19a](https://github.com/millsp/ts-toolbelt/commit/617f19ad1a33ce1c6e0b736c6f4e526e1fe017cc))
* **distributivity:** little mistakes ([73b8948](https://github.com/millsp/ts-toolbelt/commit/73b8948ea1d49e05ae8c63b17926eda4cb04e9bb))
* wrong export ([434f0c6](https://github.com/millsp/ts-toolbelt/commit/434f0c62c8031e45fe12c89af200f425203a6d04))


### Others

* **release:** 5.0.0 ([4295200](https://github.com/millsp/ts-toolbelt/commit/42952001b8ebb869c9cc7e4d5b98fc0065ba7bb2))
* update readme ([178dd48](https://github.com/millsp/ts-toolbelt/commit/178dd48f7f3f1eb9e666dfb83d598500d8149bd5))
* **list:** added suported tuple types ([2954a09](https://github.com/millsp/ts-toolbelt/commit/2954a0955ce9af6acb345ed1e8328e145ad30475))
* cleanup ([0d17972](https://github.com/millsp/ts-toolbelt/commit/0d17972698887aa3d74aa9e15d71d6c7d6440b2e))
* **recursives:** cleanup & optimize - phase 1 ([4242a13](https://github.com/millsp/ts-toolbelt/commit/4242a1300e031b5bb60062ef342057ca94da2ccf))
* cleanup ([2d1580d](https://github.com/millsp/ts-toolbelt/commit/2d1580da61369576158c5f0972d0c2a7ff10175e))
* cleanup ([9fbf2ab](https://github.com/millsp/ts-toolbelt/commit/9fbf2ab952c86579f43b5024862658cb708d2b41))
* fix misformatted [@return](https://github.com/return) tags ([30e191a](https://github.com/millsp/ts-toolbelt/commit/30e191a72fc39b436c5dc89aa59cc82606ec4cdf))
* prepare for more optimizations ([1ed033d](https://github.com/millsp/ts-toolbelt/commit/1ed033dfafeced0bcbdc87af37530c6e43c2ce43))
* update notes ([400fde1](https://github.com/millsp/ts-toolbelt/commit/400fde1fb9476e7fbb03ddda061e83de21461d02))
* **api:** delete legacy api ([ac39bc1](https://github.com/millsp/ts-toolbelt/commit/ac39bc1c25308bed2eb577a4f0ee194f80eb2a64))
* cleanup ([a83b90e](https://github.com/millsp/ts-toolbelt/commit/a83b90ecd781f350510cc59b342b0c3e70d6ecf6))
* cleanup ([dfb2506](https://github.com/millsp/ts-toolbelt/commit/dfb25067cfb5bb69b0d5102d1b2293154b9ffb19))
* cleanup ([78660f2](https://github.com/millsp/ts-toolbelt/commit/78660f24c2ed7ac8080a77af6b3850a921ee1a07))
* more optimizations ([32c27fc](https://github.com/millsp/ts-toolbelt/commit/32c27fc096b09f3d91120b822c9d3481074789cc))
* update notes ([f9f3c73](https://github.com/millsp/ts-toolbelt/commit/f9f3c73c806303b167618954b6cf39f18bd10126))

## [5.0.0](https://github.com/millsp/ts-toolbelt/compare/v4.14.6...v5.0.0) (2019-12-22)


### ⚠ BREAKING CHANGES

* **api:** 

### Features

* **l.shortest:** find shortest list ([abafc34](https://github.com/millsp/ts-toolbelt/commit/abafc34ae8fae5b7ba6897990a96533d0831e044))
* **o.p.update:** use abritrary paths (creates on the fly) ([1904812](https://github.com/millsp/ts-toolbelt/commit/190481231abf7a348fae989dba2a361d3df3e39e))
* **recursives:** allowing distribution ([bd1c891](https://github.com/millsp/ts-toolbelt/commit/bd1c8914265d2206998348537dedbf9147fb1c50))
* mapped types now all distribute ([680efd9](https://github.com/millsp/ts-toolbelt/commit/680efd988ef837d8d39fda09251a2a9eee2f3b08))


### Bug Fixes

* **distribution:** keys types can now distribute ([617f19a](https://github.com/millsp/ts-toolbelt/commit/617f19ad1a33ce1c6e0b736c6f4e526e1fe017cc))
* **distributivity:** little mistakes ([73b8948](https://github.com/millsp/ts-toolbelt/commit/73b8948ea1d49e05ae8c63b17926eda4cb04e9bb))
* wrong export ([434f0c6](https://github.com/millsp/ts-toolbelt/commit/434f0c62c8031e45fe12c89af200f425203a6d04))


### Others

* update readme ([178dd48](https://github.com/millsp/ts-toolbelt/commit/178dd48f7f3f1eb9e666dfb83d598500d8149bd5))
* **api:** delete legacy api ([ac39bc1](https://github.com/millsp/ts-toolbelt/commit/ac39bc1c25308bed2eb577a4f0ee194f80eb2a64))
* **list:** added suported tuple types ([2954a09](https://github.com/millsp/ts-toolbelt/commit/2954a0955ce9af6acb345ed1e8328e145ad30475))
* cleanup ([0d17972](https://github.com/millsp/ts-toolbelt/commit/0d17972698887aa3d74aa9e15d71d6c7d6440b2e))
* **recursives:** cleanup & optimize - phase 1 ([4242a13](https://github.com/millsp/ts-toolbelt/commit/4242a1300e031b5bb60062ef342057ca94da2ccf))
* cleanup ([2d1580d](https://github.com/millsp/ts-toolbelt/commit/2d1580da61369576158c5f0972d0c2a7ff10175e))
* cleanup ([a83b90e](https://github.com/millsp/ts-toolbelt/commit/a83b90ecd781f350510cc59b342b0c3e70d6ecf6))
* cleanup ([dfb2506](https://github.com/millsp/ts-toolbelt/commit/dfb25067cfb5bb69b0d5102d1b2293154b9ffb19))
* cleanup ([78660f2](https://github.com/millsp/ts-toolbelt/commit/78660f24c2ed7ac8080a77af6b3850a921ee1a07))
* cleanup ([9fbf2ab](https://github.com/millsp/ts-toolbelt/commit/9fbf2ab952c86579f43b5024862658cb708d2b41))
* fix misformatted [@return](https://github.com/return) tags ([30e191a](https://github.com/millsp/ts-toolbelt/commit/30e191a72fc39b436c5dc89aa59cc82606ec4cdf))
* more optimizations ([32c27fc](https://github.com/millsp/ts-toolbelt/commit/32c27fc096b09f3d91120b822c9d3481074789cc))
* prepare for more optimizations ([1ed033d](https://github.com/millsp/ts-toolbelt/commit/1ed033dfafeced0bcbdc87af37530c6e43c2ce43))
* update notes ([400fde1](https://github.com/millsp/ts-toolbelt/commit/400fde1fb9476e7fbb03ddda061e83de21461d02))
* update notes ([f9f3c73](https://github.com/millsp/ts-toolbelt/commit/f9f3c73c806303b167618954b6cf39f18bd10126))

### [4.14.6](https://github.com/millsp/ts-toolbelt/compare/v4.14.5...v4.14.6) (2019-12-19)


### Bug Fixes

* **a.omit, a.pick:** accept any input ([7facdde](https://github.com/millsp/ts-toolbelt/commit/7facdded3f78db091b741edc7eb3c8c5fbe60b22))
* **o.update:** handle wide keys properly ([057d53e](https://github.com/millsp/ts-toolbelt/commit/057d53ed9ffc4c5f2b946a065924d1e47192167f))


### Others

*  make pick & omit distributive by default ([51b5175](https://github.com/millsp/ts-toolbelt/commit/51b5175f9e074dea63fe45e5a7edb9f29c9fca0d))
* add missing data type for class ([2536b57](https://github.com/millsp/ts-toolbelt/commit/2536b57d74f31bbabae4ea6e1dd53bfa6267b3b8))
* added Union to parameters ([47f07ce](https://github.com/millsp/ts-toolbelt/commit/47f07ced2f82b61a98516c5c250d5b1b007e8540))
* cleanup ([c5e57e5](https://github.com/millsp/ts-toolbelt/commit/c5e57e51e29eda9ade1b287b45763ba64a63a7b9))
* move pick & omit out of `Union` to `Any` ([dc03a3c](https://github.com/millsp/ts-toolbelt/commit/dc03a3c998bab61fb55fc4437cf3bec7f3169187))
* optimize ([8df2416](https://github.com/millsp/ts-toolbelt/commit/8df24162fe9ca1e2379b20a92a3bd7634fea299e))
* optimize ([2b00712](https://github.com/millsp/ts-toolbelt/commit/2b0071216dee992972103b69e52b57eb237a8532))
* readme ([5718ee0](https://github.com/millsp/ts-toolbelt/commit/5718ee02f76307464002e249cb575370555f8596))
* uniform data types ([ef203c6](https://github.com/millsp/ts-toolbelt/commit/ef203c6dd0d59542bc5c58ca1fca34ae9e9c868c))
* update notes ([67e0d03](https://github.com/millsp/ts-toolbelt/commit/67e0d03047a72d6d7b13c58f1cba1eaadafc58ae))
* update notes ([1c87e5c](https://github.com/millsp/ts-toolbelt/commit/1c87e5c0c0249dc6690b33ac624e95c822c338a9))
* **list:** finish renaming tuple to list ([070e0a2](https://github.com/millsp/ts-toolbelt/commit/070e0a2c247b98b5facb0fec3bbb76f21ce417a0))

### [4.14.5](https://github.com/millsp/ts-toolbelt/compare/v4.14.4...v4.14.5) (2019-12-19)


### Bug Fixes

* **l.at:** missing strict option ([b00ec1c](https://github.com/millsp/ts-toolbelt/commit/b00ec1cfe5423ec49751b6c7cd7dcaad4c0faca6))
* **l.update:** only existing keys can be updated ([9cd8643](https://github.com/millsp/ts-toolbelt/commit/9cd8643ef2b15038f9b5f7cb5d0a5b75f8970d8d))

### [4.14.4](https://github.com/millsp/ts-toolbelt/compare/v4.14.3...v4.14.4) (2019-12-18)


### Bug Fixes

* **strictnullchecks:** maximize compatibility ([70fe9e3](https://github.com/millsp/ts-toolbelt/commit/70fe9e3aa2b622e339d2c87ed81b5a92548b053e))


### Others

* readme ([a796ecc](https://github.com/millsp/ts-toolbelt/commit/a796ecc885496bbe1487243dda767b03f685cba8))
* **list:** disable irrelevant failing test ([2315ff0](https://github.com/millsp/ts-toolbelt/commit/2315ff0eede4d84b685996c579a3dc27033aa512))
* **list:** disable irrelevant failing tests ([09fbd5c](https://github.com/millsp/ts-toolbelt/commit/09fbd5c7970acf11d139274917b5c0d92306b34d))

### [4.14.3](https://github.com/millsp/ts-toolbelt/compare/v4.14.2...v4.14.3) (2019-12-17)


### Bug Fixes

* **update:** can now add fields ([687dd4f](https://github.com/millsp/ts-toolbelt/commit/687dd4ffd925441125e114192f745caf8b4d0803))

### [4.14.2](https://github.com/millsp/ts-toolbelt/compare/v4.14.1...v4.14.2) (2019-12-17)


### Bug Fixes

* **path:** remove legacy paths ([07dee0a](https://github.com/millsp/ts-toolbelt/commit/07dee0abda11c64d04a177f1a8e726edd4873321))

### [4.14.1](https://github.com/millsp/ts-toolbelt/compare/v4.14.0...v4.14.1) (2019-12-17)

## [4.14.0](https://github.com/millsp/ts-toolbelt/compare/v4.13.6...v4.14.0) (2019-12-17)


### Features

* **o.at:** optional loose mode ([bcd18a6](https://github.com/millsp/ts-toolbelt/commit/bcd18a6fe7e6ae8954fa7d8b3ff8eacd9926b598))


### Bug Fixes

* **at:** allow to use any ([8e74f3d](https://github.com/millsp/ts-toolbelt/commit/8e74f3dc5055a4bdea1efa1485f36cc6e08eb494))


### Others

* integrate loose `At` with other utilities ([ac93d75](https://github.com/millsp/ts-toolbelt/commit/ac93d75b1dbc7cc2f619c01e1fd7eea8be719ec6))
* moved optional params to lhs ([784ae65](https://github.com/millsp/ts-toolbelt/commit/784ae65087c4c5297f8d693ec01a6732007e9a91))

### [4.13.6](https://github.com/millsp/ts-toolbelt/compare/v4.13.5...v4.13.6) (2019-12-16)


### Bug Fixes

* **a.x:** string edge cases for curry ([19420b4](https://github.com/millsp/ts-toolbelt/commit/19420b41410442c391cd8577e1fdab81a4db2767))

### [4.13.5](https://github.com/millsp/ts-toolbelt/compare/v4.13.4...v4.13.5) (2019-12-12)


### Bug Fixes

* **curry:** unary cannot be curried related to [#43](https://github.com/millsp/ts-toolbelt/issues/43) ([e226931](https://github.com/millsp/ts-toolbelt/commit/e226931cfddc90e046c2f7dc3ef88a39a981acc5))

### [4.13.4](https://github.com/millsp/ts-toolbelt/compare/v4.13.3...v4.13.4) (2019-12-11)


### Others

* **f.pipe, f.compose:** move P as last typeparam ([9c86f43](https://github.com/millsp/ts-toolbelt/commit/9c86f435175ed15c94c1538a043210ddf5085097))

### [4.13.3](https://github.com/millsp/ts-toolbelt/compare/v4.13.2...v4.13.3) (2019-12-11)


### Others

* cleanup ([a4727e5](https://github.com/millsp/ts-toolbelt/commit/a4727e563bb7db416ab465618e0cd02c9b777988))
* exclude ([c90d6bf](https://github.com/millsp/ts-toolbelt/commit/c90d6bfa6df7dd14c2d0347dbae77433eed143e0))
* moved optional param values to the left ([803bbd8](https://github.com/millsp/ts-toolbelt/commit/803bbd8efdc3eadea893ee0a5db80637f22cc150))

### [4.13.2](https://github.com/millsp/ts-toolbelt/compare/v4.13.1...v4.13.2) (2019-12-11)


### Others

* move Promisable ([e863c43](https://github.com/millsp/ts-toolbelt/commit/e863c43ea9bfea16747fe70702acf7945dd64097))
* readme ([24df3e2](https://github.com/millsp/ts-toolbelt/commit/24df3e27a2ca6d07e7033f52d5292c09a64fcd18))

### [4.13.1](https://github.com/millsp/ts-toolbelt/compare/v4.13.0...v4.13.1) (2019-12-11)


### Others

* better types for pipe/compose ([405fa8e](https://github.com/millsp/ts-toolbelt/commit/405fa8eef2086b2ba70bb5820223b6de390d411d))
* cleanup ([58dc4af](https://github.com/millsp/ts-toolbelt/commit/58dc4afd328a4f1d489785c0b4d5dae27ff1b799))
* cleanup ([e319f6c](https://github.com/millsp/ts-toolbelt/commit/e319f6c3c2c727e58162bdd4fb5da804c469f227))
* cleanup ([1432f07](https://github.com/millsp/ts-toolbelt/commit/1432f075ccee735a88c2a98c385095d24e8b09c9))

## [4.13.0](https://github.com/millsp/ts-toolbelt/compare/v4.12.13...v4.13.0) (2019-12-11)


### Features

* **u.omit/pick:** pick or omit on unions ([d90faa1](https://github.com/millsp/ts-toolbelt/commit/d90faa17f5005e0492babe00c0de69621b72e909))


### Bug Fixes

* **f.pipe/compose:** wrongs async types ([52ef6fd](https://github.com/millsp/ts-toolbelt/commit/52ef6fd51312158dc38d008400689bb9d206f0ec))


### Others

* better types for pipe/compose ([97d4782](https://github.com/millsp/ts-toolbelt/commit/97d47821cc02565b6a8e5ae3a9d76c6170422c6b))
* rename to Omit ([5a46b9a](https://github.com/millsp/ts-toolbelt/commit/5a46b9a22e7fc0c56488938ab9259c9e3af6fd0a))

### [4.12.13](https://github.com/millsp/ts-toolbelt/compare/v4.12.12...v4.12.13) (2019-12-03)


### Others

* cleanup ([cabcf91](https://github.com/millsp/ts-toolbelt/commit/cabcf917a1558e3ef595a443a4e57735d6b18a63))

### [4.12.12](https://github.com/millsp/ts-toolbelt/compare/v4.12.11...v4.12.12) (2019-12-03)


### Bug Fixes

* **a.clean:** error on mapped types [#74](https://github.com/millsp/ts-toolbelt/issues/74) ([d7d5055](https://github.com/millsp/ts-toolbelt/commit/d7d505507b0ab65b5d7e77da20ab285ea7e1446b))


### Others

* **a.clean:** update ([5598b7c](https://github.com/millsp/ts-toolbelt/commit/5598b7ce73f40106a653e0b1ea1e03a78bb1497a))

### [4.12.11](https://github.com/millsp/ts-toolbelt/compare/v4.12.10...v4.12.11) (2019-12-02)


### Others

* readme ([2f0b1ef](https://github.com/millsp/ts-toolbelt/commit/2f0b1ef08364aaddec7dca6a90423f418f88c959))
* readme ([84399ce](https://github.com/millsp/ts-toolbelt/commit/84399ceb39d900095287f2175e701bce373c2041))

### [4.12.10](https://github.com/millsp/ts-toolbelt/compare/v4.12.9...v4.12.10) (2019-12-02)


### Bug Fixes

* **deploy:** restore Ensure ([d020658](https://github.com/millsp/ts-toolbelt/commit/d020658461fad4fb8e355c997506383e3fef715f))

### [4.12.9](https://github.com/millsp/ts-toolbelt/compare/v4.12.8...v4.12.9) (2019-12-02)


### Bug Fixes

* typo ([fbfe638](https://github.com/millsp/ts-toolbelt/commit/fbfe6387c37d723c08a25a24b5f1d618ba591a60))
* **l.objectof:** improved type preservation ([cd690f7](https://github.com/millsp/ts-toolbelt/commit/cd690f7a0270f7fc5632091aa6b1a8c320294fa3))


### Others

* move Clean ([f15add7](https://github.com/millsp/ts-toolbelt/commit/f15add7e93b38f36d37487307bfa5b8bbf977484))
* rename Tuple to List ([2d15763](https://github.com/millsp/ts-toolbelt/commit/2d157633be86b19aa9e5a0b48896cdcd8e9c3fb8))

### [4.12.8](https://github.com/millsp/ts-toolbelt/compare/v4.12.7...v4.12.8) (2019-12-02)


### Bug Fixes

* **t.objectof:** type distribution ([765e94a](https://github.com/millsp/ts-toolbelt/commit/765e94ac8345f3aa8d8888dd2c2b61fd4934a5bb))


### Others

* reformat type distribution ([c825580](https://github.com/millsp/ts-toolbelt/commit/c8255801e853212f669fa40326ff018b2c09fb4a))

### [4.12.7](https://github.com/millsp/ts-toolbelt/compare/v4.12.6...v4.12.7) (2019-12-02)


### Others

* **o.mergeup:** give up tuple handling ([5214d04](https://github.com/millsp/ts-toolbelt/commit/5214d04152413cda69dd5fc25ff12e4c42775f3f))

### [4.12.6](https://github.com/millsp/ts-toolbelt/compare/v4.12.5...v4.12.6) (2019-12-01)


### Bug Fixes

* **clean:** cleaner clean ([cc1d7d6](https://github.com/millsp/ts-toolbelt/commit/cc1d7d6a1317d42f83003bc130ed92e7e9c39a12))

### [4.12.5](https://github.com/millsp/ts-toolbelt/compare/v4.12.4...v4.12.5) (2019-11-30)


### Others

* cleanup ([fcdca7a](https://github.com/millsp/ts-toolbelt/commit/fcdca7a46ff8c8e77690a8d1cbeee53ec852f12a))
* **o.mergeup:** lighter computation ([6856251](https://github.com/millsp/ts-toolbelt/commit/6856251e8089b0fc61995accb937174f63b5c3d6))

### [4.12.4](https://github.com/millsp/ts-toolbelt/compare/v4.12.3...v4.12.4) (2019-11-30)


### Bug Fixes

* **o.mergeup:** overcomputation recursive ([4fbf5d2](https://github.com/millsp/ts-toolbelt/commit/4fbf5d2ea5b2c62d32e99ae66c85317833098ed2))

### [4.12.3](https://github.com/millsp/ts-toolbelt/compare/v4.12.2...v4.12.3) (2019-11-30)


### Bug Fixes

* **o.mergeup:** unsound computation ([49c4ce4](https://github.com/millsp/ts-toolbelt/commit/49c4ce4aacbd582d30c77a3b03341411289a30c7))

### [4.12.2](https://github.com/millsp/ts-toolbelt/compare/v4.12.1...v4.12.2) (2019-11-30)


### Bug Fixes

* **deep:** over-computation on deep types & recursive mapped types ([1b1ac71](https://github.com/millsp/ts-toolbelt/commit/1b1ac71c337619b5a7e75cf8dca2612fdc0e41ff))

### [4.12.1](https://github.com/millsp/ts-toolbelt/compare/v4.12.0...v4.12.1) (2019-11-30)


### Bug Fixes

* **m.json:** working JSON types + tests ([7917679](https://github.com/millsp/ts-toolbelt/commit/7917679be161ab11e532695b998b514de422d246))

## [4.12.0](https://github.com/millsp/ts-toolbelt/compare/v4.11.0...v4.12.0) (2019-11-30)


### Features

* **m.json:** json types ([43c7437](https://github.com/millsp/ts-toolbelt/commit/43c7437453253e1e1a01f5f6d6ffae6a3397bdd7))

## [4.11.0](https://github.com/millsp/ts-toolbelt/compare/v4.10.21...v4.11.0) (2019-11-30)


### Features

* **u.hasall:** check if union contained within union ([81ccfd5](https://github.com/millsp/ts-toolbelt/commit/81ccfd5f43fcb531afb5ce452bf685f09a1794bb))


### Bug Fixes

* **o.clean:** swallowed keys named like array keys ([a8f190c](https://github.com/millsp/ts-toolbelt/commit/a8f190c29de034b2def7d6f8949730bc5e36519d))


### Others

* readme ([c401613](https://github.com/millsp/ts-toolbelt/commit/c40161395d0d94a85055a155e9e07ae7ef2fac32))

### [4.10.21](https://github.com/millsp/ts-toolbelt/compare/v4.10.20...v4.10.21) (2019-11-21)


### Others

* fix errors ([a08aae3](https://github.com/millsp/ts-toolbelt/commit/a08aae3597f5a9c638d0641a5a0881acbeaaf076))
* indent ([8506bb3](https://github.com/millsp/ts-toolbelt/commit/8506bb33c8d394a91be79974cdd9a01c69d110fc))
* readme ([2b2d577](https://github.com/millsp/ts-toolbelt/commit/2b2d577eec0e36c32e80f9ba99f9e7c608dd8ed1))
* readme ([388ffe5](https://github.com/millsp/ts-toolbelt/commit/388ffe54b8538cd14d317aa359159106afa54db1))
* **promisify:** update ([8dc2c03](https://github.com/millsp/ts-toolbelt/commit/8dc2c03125f72b2d6c1bd35def860f358a019516))

### [4.10.20](https://github.com/millsp/ts-toolbelt/compare/v4.10.19...v4.10.20) (2019-11-20)


### Others

* readme ([b60feb7](https://github.com/millsp/ts-toolbelt/commit/b60feb751892553756db1302921bc60ae4f3adf4))

### [4.10.19](https://github.com/millsp/ts-toolbelt/compare/v4.10.18...v4.10.19) (2019-11-20)


### Others

* readme ([ededc94](https://github.com/millsp/ts-toolbelt/commit/ededc9410c14042b15048ddc86cba945b4e705ca))

### [4.10.18](https://github.com/millsp/ts-toolbelt/compare/v4.10.17...v4.10.18) (2019-11-20)


### Others

* readme ([ceec4af](https://github.com/millsp/ts-toolbelt/commit/ceec4afca16697efcdc50f55990bfbb86c77733f))

### [4.10.17](https://github.com/millsp/ts-toolbelt/compare/v4.10.16...v4.10.17) (2019-11-20)


### Others

* readme ([2fa6502](https://github.com/millsp/ts-toolbelt/commit/2fa65020f1862b4980b2ec8ba063c6f578b3f44c))

### [4.10.16](https://github.com/millsp/ts-toolbelt/compare/v4.10.15...v4.10.16) (2019-11-20)


### Others

* readme ([e47ac4a](https://github.com/millsp/ts-toolbelt/commit/e47ac4a2db824fe2d50db25a935a321830f2a2f4))

### [4.10.15](https://github.com/millsp/ts-toolbelt/compare/v4.10.14...v4.10.15) (2019-11-20)


### Others

* readme ([5591c38](https://github.com/millsp/ts-toolbelt/commit/5591c38941182c4a2720c3960d073c4080670d7c))

### [4.10.14](https://github.com/millsp/ts-toolbelt/compare/v4.10.13...v4.10.14) (2019-11-20)


### Others

* **all:** added cross-references, links ([09b57e5](https://github.com/millsp/ts-toolbelt/commit/09b57e5bcfbb2324e8cd5767bb0889fa6f928481))

### [4.10.13](https://github.com/millsp/ts-toolbelt/compare/v4.10.12...v4.10.13) (2019-11-18)


### Others

* readme ([02c25b7](https://github.com/millsp/ts-toolbelt/commit/02c25b7053e12ace1b344a4631b1907fec562e95))
* readme ([f24d761](https://github.com/millsp/ts-toolbelt/commit/f24d7618c57091895b78fda87cf349adf124728f))

### [4.10.12](https://github.com/millsp/ts-toolbelt/compare/v4.10.11...v4.10.12) (2019-11-17)


### Bug Fixes

* **release:** ci not releasing ([3072c91](https://github.com/millsp/ts-toolbelt/commit/3072c91b619a25b0911952642f76707dd3aea617))

### [4.10.11](https://github.com/millsp/ts-toolbelt/compare/v4.10.10...v4.10.11) (2019-11-17)


### Bug Fixes

* **release:** not out folder ([19bf5d0](https://github.com/millsp/ts-toolbelt/commit/19bf5d09aeaed78492ae4eb1d62a63c2c3064e2b))

### [4.10.10](https://github.com/millsp/ts-toolbelt/compare/v4.10.9...v4.10.10) (2019-11-17)


### Others

* readme ([32fd31a](https://github.com/millsp/ts-toolbelt/commit/32fd31aecd5c547c0f5b51d78f60650b1a2aefa4))

### [4.10.9](https://github.com/millsp/ts-toolbelt/compare/v4.10.8...v4.10.9) (2019-11-17)


### Others

* readme ([c749e1a](https://github.com/millsp/ts-toolbelt/commit/c749e1abd703d5023aeea9bf4381f0bcf02967a8))
* **number:** fast number to string ([161f247](https://github.com/millsp/ts-toolbelt/commit/161f247a11ca5c8c9f11e8b2fea76d6277655c73))
* fix tests allowing broken types ([c70f49f](https://github.com/millsp/ts-toolbelt/commit/c70f49f708c213cc5e9641312ef328279a47a289))

### [4.10.8](https://github.com/millsp/ts-toolbelt/compare/v4.10.7...v4.10.8) (2019-11-14)


### Bug Fixes

* **keys:** fix cannot be used to index [#71](https://github.com/millsp/ts-toolbelt/issues/71) ([d39b9a4](https://github.com/millsp/ts-toolbelt/commit/d39b9a4a5b9272035adbee8dcd49e40a7c10f04f))

### [4.10.7](https://github.com/millsp/ts-toolbelt/compare/v4.10.6...v4.10.7) (2019-11-10)


### Others

* readme ([5f97114](https://github.com/millsp/ts-toolbelt/commit/5f97114a8f43124b5f59481167a149135edd8798))

### [4.10.6](https://github.com/millsp/ts-toolbelt/compare/v4.10.5...v4.10.6) (2019-11-10)


### Others

* readme ([7b52fb6](https://github.com/millsp/ts-toolbelt/commit/7b52fb69ad56f715dfeba26e235fb7dc18ccd8c7))

### [4.10.5](https://github.com/millsp/ts-toolbelt/compare/v4.10.4...v4.10.5) (2019-11-10)


### Others

* readme ([b0ac4a3](https://github.com/millsp/ts-toolbelt/commit/b0ac4a312d9f2957449dc0491951b71379249a50))

### [4.10.4](https://github.com/millsp/ts-toolbelt/compare/v4.10.3...v4.10.4) (2019-11-10)


### Others

* **o.mergeup:** example ([96bfab8](https://github.com/millsp/ts-toolbelt/commit/96bfab87a5f2ebc73f1cb027cc3dadff4714449e))

### [4.10.3](https://github.com/millsp/ts-toolbelt/compare/v4.10.2...v4.10.3) (2019-11-09)


### Others

* simpler versions ([ed619f1](https://github.com/millsp/ts-toolbelt/commit/ed619f13e822f7df866d9dbd9b75f49223414574))

### [4.10.2](https://github.com/millsp/ts-toolbelt/compare/v4.10.1...v4.10.2) (2019-11-09)


### Others

* version auto-bump ([4940525](https://github.com/millsp/ts-toolbelt/commit/4940525a2b039c2e34218fa608d0c53f44bfb58a))

### [4.10.1](https://github.com/millsp/ts-toolbelt/compare/v4.10.0...v4.10.1) (2019-11-09)


### Bug Fixes

* version ([8402974](https://github.com/millsp/ts-toolbelt/commit/84029746a1ed5837fc7f14a3676f376dbb89a99d))


### Others

* cleanup ([3def05c](https://github.com/millsp/ts-toolbelt/commit/3def05cbe65d1c4791ca91e41c4184c905d7a05e))

## [4.10.0](https://github.com/millsp/ts-toolbelt/compare/v4.9.32...v4.10.0) (2019-11-06)


### Features

* **misc:** promisable ([a08168f](https://github.com/millsp/ts-toolbelt/commit/a08168fdcf8a36aa72fb59cf3b1a1bda10424606))


### Others

* disable tslint for [#69](https://github.com/millsp/ts-toolbelt/issues/69) ([c58e17f](https://github.com/millsp/ts-toolbelt/commit/c58e17ff8b9b2846b4aefb358676daf88e8a0c81))
* **misc:** expose types ([cf85c0d](https://github.com/millsp/ts-toolbelt/commit/cf85c0d5db77924821a26d90c7d39eb5e286a59b))
* **misc:** expose types ([b633735](https://github.com/millsp/ts-toolbelt/commit/b633735e4f3322ec1cf63a4b2ed05bc06f3a449a))

### [4.9.32](https://github.com/millsp/ts-toolbelt/compare/v4.9.31...v4.9.32) (2019-11-02)


### Others

* **build:** bundle d.ts ([47122d8](https://github.com/millsp/ts-toolbelt/commit/47122d895c1891f3cd21bfedfe92aa2ebaf02237))

### [4.9.31](https://github.com/millsp/ts-toolbelt/compare/v4.9.30...v4.9.31) (2019-11-02)


### Bug Fixes

* broken on ts 3.6.4 [#68](https://github.com/millsp/ts-toolbelt/issues/68) ([c691229](https://github.com/millsp/ts-toolbelt/commit/c6912296a1c4266679e3b4208bb2935794fd6dfb))

### [4.9.30](https://github.com/millsp/ts-toolbelt/compare/v4.9.29...v4.9.30) (2019-11-01)


### Others

* readme ([eb19dca](https://github.com/millsp/ts-toolbelt/commit/eb19dca26543347e2ef667f878fcda318cd8a33c))

### [4.9.29](https://github.com/millsp/ts-toolbelt/compare/v4.9.28...v4.9.29) (2019-11-01)


### Bug Fixes

* **docs:** replace [@internal](https://github.com/internal) with [@hidden](https://github.com/hidden) ([062b51f](https://github.com/millsp/ts-toolbelt/commit/062b51fefb9793d3d944c41bade21b1a739c109a))

### [4.9.28](https://github.com/millsp/ts-toolbelt/compare/v4.9.27...v4.9.28) (2019-11-01)


### Others

* hide impl clutter ([1e5e16d](https://github.com/millsp/ts-toolbelt/commit/1e5e16de5fbe36120f6bbb89052add8daffce848))
* ts next ([dca9156](https://github.com/millsp/ts-toolbelt/commit/dca915639a055920a9772b0b7dc922e5713985a7))

### [4.9.27](https://github.com/millsp/ts-toolbelt/compare/v4.9.26...v4.9.27) (2019-10-31)


### Others

* readme ([ae67bae](https://github.com/millsp/ts-toolbelt/commit/ae67baeae1bafa47b626ee7bd0991fe0479f4c95))

### [4.9.26](https://github.com/millsp/ts-toolbelt/compare/v4.9.25...v4.9.26) (2019-10-31)


### Others

* readme ([7aa53fd](https://github.com/millsp/ts-toolbelt/commit/7aa53fdb18d1c305a1e7e92d2dd76555bc4e5a7d))

### [4.9.25](https://github.com/millsp/ts-toolbelt/compare/v4.9.24...v4.9.25) (2019-10-31)


### Others

* readme ([0a51906](https://github.com/millsp/ts-toolbelt/commit/0a51906fa7ee5108cd2f51aa4358157bb3ace433))

### [4.9.24](https://github.com/millsp/ts-toolbelt/compare/v4.9.23...v4.9.24) (2019-10-31)


### Others

* readme ([1a02ba6](https://github.com/millsp/ts-toolbelt/commit/1a02ba6ac1b6c1aebb525942754eab23462bdc0e))

### [4.9.23](https://github.com/millsp/ts-toolbelt/compare/v4.9.22...v4.9.23) (2019-10-31)


### Bug Fixes

* **compat:** types broken on 3.5 ([b5e2ce3](https://github.com/millsp/ts-toolbelt/commit/b5e2ce3d3d4b814bb7c3527a2601d02a575f6f1e))

### [4.9.22](https://github.com/millsp/ts-toolbelt/compare/v4.9.21...v4.9.22) (2019-10-31)


### Others

* readme ([51c594e](https://github.com/millsp/ts-toolbelt/commit/51c594e0bb614e794224a973927bc7c9b5967bac))

### [4.9.21](https://github.com/millsp/ts-toolbelt/compare/v4.9.20...v4.9.21) (2019-10-31)


### Others

* removal of forgotten `Keys` ([349cc0c](https://github.com/millsp/ts-toolbelt/commit/349cc0c2dd5290d83fee7c031670609ce043fd0f))

### [4.9.20](https://github.com/millsp/ts-toolbelt/compare/v4.9.19...v4.9.20) (2019-10-31)


### Others

* readme ([1018391](https://github.com/millsp/ts-toolbelt/commit/10183917c640ed1def643e921ba2058f85ff8045))

### [4.9.19](https://github.com/millsp/ts-toolbelt/compare/v4.9.18...v4.9.19) (2019-10-31)


### Others

* readme ([9427188](https://github.com/millsp/ts-toolbelt/commit/94271887739bd6f90fd18d6f82d8c0cca74ff044))

### [4.9.18](https://github.com/millsp/ts-toolbelt/compare/v4.9.17...v4.9.18) (2019-10-31)


### Others

* readme ([afac414](https://github.com/millsp/ts-toolbelt/commit/afac4142dc86263eeef3faa4a11a1d25b47697a0))

### [4.9.17](https://github.com/millsp/ts-toolbelt/compare/v4.9.16...v4.9.17) (2019-10-31)


### Others

* readme ([3be9dc6](https://github.com/millsp/ts-toolbelt/commit/3be9dc6ae52dd2641ab089562e8fdd52c9de6aab))

### [4.9.16](https://github.com/millsp/ts-toolbelt/compare/v4.9.15...v4.9.16) (2019-10-31)


### Others

* readme ([894e154](https://github.com/millsp/ts-toolbelt/commit/894e154df3a8f963ced31db3f573ee347f876e15))

### [4.9.15](https://github.com/millsp/ts-toolbelt/compare/v4.9.14...v4.9.15) (2019-10-31)


### Others

* added CoC ([2f7513f](https://github.com/millsp/ts-toolbelt/commit/2f7513ff704d1e00bcb7a1f73e6ad94cd48f025a))

### [4.9.14](https://github.com/millsp/ts-toolbelt/compare/v4.9.13...v4.9.14) (2019-10-29)


### Others

* readme ([41bf15f](https://github.com/millsp/ts-toolbelt/commit/41bf15fb1dcdfbdebebe8e8742caadaef1c00bee))

### [4.9.13](https://github.com/millsp/ts-toolbelt/compare/v4.9.12...v4.9.13) (2019-10-28)


### Others

* **thanks:** ekilah ([1b99b4e](https://github.com/millsp/ts-toolbelt/commit/1b99b4e3759900a9743bd9e33500da1522b48fee))

### [4.9.12](https://github.com/millsp/ts-toolbelt/compare/v4.9.11...v4.9.12) (2019-10-28)

### [4.9.11](https://github.com/millsp/ts-toolbelt/compare/v4.9.10...v4.9.11) (2019-10-28)


### Others

* cleanup ([651b5d8](https://github.com/millsp/ts-toolbelt/commit/651b5d86697f08ee7cdd583c5f38d35cd40aa8b8))
* **mergeup:** fixes [#63](https://github.com/millsp/ts-toolbelt/issues/63) ([0c84c66](https://github.com/millsp/ts-toolbelt/commit/0c84c669c19ee0638a126ec13305db4efebf79de))

### [4.9.10](https://github.com/millsp/ts-toolbelt/compare/v4.9.9...v4.9.10) (2019-10-28)


### Others

* **keys:** increased performance for `key` ops ([b6271f4](https://github.com/millsp/ts-toolbelt/commit/b6271f42e105a1da541445e009990ef1d01767ea))

### [4.9.9](https://github.com/millsp/ts-toolbelt/compare/v4.9.8...v4.9.9) (2019-10-27)


### Bug Fixes

* **scripts:** implicit any ([6d4f9cd](https://github.com/millsp/ts-toolbelt/commit/6d4f9cd))

### [4.9.8](https://github.com/millsp/ts-toolbelt/compare/v4.9.7...v4.9.8) (2019-10-27)


### Bug Fixes

* missing types for node ([2786ca6](https://github.com/millsp/ts-toolbelt/commit/2786ca6))

### [4.9.7](https://github.com/millsp/ts-toolbelt/compare/v4.9.6...v4.9.7) (2019-10-27)


### Others

* remove unused node types ([95b771d](https://github.com/millsp/ts-toolbelt/commit/95b771d))

### [4.9.6](https://github.com/millsp/ts-toolbelt/compare/v4.9.5...v4.9.6) (2019-10-27)


### Others

* **merge:** next ([70fd547](https://github.com/millsp/ts-toolbelt/commit/70fd547))

### [4.9.4](https://github.com/millsp/ts-toolbelt/compare/v4.9.3...v4.9.4) (2019-10-27)


### Others

* **release:** 4.9.4 ([621b1fc](https://github.com/millsp/ts-toolbelt/commit/621b1fc))

### [4.9.5](https://github.com/millsp/ts-toolbelt/compare/v4.9.3...v4.9.5) (2019-10-27)

### [4.9.4](https://github.com/millsp/ts-toolbelt/compare/v4.9.3...v4.9.4) (2019-10-27)

### Bug Fixes

* **exports:** not found on the playground ([e74e6a3](https://github.com/millsp/ts-toolbelt/commit/e74e6a3))


### Others

* **release:** 4.9.4 ([a081bf4](https://github.com/millsp/ts-toolbelt/commit/a081bf4))

### [4.9.4](https://github.com/millsp/ts-toolbelt/compare/v4.9.3...v4.9.4) (2019-10-27)


### Bug Fixes

* **exports:** not found on the playground ([e74e6a3](https://github.com/millsp/ts-toolbelt/commit/e74e6a3))

### [4.9.3](https://github.com/millsp/ts-toolbelt/compare/v4.9.1...v4.9.3) (2019-10-27)


### Bug Fixes

* using AMD types ([76e82a5](https://github.com/millsp/ts-toolbelt/commit/76e82a5))


### Others

* **release:** 4.9.2 ([384a1d7](https://github.com/millsp/ts-toolbelt/commit/384a1d7))

### [4.9.2](https://github.com/millsp/ts-toolbelt/compare/v4.9.1...v4.9.2) (2019-10-27)


### Bug Fixes

* using AMD types ([76e82a5](https://github.com/millsp/ts-toolbelt/commit/76e82a5))

### [4.9.1](https://github.com/millsp/ts-toolbelt/compare/v4.9.0...v4.9.1) (2019-10-26)


### Others

* readme ([53fea1e](https://github.com/millsp/ts-toolbelt/commit/53fea1e))

## [4.9.0](https://github.com/millsp/ts-toolbelt/compare/v4.8.29...v4.9.0) (2019-10-26)


### Bug Fixes

* **merge:** stop distribution ([fc6b6b6](https://github.com/millsp/ts-toolbelt/commit/fc6b6b6))
* **mergeup:** show distribution with unknown ([b734b7d](https://github.com/millsp/ts-toolbelt/commit/b734b7d))


### Features

* **pathup:** better path ([5f7918a](https://github.com/millsp/ts-toolbelt/commit/5f7918a))


### Others

* **o.assingup:** cleanup ([c921298](https://github.com/millsp/ts-toolbelt/commit/c921298))
* **path:** cleanup ([66b8173](https://github.com/millsp/ts-toolbelt/commit/66b8173))
* **pathup:** added missing tests ([a6917c0](https://github.com/millsp/ts-toolbelt/commit/a6917c0))

### [4.8.29](https://github.com/millsp/ts-toolbelt/compare/v4.8.28...v4.8.29) (2019-10-26)


### Bug Fixes

* **t.pathvalid/path:** `objectof` breaks numeric keys and is not needed ([3a34473](https://github.com/millsp/ts-toolbelt/commit/3a34473))

### [4.8.28](https://github.com/millsp/ts-toolbelt/compare/v4.8.27...v4.8.28) (2019-10-26)


### Others

* **p/t.pathvalid:** fix return types ([019db9a](https://github.com/millsp/ts-toolbelt/commit/019db9a))

### [4.8.27](https://github.com/millsp/ts-toolbelt/compare/v4.8.24...v4.8.27) (2019-10-26)


### Bug Fixes

* bump version ([daddbf5](https://github.com/millsp/ts-toolbelt/commit/daddbf5))


### Others

* **o/t.pathvalid:** examples ([8d3a6ff](https://github.com/millsp/ts-toolbelt/commit/8d3a6ff))
* **release:** 4.8.25 ([bed4d33](https://github.com/millsp/ts-toolbelt/commit/bed4d33))

### [4.8.25](https://github.com/millsp/ts-toolbelt/compare/v4.8.24...v4.8.25) (2019-10-26)


### Others

* **o/t.pathvalid:** examples ([8d3a6ff](https://github.com/millsp/ts-toolbelt/commit/8d3a6ff))

### [4.8.24](https://github.com/millsp/ts-toolbelt/compare/v4.8.23...v4.8.24) (2019-10-24)


### Others

* **o/t.ensure:** rename to Clean ([015ef00](https://github.com/millsp/ts-toolbelt/commit/015ef00))

### [4.8.23](https://github.com/millsp/ts-toolbelt/compare/v4.8.22...v4.8.23) (2019-10-24)


### Bug Fixes

* **t.unnest:** displayed computation ([55fcfec](https://github.com/millsp/ts-toolbelt/commit/55fcfec))

### [4.8.22](https://github.com/millsp/ts-toolbelt/compare/v4.8.21...v4.8.22) (2019-10-21)


### Others

* readme ([1c2f9cd](https://github.com/millsp/ts-toolbelt/commit/1c2f9cd))

### [4.8.21](https://github.com/millsp/ts-toolbelt/compare/v4.8.20...v4.8.21) (2019-10-20)


### Others

* removed number to string transform ([17a9dbc](https://github.com/millsp/ts-toolbelt/commit/17a9dbc))

### [4.8.20](https://github.com/millsp/ts-toolbelt/compare/v4.8.19...v4.8.20) (2019-10-19)


### Others

* add enterprise language ([f31674b](https://github.com/millsp/ts-toolbelt/commit/f31674b))
* add enterprise language ([#61](https://github.com/millsp/ts-toolbelt/issues/61)) ([5728c5b](https://github.com/millsp/ts-toolbelt/commit/5728c5b))
* readme ([7d86f1a](https://github.com/millsp/ts-toolbelt/commit/7d86f1a))

### [4.8.19](https://github.com/millsp/ts-toolbelt/compare/v4.8.18...v4.8.19) (2019-10-18)


### Bug Fixes

* **readme:** typo ([af08507](https://github.com/millsp/ts-toolbelt/commit/af08507))

### [4.8.18](https://github.com/millsp/ts-toolbelt/compare/v4.8.17...v4.8.18) (2019-10-18)


### Others

* **p.pick:** use Pos ([74f593c](https://github.com/millsp/ts-toolbelt/commit/74f593c))

### [4.8.17](https://github.com/millsp/ts-toolbelt/compare/v4.8.16...v4.8.17) (2019-10-17)


### Bug Fixes

* **p:** formatting ([897ddd6](https://github.com/millsp/ts-toolbelt/commit/897ddd6))


### Others

* **o.p:** simplify all path types to use similar code ([d5b05f0](https://github.com/millsp/ts-toolbelt/commit/d5b05f0))

### [4.8.16](https://github.com/millsp/ts-toolbelt/compare/v4.8.15...v4.8.16) (2019-10-15)


### Bug Fixes

* **a.index:** added missing export ([791f971](https://github.com/millsp/ts-toolbelt/commit/791f971))


### Others

* **a.implements:** added missing tests ([062bc18](https://github.com/millsp/ts-toolbelt/commit/062bc18))

### [4.8.15](https://github.com/millsp/ts-toolbelt/compare/v4.8.14...v4.8.15) (2019-10-15)


### Others

* readme ([da5bf5f](https://github.com/millsp/ts-toolbelt/commit/da5bf5f))
* readme ([488bc89](https://github.com/millsp/ts-toolbelt/commit/488bc89))

### [4.8.14](https://github.com/millsp/ts-toolbelt/compare/v4.8.13...v4.8.14) (2019-10-15)


### Others

* **o.mergeup:** deleted useless loops (mapped) ([0a80e07](https://github.com/millsp/ts-toolbelt/commit/0a80e07))

### [4.8.13](https://github.com/millsp/ts-toolbelt/compare/v4.8.12...v4.8.13) (2019-10-12)


### Others

* readme ([25491c3](https://github.com/millsp/ts-toolbelt/commit/25491c3))

### [4.8.12](https://github.com/millsp/ts-toolbelt/compare/v4.8.11...v4.8.12) (2019-10-12)


### Others

* update demo ([d4862cd](https://github.com/millsp/ts-toolbelt/commit/d4862cd))

### [4.8.11](https://github.com/millsp/ts-toolbelt/compare/v4.8.10...v4.8.11) (2019-10-12)


### Others

* readme ([f8325a2](https://github.com/millsp/ts-toolbelt/commit/f8325a2))

### [4.8.10](https://github.com/millsp/ts-toolbelt/compare/v4.8.9...v4.8.10) (2019-10-12)


### Others

* readme ([1042b1e](https://github.com/millsp/ts-toolbelt/commit/1042b1e))

### [4.8.9](https://github.com/millsp/ts-toolbelt/compare/v4.8.8...v4.8.9) (2019-10-12)


### Others

* update docgen ([17beb3a](https://github.com/millsp/ts-toolbelt/commit/17beb3a))

### [4.8.8](https://github.com/millsp/ts-toolbelt/compare/v4.8.7...v4.8.8) (2019-10-08)


### Others

* readme ([0b35525](https://github.com/millsp/ts-toolbelt/commit/0b35525))

### [4.8.7](https://github.com/millsp/ts-toolbelt/compare/v4.8.6...v4.8.7) (2019-10-08)


### Others

* readme ([b7e33e8](https://github.com/millsp/ts-toolbelt/commit/b7e33e8))
* readme ([1b4eb9d](https://github.com/millsp/ts-toolbelt/commit/1b4eb9d))

### [4.8.6](https://github.com/millsp/ts-toolbelt/compare/v4.8.5...v4.8.6) (2019-10-08)


### Others

* readme ([4c8b1da](https://github.com/millsp/ts-toolbelt/commit/4c8b1da))

### [4.8.5](https://github.com/millsp/ts-toolbelt/compare/v4.8.4...v4.8.5) (2019-10-08)


### Others

* cleanup ([9f9ac2f](https://github.com/millsp/ts-toolbelt/commit/9f9ac2f))
* example for Type ([c86f823](https://github.com/millsp/ts-toolbelt/commit/c86f823))

### [4.8.4](https://github.com/millsp/ts-toolbelt/compare/v4.8.3...v4.8.4) (2019-10-05)


### Bug Fixes

* remove distribution on recursive types ([ec8c882](https://github.com/millsp/ts-toolbelt/commit/ec8c882))

### [4.8.3](https://github.com/millsp/ts-toolbelt/compare/v4.8.2...v4.8.3) (2019-10-05)


### Others

* improved for numbers ([9ab7524](https://github.com/millsp/ts-toolbelt/commit/9ab7524))

### [4.8.2](https://github.com/millsp/ts-toolbelt/compare/v4.8.1...v4.8.2) (2019-10-05)


### Bug Fixes

* push tags only on release versions ([9aa2096](https://github.com/millsp/ts-toolbelt/commit/9aa2096))
* safer recursive types ([100d0ba](https://github.com/millsp/ts-toolbelt/commit/100d0ba))

### [4.8.1](https://github.com/millsp/ts-toolbelt/compare/v4.8.0...v4.8.1) (2019-10-05)


### Bug Fixes

* revert recursion limits ([44e96b6](https://github.com/millsp/ts-toolbelt/commit/44e96b6))

## [4.8.0](https://github.com/millsp/ts-toolbelt/compare/v4.7.25...v4.8.0) (2019-10-04)


### Features

* **tuple:** added Ensure ([fda189f](https://github.com/millsp/ts-toolbelt/commit/fda189f))
* **tuple:** added LastIndex ([ed7a733](https://github.com/millsp/ts-toolbelt/commit/ed7a733))

### [4.7.25](https://github.com/millsp/ts-toolbelt/compare/v4.7.24...v4.7.25) (2019-10-04)


### Bug Fixes

* overflow on recursive types ([a3351fb](https://github.com/millsp/ts-toolbelt/commit/a3351fb))


### Others

* internal comments ([a3e3d38](https://github.com/millsp/ts-toolbelt/commit/a3e3d38))

### [4.7.24](https://github.com/millsp/ts-toolbelt/compare/v4.7.23...v4.7.24) (2019-10-01)


### Others

* readme ([6503617](https://github.com/millsp/ts-toolbelt/commit/6503617))

### [4.7.23](https://github.com/millsp/ts-toolbelt/compare/v4.7.22...v4.7.23) (2019-10-01)


### Bug Fixes

* deep instantiation limit ([9a5be9c](https://github.com/millsp/ts-toolbelt/commit/9a5be9c))

### [4.7.22](https://github.com/millsp/ts-toolbelt/compare/v4.7.21...v4.7.22) (2019-10-01)


### Bug Fixes

* releases ([9a76796](https://github.com/millsp/ts-toolbelt/commit/9a76796))

### [4.7.21](https://github.com/millsp/ts-toolbelt/compare/v4.7.19...v4.7.21) (2019-10-01)


### Bug Fixes

* no more release on bugfix ([c68e8d4](https://github.com/millsp/ts-toolbelt/commit/c68e8d4))


### Others

* **release:** 4.7.1 ([ed25587](https://github.com/millsp/ts-toolbelt/commit/ed25587))

### [4.7.1](https://github.com/millsp/ts-toolbelt/compare/v4.7.19...v4.7.1) (2019-10-01)

### [4.7.19](https://github.com/millsp/ts-toolbelt/compare/v4.7.18...v4.7.19) (2019-09-30)


### Bug Fixes

* better distribution on deep types ([d675b2a](https://github.com/millsp/ts-toolbelt/commit/d675b2a))
* simpler P.Omit ([a247e02](https://github.com/millsp/ts-toolbelt/commit/a247e02))
* typo ([10cb942](https://github.com/millsp/ts-toolbelt/commit/10cb942))

### [4.7.18](https://github.com/millsp/ts-toolbelt/compare/v4.7.17...v4.7.18) (2019-09-30)


### Bug Fixes

* indent ([eff221b](https://github.com/millsp/ts-toolbelt/commit/eff221b))

### [4.7.17](https://github.com/millsp/ts-toolbelt/compare/v4.7.16...v4.7.17) (2019-09-30)


### Bug Fixes

* **p:** distribute over unions ([aa2bb21](https://github.com/millsp/ts-toolbelt/commit/aa2bb21))
* **p.pick:** distribute over unions ([0213712](https://github.com/millsp/ts-toolbelt/commit/0213712))


### Others

* **p:** add test for distributing over unions ([1c369d2](https://github.com/millsp/ts-toolbelt/commit/1c369d2))
* **p.pick:** add test for distributing over unions ([f9be819](https://github.com/millsp/ts-toolbelt/commit/f9be819))

### [4.7.16](https://github.com/millsp/ts-toolbelt/compare/v4.7.15...v4.7.16) (2019-09-28)


### Others

* logos ([5f9b4c5](https://github.com/millsp/ts-toolbelt/commit/5f9b4c5))

### [4.7.15](https://github.com/millsp/ts-toolbelt/compare/v4.7.14...v4.7.15) (2019-09-28)


### Others

* readme ([588d315](https://github.com/millsp/ts-toolbelt/commit/588d315))

### [4.7.14](https://github.com/millsp/ts-toolbelt/compare/v4.7.13...v4.7.14) (2019-09-28)


### Others

* readme ([f24d43b](https://github.com/millsp/ts-toolbelt/commit/f24d43b))

### [4.7.13](https://github.com/millsp/ts-toolbelt/compare/v4.7.12...v4.7.13) (2019-09-28)


### Others

* readme ([ab15797](https://github.com/millsp/ts-toolbelt/commit/ab15797))

### [4.7.12](https://github.com/millsp/ts-toolbelt/compare/v4.7.11...v4.7.12) (2019-09-28)


### Others

* readme ([d9a15fd](https://github.com/millsp/ts-toolbelt/commit/d9a15fd))

### [4.7.11](https://github.com/millsp/ts-toolbelt/compare/v4.7.10...v4.7.11) (2019-09-28)


### Others

* readme [skip ci] ([26c624c](https://github.com/millsp/ts-toolbelt/commit/26c624c))

### [4.7.10](https://github.com/millsp/ts-toolbelt/compare/v4.7.9...v4.7.10) (2019-09-28)


### Others

* readme ([a1fd792](https://github.com/millsp/ts-toolbelt/commit/a1fd792))

### [4.7.9](https://github.com/millsp/ts-toolbelt/compare/v4.7.8...v4.7.9) (2019-09-28)


### Others

* page [skip ci] ([4b3e959](https://github.com/millsp/ts-toolbelt/commit/4b3e959))

### [4.7.8](https://github.com/millsp/ts-toolbelt/compare/v4.7.7...v4.7.8) (2019-09-28)


### Others

* tidelift logo ([149922e](https://github.com/millsp/ts-toolbelt/commit/149922e))

### [4.7.7](https://github.com/millsp/ts-toolbelt/compare/v4.7.6...v4.7.7) (2019-09-28)


### Bug Fixes

* declaration conflicts ([737fe66](https://github.com/millsp/ts-toolbelt/commit/737fe66))

### [4.7.6](https://github.com/millsp/ts-toolbelt/compare/v4.7.5...v4.7.6) (2019-09-28)


### Others

* tidelift button ([82ab201](https://github.com/millsp/ts-toolbelt/commit/82ab201))

### [4.7.5](https://github.com/millsp/ts-toolbelt/compare/v4.7.4...v4.7.5) (2019-09-28)


### Others

* tidelift button ([d887363](https://github.com/millsp/ts-toolbelt/commit/d887363))

### [4.7.4](https://github.com/millsp/ts-toolbelt/compare/v4.7.3...v4.7.4) (2019-09-28)


### Others

* tidelift button ([60e3cc3](https://github.com/millsp/ts-toolbelt/commit/60e3cc3))

### [4.7.3](https://github.com/millsp/ts-toolbelt/compare/v4.7.2...v4.7.3) (2019-09-28)


### Bug Fixes

* enclose never ([95e707a](https://github.com/millsp/ts-toolbelt/commit/95e707a))
* missing import ([3665eb4](https://github.com/millsp/ts-toolbelt/commit/3665eb4))
* typo ([137229b](https://github.com/millsp/ts-toolbelt/commit/137229b))

### [4.7.2](https://github.com/millsp/ts-toolbelt/compare/v4.7.1...v4.7.2) (2019-09-28)


### Bug Fixes

* output of Ensure ([ae3b20b](https://github.com/millsp/ts-toolbelt/commit/ae3b20b))

### [4.7.1](https://github.com/millsp/ts-toolbelt/compare/v4.7.0...v4.7.1) (2019-09-27)


### Bug Fixes

* not detecting arrays ([25671e4](https://github.com/millsp/ts-toolbelt/commit/25671e4))

## [4.7.0](https://github.com/millsp/ts-toolbelt/compare/v4.6.0...v4.7.0) (2019-09-27)


### Features

* added Ensure ([e695925](https://github.com/millsp/ts-toolbelt/commit/e695925))


### Others

* cleanup ([a7f031a](https://github.com/millsp/ts-toolbelt/commit/a7f031a))

## [4.6.0](https://github.com/millsp/ts-toolbelt/compare/v4.5.4...v4.6.0) (2019-09-27)


### Features

* better computation ([9e60b18](https://github.com/millsp/ts-toolbelt/commit/9e60b18))

### [4.5.4](https://github.com/millsp/ts-toolbelt/compare/v4.5.3...v4.5.4) (2019-09-27)


### Others

* cleanup Keys in favor of keyof ([2f38ea4](https://github.com/millsp/ts-toolbelt/commit/2f38ea4))
* type distribution cleanup ([8fa8dc0](https://github.com/millsp/ts-toolbelt/commit/8fa8dc0))

### [4.5.3](https://github.com/millsp/ts-toolbelt/compare/v4.5.2...v4.5.3) (2019-09-27)


### Bug Fixes

* useless check on CI ([c475338](https://github.com/millsp/ts-toolbelt/commit/c475338))


### Others

* added conditional deploy ([862bfd9](https://github.com/millsp/ts-toolbelt/commit/862bfd9))

### [4.5.2](https://github.com/millsp/ts-toolbelt/compare/v4.5.1...v4.5.2) (2019-09-26)


### Others

* migrated codebase to Tuple ([e6e90a8](https://github.com/millsp/ts-toolbelt/commit/e6e90a8))

### [4.5.1](https://github.com/millsp/ts-toolbelt/compare/v4.5.0...v4.5.1) (2019-09-26)


### Bug Fixes

* infinite recursion on `any` ([d250d43](https://github.com/millsp/ts-toolbelt/commit/d250d43))

## [4.5.0](https://github.com/millsp/ts-toolbelt/compare/v4.4.7...v4.5.0) (2019-09-26)


### Features

* added AssignUp ([381de4d](https://github.com/millsp/ts-toolbelt/commit/381de4d))
* added Index ([ab5447d](https://github.com/millsp/ts-toolbelt/commit/ab5447d))


### Others

* cleanup ([9c199ec](https://github.com/millsp/ts-toolbelt/commit/9c199ec))
* moved Way ([01520ec](https://github.com/millsp/ts-toolbelt/commit/01520ec))

### [4.4.7](https://github.com/millsp/ts-toolbelt/compare/v4.4.6...v4.4.7) (2019-09-25)

### [4.4.6](https://github.com/millsp/ts-toolbelt/compare/v4.4.5...v4.4.6) (2019-09-25)


### Bug Fixes

* delay on CI deploy ([04f3df8](https://github.com/millsp/ts-toolbelt/commit/04f3df8))

### [4.4.5](https://github.com/millsp/ts-toolbelt/compare/v4.4.4...v4.4.5) (2019-09-25)

### [4.4.4](https://github.com/millsp/ts-toolbelt/compare/v4.4.3...v4.4.4) (2019-09-25)


### Bug Fixes

* broken Flatten with readonly ([b893b83](https://github.com/millsp/ts-toolbelt/commit/b893b83))

### [4.4.3](https://github.com/millsp/ts-toolbelt/compare/v4.4.2...v4.4.3) (2019-09-25)


### Bug Fixes

* added publish check on CI ([42facc1](https://github.com/millsp/ts-toolbelt/commit/42facc1))
* version bump on branches ([24f7560](https://github.com/millsp/ts-toolbelt/commit/24f7560))


### Others

* test CI ([0c5d6b8](https://github.com/millsp/ts-toolbelt/commit/0c5d6b8))

### [4.4.2](https://github.com/millsp/ts-toolbelt/compare/v4.4.1...v4.4.2) (2019-09-25)


### Bug Fixes

* bad script on CI ([4f73444](https://github.com/millsp/ts-toolbelt/commit/4f73444))

### [4.4.1](https://github.com/millsp/ts-toolbelt/compare/v4.4.0...v4.4.1) (2019-09-25)


### Others

* cleanup ([dd0730b](https://github.com/millsp/ts-toolbelt/commit/dd0730b))
* for Either ([9f33715](https://github.com/millsp/ts-toolbelt/commit/9f33715))
* for Either ([ab2f1ca](https://github.com/millsp/ts-toolbelt/commit/ab2f1ca))

## [4.4.0](https://github.com/millsp/ts-toolbelt/compare/v4.3.0...v4.4.0) (2019-09-25)


### Features

* non-strict option for Either ([7c099a4](https://github.com/millsp/ts-toolbelt/commit/7c099a4))

## [4.3.0](https://github.com/millsp/ts-toolbelt/compare/v4.2.1...v4.3.0) (2019-09-24)


### Bug Fixes

* test & export for Flatten ([5958b64](https://github.com/millsp/ts-toolbelt/commit/5958b64))


### Features

* added Flatten ([d3f9147](https://github.com/millsp/ts-toolbelt/commit/d3f9147))

### [4.2.1](https://github.com/millsp/ts-toolbelt/compare/v4.2.0...v4.2.1) (2019-09-24)


### Bug Fixes

* deplay CI after deploy ([a7cf6b1](https://github.com/millsp/ts-toolbelt/commit/a7cf6b1))

## [4.2.0](https://github.com/millsp/ts-toolbelt/compare/v4.1.1...v4.2.0) (2019-09-24)


### Features

* placeholder on Update ([e5c7f40](https://github.com/millsp/ts-toolbelt/commit/e5c7f40))

### [4.1.1](https://github.com/millsp/ts-toolbelt/compare/v4.1.0...v4.1.1) (2019-09-24)


### Bug Fixes

* readonly arrays and UnNest ([c54e23c](https://github.com/millsp/ts-toolbelt/commit/c54e23c))

## [4.1.0](https://github.com/millsp/ts-toolbelt/compare/v4.0.12...v4.1.0) (2019-09-24)


### Bug Fixes

* crash typedoc ([fc22674](https://github.com/millsp/ts-toolbelt/commit/fc22674))


### Features

* added undefined handling for MergeUp ([410d4c4](https://github.com/millsp/ts-toolbelt/commit/410d4c4))

### [4.0.12](https://github.com/millsp/ts-toolbelt/compare/v4.0.11...v4.0.12) (2019-09-23)


### Bug Fixes

* improved performance ([921b3b0](https://github.com/millsp/ts-toolbelt/commit/921b3b0))


### Others

* re-enable tags ([a4b3003](https://github.com/millsp/ts-toolbelt/commit/a4b3003))

### [4.0.11](https://github.com/millsp/ts-toolbelt/compare/v4.0.5...v4.0.11) (2019-09-23)


### Bug Fixes

* **ci:** preserve doc history ([bdb181c](https://github.com/millsp/ts-toolbelt/commit/bdb181c))
*  broken types on generics with key distribution in Keys, fixes [#50](https://github.com/millsp/ts-toolbelt/issues/50) ([2c84e2c](https://github.com/millsp/ts-toolbelt/commit/2c84e2c))
* bump version ([81edfb1](https://github.com/millsp/ts-toolbelt/commit/81edfb1))
* change priority ([44ecbca](https://github.com/millsp/ts-toolbelt/commit/44ecbca))
* cleaner merging ([165f1b2](https://github.com/millsp/ts-toolbelt/commit/165f1b2))
* typo ([35509da](https://github.com/millsp/ts-toolbelt/commit/35509da))


### Others

* add docs to gitignore ([840704e](https://github.com/millsp/ts-toolbelt/commit/840704e))
* cleanup ([4f557aa](https://github.com/millsp/ts-toolbelt/commit/4f557aa))
* disable for ci tests ([3629815](https://github.com/millsp/ts-toolbelt/commit/3629815))
* eslint remove comment indent ([7691486](https://github.com/millsp/ts-toolbelt/commit/7691486))
* force CI to fail if failed deploy ([ef532d2](https://github.com/millsp/ts-toolbelt/commit/ef532d2))
* re-enable standard-version ([c694c78](https://github.com/millsp/ts-toolbelt/commit/c694c78))
* tesing doc history ([db55dae](https://github.com/millsp/ts-toolbelt/commit/db55dae))
* tesing doc history ([ab1ec56](https://github.com/millsp/ts-toolbelt/commit/ab1ec56))

### [4.0.5](https://github.com/millsp/ts-toolbelt/compare/v4.0.4...v4.0.5) (2019-09-19)


### Others

* testing doc history ([b7b9c23](https://github.com/millsp/ts-toolbelt/commit/b7b9c23))

### [4.0.4](https://github.com/millsp/ts-toolbelt/compare/v4.0.3...v4.0.4) (2019-09-19)


### Bug Fixes

* missing keep_history ([2e697b1](https://github.com/millsp/ts-toolbelt/commit/2e697b1))

### [4.0.3](https://github.com/millsp/ts-toolbelt/compare/v4.0.2...v4.0.3) (2019-09-19)


### Others

* cleanup ([d46e29b](https://github.com/millsp/ts-toolbelt/commit/d46e29b))

### [4.0.2](https://github.com/millsp/ts-toolbelt/compare/v4.0.1...v4.0.2) (2019-09-19)


### Bug Fixes

* eol ([bbc48d2](https://github.com/millsp/ts-toolbelt/commit/bbc48d2))


### Others

* preserve doc history ([c88c59b](https://github.com/millsp/ts-toolbelt/commit/c88c59b))

### [4.0.1](https://github.com/millsp/ts-toolbelt/compare/v4.0.0...v4.0.1) (2019-09-19)


### Bug Fixes

* missing cast on O/NonNullable ([8610d9b](https://github.com/millsp/ts-toolbelt/commit/8610d9b))


### Others

* update for v4.0 [skip ci] ([e2e18ca](https://github.com/millsp/ts-toolbelt/commit/e2e18ca))

## [4.0.0](https://github.com/millsp/ts-toolbelt/compare/v3.14.0...v4.0.0) (2019-09-19)


### ⚠ BREAKING CHANGES

* 

### Bug Fixes

* accuracy of MergeUp ([97c1ceb](https://github.com/millsp/ts-toolbelt/commit/97c1ceb))
* complaint Cast ([6fc5636](https://github.com/millsp/ts-toolbelt/commit/6fc5636))
* remove extra number check ([705847f](https://github.com/millsp/ts-toolbelt/commit/705847f))
* restrict Strict to objects ([35b8669](https://github.com/millsp/ts-toolbelt/commit/35b8669))
* stricter Kind ([d26d95e](https://github.com/millsp/ts-toolbelt/commit/d26d95e))


### Features

* added Merge for object unions ([7e636ad](https://github.com/millsp/ts-toolbelt/commit/7e636ad))
* added Union/Merge ([e08a32b](https://github.com/millsp/ts-toolbelt/commit/e08a32b))
* full distributive types ([62463b6](https://github.com/millsp/ts-toolbelt/commit/62463b6))


### Others

* cleanup ([244d42e](https://github.com/millsp/ts-toolbelt/commit/244d42e))
* cleanup ([8889318](https://github.com/millsp/ts-toolbelt/commit/8889318))
* cleanup ([58dd27c](https://github.com/millsp/ts-toolbelt/commit/58dd27c))
* cleanup ([d17ff1b](https://github.com/millsp/ts-toolbelt/commit/d17ff1b))
* expensive types ([465225e](https://github.com/millsp/ts-toolbelt/commit/465225e))
* for Union/Merge ([6599de5](https://github.com/millsp/ts-toolbelt/commit/6599de5))

## [3.14.0](https://github.com/millsp/ts-toolbelt/compare/v3.13.0...v3.14.0) (2019-09-17)


### Features

* can now use Readonly tuples ([c38e077](https://github.com/millsp/ts-toolbelt/commit/c38e077))
* type for Tuple ([b898dcc](https://github.com/millsp/ts-toolbelt/commit/b898dcc))


### Others

* cleanup ([b4d65ae](https://github.com/millsp/ts-toolbelt/commit/b4d65ae))
* for Keys ([c9a034b](https://github.com/millsp/ts-toolbelt/commit/c9a034b))

## [3.13.0](https://github.com/millsp/ts-toolbelt/compare/v3.12.0...v3.13.0) (2019-09-17)


### Bug Fixes

* added Compulsory to Tuple ([94cd8da](https://github.com/millsp/ts-toolbelt/commit/94cd8da))
* missing fields for Unionize ([be8bfec](https://github.com/millsp/ts-toolbelt/commit/be8bfec))
* restored MergeUp ([a05af8f](https://github.com/millsp/ts-toolbelt/commit/a05af8f))
* typo ([94af1b3](https://github.com/millsp/ts-toolbelt/commit/94af1b3))
* typo ([e5f048a](https://github.com/millsp/ts-toolbelt/commit/e5f048a))
* typo ([a1f4e88](https://github.com/millsp/ts-toolbelt/commit/a1f4e88))


### Features

* added Compulsory, CompulsoryKeys ([ad1c626](https://github.com/millsp/ts-toolbelt/commit/ad1c626))


### Others

* added empty tests ([ef3f0b5](https://github.com/millsp/ts-toolbelt/commit/ef3f0b5))
* cleanup tests ([d3b0e2a](https://github.com/millsp/ts-toolbelt/commit/d3b0e2a))
* exports ([1c47284](https://github.com/millsp/ts-toolbelt/commit/1c47284))
* for CompulsoryKeys ([e9b114d](https://github.com/millsp/ts-toolbelt/commit/e9b114d))
* for Kind ([744b23f](https://github.com/millsp/ts-toolbelt/commit/744b23f))
* for Merge ([1589093](https://github.com/millsp/ts-toolbelt/commit/1589093))
* for MergeUp ([1cb58d7](https://github.com/millsp/ts-toolbelt/commit/1cb58d7))
* for Unionize ([71e2861](https://github.com/millsp/ts-toolbelt/commit/71e2861))

## [3.12.0](https://github.com/millsp/ts-toolbelt/compare/v3.11.0...v3.12.0) (2019-09-17)


### Bug Fixes

* shorter-simpler Merge and Merge 'deep' ([833c352](https://github.com/millsp/ts-toolbelt/commit/833c352))


### Features

* added Kind ([6d5fa08](https://github.com/millsp/ts-toolbelt/commit/6d5fa08))
* added Parameters for Class ([1673a9f](https://github.com/millsp/ts-toolbelt/commit/1673a9f))


### Others

* cleanup ([1dddb3e](https://github.com/millsp/ts-toolbelt/commit/1dddb3e))
* comments ([c6a7190](https://github.com/millsp/ts-toolbelt/commit/c6a7190))
* remove MergeUp ([62c7c09](https://github.com/millsp/ts-toolbelt/commit/62c7c09))
* remove MergeUp from tests ([2f2ead9](https://github.com/millsp/ts-toolbelt/commit/2f2ead9))
* remove MergeUp tests ([e044645](https://github.com/millsp/ts-toolbelt/commit/e044645))

## [3.11.0](https://github.com/millsp/ts-toolbelt/compare/v3.10.0...v3.11.0) (2019-09-16)


### Bug Fixes

* added K feature ([9e460d1](https://github.com/millsp/ts-toolbelt/commit/9e460d1))
* added Unionize exports, made it available for Tuple too ([d51d9c7](https://github.com/millsp/ts-toolbelt/commit/d51d9c7))
* clearer Merge ([158a5fa](https://github.com/millsp/ts-toolbelt/commit/158a5fa))
* prevent call to API within API ([d463d04](https://github.com/millsp/ts-toolbelt/commit/d463d04))
* refactor names in Tuple Merge ([006f2c1](https://github.com/millsp/ts-toolbelt/commit/006f2c1))
* typo ([25fc0a9](https://github.com/millsp/ts-toolbelt/commit/25fc0a9))
* useless fields are not to be picked ([1b38290](https://github.com/millsp/ts-toolbelt/commit/1b38290))
* useless fields are not to be picked ([79c2332](https://github.com/millsp/ts-toolbelt/commit/79c2332))


### Features

* added MergeUp ([d301e10](https://github.com/millsp/ts-toolbelt/commit/d301e10))
* added possibility to specify keys ([308e78b](https://github.com/millsp/ts-toolbelt/commit/308e78b))
* added Unionize ([151f846](https://github.com/millsp/ts-toolbelt/commit/151f846))


### Others

* for MergeUp ([9f1595f](https://github.com/millsp/ts-toolbelt/commit/9f1595f))

## [3.10.0](https://github.com/millsp/ts-toolbelt/compare/v3.9.2...v3.10.0) (2019-09-14)


### Bug Fixes

* allow Pop to work with optionals ([17bb61f](https://github.com/millsp/ts-toolbelt/commit/17bb61f))
* allow Reverse to work with optionals ([3276dee](https://github.com/millsp/ts-toolbelt/commit/3276dee))
* missing Length in recursion ([b2e6eab](https://github.com/millsp/ts-toolbelt/commit/b2e6eab))
* missing transformation for T ([c4d9e50](https://github.com/millsp/ts-toolbelt/commit/c4d9e50))


### Features

* 'deep' option for Tuple Merge ([6e15410](https://github.com/millsp/ts-toolbelt/commit/6e15410))
* allow deep merge for P.Merge ([b4ad1bc](https://github.com/millsp/ts-toolbelt/commit/b4ad1bc))


### Others

* 'deep' option of Tuple/Merge ([004afae](https://github.com/millsp/ts-toolbelt/commit/004afae))
* fix wrong option ([3236466](https://github.com/millsp/ts-toolbelt/commit/3236466))
* updated tests to allow optionals ([9ec2757](https://github.com/millsp/ts-toolbelt/commit/9ec2757))

### [3.9.2](https://github.com/millsp/ts-toolbelt/compare/v3.9.1...v3.9.2) (2019-09-14)


### Others

* added migging parameter ([0e5a1cb](https://github.com/millsp/ts-toolbelt/commit/0e5a1cb))

### [3.9.1](https://github.com/millsp/ts-toolbelt/compare/v3.9.0...v3.9.1) (2019-09-14)


### Others

* re-enable tags ([843dbf0](https://github.com/millsp/ts-toolbelt/commit/843dbf0))

## [3.9.0](https://github.com/millsp/ts-toolbelt/compare/v3.8.95...v3.9.0) (2019-09-14)


### Features

* 'deep' option for Merge ([5dfde49](https://github.com/millsp/ts-toolbelt/commit/5dfde49))


### Others

* simplify Merge ([06e2671](https://github.com/millsp/ts-toolbelt/commit/06e2671))

### [3.8.95](https://github.com/millsp/ts-toolbelt/compare/v3.8.94...v3.8.95) (2019-09-13)


### Others

* update readme ([57c5609](https://github.com/millsp/ts-toolbelt/commit/57c5609))

### [3.8.94](https://github.com/millsp/ts-toolbelt/compare/v3.8.93...v3.8.94) (2019-09-13)

### [3.8.93](https://github.com/millsp/ts-toolbelt/compare/v3.8.92...v3.8.93) (2019-09-13)


### Bug Fixes

*  use UnNest with narrowed types ([dc4402d](https://github.com/millsp/ts-toolbelt/commit/dc4402d))

### [3.8.92](https://github.com/millsp/ts-toolbelt/compare/v3.8.91...v3.8.92) (2019-09-12)


### Bug Fixes

* typo ([6d6ce2d](https://github.com/millsp/ts-toolbelt/commit/6d6ce2d))


### Others

* disbale on PRs ([aac8487](https://github.com/millsp/ts-toolbelt/commit/aac8487))
* update readme ([e22cd3f](https://github.com/millsp/ts-toolbelt/commit/e22cd3f))

### [3.8.91](https://github.com/millsp/ts-toolbelt/compare/v3.8.90...v3.8.91) (2019-09-10)


### Bug Fixes

* doc deploy ([54b9b6f](https://github.com/millsp/ts-toolbelt/commit/54b9b6f))


### Others

* remove provider list ([f34a5e5](https://github.com/millsp/ts-toolbelt/commit/f34a5e5))

### [3.8.90](https://github.com/millsp/ts-toolbelt/compare/v3.8.89...v3.8.90) (2019-09-10)


### Others

* docs have moved to a branch ([1cbe3b1](https://github.com/millsp/ts-toolbelt/commit/1cbe3b1))

### [3.8.89](https://github.com/millsp/ts-toolbelt/compare/v3.8.88...v3.8.89) (2019-09-10)


### Bug Fixes

* docs deploy ([7bd0098](https://github.com/millsp/ts-toolbelt/commit/7bd0098))

### [3.8.88](https://github.com/millsp/ts-toolbelt/compare/v3.8.87...v3.8.88) (2019-09-10)


### Bug Fixes

* invalid cmd ([012e869](https://github.com/millsp/ts-toolbelt/commit/012e869))

### [3.8.87](https://github.com/millsp/ts-toolbelt/compare/v3.8.81...v3.8.87) (2019-09-10)


### Bug Fixes

* create missing folder ([e10bdf7](https://github.com/millsp/ts-toolbelt/commit/e10bdf7))
* git depth ([e53de3f](https://github.com/millsp/ts-toolbelt/commit/e53de3f))
* travis doc deploy ([75779cb](https://github.com/millsp/ts-toolbelt/commit/75779cb))
* typo ([5e5eb5e](https://github.com/millsp/ts-toolbelt/commit/5e5eb5e))
* typo ([cf281ce](https://github.com/millsp/ts-toolbelt/commit/cf281ce))
* wrong install script ([c4b9ee6](https://github.com/millsp/ts-toolbelt/commit/c4b9ee6))


### Others

* reorganize scripts for CI ([64ca2d0](https://github.com/millsp/ts-toolbelt/commit/64ca2d0))
* **release:** 3.8.82 ([a2dd282](https://github.com/millsp/ts-toolbelt/commit/a2dd282))
* **release:** 3.8.83 ([efba258](https://github.com/millsp/ts-toolbelt/commit/efba258))
* **release:** 3.8.84 ([94deca5](https://github.com/millsp/ts-toolbelt/commit/94deca5))
* **release:** 3.8.85 ([e5371b1](https://github.com/millsp/ts-toolbelt/commit/e5371b1))
* **release:** 3.8.86 ([b1c73a5](https://github.com/millsp/ts-toolbelt/commit/b1c73a5))

### [3.8.86](https://github.com/millsp/ts-toolbelt/compare/v3.8.85...v3.8.86) (2019-09-10)


### Bug Fixes

* typo ([5e5eb5e](https://github.com/millsp/ts-toolbelt/commit/5e5eb5e))

### [3.8.85](https://github.com/millsp/ts-toolbelt/compare/v3.8.84...v3.8.85) (2019-09-10)


### Bug Fixes

* create missing folder ([e10bdf7](https://github.com/millsp/ts-toolbelt/commit/e10bdf7))

### [3.8.84](https://github.com/millsp/ts-toolbelt/compare/v3.8.83...v3.8.84) (2019-09-10)


### Bug Fixes

* git depth ([e53de3f](https://github.com/millsp/ts-toolbelt/commit/e53de3f))

### [3.8.83](https://github.com/millsp/ts-toolbelt/compare/v3.8.82...v3.8.83) (2019-09-10)


### Bug Fixes

* wrong install script ([c4b9ee6](https://github.com/millsp/ts-toolbelt/commit/c4b9ee6))

### [3.8.82](https://github.com/millsp/ts-toolbelt/compare/v3.8.81...v3.8.82) (2019-09-10)


### Bug Fixes

* typo ([cf281ce](https://github.com/millsp/ts-toolbelt/commit/cf281ce))


### Others

* reorganize scripts for CI ([64ca2d0](https://github.com/millsp/ts-toolbelt/commit/64ca2d0))

### [3.8.81](https://github.com/millsp/ts-toolbelt/compare/v3.8.80...v3.8.81) (2019-09-10)


### Others

* travis works ([33204c6](https://github.com/millsp/ts-toolbelt/commit/33204c6))
* update ([53643a4](https://github.com/millsp/ts-toolbelt/commit/53643a4))

### [3.8.80](https://github.com/millsp/ts-toolbelt/compare/v3.8.79...v3.8.80) (2019-09-10)


### Others

* deploy should fail ([8b3cc48](https://github.com/millsp/ts-toolbelt/commit/8b3cc48))
* update ([b28a220](https://github.com/millsp/ts-toolbelt/commit/b28a220))

### [3.8.79](https://github.com/millsp/ts-toolbelt/compare/v3.8.78...v3.8.79) (2019-09-10)


### Others

* travis deploy ([b8284ff](https://github.com/millsp/ts-toolbelt/commit/b8284ff))
* update ([b41560b](https://github.com/millsp/ts-toolbelt/commit/b41560b))

### [3.8.78](https://github.com/millsp/ts-toolbelt/compare/v3.8.77...v3.8.78) (2019-09-10)


### Others

* travis deploy ([525eca1](https://github.com/millsp/ts-toolbelt/commit/525eca1))
* update ([790d176](https://github.com/millsp/ts-toolbelt/commit/790d176))

### [3.8.77](https://github.com/millsp/ts-toolbelt/compare/v3.8.76...v3.8.77) (2019-09-10)


### Others

* travis deploy ([a905dbe](https://github.com/millsp/ts-toolbelt/commit/a905dbe))
* update ([abac5d7](https://github.com/millsp/ts-toolbelt/commit/abac5d7))

### [3.8.76](https://github.com/millsp/ts-toolbelt/compare/v3.8.75...v3.8.76) (2019-09-09)


### Others

* debug travis ([347a083](https://github.com/millsp/ts-toolbelt/commit/347a083))
* update ([82fd5ff](https://github.com/millsp/ts-toolbelt/commit/82fd5ff))

### [3.8.75](https://github.com/millsp/ts-toolbelt/compare/v3.8.74...v3.8.75) (2019-09-09)


### Others

* debug travis ([e41d661](https://github.com/millsp/ts-toolbelt/commit/e41d661))
* update ([badb7f2](https://github.com/millsp/ts-toolbelt/commit/badb7f2))

### [3.8.74](https://github.com/millsp/ts-toolbelt/compare/v3.8.73...v3.8.74) (2019-09-09)


### Others

* checking travis ([c2b00a8](https://github.com/millsp/ts-toolbelt/commit/c2b00a8))
* update ([e66972d](https://github.com/millsp/ts-toolbelt/commit/e66972d))

### [3.8.73](https://github.com/millsp/ts-toolbelt/compare/v3.8.72...v3.8.73) (2019-09-09)


### Bug Fixes

* ramda ([6abf5cd](https://github.com/millsp/ts-toolbelt/commit/6abf5cd))


### Others

* update ([7d505ca](https://github.com/millsp/ts-toolbelt/commit/7d505ca))

### [3.8.72](https://github.com/millsp/ts-toolbelt/compare/v3.8.71...v3.8.72) (2019-09-07)


### Bug Fixes

* wrong version [#44](https://github.com/millsp/ts-toolbelt/issues/44) ([3b07263](https://github.com/millsp/ts-toolbelt/commit/3b07263))


### Others

* update ([5a567f6](https://github.com/millsp/ts-toolbelt/commit/5a567f6))

### [3.8.71](https://github.com/millsp/ts-toolbelt/compare/v3.8.70...v3.8.71) (2019-09-07)


### Bug Fixes

* wrong versioning for test packages [#44](https://github.com/millsp/ts-toolbelt/issues/44) ([1936949](https://github.com/millsp/ts-toolbelt/commit/1936949))


### Others

* update ([17c61ad](https://github.com/millsp/ts-toolbelt/commit/17c61ad))

### [3.8.70](https://github.com/millsp/ts-toolbelt/compare/v3.8.69...v3.8.70) (2019-09-06)


### Others

* test CI ([5c70565](https://github.com/millsp/ts-toolbelt/commit/5c70565))
* update ([9f83947](https://github.com/millsp/ts-toolbelt/commit/9f83947))

### [3.8.69](https://github.com/millsp/ts-toolbelt/compare/v3.8.68...v3.8.69) (2019-09-06)


### Others

* restore tags ([1eb7893](https://github.com/millsp/ts-toolbelt/commit/1eb7893))
* update ([c4b16ca](https://github.com/millsp/ts-toolbelt/commit/c4b16ca))

### [3.8.68](https://github.com/millsp/ts-toolbelt/compare/v3.8.67...v3.8.68) (2019-09-06)


### Bug Fixes

* wrong conf ([f737a4a](https://github.com/millsp/ts-toolbelt/commit/f737a4a))


### Others

* update ([8a5f1d8](https://github.com/millsp/ts-toolbelt/commit/8a5f1d8))

### [3.8.67](https://github.com/millsp/ts-toolbelt/compare/v3.8.66...v3.8.67) (2019-09-06)


### Bug Fixes

* attempt to fix CI ([312401d](https://github.com/millsp/ts-toolbelt/commit/312401d))
* attempt to fix CI ([4d197c0](https://github.com/millsp/ts-toolbelt/commit/4d197c0))


### Others

* disable tags ([80a664d](https://github.com/millsp/ts-toolbelt/commit/80a664d))
* update ([c712d06](https://github.com/millsp/ts-toolbelt/commit/c712d06))

### [3.8.66](https://github.com/millsp/ts-toolbelt/compare/v3.8.65...v3.8.66) (2019-09-06)


### Bug Fixes

* conflicting versions in CI deploy ([67c402e](https://github.com/millsp/ts-toolbelt/commit/67c402e))


### Others

* clean ([230c155](https://github.com/millsp/ts-toolbelt/commit/230c155))
* update ([2f77c6c](https://github.com/millsp/ts-toolbelt/commit/2f77c6c))

### [3.8.65](https://github.com/millsp/ts-toolbelt/compare/v3.8.64...v3.8.65) (2019-09-06)


### Bug Fixes

* deploy version for tests ([a5c4ce3](https://github.com/millsp/ts-toolbelt/commit/a5c4ce3))


### Others

* update ([7d57494](https://github.com/millsp/ts-toolbelt/commit/7d57494))

### [3.8.64](https://github.com/millsp/ts-toolbelt/compare/v3.8.63...v3.8.64) (2019-09-06)


### Bug Fixes

* mistyped curry [#43](https://github.com/millsp/ts-toolbelt/issues/43) ([a9227ba](https://github.com/millsp/ts-toolbelt/commit/a9227ba))
* wrong link ([0eb92c0](https://github.com/millsp/ts-toolbelt/commit/0eb92c0))


### Others

* funding ([1d02fd4](https://github.com/millsp/ts-toolbelt/commit/1d02fd4))
* security ([85a5673](https://github.com/millsp/ts-toolbelt/commit/85a5673))
* tidelift ([85cf046](https://github.com/millsp/ts-toolbelt/commit/85cf046))
* update ([2693059](https://github.com/millsp/ts-toolbelt/commit/2693059))

### [3.8.63](https://github.com/millsp/ts-toolbelt/compare/v3.8.62...v3.8.63) (2019-08-28)


### Bug Fixes

* make Pipe & Compose more tolerant with generics (unknown) ([a0c754d](https://github.com/millsp/ts-toolbelt/commit/a0c754d))


### Others

* update ([6a2ed21](https://github.com/millsp/ts-toolbelt/commit/6a2ed21))

### [3.8.62](https://github.com/millsp/ts-toolbelt/compare/v3.8.61...v3.8.62) (2019-08-28)


### Bug Fixes

* wrong promise unwrapping on Pipe & Compose ([6a5fb0d](https://github.com/millsp/ts-toolbelt/commit/6a5fb0d))


### Others

* cleanup scripts ([6db6939](https://github.com/millsp/ts-toolbelt/commit/6db6939))
* update ([4da0b05](https://github.com/millsp/ts-toolbelt/commit/4da0b05))

### [3.8.61](https://github.com/millsp/ts-toolbelt/compare/v3.8.60...v3.8.61) (2019-08-28)


### Others

* update ([632b60d](https://github.com/millsp/ts-toolbelt/commit/632b60d))
* update moto ([62c1e9c](https://github.com/millsp/ts-toolbelt/commit/62c1e9c))

### [3.8.60](https://github.com/millsp/ts-toolbelt/compare/v3.8.58...v3.8.60) (2019-08-28)


### Bug Fixes

* travis deploy ([6a1bce1](https://github.com/millsp/ts-toolbelt/commit/6a1bce1))


### Others

* update ([e7f9992](https://github.com/millsp/ts-toolbelt/commit/e7f9992))

### [3.8.58](https://github.com/millsp/ts-toolbelt/compare/v3.8.57...v3.8.58) (2019-08-27)


### Others

* update ([aeff96e](https://github.com/millsp/ts-toolbelt/commit/aeff96e))

### [3.8.57](https://github.com/millsp/ts-toolbelt/compare/v3.8.56...v3.8.57) (2019-08-27)


### Others

* debug ([6ca2e9a](https://github.com/millsp/ts-toolbelt/commit/6ca2e9a))
* debug ([748e8e7](https://github.com/millsp/ts-toolbelt/commit/748e8e7))
* update ([12a489e](https://github.com/millsp/ts-toolbelt/commit/12a489e))

### [3.8.56](https://github.com/millsp/ts-toolbelt/compare/v3.8.54...v3.8.56) (2019-08-27)


### Others

* debug ([18d80d9](https://github.com/millsp/ts-toolbelt/commit/18d80d9))
* update ([a76f1ca](https://github.com/millsp/ts-toolbelt/commit/a76f1ca))

### [3.8.54](https://github.com/millsp/ts-toolbelt/compare/v3.8.53...v3.8.54) (2019-08-27)


### Bug Fixes

* attempt to fix [#42](https://github.com/millsp/ts-toolbelt/issues/42) ([47a8770](https://github.com/millsp/ts-toolbelt/commit/47a8770))


### Others

* update ([21b383b](https://github.com/millsp/ts-toolbelt/commit/21b383b))

### [3.8.53](https://github.com/millsp/ts-toolbelt/compare/v3.8.52...v3.8.53) (2019-08-27)


### Bug Fixes

* add missing && ([e4d5da8](https://github.com/millsp/ts-toolbelt/commit/e4d5da8))
* attempt to fix [#42](https://github.com/millsp/ts-toolbelt/issues/42) ([d58cea8](https://github.com/millsp/ts-toolbelt/commit/d58cea8))


### Others

* update ([422cdab](https://github.com/millsp/ts-toolbelt/commit/422cdab))

### [3.8.52](https://github.com/millsp/ts-toolbelt/compare/v3.8.51...v3.8.52) (2019-08-27)


### Others

* describe CI in readme ([779852a](https://github.com/millsp/ts-toolbelt/commit/779852a))
* re-enable git tags ([b82ba9a](https://github.com/millsp/ts-toolbelt/commit/b82ba9a))
* update ([77b77fe](https://github.com/millsp/ts-toolbelt/commit/77b77fe))

### [3.8.51](https://github.com/millsp/ts-toolbelt/compare/v3.8.50...v3.8.51) (2019-08-26)


### Bug Fixes

* upgrade nodejs ([f2f4e78](https://github.com/millsp/ts-toolbelt/commit/f2f4e78))


### Others

* update ([a44fdf6](https://github.com/millsp/ts-toolbelt/commit/a44fdf6))

### [3.8.50](https://github.com/millsp/ts-toolbelt/compare/v3.8.49...v3.8.50) (2019-08-26)


### Bug Fixes

* improved travis scripts ([73949a6](https://github.com/millsp/ts-toolbelt/commit/73949a6))
* travis scripts ([093cd93](https://github.com/millsp/ts-toolbelt/commit/093cd93))


### Others

* eslint config ([5524463](https://github.com/millsp/ts-toolbelt/commit/5524463))
* update ([530dda3](https://github.com/millsp/ts-toolbelt/commit/530dda3))

### [3.8.49](https://github.com/millsp/ts-toolbelt/compare/v3.8.48...v3.8.49) (2019-08-24)


### Bug Fixes

* travis branch conditions for build ([4ffcfdb](https://github.com/millsp/ts-toolbelt/commit/4ffcfdb))


### Others

* update ([74637b8](https://github.com/millsp/ts-toolbelt/commit/74637b8))

### [3.8.48](https://github.com/millsp/ts-toolbelt/compare/v3.8.47...v3.8.48) (2019-08-24)


### Bug Fixes

* travis scripts ([437e503](https://github.com/millsp/ts-toolbelt/commit/437e503))


### Others

* update ([9d522ba](https://github.com/millsp/ts-toolbelt/commit/9d522ba))

### [3.8.47](https://github.com/millsp/ts-toolbelt/compare/v3.8.46...v3.8.47) (2019-08-24)


### Bug Fixes

* travis scripts ([be42804](https://github.com/millsp/ts-toolbelt/commit/be42804))


### Others

* update ([92d4d07](https://github.com/millsp/ts-toolbelt/commit/92d4d07))

### [3.8.46](https://github.com/millsp/ts-toolbelt/compare/v3.8.45...v3.8.46) (2019-08-24)


### Bug Fixes

* prevent double doc gen ([1298e5e](https://github.com/millsp/ts-toolbelt/commit/1298e5e))


### Others

* update ([833bcf0](https://github.com/millsp/ts-toolbelt/commit/833bcf0))

### [3.8.45](https://github.com/millsp/ts-toolbelt/compare/v3.8.44...v3.8.45) (2019-08-24)


### Bug Fixes

* less piggy script for travis ([87b8eb7](https://github.com/millsp/ts-toolbelt/commit/87b8eb7))


### Others

* reorganize scripts ([dd35557](https://github.com/millsp/ts-toolbelt/commit/dd35557))
* testing travis deploy ([1307350](https://github.com/millsp/ts-toolbelt/commit/1307350))
* update ([95be342](https://github.com/millsp/ts-toolbelt/commit/95be342))
* update ([90e1cdb](https://github.com/millsp/ts-toolbelt/commit/90e1cdb))

### [3.8.44](https://github.com/millsp/ts-toolbelt/compare/v3.8.43...v3.8.44) (2019-08-23)


### Bug Fixes

* missing build:docs ([0db3a23](https://github.com/millsp/ts-toolbelt/commit/0db3a23))


### Others

* update ([9e2828b](https://github.com/millsp/ts-toolbelt/commit/9e2828b))
* update ([7acf28e](https://github.com/millsp/ts-toolbelt/commit/7acf28e))

### [3.8.43](https://github.com/millsp/ts-toolbelt/compare/v3.8.42...v3.8.43) (2019-08-23)


### Bug Fixes

* missing build:docs ([4c16a64](https://github.com/millsp/ts-toolbelt/commit/4c16a64))
* reorganize scripts ([4f31bd4](https://github.com/millsp/ts-toolbelt/commit/4f31bd4))


### Others

* update ([3ad3242](https://github.com/millsp/ts-toolbelt/commit/3ad3242))

### [3.8.42](https://github.com/millsp/ts-toolbelt/compare/v3.8.41...v3.8.42) (2019-08-23)


### Bug Fixes

* fd overflow travis ([0659e25](https://github.com/millsp/ts-toolbelt/commit/0659e25))


### Others

* update ([a9893c8](https://github.com/millsp/ts-toolbelt/commit/a9893c8))

### [3.8.41](https://github.com/millsp/ts-toolbelt/compare/v3.8.36...v3.8.41) (2019-08-23)


### Bug Fixes

* version ([7cf6d6c](https://github.com/millsp/ts-toolbelt/commit/7cf6d6c))


### Others

* update ([a986609](https://github.com/millsp/ts-toolbelt/commit/a986609))

### [3.8.36](https://github.com/millsp/ts-toolbelt/compare/v3.8.35...v3.8.36) (2019-08-23)


### Others

* update ([86ac333](https://github.com/millsp/ts-toolbelt/commit/86ac333))

### [3.8.35](https://github.com/millsp/ts-toolbelt/compare/v3.8.34...v3.8.35) (2019-08-23)


### Bug Fixes

* build docs on master only, run tests against dt dependents ([50fa9c9](https://github.com/millsp/ts-toolbelt/commit/50fa9c9))
* do not track dt ([8dc2f11](https://github.com/millsp/ts-toolbelt/commit/8dc2f11))
* dt test script ([1ea1376](https://github.com/millsp/ts-toolbelt/commit/1ea1376))
* test dt with local version ([20e3b90](https://github.com/millsp/ts-toolbelt/commit/20e3b90))


### Others

* update ([5f806b7](https://github.com/millsp/ts-toolbelt/commit/5f806b7))
* update ([dbfd642](https://github.com/millsp/ts-toolbelt/commit/dbfd642))

### [3.8.34](https://github.com/millsp/ts-toolbelt/compare/v3.8.33...v3.8.34) (2019-08-23)


### Bug Fixes

* ci test flow ([c87e891](https://github.com/millsp/ts-toolbelt/commit/c87e891))
* typo ([f04d3af](https://github.com/millsp/ts-toolbelt/commit/f04d3af))

### [3.8.33](https://github.com/millsp/ts-toolbelt/compare/v3.8.32...v3.8.33) (2019-08-23)


### Bug Fixes

* travis did not push mater (tags disabled) ([b5238a1](https://github.com/millsp/ts-toolbelt/commit/b5238a1))


### Others

* merge branch next ([6b9b185](https://github.com/millsp/ts-toolbelt/commit/6b9b185))
* move folder ([97ad6bd](https://github.com/millsp/ts-toolbelt/commit/97ad6bd))
* tests for dt ([054b2a3](https://github.com/millsp/ts-toolbelt/commit/054b2a3))

### [3.8.24](https://github.com/millsp/ts-toolbelt/compare/v3.8.23...v3.8.24) (2019-08-23)


### Others

* update ([7788d64](https://github.com/millsp/ts-toolbelt/commit/7788d64))
* **release:** 3.8.24 ([b6ba9bd](https://github.com/millsp/ts-toolbelt/commit/b6ba9bd))

### [3.8.24](https://github.com/millsp/ts-toolbelt/compare/v3.8.23...v3.8.24) (2019-08-23)


### Others

* release with Travis works ([fd4a19a](https://github.com/millsp/ts-toolbelt/commit/fd4a19a))
* update ([7788d64](https://github.com/millsp/ts-toolbelt/commit/7788d64))
### [3.8.32](https://github.com/millsp/ts-toolbelt/compare/v3.8.31...v3.8.32) (2019-08-23)


### Bug Fixes

* docs only build for master ([fe5f823](https://github.com/millsp/ts-toolbelt/commit/fe5f823))
* travis build ([31b08ba](https://github.com/millsp/ts-toolbelt/commit/31b08ba))
* travis was cleaning files to publish ([35883f5](https://github.com/millsp/ts-toolbelt/commit/35883f5))

### [3.8.31](https://github.com/millsp/ts-toolbelt/compare/v3.8.23...v3.8.31) (2019-08-23)


### Bug Fixes

* travis deploy stages for branches ([03cdb1f](https://github.com/millsp/ts-toolbelt/commit/03cdb1f))
* update version ([2d466b4](https://github.com/millsp/ts-toolbelt/commit/2d466b4))


### Others

* release with Travis works ([fd4a19a](https://github.com/millsp/ts-toolbelt/commit/fd4a19a))
* **release:** 3.8.24 ([a09644b](https://github.com/millsp/ts-toolbelt/commit/a09644b))
* update ([5f9abb6](https://github.com/millsp/ts-toolbelt/commit/5f9abb6))
* update ([4d5fd4a](https://github.com/millsp/ts-toolbelt/commit/4d5fd4a))

### [3.8.24](https://github.com/millsp/ts-toolbelt/compare/v3.8.23...v3.8.24) (2019-08-23)


### Bug Fixes

* travis deploy stages for branches ([03cdb1f](https://github.com/millsp/ts-toolbelt/commit/03cdb1f))


### Others

* release with Travis works ([fd4a19a](https://github.com/millsp/ts-toolbelt/commit/fd4a19a))
* update ([4d5fd4a](https://github.com/millsp/ts-toolbelt/commit/4d5fd4a))

### [3.8.23](https://github.com/millsp/ts-toolbelt/compare/v3.8.22...v3.8.23) (2019-08-23)


### Others

* testing Travis deploy ([c29c6d7](https://github.com/millsp/ts-toolbelt/commit/c29c6d7))
* update ([0ba201a](https://github.com/millsp/ts-toolbelt/commit/0ba201a))

### [3.8.22](https://github.com/millsp/ts-toolbelt/compare/v3.8.21...v3.8.22) (2019-08-23)


### Others

* setup Travis for release ([b5b23e9](https://github.com/millsp/ts-toolbelt/commit/b5b23e9))
* update ([0dbbd3c](https://github.com/millsp/ts-toolbelt/commit/0dbbd3c))

### [3.8.21](https://github.com/millsp/ts-toolbelt/compare/v3.8.20...v3.8.21) (2019-08-23)


### Bug Fixes

* typo ([807c255](https://github.com/millsp/ts-toolbelt/commit/807c255))


### Others

* update ([f72f07f](https://github.com/millsp/ts-toolbelt/commit/f72f07f))

### [3.8.20](https://github.com/millsp/ts-toolbelt/compare/v3.8.19...v3.8.20) (2019-08-23)


### Bug Fixes

* typo ([e9b3a85](https://github.com/millsp/ts-toolbelt/commit/e9b3a85))


### Others

* let Travis do the release if success ([a374011](https://github.com/millsp/ts-toolbelt/commit/a374011))
* update ([828abe7](https://github.com/millsp/ts-toolbelt/commit/828abe7))

### [3.8.19](https://github.com/millsp/ts-toolbelt/compare/v3.8.18...v3.8.19) (2019-08-23)


### Bug Fixes

* improved performance for Curry ([17f77f0](https://github.com/millsp/ts-toolbelt/commit/17f77f0))


### Others

* update ([c01cbd1](https://github.com/millsp/ts-toolbelt/commit/c01cbd1))

### [3.8.18](https://github.com/millsp/ts-toolbelt/compare/v3.8.17...v3.8.18) (2019-08-23)


### Others

* update ([6f4e44e](https://github.com/millsp/ts-toolbelt/commit/6f4e44e))

### [3.8.17](https://github.com/millsp/ts-toolbelt/compare/v3.8.16...v3.8.17) (2019-08-23)


### Bug Fixes

* [#41](https://github.com/millsp/ts-toolbelt/issues/41) ([b6ceeb9](https://github.com/millsp/ts-toolbelt/commit/b6ceeb9))


### Others

* update ([82affd0](https://github.com/millsp/ts-toolbelt/commit/82affd0))

### [3.8.16](https://github.com/millsp/ts-toolbelt/compare/v3.8.9...v3.8.16) (2019-08-23)


### Bug Fixes

* don't push tags on non-master branch ([13708df](https://github.com/millsp/ts-toolbelt/commit/13708df))
* ramda tests ([607fb01](https://github.com/millsp/ts-toolbelt/commit/607fb01))
* version ([31e28c1](https://github.com/millsp/ts-toolbelt/commit/31e28c1))


### Others

* **release:** 3.8.10 ([80d7af6](https://github.com/millsp/ts-toolbelt/commit/80d7af6))
* update ([6d28807](https://github.com/millsp/ts-toolbelt/commit/6d28807))
* update ([eb1110a](https://github.com/millsp/ts-toolbelt/commit/eb1110a))

### [3.8.10](https://github.com/millsp/ts-toolbelt/compare/v3.8.9...v3.8.10) (2019-08-23)


### Bug Fixes

* ramda tests ([607fb01](https://github.com/millsp/ts-toolbelt/commit/607fb01))


### Others

* update ([eb1110a](https://github.com/millsp/ts-toolbelt/commit/eb1110a))

### [3.8.9](https://github.com/millsp/ts-toolbelt/compare/v3.8.8...v3.8.9) (2019-08-23)


### Others

* update ([23a33a6](https://github.com/millsp/ts-toolbelt/commit/23a33a6))

### [3.8.8](https://github.com/millsp/ts-toolbelt/compare/v3.8.4...v3.8.8) (2019-08-23)


### Bug Fixes

* rollback ([361fa40](https://github.com/millsp/ts-toolbelt/commit/361fa40))


### Others

* update ([b680acb](https://github.com/millsp/ts-toolbelt/commit/b680acb))

### [3.8.4](https://github.com/millsp/ts-toolbelt/compare/v3.8.3...v3.8.4) (2019-08-22)


### Bug Fixes

* compose & pipe were transforming array properties ([5c423f6](https://github.com/millsp/ts-toolbelt/commit/5c423f6))
* https://github.com/DefinitelyTyped/DefinitelyTyped/issues/37837 ([62b783e](https://github.com/millsp/ts-toolbelt/commit/62b783e))


### Others

* update ([6776a9c](https://github.com/millsp/ts-toolbelt/commit/6776a9c))

### [3.8.3](https://github.com/millsp/ts-toolbelt/compare/v3.8.2...v3.8.3) (2019-08-20)


### Bug Fixes

* simplify the way Head works ([daaaa23](https://github.com/millsp/ts-toolbelt/commit/daaaa23))


### Others

* update ([678153e](https://github.com/millsp/ts-toolbelt/commit/678153e))

### [3.8.2](https://github.com/millsp/ts-toolbelt/compare/v3.8.1...v3.8.2) (2019-08-20)


### Bug Fixes

* now PromiseOf unwraps any kind of type like the JS Promise does ([186544d](https://github.com/millsp/ts-toolbelt/commit/186544d))


### Others

* update ([c8e9a5f](https://github.com/millsp/ts-toolbelt/commit/c8e9a5f))

### [3.8.1](https://github.com/millsp/ts-toolbelt/compare/v3.8.0...v3.8.1) (2019-08-19)


### Others

* fix inconsitencies ([94f5e71](https://github.com/millsp/ts-toolbelt/commit/94f5e71))
* removed old wiki from readme ([9bb4e05](https://github.com/millsp/ts-toolbelt/commit/9bb4e05))
* update ([d0605ec](https://github.com/millsp/ts-toolbelt/commit/d0605ec))

## [3.8.0](https://github.com/millsp/ts-toolbelt/compare/v3.7.1...v3.8.0) (2019-08-16)


### Features

* added Invert type for Object ([1f41a09](https://github.com/millsp/ts-toolbelt/commit/1f41a09))


### Others

* added tests for Invert ([ddadee1](https://github.com/millsp/ts-toolbelt/commit/ddadee1))
* update ([00dee85](https://github.com/millsp/ts-toolbelt/commit/00dee85))

### [3.7.1](https://github.com/millsp/ts-toolbelt/compare/v3.7.0...v3.7.1) (2019-08-12)


### Bug Fixes

* resolved Compute breaking unions ([08da1fa](https://github.com/millsp/ts-toolbelt/commit/08da1fa))


### Others

* update ([07df193](https://github.com/millsp/ts-toolbelt/commit/07df193))

## [3.7.0](https://github.com/millsp/ts-toolbelt/compare/v3.6.1...v3.7.0) (2019-08-12)


### Bug Fixes

* changelog had `feat` in wrong category ([2b6a86e](https://github.com/millsp/ts-toolbelt/commit/2b6a86e))
* improved type distribution on TupleOf, ObjectOf, Last, IntersectOf ([0e628df](https://github.com/millsp/ts-toolbelt/commit/0e628df))


### Features

* added a Keys type for Union ([3ce0afd](https://github.com/millsp/ts-toolbelt/commit/3ce0afd))
* added a Strict type for Union ([7997fc0](https://github.com/millsp/ts-toolbelt/commit/7997fc0))
* added an Either type for Object, Tuple ([740742c](https://github.com/millsp/ts-toolbelt/commit/740742c))


### Others

* added comments ([ac21f1b](https://github.com/millsp/ts-toolbelt/commit/ac21f1b))
* update ([3fc977d](https://github.com/millsp/ts-toolbelt/commit/3fc977d))

### [3.6.1](https://github.com/millsp/ts-toolbelt/compare/v3.6.0...v3.6.1) (2019-08-10)


### Others

* added tests for Assign ([2b7ca70](https://github.com/millsp/ts-toolbelt/commit/2b7ca70))
* update ([87737d8](https://github.com/millsp/ts-toolbelt/commit/87737d8))

## [3.6.0](https://github.com/millsp/ts-toolbelt/compare/v3.5.1...v3.6.0) (2019-08-10)


### Others

* fix tests for DT ([253b179](https://github.com/millsp/ts-toolbelt/commit/253b179))
* fix tslint default type for DT ([5fcade2](https://github.com/millsp/ts-toolbelt/commit/5fcade2))
* fix typo for DT ([9685af8](https://github.com/millsp/ts-toolbelt/commit/9685af8))
* fix typo for DT ([fb25217](https://github.com/millsp/ts-toolbelt/commit/fb25217))
* **function:** the Pipe and Compose can be async ([8d9d974](https://github.com/millsp/ts-toolbelt/commit/8d9d974))
* update ([cf140f2](https://github.com/millsp/ts-toolbelt/commit/cf140f2))

### [3.5.1](https://github.com/millsp/ts-toolbelt/compare/v3.5.0...v3.5.1) (2019-08-09)


### Others

* prepare the tests to be compatible with DT ([f5f060b](https://github.com/millsp/ts-toolbelt/commit/f5f060b))
* update ([3be3e93](https://github.com/millsp/ts-toolbelt/commit/3be3e93))

## [3.5.0](https://github.com/millsp/ts-toolbelt/compare/v3.4.1...v3.5.0) (2019-08-08)


### Others

* **assign:** assign for Object & Tuple ([8ecc461](https://github.com/millsp/ts-toolbelt/commit/8ecc461))
* update ([7094d07](https://github.com/millsp/ts-toolbelt/commit/7094d07))

### [3.4.1](https://github.com/millsp/ts-toolbelt/compare/v3.4.0...v3.4.1) (2019-08-08)


### Others

* finish renaming arrow to function ([55fbb78](https://github.com/millsp/ts-toolbelt/commit/55fbb78))
* update ([22c4cd3](https://github.com/millsp/ts-toolbelt/commit/22c4cd3))

## [3.4.0](https://github.com/millsp/ts-toolbelt/compare/v3.2.23...v3.4.0) (2019-08-08)


### Bug Fixes

* **p.merge:** now picks up fields as it dives [#28](https://github.com/millsp/ts-toolbelt/issues/28) ([68d49f0](https://github.com/millsp/ts-toolbelt/commit/68d49f0))
* bump version ([0059d8c](https://github.com/millsp/ts-toolbelt/commit/0059d8c))


### Others

* **release:** 3.2.24 ([fdeab77](https://github.com/millsp/ts-toolbelt/commit/fdeab77))
* cleanup ([5eae9ff](https://github.com/millsp/ts-toolbelt/commit/5eae9ff))
* cleanup ([b65c757](https://github.com/millsp/ts-toolbelt/commit/b65c757))
* **release:** 3.3.0 ([0f0a4ea](https://github.com/millsp/ts-toolbelt/commit/0f0a4ea))
* **tsconfig:** add missing fields ([c97a34c](https://github.com/millsp/ts-toolbelt/commit/c97a34c))
* **tuple:** added EndOf type ([73ddc8a](https://github.com/millsp/ts-toolbelt/commit/73ddc8a))
* update ([5e26347](https://github.com/millsp/ts-toolbelt/commit/5e26347))
* update ([c9890c8](https://github.com/millsp/ts-toolbelt/commit/c9890c8))
* update ([5539f64](https://github.com/millsp/ts-toolbelt/commit/5539f64))
* using EndOf ([8154168](https://github.com/millsp/ts-toolbelt/commit/8154168))

## [3.3.0](https://github.com/millsp/ts-toolbelt/compare/v3.2.23...v3.3.0) (2019-08-08)


### Bug Fixes

* **p.merge:** now picks up fields as it dives [#28](https://github.com/millsp/ts-toolbelt/issues/28) ([68d49f0](https://github.com/millsp/ts-toolbelt/commit/68d49f0))


### Others

* **release:** 3.2.24 ([fdeab77](https://github.com/millsp/ts-toolbelt/commit/fdeab77))
* **tsconfig:** add missing fields ([c97a34c](https://github.com/millsp/ts-toolbelt/commit/c97a34c))
* **tuple:** added EndOf type ([73ddc8a](https://github.com/millsp/ts-toolbelt/commit/73ddc8a))
* cleanup ([5eae9ff](https://github.com/millsp/ts-toolbelt/commit/5eae9ff))
* cleanup ([b65c757](https://github.com/millsp/ts-toolbelt/commit/b65c757))
* update ([c9890c8](https://github.com/millsp/ts-toolbelt/commit/c9890c8))
* update ([5539f64](https://github.com/millsp/ts-toolbelt/commit/5539f64))
* using EndOf ([8154168](https://github.com/millsp/ts-toolbelt/commit/8154168))

### [3.2.24](https://github.com/millsp/ts-toolbelt/compare/v3.2.23...v3.2.24) (2019-08-07)


### Bug Fixes

* **p.merge:** now picks up fields as it dives [#28](https://github.com/millsp/ts-toolbelt/issues/28) ([68d49f0](https://github.com/millsp/ts-toolbelt/commit/68d49f0))


### Others

* **tsconfig:** add missing fields ([c97a34c](https://github.com/millsp/ts-toolbelt/commit/c97a34c))
* update ([5539f64](https://github.com/millsp/ts-toolbelt/commit/5539f64))

### [3.2.23](https://github.com/millsp/ts-toolbelt/compare/v3.2.22...v3.2.23) (2019-08-06)


### Others

* **readme:** more details ([a34faa6](https://github.com/millsp/ts-toolbelt/commit/a34faa6))
* **readme:** recommend strict ([70a9af8](https://github.com/millsp/ts-toolbelt/commit/70a9af8))
* update ([5676b1a](https://github.com/millsp/ts-toolbelt/commit/5676b1a))

### [3.2.22](https://github.com/millsp/ts-toolbelt/compare/v3.2.21...v3.2.22) (2019-08-05)


### Others

* **travis:** debug failure -> eslint version rollback ([5fcaffa](https://github.com/millsp/ts-toolbelt/commit/5fcaffa))
* **travis:** debug failure, cleared cache ([f6a919d](https://github.com/millsp/ts-toolbelt/commit/f6a919d))
* update ([f766846](https://github.com/millsp/ts-toolbelt/commit/f766846))

### [3.2.21](https://github.com/millsp/ts-toolbelt/compare/v3.2.20...v3.2.21) (2019-08-05)


### Others

* **travis:** debug failure ([3576ae8](https://github.com/millsp/ts-toolbelt/commit/3576ae8))
* update ([4369f09](https://github.com/millsp/ts-toolbelt/commit/4369f09))

### [3.2.20](https://github.com/millsp/ts-toolbelt/compare/v3.2.19...v3.2.20) (2019-08-05)


### Others

* **travis:** debug failure ([d5cb22a](https://github.com/millsp/ts-toolbelt/commit/d5cb22a))
* **travis:** debug failure ([ee3063a](https://github.com/millsp/ts-toolbelt/commit/ee3063a))
* update ([a592bae](https://github.com/millsp/ts-toolbelt/commit/a592bae))

### [3.2.19](https://github.com/millsp/ts-toolbelt/compare/v3.2.18...v3.2.19) (2019-08-05)


### Others

* update ([1c560de](https://github.com/millsp/ts-toolbelt/commit/1c560de))
* Update docstring for Curry type ([52f5c95](https://github.com/millsp/ts-toolbelt/commit/52f5c95))

### [3.2.18](https://github.com/millsp/ts-toolbelt/compare/v3.2.17...v3.2.18) (2019-08-05)


### Bug Fixes

* **eslint:** remove fp flags ([e009db0](https://github.com/millsp/ts-toolbelt/commit/e009db0))
* **eslint:** remove fp inlined flags ([f4848b0](https://github.com/millsp/ts-toolbelt/commit/f4848b0))


### Others

* **deps:** update deps versions ([993b9bb](https://github.com/millsp/ts-toolbelt/commit/993b9bb))
* update ([cf3f697](https://github.com/millsp/ts-toolbelt/commit/cf3f697))
* update ([1dec110](https://github.com/millsp/ts-toolbelt/commit/1dec110))

### [3.2.17](https://github.com/millsp/ts-toolbelt/compare/v3.2.16...v3.2.17) (2019-08-05)


### Others

* **eslint:** remove fp settings ([83a7658](https://github.com/millsp/ts-toolbelt/commit/83a7658))
* update ([83ced5f](https://github.com/millsp/ts-toolbelt/commit/83ced5f))

### [3.2.16](https://github.com/millsp/ts-toolbelt/compare/v3.2.15...v3.2.16) (2019-08-05)


### Others

* **cleanup:** removed fp ([647c622](https://github.com/millsp/ts-toolbelt/commit/647c622))
* **commits:** display all the commits in the changelog ([94d62ee](https://github.com/millsp/ts-toolbelt/commit/94d62ee))
* update ([8c23630](https://github.com/millsp/ts-toolbelt/commit/8c23630))

### [3.2.15](https://github.com/millsp/ts-toolbelt/compare/v3.2.14...v3.2.15) (2019-08-05)


### Bug Fixes

* **path types:** fixes [#26](https://github.com/millsp/ts-toolbelt/issues/26), adds docs + tests for O.P ([5b601a7](https://github.com/millsp/ts-toolbelt/commit/5b601a7))

### [3.2.14](https://github.com/millsp/ts-toolbelt/compare/v3.2.13...v3.2.14) (2019-08-02)


### Bug Fixes

* **doc-gen:** docs are now commited ([8a7adfb](https://github.com/millsp/ts-toolbelt/commit/8a7adfb))

### [3.2.13](https://github.com/millsp/ts-toolbelt/compare/v3.2.12...v3.2.13) (2019-08-02)


### Bug Fixes

* **release:** did not build on release cmd ([b0fba7e](https://github.com/millsp/ts-toolbelt/commit/b0fba7e))

### [3.2.12](https://github.com/millsp/ts-toolbelt/compare/v3.2.11...v3.2.12) (2019-08-01)

### [3.2.11](https://github.com/millsp/ts-toolbelt/compare/v3.2.10...v3.2.11) (2019-08-01)

### [3.2.10](https://github.com/millsp/ts-toolbelt/compare/v3.2.9...v3.2.10) (2019-08-01)


### Bug Fixes

* **ci:** added npx, travis does not have it ([b336220](https://github.com/millsp/ts-toolbelt/commit/b336220))

### [3.2.9](https://github.com/millsp/ts-toolbelt/compare/v3.2.8...v3.2.9) (2019-08-01)


### Bug Fixes

* **release:** changed HEAD to $BRANCH ([af8999b](https://github.com/millsp/ts-toolbelt/commit/af8999b))
* **release:** let standard-version do the commit ([8115e5e](https://github.com/millsp/ts-toolbelt/commit/8115e5e))
* **tests:** fix tests not runnin gin release ([3a833a4](https://github.com/millsp/ts-toolbelt/commit/3a833a4))

### [3.2.8](https://github.com/millsp/ts-toolbelt/compare/v3.2.7...v3.2.8) (2019-08-01)

### [3.2.7](https://github.com/millsp/ts-toolbelt/compare/v3.2.6...v3.2.7) (2019-08-01)

### [3.2.6](https://github.com/millsp/ts-toolbelt/compare/v3.2.5...v3.2.6) (2019-08-01)

### [3.2.5](https://github.com/millsp/ts-toolbelt/compare/v3.2.4...v3.2.5) (2019-08-01)

### [3.2.4](https://github.com/millsp/ts-toolbelt/compare/v3.2.3...v3.2.4) (2019-08-01)

## [3.2.0-0](https://github.com/millsp/ts-toolbelt/compare/v3.1.6...v3.2.0-0) (2019-08-01)

### [3.1.6](https://github.com/millsp/ts-toolbelt/compare/v3.1.5...v3.1.6) (2019-08-01)

### [3.1.5](https://github.com/millsp/ts-toolbelt/compare/v3.1.4...v3.1.5) (2019-08-01)

### 3.1.4 (2019-08-01)

### [3.2.3](https://github.com/millsp/ts-toolbelt/compare/v3.2.2...v3.2.3) (2019-08-01)

### 3.2.2 (2019-08-01)


### Bug Fixes

* package version ([0b22663](https://github.com/millsp/ts-toolbelt/commit/0b22663))

### 3.1.3 (2019-07-31)

### 3.1.2 (2019-07-30)

### 3.1.1 (2019-07-25)

## 3.1.0 (2019-07-25)

### 3.0.5 (2019-07-25)

### 3.0.4 (2019-07-25)

### 3.0.2 (2019-07-25)

## 3.0.0 (2019-07-25)

## 3.0.0-12 (2019-07-23)

## 3.0.0-11 (2019-07-16)

## 3.0.0-10 (2019-07-16)

## 3.0.0-9 (2019-07-08)

## 3.0.0-8 (2019-07-08)

## 3.0.0-7 (2019-07-08)

## 3.0.0-6 (2019-07-07)

## 3.0.0-5 (2019-07-07)

## 3.0.0-4 (2019-07-02)

## 3.0.0-3 (2019-07-02)

## 3.0.0-2 (2019-07-01)

## 3.0.0-1 (2019-07-01)

## 3.0.0-0 (2019-07-01)

### 2.1.5 (2019-06-29)

### 2.1.4 (2019-06-29)

### 2.1.3 (2019-06-28)

### 2.1.2 (2019-06-28)

### 2.1.1 (2019-06-27)

## 2.1.0 (2019-06-27)

### 2.0.17 (2019-06-27)

### 2.0.16 (2019-06-27)

### 2.0.15 (2019-06-27)

### 2.0.14 (2019-06-26)

### 2.0.13 (2019-06-26)

### 2.0.12 (2019-06-26)

### 2.0.11 (2019-06-26)

### 2.0.10 (2019-06-26)

### 2.0.9 (2019-06-26)

### 2.0.8 (2019-06-26)

### 2.0.7 (2019-06-26)

### 2.0.6 (2019-06-26)

### 2.0.5 (2019-06-26)

### 2.0.4 (2019-06-25)

### 2.0.3 (2019-06-25)

### 2.0.2 (2019-06-25)

### 2.0.1 (2019-06-25)

## 2.0.0 (2019-06-25)

### 1.1.14 (2019-06-23)

### 1.1.13 (2019-06-22)

### 1.1.12 (2019-06-21)

### 1.1.11 (2019-06-21)

### 1.1.10 (2019-06-21)

### 1.1.9 (2019-06-21)

### 1.1.8 (2019-06-21)

### 1.1.7 (2019-06-21)

### 1.1.6 (2019-06-20)

### 1.1.5 (2019-06-20)

### 1.1.4 (2019-06-20)

### 1.1.3 (2019-06-20)

### 1.1.2 (2019-06-20)

### 1.1.1 (2019-06-20)

## 1.1.0 (2019-06-20)

### 1.0.55 (2019-06-20)

### 1.0.54 (2019-06-20)

### 1.0.53 (2019-06-20)

### 1.0.52 (2019-06-19)

### 1.0.49 (2019-06-19)

### 1.0.48 (2019-06-19)

### 1.0.47 (2019-06-19)

### 1.0.46 (2019-06-19)

### 1.0.45 (2019-06-19)

### 1.0.44 (2019-06-19)

### 1.0.43 (2019-06-19)

### 1.0.42 (2019-06-19)

### 1.0.41 (2019-06-19)

### 1.0.40 (2019-06-19)

### 1.0.39 (2019-06-19)

### 1.0.38 (2019-06-19)

### 1.0.37 (2019-06-19)

### 1.0.36 (2019-06-19)

### 1.0.35 (2019-06-19)

### 1.0.34 (2019-06-19)

### 1.0.33 (2019-06-18)

### 1.0.32 (2019-06-18)

### 1.0.31 (2019-06-18)

### 1.0.30 (2019-06-18)

### 1.0.29 (2019-06-18)

### 1.0.28 (2019-06-18)

### 1.0.27 (2019-06-18)

### 1.0.26 (2019-06-18)

### 1.0.25 (2019-06-18)

### 1.0.24 (2019-06-18)

### 1.0.23 (2019-06-18)

### 1.0.22 (2019-06-18)

### 1.0.21 (2019-06-18)

### 1.0.20 (2019-06-18)

### 1.0.19 (2019-06-18)

### 3.1.4 (2019-08-01)

### 3.1.3 (2019-07-31)

### 3.1.2 (2019-07-30)

### 3.1.1 (2019-07-25)

## 3.1.0 (2019-07-25)

### 3.0.5 (2019-07-25)

### 3.0.4 (2019-07-25)

### 3.0.2 (2019-07-25)

## 3.0.0 (2019-07-25)

## 3.0.0-12 (2019-07-23)

## 3.0.0-11 (2019-07-16)

## 3.0.0-10 (2019-07-16)

## 3.0.0-9 (2019-07-08)

## 3.0.0-8 (2019-07-08)

## 3.0.0-7 (2019-07-08)

## 3.0.0-6 (2019-07-07)

## 3.0.0-5 (2019-07-07)

## 3.0.0-4 (2019-07-02)

## 3.0.0-3 (2019-07-02)

## 3.0.0-2 (2019-07-01)

## 3.0.0-1 (2019-07-01)

## 3.0.0-0 (2019-07-01)

### 2.1.5 (2019-06-29)

### 2.1.4 (2019-06-29)

### 2.1.3 (2019-06-28)

### 2.1.2 (2019-06-28)

### 2.1.1 (2019-06-27)

## 2.1.0 (2019-06-27)

### 2.0.17 (2019-06-27)

### 2.0.16 (2019-06-27)

### 2.0.15 (2019-06-27)

### 2.0.14 (2019-06-26)

### 2.0.13 (2019-06-26)

### 2.0.12 (2019-06-26)

### 2.0.11 (2019-06-26)

### 2.0.10 (2019-06-26)

### 2.0.9 (2019-06-26)

### 2.0.8 (2019-06-26)

### 2.0.7 (2019-06-26)

### 2.0.6 (2019-06-26)

### 2.0.5 (2019-06-26)

### 2.0.4 (2019-06-25)

### 2.0.3 (2019-06-25)

### 2.0.2 (2019-06-25)

### 2.0.1 (2019-06-25)

## 2.0.0 (2019-06-25)

### 1.1.14 (2019-06-23)

### 1.1.13 (2019-06-22)

### 1.1.12 (2019-06-21)

### 1.1.11 (2019-06-21)

### 1.1.10 (2019-06-21)

### 1.1.9 (2019-06-21)

### 1.1.8 (2019-06-21)

### 1.1.7 (2019-06-21)

### 1.1.6 (2019-06-20)

### 1.1.5 (2019-06-20)

### 1.1.4 (2019-06-20)

### 1.1.3 (2019-06-20)

### 1.1.2 (2019-06-20)

### 1.1.1 (2019-06-20)

## 1.1.0 (2019-06-20)

### 1.0.55 (2019-06-20)

### 1.0.54 (2019-06-20)

### 1.0.53 (2019-06-20)

### 1.0.52 (2019-06-19)

### 1.0.49 (2019-06-19)

### 1.0.48 (2019-06-19)

### 1.0.47 (2019-06-19)

### 1.0.46 (2019-06-19)

### 1.0.45 (2019-06-19)

### 1.0.44 (2019-06-19)

### 1.0.43 (2019-06-19)

### 1.0.42 (2019-06-19)

### 1.0.41 (2019-06-19)

### 1.0.40 (2019-06-19)

### 1.0.39 (2019-06-19)

### 1.0.38 (2019-06-19)

### 1.0.37 (2019-06-19)

### 1.0.36 (2019-06-19)

### 1.0.35 (2019-06-19)

### 1.0.34 (2019-06-19)

### 1.0.33 (2019-06-18)

### 1.0.32 (2019-06-18)

### 1.0.31 (2019-06-18)

### 1.0.30 (2019-06-18)

### 1.0.29 (2019-06-18)

### 1.0.28 (2019-06-18)

### 1.0.27 (2019-06-18)

### 1.0.26 (2019-06-18)

### 1.0.25 (2019-06-18)

### 1.0.24 (2019-06-18)

### 1.0.23 (2019-06-18)

### 1.0.22 (2019-06-18)

### 1.0.21 (2019-06-18)

### 1.0.20 (2019-06-18)

### 1.0.19 (2019-06-18)
