export declare function destroy_block(block: any, lookup: any): void;
export declare function outro_and_destroy_block(block: any, lookup: any): void;
export declare function fix_and_destroy_block(block: any, lookup: any): void;
export declare function fix_and_outro_and_destroy_block(block: any, lookup: any): void;
export declare function update_keyed_each(old_blocks: any, dirty: any, get_key: any, dynamic: any, ctx: any, list: any, lookup: any, node: any, destroy: any, create_each_block: any, next: any, get_context: any): any[];
export declare function validate_each_keys(ctx: any, list: any, get_context: any, get_key: any): void;
