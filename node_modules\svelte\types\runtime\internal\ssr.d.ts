export { is_void } from '../../shared/utils/names';
export declare const invalid_attribute_name_character: RegExp;
export declare function spread(args: any, attrs_to_add: any): string;
export declare function merge_ssr_styles(style_attribute: any, style_directive: any): {};
/**
 * Note: this method is performance sensitive and has been optimized
 * https://github.com/sveltejs/svelte/pull/5701
 */
export declare function escape(value: unknown, is_attr?: boolean): string;
export declare function escape_attribute_value(value: any): any;
export declare function escape_object(obj: any): {};
export declare function each(items: any, fn: any): string;
export declare const missing_component: {
    $$render: () => string;
};
export declare function validate_component(component: any, name: any): any;
export declare function debug(file: any, line: any, column: any, values: any): string;
export declare function create_ssr_component(fn: any): {
    render: (props?: {}, { $$slots, context }?: {
        $$slots?: {};
        context?: Map<any, any>;
    }) => {
        html: any;
        css: {
            code: string;
            map: any;
        };
        head: string;
    };
    $$render: (result: any, props: any, bindings: any, slots: any, context: any) => any;
};
export declare function add_attribute(name: any, value: any, boolean: any): string;
export declare function add_classes(classes: any): string;
export declare function add_styles(style_object: any): string;
