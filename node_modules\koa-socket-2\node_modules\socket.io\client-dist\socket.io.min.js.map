{"version": 3, "sources": ["webpack://io/webpack/universalModuleDefinition", "webpack://io/webpack/bootstrap", "webpack://io/./node_modules/component-emitter/index.js", "webpack://io/./node_modules/engine.io-parser/lib/index.js", "webpack://io/./node_modules/engine.io-client/lib/globalThis.browser.js", "webpack://io/./node_modules/engine.io-client/lib/transport.js", "webpack://io/./node_modules/parseqs/index.js", "webpack://io/./node_modules/socket.io-parser/dist/index.js", "webpack://io/./node_modules/parseuri/index.js", "webpack://io/./build/manager.js", "webpack://io/./node_modules/engine.io-client/lib/transports/index.js", "webpack://io/./node_modules/engine.io-client/lib/xmlhttprequest.js", "webpack://io/./node_modules/engine.io-client/lib/transports/polling.js", "webpack://io/./node_modules/engine.io-parser/lib/commons.js", "webpack://io/./node_modules/yeast/index.js", "webpack://io/./node_modules/engine.io-client/lib/util.js", "webpack://io/./build/socket.js", "webpack://io/./node_modules/socket.io-parser/dist/is-binary.js", "webpack://io/./build/on.js", "webpack://io/./build/index.js", "webpack://io/./build/url.js", "webpack://io/./node_modules/engine.io-client/lib/index.js", "webpack://io/./node_modules/engine.io-client/lib/socket.js", "webpack://io/./node_modules/has-cors/index.js", "webpack://io/./node_modules/engine.io-client/lib/transports/polling-xhr.js", "webpack://io/./node_modules/engine.io-parser/lib/encodePacket.browser.js", "webpack://io/./node_modules/engine.io-parser/lib/decodePacket.browser.js", "webpack://io/./node_modules/engine.io-parser/node_modules/base64-arraybuffer/lib/base64-arraybuffer.js", "webpack://io/./node_modules/engine.io-client/lib/transports/polling-jsonp.js", "webpack://io/./node_modules/engine.io-client/lib/transports/websocket.js", "webpack://io/./node_modules/engine.io-client/lib/transports/websocket-constructor.browser.js", "webpack://io/./node_modules/socket.io-parser/dist/binary.js", "webpack://io/./node_modules/backo2/index.js"], "names": ["root", "factory", "exports", "module", "define", "amd", "this", "installedModules", "__webpack_require__", "moduleId", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "Emitter", "obj", "mixin", "on", "addEventListener", "event", "fn", "_callbacks", "push", "once", "off", "apply", "arguments", "removeListener", "removeAllListeners", "removeEventListener", "length", "cb", "callbacks", "splice", "emit", "args", "Array", "len", "slice", "listeners", "hasListeners", "encodePacket", "require", "decodePacket", "SEPARATOR", "String", "fromCharCode", "protocol", "encodePayload", "packets", "callback", "encodedPackets", "count", "for<PERSON>ach", "packet", "encodedPacket", "join", "decodePayload", "encodedPayload", "binaryType", "split", "decodedPacket", "type", "self", "window", "Function", "parser", "Transport", "opts", "query", "readyState", "socket", "msg", "desc", "err", "Error", "description", "doOpen", "doClose", "onClose", "write", "writable", "data", "onPacket", "encode", "str", "encodeURIComponent", "decode", "qs", "qry", "pairs", "pair", "decodeURIComponent", "Decoder", "Encoder", "PacketType", "binary_1", "is_binary_1", "EVENT", "ACK", "hasBinary", "encodeAsString", "BINARY_EVENT", "BINARY_ACK", "encodeAsBinary", "attachments", "nsp", "id", "JSON", "stringify", "deconstruction", "deconstructPacket", "pack", "buffers", "unshift", "decodeString", "reconstructor", "BinaryReconstructor", "isBinary", "base64", "takeBinaryData", "Number", "char<PERSON>t", "undefined", "start", "buf", "substring", "next", "payload", "parse", "e", "try<PERSON><PERSON><PERSON>", "substr", "isPayloadValid", "finishedReconstruction", "CONNECT", "DISCONNECT", "CONNECT_ERROR", "isArray", "reconPack", "binData", "reconstructPacket", "re", "parts", "src", "b", "indexOf", "replace", "exec", "uri", "source", "host", "authority", "ipv6uri", "pathNames", "path", "names", "query<PERSON><PERSON>", "$0", "$1", "$2", "Manager", "eio", "socket_1", "on_1", "Backoff", "nsps", "subs", "reconnection", "reconnectionAttempts", "Infinity", "reconnectionDelay", "reconnectionDelayMax", "randomizationFactor", "backoff", "min", "max", "jitter", "timeout", "_readyState", "_parser", "encoder", "decoder", "_autoConnect", "autoConnect", "open", "v", "_reconnection", "_reconnectionAttempts", "_a", "_reconnectionDelay", "setMin", "_randomizationFactor", "setJitter", "_reconnectionDelayMax", "setMax", "_timeout", "_reconnecting", "attempts", "reconnect", "engine", "skipReconnect", "openSubDestroy", "onopen", "errorSub", "cleanup", "maybeReconnectOnOpen", "timer", "setTimeout", "close", "clearTimeout", "onping", "ondata", "onerror", "onclose", "ondecoded", "add", "Socket", "keys", "active", "_close", "options", "subDestroy", "destroy", "reset", "reason", "delay", "duration", "onreconnect", "attempt", "XMLHttpRequest", "XHR", "JSONP", "websocket", "polling", "xd", "xs", "jsonp", "location", "isSSL", "port", "hostname", "secure", "xdomain", "xscheme", "forceJSONP", "hasCORS", "globalThis", "enablesXDR", "XDomainRequest", "concat", "parseqs", "yeast", "Polling", "poll", "onPause", "pause", "total", "doPoll", "index", "onOpen", "doWrite", "schema", "timestampRequests", "timestampParam", "supportsBinary", "sid", "b64", "PACKET_TYPES", "PACKET_TYPES_REVERSE", "ERROR_PACKET", "prev", "alphabet", "map", "seed", "num", "encoded", "Math", "floor", "now", "Date", "decoded", "pick", "attr", "reduce", "acc", "k", "socket_io_parser_1", "RESERVED_EVENTS", "freeze", "connect", "connect_error", "disconnect", "disconnecting", "newListener", "io", "<PERSON><PERSON><PERSON><PERSON>", "send<PERSON><PERSON><PERSON>", "ids", "acks", "flags", "connected", "disconnected", "auth", "onpacket", "subEvents", "ev", "compress", "pop", "isTransportWritable", "transport", "discardPacket", "_packet", "onconnect", "onevent", "onack", "ondisconnect", "message", "ack", "emitEvent", "_anyListeners", "sent", "emitBuffered", "listener", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toString", "withNativeBlob", "Blob", "withNativeFile", "File", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "toJSON", "url_1", "manager_1", "lookup", "cache", "managers", "parsed", "url", "sameNamespace", "forceNew", "multiplex", "manager_2", "parseuri", "loc", "test", "ipv6", "href", "transports", "writeBuffer", "prevBufferLen", "agent", "withCredentials", "upgrade", "rememberUpgrade", "rejectUnauthorized", "perMessageDeflate", "threshold", "transportOptions", "upgrades", "pingInterval", "pingTimeout", "pingTimeoutTimer", "clone", "EIO", "priorWebsocketSuccess", "createTransport", "shift", "setTransport", "onDrain", "onError", "probe", "failed", "onTransportOpen", "onlyBinaryUpgrades", "upgradeLosesBinary", "send", "upgrading", "flush", "freezeTransport", "error", "onTransportClose", "onupgrade", "to", "onHandshake", "resetPingTimeout", "sendPacket", "code", "filterUpgrades", "cleanupAndClose", "waitForUpgrade", "pingIntervalTimer", "filteredUpgrades", "j", "empty", "hasXHR2", "responseType", "forceBase64", "Request", "req", "request", "method", "onData", "pollXhr", "async", "xhr", "extraHeaders", "setDisableHeaderCheck", "setRequestHeader", "requestTimeout", "hasXDR", "onload", "onLoad", "responseText", "onreadystatechange", "status", "document", "requestsCount", "requests", "onSuccess", "fromError", "abort", "attachEvent", "unload<PERSON><PERSON><PERSON>", "encodeBlobAsBase64", "fileReader", "FileReader", "content", "result", "readAsDataURL", "base64decoder", "decodeBase64Packet", "mapBinary", "chars", "arraybuffer", "bytes", "Uint8Array", "encoded1", "encoded2", "encoded3", "encoded4", "bufferLength", "rNewline", "rEscapedNewline", "JSONPPolling", "___eio", "script", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "form", "iframe", "createElement", "insertAt", "getElementsByTagName", "insertBefore", "head", "body", "append<PERSON><PERSON><PERSON>", "navigator", "userAgent", "area", "iframeId", "className", "style", "position", "top", "left", "target", "setAttribute", "complete", "initIframe", "html", "action", "submit", "WebSocket", "usingBrowserWebSocket", "defaultBinaryType", "isReactNative", "product", "toLowerCase", "WS", "check", "protocols", "headers", "ws", "addEventListeners", "onmessage", "<PERSON><PERSON><PERSON>", "byteLength", "MozWebSocket", "packetData", "_deconstructPacket", "placeholder", "_placeholder", "newData", "_reconstructPacket", "ms", "factor", "pow", "rand", "random", "deviation"], "mappings": ";;;;;CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAY,GAAID,IAEhBD,EAAS,GAAIC,IARf,CASGK,MAAM,WACT,O,YCTE,IAAIC,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUP,QAGnC,IAAIC,EAASI,EAAiBE,GAAY,CACzCC,EAAGD,EACHE,GAAG,EACHT,QAAS,IAUV,OANAU,EAAQH,GAAUI,KAAKV,EAAOD,QAASC,EAAQA,EAAOD,QAASM,GAG/DL,EAAOQ,GAAI,EAGJR,EAAOD,QA0Df,OArDAM,EAAoBM,EAAIF,EAGxBJ,EAAoBO,EAAIR,EAGxBC,EAAoBQ,EAAI,SAASd,EAASe,EAAMC,GAC3CV,EAAoBW,EAAEjB,EAASe,IAClCG,OAAOC,eAAenB,EAASe,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhEV,EAAoBgB,EAAI,SAAStB,GACX,oBAAXuB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAenB,EAASuB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,KAQvDnB,EAAoBoB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQnB,EAAoBmB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,iBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFAxB,EAAoBgB,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOnB,EAAoBQ,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRvB,EAAoB2B,EAAI,SAAShC,GAChC,IAAIe,EAASf,GAAUA,EAAO2B,WAC7B,WAAwB,OAAO3B,EAAgB,SAC/C,WAA8B,OAAOA,GAEtC,OADAK,EAAoBQ,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRV,EAAoBW,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,IAGzG7B,EAAoBgC,EAAI,GAIjBhC,EAAoBA,EAAoBiC,EAAI,I,kBCnErD,SAASC,EAAQC,GACf,GAAIA,EAAK,OAWX,SAAeA,GACb,IAAK,IAAIV,KAAOS,EAAQJ,UACtBK,EAAIV,GAAOS,EAAQJ,UAAUL,GAE/B,OAAOU,EAfSC,CAAMD,GAVtBxC,EAAOD,QAAUwC,EAqCnBA,EAAQJ,UAAUO,GAClBH,EAAQJ,UAAUQ,iBAAmB,SAASC,EAAOC,GAInD,OAHA1C,KAAK2C,WAAa3C,KAAK2C,YAAc,IACpC3C,KAAK2C,WAAW,IAAMF,GAASzC,KAAK2C,WAAW,IAAMF,IAAU,IAC7DG,KAAKF,GACD1C,MAaToC,EAAQJ,UAAUa,KAAO,SAASJ,EAAOC,GACvC,SAASH,IACPvC,KAAK8C,IAAIL,EAAOF,GAChBG,EAAGK,MAAM/C,KAAMgD,WAKjB,OAFAT,EAAGG,GAAKA,EACR1C,KAAKuC,GAAGE,EAAOF,GACRvC,MAaToC,EAAQJ,UAAUc,IAClBV,EAAQJ,UAAUiB,eAClBb,EAAQJ,UAAUkB,mBAClBd,EAAQJ,UAAUmB,oBAAsB,SAASV,EAAOC,GAItD,GAHA1C,KAAK2C,WAAa3C,KAAK2C,YAAc,GAGjC,GAAKK,UAAUI,OAEjB,OADApD,KAAK2C,WAAa,GACX3C,KAIT,IAUIqD,EAVAC,EAAYtD,KAAK2C,WAAW,IAAMF,GACtC,IAAKa,EAAW,OAAOtD,KAGvB,GAAI,GAAKgD,UAAUI,OAEjB,cADOpD,KAAK2C,WAAW,IAAMF,GACtBzC,KAKT,IAAK,IAAII,EAAI,EAAGA,EAAIkD,EAAUF,OAAQhD,IAEpC,IADAiD,EAAKC,EAAUlD,MACJsC,GAAMW,EAAGX,KAAOA,EAAI,CAC7BY,EAAUC,OAAOnD,EAAG,GACpB,MAUJ,OAJyB,IAArBkD,EAAUF,eACLpD,KAAK2C,WAAW,IAAMF,GAGxBzC,MAWToC,EAAQJ,UAAUwB,KAAO,SAASf,GAChCzC,KAAK2C,WAAa3C,KAAK2C,YAAc,GAKrC,IAHA,IAAIc,EAAO,IAAIC,MAAMV,UAAUI,OAAS,GACpCE,EAAYtD,KAAK2C,WAAW,IAAMF,GAE7BrC,EAAI,EAAGA,EAAI4C,UAAUI,OAAQhD,IACpCqD,EAAKrD,EAAI,GAAK4C,UAAU5C,GAG1B,GAAIkD,EAEG,CAAIlD,EAAI,EAAb,IAAK,IAAWuD,GADhBL,EAAYA,EAAUM,MAAM,IACIR,OAAQhD,EAAIuD,IAAOvD,EACjDkD,EAAUlD,GAAG2C,MAAM/C,KAAMyD,GAI7B,OAAOzD,MAWToC,EAAQJ,UAAU6B,UAAY,SAASpB,GAErC,OADAzC,KAAK2C,WAAa3C,KAAK2C,YAAc,GAC9B3C,KAAK2C,WAAW,IAAMF,IAAU,IAWzCL,EAAQJ,UAAU8B,aAAe,SAASrB,GACxC,QAAUzC,KAAK6D,UAAUpB,GAAOW,S,gBC7KlC,IAAMW,EAAeC,EAAQ,IACvBC,EAAeD,EAAQ,IAEvBE,EAAYC,OAAOC,aAAa,IAgCtCvE,EAAOD,QAAU,CACfyE,SAAU,EACVN,eACAO,cAjCoB,SAACC,EAASC,GAE9B,IAAMpB,EAASmB,EAAQnB,OACjBqB,EAAiB,IAAIf,MAAMN,GAC7BsB,EAAQ,EAEZH,EAAQI,SAAQ,SAACC,EAAQxE,GAEvB2D,EAAaa,GAAQ,GAAO,SAAAC,GAC1BJ,EAAerE,GAAKyE,IACdH,IAAUtB,GACdoB,EAASC,EAAeK,KAAKZ,WAuBnCD,eACAc,cAlBoB,SAACC,EAAgBC,GAGrC,IAFA,IAAMR,EAAiBO,EAAeE,MAAMhB,GACtCK,EAAU,GACPnE,EAAI,EAAGA,EAAIqE,EAAerB,OAAQhD,IAAK,CAC9C,IAAM+E,EAAgBlB,EAAaQ,EAAerE,GAAI6E,GAEtD,GADAV,EAAQ3B,KAAKuC,GACc,UAAvBA,EAAcC,KAChB,MAGJ,OAAOb,K,cChCT1E,EAAOD,QACe,oBAATyF,KACFA,KACoB,oBAAXC,OACTA,OAEAC,SAAS,cAATA,I,ytCCNX,IAAMC,EAASxB,EAAQ,GAGjByB,E,sQAOJ,WAAYC,GAAM,a,4FAAA,UAChB,gBAEKA,KAAOA,EACZ,EAAKC,MAAQD,EAAKC,MAClB,EAAKC,WAAa,GAClB,EAAKC,OAASH,EAAKG,OANH,E,6CAgBVC,EAAKC,GACX,IAAMC,EAAM,IAAIC,MAAMH,GAItB,OAHAE,EAAIZ,KAAO,iBACXY,EAAIE,YAAcH,EAClB/F,KAAKwD,KAAK,QAASwC,GACZhG,O,6BAcP,MALI,WAAaA,KAAK4F,YAAc,KAAO5F,KAAK4F,aAC9C5F,KAAK4F,WAAa,UAClB5F,KAAKmG,UAGAnG,O,8BAcP,MALI,YAAcA,KAAK4F,YAAc,SAAW5F,KAAK4F,aACnD5F,KAAKoG,UACLpG,KAAKqG,WAGArG,O,2BASJuE,GACH,GAAI,SAAWvE,KAAK4F,WAGlB,MAAM,IAAIK,MAAM,sBAFhBjG,KAAKsG,MAAM/B,K,+BAYbvE,KAAK4F,WAAa,OAClB5F,KAAKuG,UAAW,EAChBvG,KAAKwD,KAAK,U,6BASLgD,GACL,IAAM5B,EAASY,EAAOvB,aAAauC,EAAMxG,KAAK6F,OAAOZ,YACrDjF,KAAKyG,SAAS7B,K,+BAMPA,GACP5E,KAAKwD,KAAK,SAAUoB,K,gCASpB5E,KAAK4F,WAAa,SAClB5F,KAAKwD,KAAK,c,8BA/GEQ,EAAQ,IAmHxBnE,EAAOD,QAAU6F,G,cC5GjB7F,EAAQ8G,OAAS,SAAUrE,GACzB,IAAIsE,EAAM,GAEV,IAAK,IAAIvG,KAAKiC,EACRA,EAAIJ,eAAe7B,KACjBuG,EAAIvD,SAAQuD,GAAO,KACvBA,GAAOC,mBAAmBxG,GAAK,IAAMwG,mBAAmBvE,EAAIjC,KAIhE,OAAOuG,GAUT/G,EAAQiH,OAAS,SAASC,GAGxB,IAFA,IAAIC,EAAM,GACNC,EAAQF,EAAG5B,MAAM,KACZ9E,EAAI,EAAGC,EAAI2G,EAAM5D,OAAQhD,EAAIC,EAAGD,IAAK,CAC5C,IAAI6G,EAAOD,EAAM5G,GAAG8E,MAAM,KAC1B6B,EAAIG,mBAAmBD,EAAK,KAAOC,mBAAmBD,EAAK,IAE7D,OAAOF,I,oqDClCTjG,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,IACtDzB,EAAQuH,QAAUvH,EAAQwH,QAAUxH,EAAQyH,WAAazH,EAAQyE,cAAW,EAC5E,IAWIgD,EAXEjF,EAAU4B,EAAQ,GAClBsD,EAAWtD,EAAQ,IACnBuD,EAAcvD,EAAQ,IAQ5BpE,EAAQyE,SAAW,EAEnB,SAAWgD,GACPA,EAAWA,EAAU,QAAc,GAAK,UACxCA,EAAWA,EAAU,WAAiB,GAAK,aAC3CA,EAAWA,EAAU,MAAY,GAAK,QACtCA,EAAWA,EAAU,IAAU,GAAK,MACpCA,EAAWA,EAAU,cAAoB,GAAK,gBAC9CA,EAAWA,EAAU,aAAmB,GAAK,eAC7CA,EAAWA,EAAU,WAAiB,GAAK,aAP/C,CAQGA,EAAazH,EAAQyH,aAAezH,EAAQyH,WAAa,K,IAItDD,E,2EAOK/E,GAGH,OAAIA,EAAI+C,OAASiC,EAAWG,OAASnF,EAAI+C,OAASiC,EAAWI,MACrDF,EAAYG,UAAUrF,GAQvB,CAACrC,KAAK2H,eAAetF,KAPpBA,EAAI+C,KACA/C,EAAI+C,OAASiC,EAAWG,MAClBH,EAAWO,aACXP,EAAWQ,WACd7H,KAAK8H,eAAezF,M,qCAQxBA,GAEX,IAAIsE,EAAM,GAAKtE,EAAI+C,KAqBnB,OAnBI/C,EAAI+C,OAASiC,EAAWO,cACxBvF,EAAI+C,OAASiC,EAAWQ,aACxBlB,GAAOtE,EAAI0F,YAAc,KAIzB1F,EAAI2F,KAAO,MAAQ3F,EAAI2F,MACvBrB,GAAOtE,EAAI2F,IAAM,KAGjB,MAAQ3F,EAAI4F,KACZtB,GAAOtE,EAAI4F,IAGX,MAAQ5F,EAAImE,OACZG,GAAOuB,KAAKC,UAAU9F,EAAImE,OAIvBG,I,qCAOItE,GACX,IAAM+F,EAAiBd,EAASe,kBAAkBhG,GAC5CiG,EAAOtI,KAAK2H,eAAeS,EAAexD,QAC1C2D,EAAUH,EAAeG,QAE/B,OADAA,EAAQC,QAAQF,GACTC,M,KAGf3I,EAAQwH,QAAUA,E,IAMZD,E,gQACF,aAAc,8B,sCAQV9E,GACA,IAAIuC,EACJ,GAAmB,iBAARvC,GACPuC,EAAS5E,KAAKyI,aAAapG,IAChB+C,OAASiC,EAAWO,cAC3BhD,EAAOQ,OAASiC,EAAWQ,YAE3B7H,KAAK0I,cAAgB,IAAIC,EAAoB/D,GAElB,IAAvBA,EAAOmD,aACP,wCAAW,UAAWnD,IAK1B,wCAAW,UAAWA,OAGzB,KAAI2C,EAAYqB,SAASvG,KAAQA,EAAIwG,OAetC,MAAM,IAAI5C,MAAM,iBAAmB5D,GAbnC,IAAKrC,KAAK0I,cACN,MAAM,IAAIzC,MAAM,qDAGhBrB,EAAS5E,KAAK0I,cAAcI,eAAezG,MAGvCrC,KAAK0I,cAAgB,KACrB,wCAAW,UAAW9D,O,mCAczB+B,GACT,IAAIvG,EAAI,EAEF8B,EAAI,CACNkD,KAAM2D,OAAOpC,EAAIqC,OAAO,KAE5B,QAA2BC,IAAvB5B,EAAWnF,EAAEkD,MACb,MAAM,IAAIa,MAAM,uBAAyB/D,EAAEkD,MAG/C,GAAIlD,EAAEkD,OAASiC,EAAWO,cACtB1F,EAAEkD,OAASiC,EAAWQ,WAAY,CAElC,IADA,IAAMqB,EAAQ9I,EAAI,EACS,MAApBuG,EAAIqC,SAAS5I,IAAcA,GAAKuG,EAAIvD,SAC3C,IAAM+F,EAAMxC,EAAIyC,UAAUF,EAAO9I,GACjC,GAAI+I,GAAOJ,OAAOI,IAA0B,MAAlBxC,EAAIqC,OAAO5I,GACjC,MAAM,IAAI6F,MAAM,uBAEpB/D,EAAE6F,YAAcgB,OAAOI,GAG3B,GAAI,MAAQxC,EAAIqC,OAAO5I,EAAI,GAAI,CAE3B,IADA,IAAM8I,EAAQ9I,EAAI,IACTA,GAAG,CAER,GAAI,MADMuG,EAAIqC,OAAO5I,GAEjB,MACJ,GAAIA,IAAMuG,EAAIvD,OACV,MAERlB,EAAE8F,IAAMrB,EAAIyC,UAAUF,EAAO9I,QAG7B8B,EAAE8F,IAAM,IAGZ,IAAMqB,EAAO1C,EAAIqC,OAAO5I,EAAI,GAC5B,GAAI,KAAOiJ,GAAQN,OAAOM,IAASA,EAAM,CAErC,IADA,IAAMH,EAAQ9I,EAAI,IACTA,GAAG,CACR,IAAMK,EAAIkG,EAAIqC,OAAO5I,GACrB,GAAI,MAAQK,GAAKsI,OAAOtI,IAAMA,EAAG,GAC3BL,EACF,MAEJ,GAAIA,IAAMuG,EAAIvD,OACV,MAERlB,EAAE+F,GAAKc,OAAOpC,EAAIyC,UAAUF,EAAO9I,EAAI,IAG3C,GAAIuG,EAAIqC,SAAS5I,GAAI,CACjB,IAAMkJ,EAsClB,SAAkB3C,GACd,IACI,OAAOuB,KAAKqB,MAAM5C,GAEtB,MAAO6C,GACH,OAAO,GA3CaC,CAAS9C,EAAI+C,OAAOtJ,IACpC,IAAI+G,EAAQwC,eAAezH,EAAEkD,KAAMkE,GAI/B,MAAM,IAAIrD,MAAM,mBAHhB/D,EAAEsE,KAAO8C,EAQjB,OAAOpH,I,gCAsBHlC,KAAK0I,eACL1I,KAAK0I,cAAckB,4B,sCArBLxE,EAAMkE,GACxB,OAAQlE,GACJ,KAAKiC,EAAWwC,QACZ,MAA0B,WAAnB,EAAOP,GAClB,KAAKjC,EAAWyC,WACZ,YAAmBb,IAAZK,EACX,KAAKjC,EAAW0C,cACZ,MAA0B,iBAAZT,GAA2C,WAAnB,EAAOA,GACjD,KAAKjC,EAAWG,MAChB,KAAKH,EAAWO,aACZ,OAAOlE,MAAMsG,QAAQV,IAAYA,EAAQlG,OAAS,EACtD,KAAKiE,EAAWI,IAChB,KAAKJ,EAAWQ,WACZ,OAAOnE,MAAMsG,QAAQV,Q,GAhIflH,GA4ItBxC,EAAQuH,QAAUA,E,IAiBZwB,E,WACF,WAAY/D,GAAQ,UAChB5E,KAAK4E,OAASA,EACd5E,KAAKuI,QAAU,GACfvI,KAAKiK,UAAYrF,E,iDAUNsF,GAEX,GADAlK,KAAKuI,QAAQ3F,KAAKsH,GACdlK,KAAKuI,QAAQnF,SAAWpD,KAAKiK,UAAUlC,YAAa,CAEpD,IAAMnD,EAAS0C,EAAS6C,kBAAkBnK,KAAKiK,UAAWjK,KAAKuI,SAE/D,OADAvI,KAAK4J,yBACEhF,EAEX,OAAO,O,+CAMP5E,KAAKiK,UAAY,KACjBjK,KAAKuI,QAAU,O,oBClRvB,IAAI6B,EAAK,0OAELC,EAAQ,CACR,SAAU,WAAY,YAAa,WAAY,OAAQ,WAAY,OAAQ,OAAQ,WAAY,OAAQ,YAAa,OAAQ,QAAS,UAGzIxK,EAAOD,QAAU,SAAkB+G,GAC/B,IAAI2D,EAAM3D,EACN4D,EAAI5D,EAAI6D,QAAQ,KAChBhB,EAAI7C,EAAI6D,QAAQ,MAEV,GAAND,IAAiB,GAANf,IACX7C,EAAMA,EAAIyC,UAAU,EAAGmB,GAAK5D,EAAIyC,UAAUmB,EAAGf,GAAGiB,QAAQ,KAAM,KAAO9D,EAAIyC,UAAUI,EAAG7C,EAAIvD,SAO9F,IAJA,IAmCmBuC,EACfa,EApCAhG,EAAI4J,EAAGM,KAAK/D,GAAO,IACnBgE,EAAM,GACNvK,EAAI,GAEDA,KACHuK,EAAIN,EAAMjK,IAAMI,EAAEJ,IAAM,GAa5B,OAVU,GAANmK,IAAiB,GAANf,IACXmB,EAAIC,OAASN,EACbK,EAAIE,KAAOF,EAAIE,KAAKzB,UAAU,EAAGuB,EAAIE,KAAKzH,OAAS,GAAGqH,QAAQ,KAAM,KACpEE,EAAIG,UAAYH,EAAIG,UAAUL,QAAQ,IAAK,IAAIA,QAAQ,IAAK,IAAIA,QAAQ,KAAM,KAC9EE,EAAII,SAAU,GAGlBJ,EAAIK,UAMR,SAAmB3I,EAAK4I,GACpB,IACIC,EAAQD,EAAKR,QADN,WACoB,KAAKvF,MAAM,KAEjB,KAArB+F,EAAKvB,OAAO,EAAG,IAA6B,IAAhBuB,EAAK7H,QACjC8H,EAAM3H,OAAO,EAAG,GAEmB,KAAnC0H,EAAKvB,OAAOuB,EAAK7H,OAAS,EAAG,IAC7B8H,EAAM3H,OAAO2H,EAAM9H,OAAS,EAAG,GAGnC,OAAO8H,EAjBSF,CAAUL,EAAKA,EAAG,MAClCA,EAAIQ,UAmBexF,EAnBUgF,EAAG,MAoB5BnE,EAAO,GAEXb,EAAM8E,QAAQ,6BAA6B,SAAUW,EAAIC,EAAIC,GACrDD,IACA7E,EAAK6E,GAAMC,MAIZ9E,GA1BAmE,I,6gDCvCX7J,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,IACtDzB,EAAQ2L,aAAU,EAClB,IAAMC,EAAMxH,EAAQ,IACdyH,EAAWzH,EAAQ,IACnB5B,EAAU4B,EAAQ,GAClBwB,EAASxB,EAAQ,GACjB0H,EAAO1H,EAAQ,IACf2H,EAAU3H,EAAQ,IAGlBuH,E,sQACF,WAAYZ,EAAKjF,GAAM,O,4FAAA,UACnB,gBACKkG,KAAO,GACZ,EAAKC,KAAO,GACRlB,GAAO,WAAa,EAAOA,KAC3BjF,EAAOiF,EACPA,OAAM1B,IAEVvD,EAAOA,GAAQ,IACVuF,KAAOvF,EAAKuF,MAAQ,aACzB,EAAKvF,KAAOA,EACZ,EAAKoG,cAAmC,IAAtBpG,EAAKoG,cACvB,EAAKC,qBAAqBrG,EAAKqG,sBAAwBC,KACvD,EAAKC,kBAAkBvG,EAAKuG,mBAAqB,KACjD,EAAKC,qBAAqBxG,EAAKwG,sBAAwB,KACvD,EAAKC,oBAAoBzG,EAAKyG,qBAAuB,IACrD,EAAKC,QAAU,IAAIT,EAAQ,CACvBU,IAAK,EAAKJ,oBACVK,IAAK,EAAKJ,uBACVK,OAAQ,EAAKJ,wBAEjB,EAAKK,QAAQ,MAAQ9G,EAAK8G,QAAU,IAAQ9G,EAAK8G,SACjD,EAAKC,YAAc,SACnB,EAAK9B,IAAMA,EACX,IAAM+B,EAAUhH,EAAKF,QAAUA,EAxBZ,OAyBnB,EAAKmH,QAAU,IAAID,EAAQtF,QAC3B,EAAKwF,QAAU,IAAIF,EAAQvF,QAC3B,EAAK0F,cAAoC,IAArBnH,EAAKoH,YACrB,EAAKD,cACL,EAAKE,OA7BU,E,kDA+BVC,GACT,OAAKhK,UAAUI,QAEfpD,KAAKiN,gBAAkBD,EAChBhN,MAFIA,KAAKiN,gB,2CAICD,GACjB,YAAU/D,IAAN+D,EACOhN,KAAKkN,uBAChBlN,KAAKkN,sBAAwBF,EACtBhN,Q,wCAEOgN,GACd,IAAIG,EACJ,YAAUlE,IAAN+D,EACOhN,KAAKoN,oBAChBpN,KAAKoN,mBAAqBJ,EACF,QAAvBG,EAAKnN,KAAKoM,eAA4B,IAAPe,GAAyBA,EAAGE,OAAOL,GAC5DhN,Q,0CAESgN,GAChB,IAAIG,EACJ,YAAUlE,IAAN+D,EACOhN,KAAKsN,sBAChBtN,KAAKsN,qBAAuBN,EACJ,QAAvBG,EAAKnN,KAAKoM,eAA4B,IAAPe,GAAyBA,EAAGI,UAAUP,GAC/DhN,Q,2CAEUgN,GACjB,IAAIG,EACJ,YAAUlE,IAAN+D,EACOhN,KAAKwN,uBAChBxN,KAAKwN,sBAAwBR,EACL,QAAvBG,EAAKnN,KAAKoM,eAA4B,IAAPe,GAAyBA,EAAGM,OAAOT,GAC5DhN,Q,8BAEHgN,GACJ,OAAKhK,UAAUI,QAEfpD,KAAK0N,SAAWV,EACThN,MAFIA,KAAK0N,W,8CAYX1N,KAAK2N,eACN3N,KAAKiN,eACqB,IAA1BjN,KAAKoM,QAAQwB,UAEb5N,KAAK6N,c,2BAURnL,GAAI,WAGL,IAAK1C,KAAKyM,YAAYjC,QAAQ,QAC1B,OAAOxK,KAGXA,KAAK8N,OAAStC,EAAIxL,KAAK2K,IAAK3K,KAAK0F,MACjC,IAAMG,EAAS7F,KAAK8N,OACdzI,EAAOrF,KACbA,KAAKyM,YAAc,UACnBzM,KAAK+N,eAAgB,EAErB,IAAMC,EAAiBtC,EAAKnJ,GAAGsD,EAAQ,QAAQ,WAC3CR,EAAK4I,SACLvL,GAAMA,OAGJwL,EAAWxC,EAAKnJ,GAAGsD,EAAQ,SAAS,SAACG,GAGvCX,EAAK8I,UACL9I,EAAKoH,YAAc,SACnB,kCAAW,QAASzG,GAChBtD,EACAA,EAAGsD,GAIHX,EAAK+I,0BAGb,IAAI,IAAUpO,KAAK0N,SAAU,CACzB,IAAMlB,EAAUxM,KAAK0N,SAGL,IAAZlB,GACAwB,IAGJ,IAAMK,EAAQC,YAAW,WAGrBN,IACAnI,EAAO0I,QACP1I,EAAOrC,KAAK,QAAS,IAAIyC,MAAM,cAChCuG,GACHxM,KAAK6L,KAAKjJ,MAAK,WACX4L,aAAaH,MAKrB,OAFArO,KAAK6L,KAAKjJ,KAAKoL,GACfhO,KAAK6L,KAAKjJ,KAAKsL,GACRlO,O,8BAQH0C,GACJ,OAAO1C,KAAK+M,KAAKrK,K,+BAWjB1C,KAAKmO,UAELnO,KAAKyM,YAAc,OACnB,wCAAW,QAEX,IAAM5G,EAAS7F,KAAK8N,OACpB9N,KAAK6L,KAAKjJ,KAAK8I,EAAKnJ,GAAGsD,EAAQ,OAAQ7F,KAAKyO,OAAO7M,KAAK5B,OAAQ0L,EAAKnJ,GAAGsD,EAAQ,OAAQ7F,KAAK0O,OAAO9M,KAAK5B,OAAQ0L,EAAKnJ,GAAGsD,EAAQ,QAAS7F,KAAK2O,QAAQ/M,KAAK5B,OAAQ0L,EAAKnJ,GAAGsD,EAAQ,QAAS7F,KAAK4O,QAAQhN,KAAK5B,OAAQ0L,EAAKnJ,GAAGvC,KAAK4M,QAAS,UAAW5M,KAAK6O,UAAUjN,KAAK5B,U,+BAQ5Q,wCAAW,U,6BAORwG,GACHxG,KAAK4M,QAAQkC,IAAItI,K,gCAOX5B,GACN,wCAAW,SAAUA,K,8BAOjBoB,GAGJ,wCAAW,QAASA,K,6BAQjBgC,EAAKtC,GACR,IAAIG,EAAS7F,KAAK4L,KAAK5D,GAKvB,OAJKnC,IACDA,EAAS,IAAI4F,EAASsD,OAAO/O,KAAMgI,EAAKtC,GACxC1F,KAAK4L,KAAK5D,GAAOnC,GAEdA,I,+BAQFA,GAEL,IADA,IACA,MADa/E,OAAOkO,KAAKhP,KAAK4L,MAC9B,eAAwB,CAAnB,IAAM5D,EAAG,KAEV,GADehI,KAAK4L,KAAK5D,GACdiH,OAGP,OAGRjP,KAAKkP,W,8BAQDtK,GAIJ,IADA,IAAMH,EAAiBzE,KAAK2M,QAAQjG,OAAO9B,GAClCxE,EAAI,EAAGA,EAAIqE,EAAerB,OAAQhD,IACvCJ,KAAK8N,OAAOxH,MAAM7B,EAAerE,GAAIwE,EAAOuK,W,gCAWhDnP,KAAK6L,KAAKlH,SAAQ,SAACyK,GAAD,OAAgBA,OAClCpP,KAAK6L,KAAKzI,OAAS,EACnBpD,KAAK4M,QAAQyC,Y,+BAUbrP,KAAK+N,eAAgB,EACrB/N,KAAK2N,eAAgB,EACjB,YAAc3N,KAAKyM,aAGnBzM,KAAKmO,UAETnO,KAAKoM,QAAQkD,QACbtP,KAAKyM,YAAc,SACfzM,KAAK8N,QACL9N,KAAK8N,OAAOS,U,mCAQhB,OAAOvO,KAAKkP,W,8BAORK,GAGJvP,KAAKmO,UACLnO,KAAKoM,QAAQkD,QACbtP,KAAKyM,YAAc,SACnB,wCAAW,QAAS8C,GAChBvP,KAAKiN,gBAAkBjN,KAAK+N,eAC5B/N,KAAK6N,c,kCAQD,WACR,GAAI7N,KAAK2N,eAAiB3N,KAAK+N,cAC3B,OAAO/N,KACX,IAAMqF,EAAOrF,KACb,GAAIA,KAAKoM,QAAQwB,UAAY5N,KAAKkN,sBAG9BlN,KAAKoM,QAAQkD,QACb,wCAAW,oBACXtP,KAAK2N,eAAgB,MAEpB,CACD,IAAM6B,EAAQxP,KAAKoM,QAAQqD,WAG3BzP,KAAK2N,eAAgB,EACrB,IAAMU,EAAQC,YAAW,WACjBjJ,EAAK0I,gBAIT,kCAAW,oBAAqB1I,EAAK+G,QAAQwB,UAEzCvI,EAAK0I,eAET1I,EAAK0H,MAAK,SAAC/G,GACHA,GAGAX,EAAKsI,eAAgB,EACrBtI,EAAKwI,YACL,kCAAW,kBAAmB7H,IAK9BX,EAAKqK,oBAGdF,GACHxP,KAAK6L,KAAKjJ,MAAK,WACX4L,aAAaH,S,oCAUrB,IAAMsB,EAAU3P,KAAKoM,QAAQwB,SAC7B5N,KAAK2N,eAAgB,EACrB3N,KAAKoM,QAAQkD,QACb,wCAAW,YAAaK,Q,8BApXVvN,GAuXtBxC,EAAQ2L,QAAUA,G,gBClYlB,IAAMqE,EAAiB5L,EAAQ,GACzB6L,EAAM7L,EAAQ,IACd8L,EAAQ9L,EAAQ,IAChB+L,EAAY/L,EAAQ,IAE1BpE,EAAQoQ,QAUR,SAAiBtK,GACf,IACIuK,GAAK,EACLC,GAAK,EACHC,GAAQ,IAAUzK,EAAKyK,MAE7B,GAAwB,oBAAbC,SAA0B,CACnC,IAAMC,EAAQ,WAAaD,SAAS/L,SAChCiM,EAAOF,SAASE,KAGfA,IACHA,EAAOD,EAAQ,IAAM,IAGvBJ,EAAKvK,EAAK6K,WAAaH,SAASG,UAAYD,IAAS5K,EAAK4K,KAC1DJ,EAAKxK,EAAK8K,SAAWH,EAOvB,GAJA3K,EAAK+K,QAAUR,EACfvK,EAAKgL,QAAUR,EAGX,SAFE,IAAIN,EAAelK,KAEHA,EAAKiL,WACzB,OAAO,IAAId,EAAInK,GAEf,IAAKyK,EAAO,MAAM,IAAIlK,MAAM,kBAC5B,OAAO,IAAI6J,EAAMpK,IApCrB9F,EAAQmQ,UAAYA,G,gBCJpB,IAAMa,EAAU5M,EAAQ,IAClB6M,EAAa7M,EAAQ,GAE3BnE,EAAOD,QAAU,SAAS8F,GACxB,IAAM+K,EAAU/K,EAAK+K,QAIfC,EAAUhL,EAAKgL,QAIfI,EAAapL,EAAKoL,WAGxB,IACE,GAAI,oBAAuBlB,kBAAoBa,GAAWG,GACxD,OAAO,IAAIhB,eAEb,MAAOpG,IAKT,IACE,GAAI,oBAAuBuH,iBAAmBL,GAAWI,EACvD,OAAO,IAAIC,eAEb,MAAOvH,IAET,IAAKiH,EACH,IACE,OAAO,IAAII,EAAW,CAAC,UAAUG,OAAO,UAAUlM,KAAK,OACrD,qBAEF,MAAO0E,O,uzCCrCb,IAAM/D,EAAYzB,EAAQ,GACpBiN,EAAUjN,EAAQ,GAClBwB,EAASxB,EAAQ,GACjBkN,EAAQlN,EAAQ,IAKhBmN,E,0WAeFnR,KAAKoR,S,4BASDC,GACJ,IAAMhM,EAAOrF,KAIb,SAASsR,IAGPjM,EAAKO,WAAa,SAClByL,IAGF,GATArR,KAAK4F,WAAa,UASd5F,KAAKgQ,UAAYhQ,KAAKuG,SAAU,CAClC,IAAIgL,EAAQ,EAERvR,KAAKgQ,UAGPuB,IACAvR,KAAK6C,KAAK,gBAAgB,aAGtB0O,GAASD,QAIVtR,KAAKuG,WAGRgL,IACAvR,KAAK6C,KAAK,SAAS,aAGf0O,GAASD,aAIfA,M,6BAYFtR,KAAKgQ,SAAU,EACfhQ,KAAKwR,SACLxR,KAAKwD,KAAK,U,6BAQLgD,GACL,IAAMnB,EAAOrF,KAoBbwF,EAAOT,cAAcyB,EAAMxG,KAAK6F,OAAOZ,YAAYN,SAjBlC,SAASC,EAAQ6M,EAAOF,GAOvC,GALI,YAAclM,EAAKO,YAA8B,SAAhBhB,EAAOQ,MAC1CC,EAAKqM,SAIH,UAAY9M,EAAOQ,KAErB,OADAC,EAAKgB,WACE,EAIThB,EAAKoB,SAAS7B,MAOZ,WAAa5E,KAAK4F,aAEpB5F,KAAKgQ,SAAU,EACfhQ,KAAKwD,KAAK,gBAEN,SAAWxD,KAAK4F,YAClB5F,KAAKoR,U,gCAcT,IAAM/L,EAAOrF,KAEb,SAASuO,IAGPlJ,EAAKiB,MAAM,CAAC,CAAElB,KAAM,WAGlB,SAAWpF,KAAK4F,WAGlB2I,IAMAvO,KAAK6C,KAAK,OAAQ0L,K,4BAWhBhK,GAAS,WACbvE,KAAKuG,UAAW,EAEhBf,EAAOlB,cAAcC,GAAS,SAAAiC,GAC5B,EAAKmL,QAAQnL,GAAM,WACjB,EAAKD,UAAW,EAChB,EAAK/C,KAAK,iB,4BAWd,IAAImC,EAAQ3F,KAAK2F,OAAS,GACpBiM,EAAS5R,KAAK0F,KAAK8K,OAAS,QAAU,OACxCF,EAAO,GA4BX,OAzBI,IAAUtQ,KAAK0F,KAAKmM,oBACtBlM,EAAM3F,KAAK0F,KAAKoM,gBAAkBZ,KAG/BlR,KAAK+R,gBAAmBpM,EAAMqM,MACjCrM,EAAMsM,IAAM,GAGdtM,EAAQsL,EAAQvK,OAAOf,GAIrB3F,KAAK0F,KAAK4K,OACR,UAAYsB,GAAqC,MAA3B7I,OAAO/I,KAAK0F,KAAK4K,OACtC,SAAWsB,GAAqC,KAA3B7I,OAAO/I,KAAK0F,KAAK4K,SAEzCA,EAAO,IAAMtQ,KAAK0F,KAAK4K,MAIrB3K,EAAMvC,SACRuC,EAAQ,IAAMA,GAKdiM,EACA,QAHgD,IAArC5R,KAAK0F,KAAK6K,SAAS/F,QAAQ,KAI9B,IAAMxK,KAAK0F,KAAK6K,SAAW,IAAMvQ,KAAK0F,KAAK6K,UACnDD,EACAtQ,KAAK0F,KAAKuF,KACVtF,I,2BA3MF,MAAO,e,8BALWF,GAqNtB5F,EAAOD,QAAUuR,G,cC7NjB,IAAMe,EAAepR,OAAOY,OAAO,MACnCwQ,EAAY,KAAW,IACvBA,EAAY,MAAY,IACxBA,EAAY,KAAW,IACvBA,EAAY,KAAW,IACvBA,EAAY,QAAc,IAC1BA,EAAY,QAAc,IAC1BA,EAAY,KAAW,IAEvB,IAAMC,EAAuBrR,OAAOY,OAAO,MAC3CZ,OAAOkO,KAAKkD,GAAcvN,SAAQ,SAAAhD,GAChCwQ,EAAqBD,EAAavQ,IAAQA,KAK5C9B,EAAOD,QAAU,CACfsS,eACAC,uBACAC,aALmB,CAAEhN,KAAM,QAASoB,KAAM,kB,6BCZ5C,IAKI6L,EALAC,EAAW,mEAAmEpN,MAAM,IAEpFqN,EAAM,GACNC,EAAO,EACPpS,EAAI,EAUR,SAASsG,EAAO+L,GACd,IAAIC,EAAU,GAEd,GACEA,EAAUJ,EAASG,EAjBV,IAiB0BC,EACnCD,EAAME,KAAKC,MAAMH,EAlBR,UAmBFA,EAAM,GAEf,OAAOC,EA0BT,SAASxB,IACP,IAAI2B,EAAMnM,GAAQ,IAAIoM,MAEtB,OAAID,IAAQR,GAAaG,EAAO,EAAGH,EAAOQ,GACnCA,EAAK,IAAKnM,EAAO8L,KAM1B,KAAOpS,EAzDM,GAyDMA,IAAKmS,EAAID,EAASlS,IAAMA,EAK3C8Q,EAAMxK,OAASA,EACfwK,EAAMrK,OAhCN,SAAgBF,GACd,IAAIoM,EAAU,EAEd,IAAK3S,EAAI,EAAGA,EAAIuG,EAAIvD,OAAQhD,IAC1B2S,EAnCS,GAmCCA,EAAmBR,EAAI5L,EAAIqC,OAAO5I,IAG9C,OAAO2S,GA0BTlT,EAAOD,QAAUsR,G,cCnEjBrR,EAAOD,QAAQoT,KAAO,SAAC3Q,GAAiB,2BAAT4Q,EAAS,iCAATA,EAAS,kBACtC,OAAOA,EAAKC,QAAO,SAACC,EAAKC,GAIvB,OAHI/Q,EAAIJ,eAAemR,KACrBD,EAAIC,GAAK/Q,EAAI+Q,IAERD,IACN,M,8hFCLLrS,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,IACtDzB,EAAQmP,YAAS,EACjB,IAAMsE,EAAqBrP,EAAQ,GAC7B5B,EAAU4B,EAAQ,GAClB0H,EAAO1H,EAAQ,IAOfsP,EAAkBxS,OAAOyS,OAAO,CAClCC,QAAS,EACTC,cAAe,EACfC,WAAY,EACZC,cAAe,EAEfC,YAAa,EACb3Q,eAAgB,IAEd8L,E,sQAMF,WAAY8E,EAAI7L,EAAKtC,GAAM,a,4FAAA,UACvB,gBACKoO,cAAgB,GACrB,EAAKC,WAAa,GAClB,EAAKC,IAAM,EACX,EAAKC,KAAO,GACZ,EAAKC,MAAQ,GACb,EAAKL,GAAKA,EACV,EAAK7L,IAAMA,EACX,EAAKgM,IAAM,EACX,EAAKC,KAAO,GACZ,EAAKH,cAAgB,GACrB,EAAKC,WAAa,GAClB,EAAKI,WAAY,EACjB,EAAKC,cAAe,EACpB,EAAKF,MAAQ,GACTxO,GAAQA,EAAK2O,OACb,EAAKA,KAAO3O,EAAK2O,MAEjB,EAAKR,GAAGhH,cACR,EAAKE,OApBc,E,iDA4BvB,IAAI/M,KAAK6L,KAAT,CAEA,IAAMgI,EAAK7T,KAAK6T,GAChB7T,KAAK6L,KAAO,CACRH,EAAKnJ,GAAGsR,EAAI,OAAQ7T,KAAKiO,OAAOrM,KAAK5B,OACrC0L,EAAKnJ,GAAGsR,EAAI,SAAU7T,KAAKsU,SAAS1S,KAAK5B,OACzC0L,EAAKnJ,GAAGsR,EAAI,QAAS7T,KAAK2O,QAAQ/M,KAAK5B,OACvC0L,EAAKnJ,GAAGsR,EAAI,QAAS7T,KAAK4O,QAAQhN,KAAK5B,W,gCAe3C,OAAIA,KAAKmU,YAETnU,KAAKuU,YACAvU,KAAK6T,GAAL,eACD7T,KAAK6T,GAAG9G,OACR,SAAW/M,KAAK6T,GAAGpH,aACnBzM,KAAKiO,UALEjO,O,6BAYX,OAAOA,KAAKwT,Y,6BAQF,2BAAN/P,EAAM,yBAANA,EAAM,gBAGV,OAFAA,EAAK+E,QAAQ,WACbxI,KAAKwD,KAAKT,MAAM/C,KAAMyD,GACfzD,O,2BAUNwU,GACD,GAAIlB,EAAgBrR,eAAeuS,GAC/B,MAAM,IAAIvO,MAAM,IAAMuO,EAAK,8BAFjB,2BAAN/Q,EAAM,iCAANA,EAAM,kBAIdA,EAAK+E,QAAQgM,GACb,IAAM5P,EAAS,CACXQ,KAAMiO,EAAmBhM,WAAWG,MACpChB,KAAM/C,EAEVmB,QAAiB,IACjBA,EAAOuK,QAAQsF,UAAmC,IAAxBzU,KAAKkU,MAAMO,SAEjC,mBAAsBhR,EAAKA,EAAKL,OAAS,KAGzCpD,KAAKiU,KAAKjU,KAAKgU,KAAOvQ,EAAKiR,MAC3B9P,EAAOqD,GAAKjI,KAAKgU,OAErB,IAAMW,EAAsB3U,KAAK6T,GAAG/F,QAChC9N,KAAK6T,GAAG/F,OAAO8G,WACf5U,KAAK6T,GAAG/F,OAAO8G,UAAUrO,SACvBsO,EAAgB7U,KAAKkU,MAAL,YAAyBS,IAAwB3U,KAAKmU,WAY5E,OAXIU,IAIK7U,KAAKmU,UACVnU,KAAK4E,OAAOA,GAGZ5E,KAAK+T,WAAWnR,KAAKgC,IAEzB5E,KAAKkU,MAAQ,GACNlU,O,6BAQJ4E,GACHA,EAAOoD,IAAMhI,KAAKgI,IAClBhI,KAAK6T,GAAGiB,QAAQlQ,K,+BAOX,WAGmB,mBAAb5E,KAAKqU,KACZrU,KAAKqU,MAAK,SAAC7N,GACP,EAAK5B,OAAO,CAAEQ,KAAMiO,EAAmBhM,WAAWwC,QAASrD,YAI/DxG,KAAK4E,OAAO,CAAEQ,KAAMiO,EAAmBhM,WAAWwC,QAASrD,KAAMxG,KAAKqU,S,8BAStErO,GACChG,KAAKmU,WACN,wCAAW,gBAAiBnO,K,8BAS5BuJ,GAGJvP,KAAKmU,WAAY,EACjBnU,KAAKoU,cAAe,SACbpU,KAAKiI,GACZ,wCAAW,aAAcsH,K,+BAQpB3K,GAEL,GADsBA,EAAOoD,MAAQhI,KAAKgI,IAG1C,OAAQpD,EAAOQ,MACX,KAAKiO,EAAmBhM,WAAWwC,QAC/B,GAAIjF,EAAO4B,MAAQ5B,EAAO4B,KAAKwL,IAAK,CAChC,IAAM/J,EAAKrD,EAAO4B,KAAKwL,IACvBhS,KAAK+U,UAAU9M,QAGf,wCAAW,gBAAiB,IAAIhC,MAAM,8LAE1C,MACJ,KAAKoN,EAAmBhM,WAAWG,MAGnC,KAAK6L,EAAmBhM,WAAWO,aAC/B5H,KAAKgV,QAAQpQ,GACb,MACJ,KAAKyO,EAAmBhM,WAAWI,IAGnC,KAAK4L,EAAmBhM,WAAWQ,WAC/B7H,KAAKiV,MAAMrQ,GACX,MACJ,KAAKyO,EAAmBhM,WAAWyC,WAC/B9J,KAAKkV,eACL,MACJ,KAAK7B,EAAmBhM,WAAW0C,cAC/B,IAAM/D,EAAM,IAAIC,MAAMrB,EAAO4B,KAAK2O,SAElCnP,EAAIQ,KAAO5B,EAAO4B,KAAKA,KACvB,wCAAW,gBAAiBR,M,8BAUhCpB,GACJ,IAAMnB,EAAOmB,EAAO4B,MAAQ,GAGxB,MAAQ5B,EAAOqD,IAGfxE,EAAKb,KAAK5C,KAAKoV,IAAIxQ,EAAOqD,KAE1BjI,KAAKmU,UACLnU,KAAKqV,UAAU5R,GAGfzD,KAAK8T,cAAclR,KAAK9B,OAAOyS,OAAO9P,M,gCAGpCA,GACN,GAAIzD,KAAKsV,eAAiBtV,KAAKsV,cAAclS,OAAQ,CACjD,IADiD,MAC/BpD,KAAKsV,cAAc1R,SADY,IAEjD,2BAAkC,QACrBb,MAAM/C,KAAMyD,GAHwB,+BAMrD,8BAAWV,MAAM/C,KAAMyD,K,0BAOvBwE,GACA,IAAM5C,EAAOrF,KACTuV,GAAO,EACX,OAAO,WAEH,IAAIA,EAAJ,CAEAA,GAAO,EAJe,2BAAN9R,EAAM,yBAANA,EAAM,gBAOtB4B,EAAKT,OAAO,CACRQ,KAAMiO,EAAmBhM,WAAWI,IACpCQ,GAAIA,EACJzB,KAAM/C,Q,4BAUZmB,GACF,IAAMwQ,EAAMpV,KAAKiU,KAAKrP,EAAOqD,IACzB,mBAAsBmN,IAGtBA,EAAIrS,MAAM/C,KAAM4E,EAAO4B,aAChBxG,KAAKiU,KAAKrP,EAAOqD,O,gCAYtBA,GAGNjI,KAAKiI,GAAKA,EACVjI,KAAKmU,WAAY,EACjBnU,KAAKoU,cAAe,EACpB,wCAAW,WACXpU,KAAKwV,iB,qCAOM,WACXxV,KAAK8T,cAAcnP,SAAQ,SAAClB,GAAD,OAAU,EAAK4R,UAAU5R,MACpDzD,KAAK8T,cAAgB,GACrB9T,KAAK+T,WAAWpP,SAAQ,SAACC,GAAD,OAAY,EAAKA,OAAOA,MAChD5E,KAAK+T,WAAa,K,qCAUlB/T,KAAKqP,UACLrP,KAAK4O,QAAQ,0B,gCAUT5O,KAAK6L,OAEL7L,KAAK6L,KAAKlH,SAAQ,SAACyK,GAAD,OAAgBA,OAClCpP,KAAK6L,UAAO5C,GAEhBjJ,KAAK6T,GAAL,SAAoB7T,Q,mCAoBpB,OAXIA,KAAKmU,WAGLnU,KAAK4E,OAAO,CAAEQ,KAAMiO,EAAmBhM,WAAWyC,aAGtD9J,KAAKqP,UACDrP,KAAKmU,WAELnU,KAAK4O,QAAQ,wBAEV5O,O,8BASP,OAAOA,KAAK0T,e,+BASPe,GAEL,OADAzU,KAAKkU,MAAMO,SAAWA,EACfzU,O,4BAoBLyV,GAGF,OAFAzV,KAAKsV,cAAgBtV,KAAKsV,eAAiB,GAC3CtV,KAAKsV,cAAc1S,KAAK6S,GACjBzV,O,iCASAyV,GAGP,OAFAzV,KAAKsV,cAAgBtV,KAAKsV,eAAiB,GAC3CtV,KAAKsV,cAAc9M,QAAQiN,GACpBzV,O,6BAQJyV,GACH,IAAKzV,KAAKsV,cACN,OAAOtV,KAEX,GAAIyV,GAEA,IADA,IAAM5R,EAAY7D,KAAKsV,cACdlV,EAAI,EAAGA,EAAIyD,EAAUT,OAAQhD,IAClC,GAAIqV,IAAa5R,EAAUzD,GAEvB,OADAyD,EAAUN,OAAOnD,EAAG,GACbJ,UAKfA,KAAKsV,cAAgB,GAEzB,OAAOtV,O,qCASP,OAAOA,KAAKsV,eAAiB,K,6BAjZ7B,QAAStV,KAAK6L,O,+BAwVd,OADA7L,KAAKkU,MAAL,UAAsB,EACflU,U,8BAxYMoC,GAocrBxC,EAAQmP,OAASA,G,kQCxdjBjO,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,IACtDzB,EAAQ8H,UAAY9H,EAAQgJ,cAAW,EACvC,IAAM8M,EAA+C,mBAAhBC,YAM/BC,EAAW9U,OAAOkB,UAAU4T,SAC5BC,EAAiC,mBAATC,MACT,oBAATA,MACoB,6BAAxBF,EAASrV,KAAKuV,MAChBC,EAAiC,mBAATC,MACT,oBAATA,MACoB,6BAAxBJ,EAASrV,KAAKyV,MAMtB,SAASpN,EAASvG,GACd,OAASqT,IAA0BrT,aAAesT,aAlBvC,SAACtT,GACZ,MAAqC,mBAAvBsT,YAAYM,OACpBN,YAAYM,OAAO5T,GACnBA,EAAI6T,kBAAkBP,YAeqCM,CAAO5T,KACnEwT,GAAkBxT,aAAeyT,MACjCC,GAAkB1T,aAAe2T,KAE1CpW,EAAQgJ,SAAWA,EA4BnBhJ,EAAQ8H,UA3BR,SAASA,EAAUrF,EAAK8T,GACpB,IAAK9T,GAAsB,WAAf,EAAOA,GACf,OAAO,EAEX,GAAIqB,MAAMsG,QAAQ3H,GAAM,CACpB,IAAK,IAAIjC,EAAI,EAAGC,EAAIgC,EAAIe,OAAQhD,EAAIC,EAAGD,IACnC,GAAIsH,EAAUrF,EAAIjC,IACd,OAAO,EAGf,OAAO,EAEX,GAAIwI,EAASvG,GACT,OAAO,EAEX,GAAIA,EAAI8T,QACkB,mBAAf9T,EAAI8T,QACU,IAArBnT,UAAUI,OACV,OAAOsE,EAAUrF,EAAI8T,UAAU,GAEnC,IAAK,IAAMxU,KAAOU,EACd,GAAIvB,OAAOkB,UAAUC,eAAe1B,KAAK8B,EAAKV,IAAQ+F,EAAUrF,EAAIV,IAChE,OAAO,EAGf,OAAO,I,6BCnDXb,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,IACtDzB,EAAQ2C,QAAK,EAOb3C,EAAQ2C,GANR,SAAYF,EAAKmS,EAAI9R,GAEjB,OADAL,EAAIE,GAAGiS,EAAI9R,GACJ,WACHL,EAAIS,IAAI0R,EAAI9R,M,kQCLpB5B,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,IACtDzB,EAAQmP,OAASnP,EAAQiU,GAAKjU,EAAQ2L,QAAU3L,EAAQyE,cAAW,EACnE,IAAM+R,EAAQpS,EAAQ,IAChBqS,EAAYrS,EAAQ,GACpByH,EAAWzH,EAAQ,IACzBlD,OAAOC,eAAenB,EAAS,SAAU,CAAEoB,YAAY,EAAMC,IAAK,WAAc,OAAOwK,EAASsD,UAMhGlP,EAAOD,QAAUA,EAAU0W,EAI3B,IAAMC,EAAS3W,EAAQ4W,SAAW,GAClC,SAASF,EAAO3L,EAAKjF,GACE,WAAf,EAAOiF,KACPjF,EAAOiF,EACPA,OAAM1B,GAEVvD,EAAOA,GAAQ,GACf,IASImO,EATE4C,EAASL,EAAMM,IAAI/L,EAAKjF,EAAKuF,MAC7BL,EAAS6L,EAAO7L,OAChB3C,EAAKwO,EAAOxO,GACZgD,EAAOwL,EAAOxL,KACd0L,EAAgBJ,EAAMtO,IAAOgD,KAAQsL,EAAMtO,GAAN,KAsB3C,OArBsBvC,EAAKkR,UACvBlR,EAAK,0BACL,IAAUA,EAAKmR,WACfF,EAKA9C,EAAK,IAAIwC,EAAU9K,QAAQX,EAAQlF,IAG9B6Q,EAAMtO,KAGPsO,EAAMtO,GAAM,IAAIoO,EAAU9K,QAAQX,EAAQlF,IAE9CmO,EAAK0C,EAAMtO,IAEXwO,EAAO9Q,QAAUD,EAAKC,QACtBD,EAAKC,MAAQ8Q,EAAOtL,UAEjB0I,EAAGhO,OAAO4Q,EAAOxL,KAAMvF,GAElC9F,EAAQiU,GAAKyC,EAMb,IAAIjD,EAAqBrP,EAAQ,GACjClD,OAAOC,eAAenB,EAAS,WAAY,CAAEoB,YAAY,EAAMC,IAAK,WAAc,OAAOoS,EAAmBhP,YAO5GzE,EAAQ4T,QAAU8C,EAMlB,IAAIQ,EAAY9S,EAAQ,GACxBlD,OAAOC,eAAenB,EAAS,UAAW,CAAEoB,YAAY,EAAMC,IAAK,WAAc,OAAO6V,EAAUvL,Y,6BCvElGzK,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,IACtDzB,EAAQ8W,SAAM,EACd,IAAMK,EAAW/S,EAAQ,GAiEzBpE,EAAQ8W,IArDR,SAAa/L,GAAqB,IAAhBM,EAAgB,uDAAT,GAAI+L,EAAK,uCAC1B3U,EAAMsI,EAEVqM,EAAMA,GAA4B,oBAAb5G,UAA4BA,SAC7C,MAAQzF,IACRA,EAAMqM,EAAI3S,SAAW,KAAO2S,EAAInM,MAEjB,iBAARF,IACH,MAAQA,EAAI3B,OAAO,KAEf2B,EADA,MAAQA,EAAI3B,OAAO,GACbgO,EAAI3S,SAAWsG,EAGfqM,EAAInM,KAAOF,GAGpB,sBAAsBsM,KAAKtM,KAIxBA,OADA,IAAuBqM,EACjBA,EAAI3S,SAAW,KAAOsG,EAGtB,WAAaA,GAM3BtI,EAAM0U,EAASpM,IAGdtI,EAAIiO,OACD,cAAc2G,KAAK5U,EAAIgC,UACvBhC,EAAIiO,KAAO,KAEN,eAAe2G,KAAK5U,EAAIgC,YAC7BhC,EAAIiO,KAAO,QAGnBjO,EAAI4I,KAAO5I,EAAI4I,MAAQ,IACvB,IAAMiM,GAAkC,IAA3B7U,EAAIwI,KAAKL,QAAQ,KACxBK,EAAOqM,EAAO,IAAM7U,EAAIwI,KAAO,IAAMxI,EAAIwI,KAS/C,OAPAxI,EAAI4F,GAAK5F,EAAIgC,SAAW,MAAQwG,EAAO,IAAMxI,EAAIiO,KAAOrF,EAExD5I,EAAI8U,KACA9U,EAAIgC,SACA,MACAwG,GACCmM,GAAOA,EAAI1G,OAASjO,EAAIiO,KAAO,GAAK,IAAMjO,EAAIiO,MAChDjO,I,gBClEX,IAAM0M,EAAS/K,EAAQ,IAEvBnE,EAAOD,QAAU,SAAC+K,EAAKjF,GAAN,OAAe,IAAIqJ,EAAOpE,EAAKjF,IAOhD7F,EAAOD,QAAQmP,OAASA,EACxBlP,EAAOD,QAAQyE,SAAW0K,EAAO1K,SACjCxE,EAAOD,QAAQ6F,UAAYzB,EAAQ,GACnCnE,EAAOD,QAAQwX,WAAapT,EAAQ,GACpCnE,EAAOD,QAAQ4F,OAASxB,EAAQ,I,sgDCbhC,IAAMoT,EAAapT,EAAQ,GACrB5B,EAAU4B,EAAQ,GAGlBwB,EAASxB,EAAQ,GACjB+S,EAAW/S,EAAQ,GACnBiN,EAAUjN,EAAQ,GAElB+K,E,sQAQJ,WAAYpE,GAAgB,MAAXjF,EAAW,uDAAJ,GAAI,iBAC1B,eAEIiF,GAAO,WAAa,EAAOA,KAC7BjF,EAAOiF,EACPA,EAAM,MAGJA,GACFA,EAAMoM,EAASpM,GACfjF,EAAK6K,SAAW5F,EAAIE,KACpBnF,EAAK8K,OAA0B,UAAjB7F,EAAItG,UAAyC,QAAjBsG,EAAItG,SAC9CqB,EAAK4K,KAAO3F,EAAI2F,KACZ3F,EAAIhF,QAAOD,EAAKC,MAAQgF,EAAIhF,QACvBD,EAAKmF,OACdnF,EAAK6K,SAAWwG,EAASrR,EAAKmF,MAAMA,MAGtC,EAAK2F,OACH,MAAQ9K,EAAK8K,OACT9K,EAAK8K,OACe,oBAAbJ,UAA4B,WAAaA,SAAS/L,SAE3DqB,EAAK6K,WAAa7K,EAAK4K,OAEzB5K,EAAK4K,KAAO,EAAKE,OAAS,MAAQ,MAGpC,EAAKD,SACH7K,EAAK6K,WACgB,oBAAbH,SAA2BA,SAASG,SAAW,aACzD,EAAKD,KACH5K,EAAK4K,OACgB,oBAAbF,UAA4BA,SAASE,KACzCF,SAASE,KACT,EAAKE,OACL,IACA,IAEN,EAAK4G,WAAa1R,EAAK0R,YAAc,CAAC,UAAW,aACjD,EAAKxR,WAAa,GAClB,EAAKyR,YAAc,GACnB,EAAKC,cAAgB,EAErB,EAAK5R,KAAO,EACV,CACEuF,KAAM,aACNsM,OAAO,EACPC,iBAAiB,EACjBC,SAAS,EACTtH,OAAO,EACP2B,eAAgB,IAChB4F,iBAAiB,EACjBC,oBAAoB,EACpBC,kBAAmB,CACjBC,UAAW,MAEbC,iBAAkB,IAEpBpS,GAGF,EAAKA,KAAKuF,KAAO,EAAKvF,KAAKuF,KAAKR,QAAQ,MAAO,IAAM,IAEtB,iBAApB,EAAK/E,KAAKC,QACnB,EAAKD,KAAKC,MAAQsL,EAAQpK,OAAO,EAAKnB,KAAKC,QAI7C,EAAKsC,GAAK,KACV,EAAK8P,SAAW,KAChB,EAAKC,aAAe,KACpB,EAAKC,YAAc,KAGnB,EAAKC,iBAAmB,KAEQ,mBAArB1V,kBACTA,iBACE,gBACA,WACM,EAAKoS,YAEP,EAAKA,UAAU1R,qBACf,EAAK0R,UAAUrG,YAGnB,GAIJ,EAAKxB,OA3FqB,E,qDAqGZpM,GAGd,IAAMgF,EA2jBV,SAAetD,GACb,IAAMxB,EAAI,GACV,IAAK,IAAIT,KAAKiC,EACRA,EAAIJ,eAAe7B,KACrBS,EAAET,GAAKiC,EAAIjC,IAGf,OAAOS,EAlkBSsX,CAAMnY,KAAK0F,KAAKC,OAG9BA,EAAMyS,IAAM5S,EAAOnB,SAGnBsB,EAAMiP,UAAYjU,EAGdX,KAAKiI,KAAItC,EAAMqM,IAAMhS,KAAKiI,IAE9B,IAAMvC,EAAO,EACX,GACA1F,KAAK0F,KAAKoS,iBAAiBnX,GAC3BX,KAAK0F,KACL,CACEC,QACAE,OAAQ7F,KACRuQ,SAAUvQ,KAAKuQ,SACfC,OAAQxQ,KAAKwQ,OACbF,KAAMtQ,KAAKsQ,OAOf,OAAO,IAAI8G,EAAWzW,GAAM+E,K,6BAS5B,IAAIkP,EACJ,GACE5U,KAAK0F,KAAKgS,iBACV3I,EAAOsJ,wBACmC,IAA1CrY,KAAKoX,WAAW5M,QAAQ,aAExBoK,EAAY,gBACP,IAAI,IAAM5U,KAAKoX,WAAWhU,OAAQ,CAEvC,IAAMiC,EAAOrF,KAIb,YAHAsO,YAAW,WACTjJ,EAAK7B,KAAK,QAAS,6BAClB,GAGHoR,EAAY5U,KAAKoX,WAAW,GAE9BpX,KAAK4F,WAAa,UAGlB,IACEgP,EAAY5U,KAAKsY,gBAAgB1D,GACjC,MAAOpL,GAKP,OAFAxJ,KAAKoX,WAAWmB,aAChBvY,KAAK+M,OAIP6H,EAAU7H,OACV/M,KAAKwY,aAAa5D,K,mCAQPA,GAGX,IAAMvP,EAAOrF,KAETA,KAAK4U,WAGP5U,KAAK4U,UAAU1R,qBAIjBlD,KAAK4U,UAAYA,EAGjBA,EACGrS,GAAG,SAAS,WACX8C,EAAKoT,aAENlW,GAAG,UAAU,SAASqC,GACrBS,EAAKoB,SAAS7B,MAEfrC,GAAG,SAAS,SAASiH,GACpBnE,EAAKqT,QAAQlP,MAEdjH,GAAG,SAAS,WACX8C,EAAKgB,QAAQ,wB,4BAUb1F,GAGJ,IAAIiU,EAAY5U,KAAKsY,gBAAgB3X,EAAM,CAAEgY,MAAO,IAChDC,GAAS,EACPvT,EAAOrF,KAIb,SAAS6Y,IACP,GAAIxT,EAAKyT,mBAAoB,CAC3B,IAAMC,GACH/Y,KAAK+R,gBAAkB1M,EAAKuP,UAAU7C,eACzC6G,EAASA,GAAUG,EAEjBH,IAIJhE,EAAUoE,KAAK,CAAC,CAAE5T,KAAM,OAAQoB,KAAM,WACtCoO,EAAU/R,KAAK,UAAU,SAASiD,GAChC,IAAI8S,EACJ,GAAI,SAAW9S,EAAIV,MAAQ,UAAYU,EAAIU,KAAM,CAK/C,GAFAnB,EAAK4T,WAAY,EACjB5T,EAAK7B,KAAK,YAAaoR,IAClBA,EAAW,OAChB7F,EAAOsJ,sBAAwB,cAAgBzD,EAAUjU,KAIzD0E,EAAKuP,UAAUtD,OAAM,WACfsH,GACA,WAAavT,EAAKO,aAItBuI,IAEA9I,EAAKmT,aAAa5D,GAClBA,EAAUoE,KAAK,CAAC,CAAE5T,KAAM,aACxBC,EAAK7B,KAAK,UAAWoR,GACrBA,EAAY,KACZvP,EAAK4T,WAAY,EACjB5T,EAAK6T,gBAEF,CAGL,IAAMlT,EAAM,IAAIC,MAAM,eACtBD,EAAI4O,UAAYA,EAAUjU,KAC1B0E,EAAK7B,KAAK,eAAgBwC,QAKhC,SAASmT,IACHP,IAGJA,GAAS,EAETzK,IAEAyG,EAAUrG,QACVqG,EAAY,MAId,SAASjG,EAAQ3I,GACf,IAAMoT,EAAQ,IAAInT,MAAM,gBAAkBD,GAC1CoT,EAAMxE,UAAYA,EAAUjU,KAE5BwY,IAKA9T,EAAK7B,KAAK,eAAgB4V,GAG5B,SAASC,IACP1K,EAAQ,oBAIV,SAASC,IACPD,EAAQ,iBAIV,SAAS2K,EAAUC,GACb3E,GAAa2E,EAAG5Y,OAASiU,EAAUjU,MAGrCwY,IAKJ,SAAShL,IACPyG,EAAU3R,eAAe,OAAQ4V,GACjCjE,EAAU3R,eAAe,QAAS0L,GAClCiG,EAAU3R,eAAe,QAASoW,GAClChU,EAAKpC,eAAe,QAAS2L,GAC7BvJ,EAAKpC,eAAe,YAAaqW,GAnGnCvK,EAAOsJ,uBAAwB,EAsG/BzD,EAAU/R,KAAK,OAAQgW,GACvBjE,EAAU/R,KAAK,QAAS8L,GACxBiG,EAAU/R,KAAK,QAASwW,GAExBrZ,KAAK6C,KAAK,QAAS+L,GACnB5O,KAAK6C,KAAK,YAAayW,GAEvB1E,EAAU7H,S,+BAkBV,GAPA/M,KAAK4F,WAAa,OAClBmJ,EAAOsJ,sBAAwB,cAAgBrY,KAAK4U,UAAUjU,KAC9DX,KAAKwD,KAAK,QACVxD,KAAKkZ,QAKH,SAAWlZ,KAAK4F,YAChB5F,KAAK0F,KAAK+R,SACVzX,KAAK4U,UAAUtD,MAMf,IAFA,IAAIlR,EAAI,EACFC,EAAIL,KAAK+X,SAAS3U,OACjBhD,EAAIC,EAAGD,IACZJ,KAAK2Y,MAAM3Y,KAAK+X,SAAS3X,M,+BAUtBwE,GACP,GACE,YAAc5E,KAAK4F,YACnB,SAAW5F,KAAK4F,YAChB,YAAc5F,KAAK4F,WAUnB,OALA5F,KAAKwD,KAAK,SAAUoB,GAGpB5E,KAAKwD,KAAK,aAEFoB,EAAOQ,MACb,IAAK,OACHpF,KAAKwZ,YAAYtR,KAAKqB,MAAM3E,EAAO4B,OACnC,MAEF,IAAK,OACHxG,KAAKyZ,mBACLzZ,KAAK0Z,WAAW,QAChB1Z,KAAKwD,KAAK,QACV,MAEF,IAAK,QACH,IAAMwC,EAAM,IAAIC,MAAM,gBACtBD,EAAI2T,KAAO/U,EAAO4B,KAClBxG,KAAK0Y,QAAQ1S,GACb,MAEF,IAAK,UACHhG,KAAKwD,KAAK,OAAQoB,EAAO4B,MACzBxG,KAAKwD,KAAK,UAAWoB,EAAO4B,S,kCAexBA,GACVxG,KAAKwD,KAAK,YAAagD,GACvBxG,KAAKiI,GAAKzB,EAAKwL,IACfhS,KAAK4U,UAAUjP,MAAMqM,IAAMxL,EAAKwL,IAChChS,KAAK+X,SAAW/X,KAAK4Z,eAAepT,EAAKuR,UACzC/X,KAAKgY,aAAexR,EAAKwR,aACzBhY,KAAKiY,YAAczR,EAAKyR,YACxBjY,KAAK0R,SAED,WAAa1R,KAAK4F,YACtB5F,KAAKyZ,qB,yCAQY,WACjBjL,aAAaxO,KAAKkY,kBAClBlY,KAAKkY,iBAAmB5J,YAAW,WACjC,EAAKjI,QAAQ,kBACZrG,KAAKgY,aAAehY,KAAKiY,e,gCAS5BjY,KAAKqX,YAAY9T,OAAO,EAAGvD,KAAKsX,eAKhCtX,KAAKsX,cAAgB,EAEjB,IAAMtX,KAAKqX,YAAYjU,OACzBpD,KAAKwD,KAAK,SAEVxD,KAAKkZ,U,8BAWL,WAAalZ,KAAK4F,YAClB5F,KAAK4U,UAAUrO,WACdvG,KAAKiZ,WACNjZ,KAAKqX,YAAYjU,SAIjBpD,KAAK4U,UAAUoE,KAAKhZ,KAAKqX,aAGzBrX,KAAKsX,cAAgBtX,KAAKqX,YAAYjU,OACtCpD,KAAKwD,KAAK,Y,4BAaRsC,EAAKqJ,EAASzM,GAElB,OADA1C,KAAK0Z,WAAW,UAAW5T,EAAKqJ,EAASzM,GAClC1C,O,2BAGJ8F,EAAKqJ,EAASzM,GAEjB,OADA1C,KAAK0Z,WAAW,UAAW5T,EAAKqJ,EAASzM,GAClC1C,O,iCAYEoF,EAAMoB,EAAM2I,EAASzM,GAW9B,GAVI,mBAAsB8D,IACxB9D,EAAK8D,EACLA,OAAOyC,GAGL,mBAAsBkG,IACxBzM,EAAKyM,EACLA,EAAU,MAGR,YAAcnP,KAAK4F,YAAc,WAAa5F,KAAK4F,WAAvD,EAIAuJ,EAAUA,GAAW,IACbsF,UAAW,IAAUtF,EAAQsF,SAErC,IAAM7P,EAAS,CACbQ,KAAMA,EACNoB,KAAMA,EACN2I,QAASA,GAEXnP,KAAKwD,KAAK,eAAgBoB,GAC1B5E,KAAKqX,YAAYzU,KAAKgC,GAClBlC,GAAI1C,KAAK6C,KAAK,QAASH,GAC3B1C,KAAKkZ,W,8BASL,IAAM7T,EAAOrF,KAoBb,SAASuO,IACPlJ,EAAKgB,QAAQ,gBAGbhB,EAAKuP,UAAUrG,QAGjB,SAASsL,IACPxU,EAAKpC,eAAe,UAAW4W,GAC/BxU,EAAKpC,eAAe,eAAgB4W,GACpCtL,IAGF,SAASuL,IAEPzU,EAAKxC,KAAK,UAAWgX,GACrBxU,EAAKxC,KAAK,eAAgBgX,GAG5B,MArCI,YAAc7Z,KAAK4F,YAAc,SAAW5F,KAAK4F,aACnD5F,KAAK4F,WAAa,UAEd5F,KAAKqX,YAAYjU,OACnBpD,KAAK6C,KAAK,SAAS,WACb7C,KAAKiZ,UACPa,IAEAvL,OAGKvO,KAAKiZ,UACda,IAEAvL,KAuBGvO,O,8BAQDgG,GAGN+I,EAAOsJ,uBAAwB,EAC/BrY,KAAKwD,KAAK,QAASwC,GACnBhG,KAAKqG,QAAQ,kBAAmBL,K,8BAQ1BuJ,EAAQxJ,GAEZ,YAAc/F,KAAK4F,YACnB,SAAW5F,KAAK4F,YAChB,YAAc5F,KAAK4F,aAOnB4I,aAAaxO,KAAK+Z,mBAClBvL,aAAaxO,KAAKkY,kBAGlBlY,KAAK4U,UAAU1R,mBAAmB,SAGlClD,KAAK4U,UAAUrG,QAGfvO,KAAK4U,UAAU1R,qBAGflD,KAAK4F,WAAa,SAGlB5F,KAAKiI,GAAK,KAGVjI,KAAKwD,KAAK,QAAS+L,EAAQxJ,GAtBd/F,KA0BRqX,YAAc,GA1BNrX,KA2BRsX,cAAgB,K,qCAWVS,GAIb,IAHA,IAAMiC,EAAmB,GACrB5Z,EAAI,EACF6Z,EAAIlC,EAAS3U,OACZhD,EAAI6Z,EAAG7Z,KACPJ,KAAKoX,WAAW5M,QAAQuN,EAAS3X,KACpC4Z,EAAiBpX,KAAKmV,EAAS3X,IAEnC,OAAO4Z,O,8BA7pBU5X,GAiqBrB2M,EAAOsJ,uBAAwB,EAQ/BtJ,EAAO1K,SAAWmB,EAAOnB,SAYzBxE,EAAOD,QAAUmP,G,cCprBjB,IACElP,EAAOD,QAAoC,oBAAnBgQ,gBACtB,oBAAqB,IAAIA,eAC3B,MAAO5J,GAGPnG,EAAOD,SAAU,I,myDCbnB,IAAMgQ,EAAiB5L,EAAQ,GACzBmN,EAAUnN,EAAQ,IAClB5B,EAAU4B,EAAQ,GAChBgP,EAAShP,EAAQ,IAAjBgP,KACFnC,EAAa7M,EAAQ,GAS3B,SAASkW,KAET,IAAMC,EAEG,MADK,IAAIvK,EAAe,CAAEa,SAAS,IACvB2J,aAGfvK,E,8BAOJ,WAAYnK,GAAM,MAGhB,GAHgB,UAChB,cAAMA,GAEkB,oBAAb0K,SAA0B,CACnC,IAAMC,EAAQ,WAAaD,SAAS/L,SAChCiM,EAAOF,SAASE,KAGfA,IACHA,EAAOD,EAAQ,IAAM,IAGvB,EAAKJ,GACkB,oBAAbG,UACN1K,EAAK6K,WAAaH,SAASG,UAC7BD,IAAS5K,EAAK4K,KAChB,EAAKJ,GAAKxK,EAAK8K,SAAWH,EAK5B,IAAMgK,EAAc3U,GAAQA,EAAK2U,YArBjB,OAsBhB,EAAKtI,eAAiBoI,IAAYE,EAtBlB,E,4CA+BC,IAAX3U,EAAW,uDAAJ,GAEb,OADA,EAAcA,EAAM,CAAEuK,GAAIjQ,KAAKiQ,GAAIC,GAAIlQ,KAAKkQ,IAAMlQ,KAAK0F,MAChD,IAAI4U,EAAQta,KAAK2K,MAAOjF,K,8BAUzBc,EAAM9D,GACZ,IAAM6X,EAAMva,KAAKwa,QAAQ,CACvBC,OAAQ,OACRjU,KAAMA,IAEFnB,EAAOrF,KACbua,EAAIhY,GAAG,UAAWG,GAClB6X,EAAIhY,GAAG,SAAS,SAASyD,GACvBX,EAAKqT,QAAQ,iBAAkB1S,Q,+BAYjC,IAAMuU,EAAMva,KAAKwa,UACXnV,EAAOrF,KACbua,EAAIhY,GAAG,QAAQ,SAASiE,GACtBnB,EAAKqV,OAAOlU,MAEd+T,EAAIhY,GAAG,SAAS,SAASyD,GACvBX,EAAKqT,QAAQ,iBAAkB1S,MAEjChG,KAAK2a,QAAUJ,M,GA9EDpJ,GAkFZmJ,E,8BAOJ,WAAY3P,EAAKjF,GAAM,wBACrB,gBACKA,KAAOA,EAEZ,EAAK+U,OAAS/U,EAAK+U,QAAU,MAC7B,EAAK9P,IAAMA,EACX,EAAKiQ,OAAQ,IAAUlV,EAAKkV,MAC5B,EAAKpU,UAAOyC,IAAcvD,EAAKc,KAAOd,EAAKc,KAAO,KAElD,EAAK9E,SATgB,E,2CAkBrB,IAAMgE,EAAOsN,EACXhT,KAAK0F,KACL,QACA,aACA,MACA,MACA,aACA,OACA,KACA,UACA,sBAEFA,EAAK+K,UAAYzQ,KAAK0F,KAAKuK,GAC3BvK,EAAKgL,UAAY1Q,KAAK0F,KAAKwK,GAE3B,IAAM2K,EAAO7a,KAAK6a,IAAM,IAAIjL,EAAelK,GACrCL,EAAOrF,KAEb,IAGE6a,EAAI9N,KAAK/M,KAAKya,OAAQza,KAAK2K,IAAK3K,KAAK4a,OACrC,IACE,GAAI5a,KAAK0F,KAAKoV,aAEZ,IAAK,IAAI1a,KADTya,EAAIE,uBAAyBF,EAAIE,uBAAsB,GACzC/a,KAAK0F,KAAKoV,aAClB9a,KAAK0F,KAAKoV,aAAa7Y,eAAe7B,IACxCya,EAAIG,iBAAiB5a,EAAGJ,KAAK0F,KAAKoV,aAAa1a,IAIrD,MAAOoJ,IAET,GAAI,SAAWxJ,KAAKya,OAClB,IACEI,EAAIG,iBAAiB,eAAgB,4BACrC,MAAOxR,IAGX,IACEqR,EAAIG,iBAAiB,SAAU,OAC/B,MAAOxR,IAGL,oBAAqBqR,IACvBA,EAAIrD,gBAAkBxX,KAAK0F,KAAK8R,iBAG9BxX,KAAK0F,KAAKuV,iBACZJ,EAAIrO,QAAUxM,KAAK0F,KAAKuV,gBAGtBjb,KAAKkb,UACPL,EAAIM,OAAS,WACX9V,EAAK+V,UAEPP,EAAIlM,QAAU,WACZtJ,EAAKqT,QAAQmC,EAAIQ,gBAGnBR,EAAIS,mBAAqB,WACnB,IAAMT,EAAIjV,aACV,MAAQiV,EAAIU,QAAU,OAASV,EAAIU,OACrClW,EAAK+V,SAIL9M,YAAW,WACTjJ,EAAKqT,QAA8B,iBAAfmC,EAAIU,OAAsBV,EAAIU,OAAS,KAC1D,KAOTV,EAAI7B,KAAKhZ,KAAKwG,MACd,MAAOgD,GAOP,YAHA8E,YAAW,WACTjJ,EAAKqT,QAAQlP,KACZ,GAImB,oBAAbgS,WACTxb,KAAKyR,MAAQ6I,EAAQmB,gBACrBnB,EAAQoB,SAAS1b,KAAKyR,OAASzR,Q,kCAUjCA,KAAKwD,KAAK,WACVxD,KAAKmO,Y,6BAQA3H,GACLxG,KAAKwD,KAAK,OAAQgD,GAClBxG,KAAK2b,c,8BAQC3V,GACNhG,KAAKwD,KAAK,QAASwC,GACnBhG,KAAKmO,SAAQ,K,8BAQPyN,GACN,QAAI,IAAuB5b,KAAK6a,KAAO,OAAS7a,KAAK6a,IAArD,CAUA,GANI7a,KAAKkb,SACPlb,KAAK6a,IAAIM,OAASnb,KAAK6a,IAAIlM,QAAUuL,EAErCla,KAAK6a,IAAIS,mBAAqBpB,EAG5B0B,EACF,IACE5b,KAAK6a,IAAIgB,QACT,MAAOrS,IAGa,oBAAbgS,iBACFlB,EAAQoB,SAAS1b,KAAKyR,OAG/BzR,KAAK6a,IAAM,Q,+BASX,IAAMrU,EAAOxG,KAAK6a,IAAIQ,aACT,OAAT7U,GACFxG,KAAK0a,OAAOlU,K,+BAUd,MAAiC,oBAAnBuK,iBAAmC/Q,KAAKkQ,IAAMlQ,KAAK8Q,a,8BASjE9Q,KAAKmO,c,GA5Ma/L,GAyNtB,GAHAkY,EAAQmB,cAAgB,EACxBnB,EAAQoB,SAAW,GAEK,oBAAbF,SACT,GAA2B,mBAAhBM,YACTA,YAAY,WAAYC,QACnB,GAAgC,mBAArBvZ,iBAAiC,CAEjDA,iBADyB,eAAgBqO,EAAa,WAAa,SAChCkL,GAAe,GAItD,SAASA,IACP,IAAK,IAAI3b,KAAKka,EAAQoB,SAChBpB,EAAQoB,SAASzZ,eAAe7B,IAClCka,EAAQoB,SAAStb,GAAGyb,QAK1Bhc,EAAOD,QAAUiQ,EACjBhQ,EAAOD,QAAQ0a,QAAUA,G,oBCnVjBpI,EAAiBlO,EAAQ,IAAzBkO,aAEF2D,EACY,mBAATC,MACU,oBAATA,MACmC,6BAAzChV,OAAOkB,UAAU4T,SAASrV,KAAKuV,MAC7BJ,EAA+C,mBAAhBC,YA8B/BqG,EAAqB,SAACxV,EAAMhC,GAChC,IAAMyX,EAAa,IAAIC,WAKvB,OAJAD,EAAWd,OAAS,WAClB,IAAMgB,EAAUF,EAAWG,OAAOlX,MAAM,KAAK,GAC7CV,EAAS,IAAM2X,IAEVF,EAAWI,cAAc7V,IAGlC3G,EAAOD,QA9Bc,SAAC,EAAgBmS,EAAgBvN,GAAa,IANpDnC,EAMS+C,EAA2C,EAA3CA,KAAMoB,EAAqC,EAArCA,KAC5B,OAAIqP,GAAkBrP,aAAgBsP,KAChC/D,EACKvN,EAASgC,GAETwV,EAAmBxV,EAAMhC,GAGlCkR,IACClP,aAAgBmP,cAfNtT,EAe4BmE,EAdJ,mBAAvBmP,YAAYM,OACtBN,YAAYM,OAAO5T,GACnBA,GAAOA,EAAI6T,kBAAkBP,cAc3B5D,EACKvN,EAASgC,aAAgBmP,YAAcnP,EAAOA,EAAK0P,QAEnD8F,EAAmB,IAAIlG,KAAK,CAACtP,IAAQhC,GAIzCA,EAAS0N,EAAa9M,IAASoB,GAAQ,O,oBC7B5C8V,E,EAJ2CtY,EAAQ,IAA/CmO,E,EAAAA,qBAAsBC,E,EAAAA,aAEuB,mBAAhBuD,cAInC2G,EAAgBtY,EAAQ,KAG1B,IA4BMuY,EAAqB,SAAC/V,EAAMvB,GAChC,GAAIqX,EAAe,CACjB,IAAMvJ,EAAUuJ,EAAczV,OAAOL,GACrC,OAAOgW,EAAUzJ,EAAS9N,GAE1B,MAAO,CAAE4D,QAAQ,EAAMrC,SAIrBgW,EAAY,SAAChW,EAAMvB,GACvB,OAAQA,GACN,IAAK,OACH,OAAOuB,aAAgBmP,YAAc,IAAIG,KAAK,CAACtP,IAASA,EAC1D,IAAK,cACL,QACE,OAAOA,IAIb3G,EAAOD,QA/Cc,SAACiF,EAAeI,GACnC,GAA6B,iBAAlBJ,EACT,MAAO,CACLO,KAAM,UACNoB,KAAMgW,EAAU3X,EAAeI,IAGnC,IAAMG,EAAOP,EAAcmE,OAAO,GAClC,MAAa,MAAT5D,EACK,CACLA,KAAM,UACNoB,KAAM+V,EAAmB1X,EAAcuE,UAAU,GAAInE,IAGtCkN,EAAqB/M,GAIjCP,EAAczB,OAAS,EAC1B,CACEgC,KAAM+M,EAAqB/M,GAC3BoB,KAAM3B,EAAcuE,UAAU,IAEhC,CACEhE,KAAM+M,EAAqB/M,IARxBgN,I,eClBX,SAAUqK,GACR,aAEA7c,EAAQ8G,OAAS,SAASgW,GACxB,IACAtc,EADIuc,EAAQ,IAAIC,WAAWF,GACxB/Y,EAAMgZ,EAAMvZ,OAAQyF,EAAS,GAEhC,IAAKzI,EAAI,EAAGA,EAAIuD,EAAKvD,GAAG,EACtByI,GAAU4T,EAAME,EAAMvc,IAAM,GAC5ByI,GAAU4T,GAAmB,EAAXE,EAAMvc,KAAW,EAAMuc,EAAMvc,EAAI,IAAM,GACzDyI,GAAU4T,GAAuB,GAAfE,EAAMvc,EAAI,KAAY,EAAMuc,EAAMvc,EAAI,IAAM,GAC9DyI,GAAU4T,EAAqB,GAAfE,EAAMvc,EAAI,IAS5B,OANKuD,EAAM,GAAO,EAChBkF,EAASA,EAAOO,UAAU,EAAGP,EAAOzF,OAAS,GAAK,IACzCO,EAAM,GAAM,IACrBkF,EAASA,EAAOO,UAAU,EAAGP,EAAOzF,OAAS,GAAK,MAG7CyF,GAGTjJ,EAAQiH,OAAU,SAASgC,GACzB,IACqBzI,EACrByc,EAAUC,EAAUC,EAAUC,EAF1BC,EAA+B,IAAhBpU,EAAOzF,OAC1BO,EAAMkF,EAAOzF,OAAWlB,EAAI,EAGM,MAA9B2G,EAAOA,EAAOzF,OAAS,KACzB6Z,IACkC,MAA9BpU,EAAOA,EAAOzF,OAAS,IACzB6Z,KAIJ,IAAIP,EAAc,IAAI/G,YAAYsH,GAClCN,EAAQ,IAAIC,WAAWF,GAEvB,IAAKtc,EAAI,EAAGA,EAAIuD,EAAKvD,GAAG,EACtByc,EAAWJ,EAAMjS,QAAQ3B,EAAOzI,IAChC0c,EAAWL,EAAMjS,QAAQ3B,EAAOzI,EAAE,IAClC2c,EAAWN,EAAMjS,QAAQ3B,EAAOzI,EAAE,IAClC4c,EAAWP,EAAMjS,QAAQ3B,EAAOzI,EAAE,IAElCuc,EAAMza,KAAQ2a,GAAY,EAAMC,GAAY,EAC5CH,EAAMza,MAAoB,GAAX4a,IAAkB,EAAMC,GAAY,EACnDJ,EAAMza,MAAoB,EAAX6a,IAAiB,EAAiB,GAAXC,EAGxC,OAAON,GAjDX,CAmDG,qE,mgDC1DH,IAUIpZ,EAVE6N,EAAUnN,EAAQ,IAClB6M,EAAa7M,EAAQ,GAErBkZ,EAAW,MACXC,EAAkB,OAQlBC,E,sQAOJ,WAAY1X,GAAM,O,4FAAA,UAChB,cAAMA,IAEDC,MAAQ,EAAKA,OAAS,GAItBrC,IAEHA,EAAYuN,EAAWwM,OAASxM,EAAWwM,QAAU,IAIvD,EAAK5L,MAAQnO,EAAUF,OAGvB,IAAMiC,EAAO,EAAH,GAhBM,OAiBhB/B,EAAUV,MAAK,SAASkD,GACtBT,EAAKqV,OAAO5U,MAId,EAAKH,MAAMsU,EAAI,EAAKxI,MAtBJ,E,+CAsCZzR,KAAKsd,SAEPtd,KAAKsd,OAAO3O,QAAU,aACtB3O,KAAKsd,OAAOC,WAAWC,YAAYxd,KAAKsd,QACxCtd,KAAKsd,OAAS,MAGZtd,KAAKyd,OACPzd,KAAKyd,KAAKF,WAAWC,YAAYxd,KAAKyd,MACtCzd,KAAKyd,KAAO,KACZzd,KAAK0d,OAAS,MAGhB,8C,+BASA,IAAMrY,EAAOrF,KACPsd,EAAS9B,SAASmC,cAAc,UAElC3d,KAAKsd,SACPtd,KAAKsd,OAAOC,WAAWC,YAAYxd,KAAKsd,QACxCtd,KAAKsd,OAAS,MAGhBA,EAAO1C,OAAQ,EACf0C,EAAOhT,IAAMtK,KAAK2K,MAClB2S,EAAO3O,QAAU,SAASnF,GACxBnE,EAAKqT,QAAQ,mBAAoBlP,IAGnC,IAAMoU,EAAWpC,SAASqC,qBAAqB,UAAU,GACrDD,EACFA,EAASL,WAAWO,aAAaR,EAAQM,IAExCpC,SAASuC,MAAQvC,SAASwC,MAAMC,YAAYX,GAE/Ctd,KAAKsd,OAASA,EAGZ,oBAAuBY,WAAa,SAASjH,KAAKiH,UAAUC,YAG5D7P,YAAW,WACT,IAAMoP,EAASlC,SAASmC,cAAc,UACtCnC,SAASwC,KAAKC,YAAYP,GAC1BlC,SAASwC,KAAKR,YAAYE,KACzB,O,8BAWClX,EAAM9D,GACZ,IACIgb,EADErY,EAAOrF,KAGb,IAAKA,KAAKyd,KAAM,CACd,IAAMA,EAAOjC,SAASmC,cAAc,QAC9BS,EAAO5C,SAASmC,cAAc,YAC9B1V,EAAMjI,KAAKqe,SAAW,cAAgBre,KAAKyR,MAEjDgM,EAAKa,UAAY,WACjBb,EAAKc,MAAMC,SAAW,WACtBf,EAAKc,MAAME,IAAM,UACjBhB,EAAKc,MAAMG,KAAO,UAClBjB,EAAKkB,OAAS1W,EACdwV,EAAKhD,OAAS,OACdgD,EAAKmB,aAAa,iBAAkB,SACpCR,EAAKzd,KAAO,IACZ8c,EAAKQ,YAAYG,GACjB5C,SAASwC,KAAKC,YAAYR,GAE1Bzd,KAAKyd,KAAOA,EACZzd,KAAKoe,KAAOA,EAKd,SAASS,IACPC,IACApc,IAGF,SAASoc,IACP,GAAIzZ,EAAKqY,OACP,IACErY,EAAKoY,KAAKD,YAAYnY,EAAKqY,QAC3B,MAAOlU,GACPnE,EAAKqT,QAAQ,qCAAsClP,GAIvD,IAEE,IAAMuV,EAAO,oCAAsC1Z,EAAKgZ,SAAW,KACnEX,EAASlC,SAASmC,cAAcoB,GAChC,MAAOvV,IACPkU,EAASlC,SAASmC,cAAc,WACzBhd,KAAO0E,EAAKgZ,SACnBX,EAAOpT,IAAM,eAGfoT,EAAOzV,GAAK5C,EAAKgZ,SAEjBhZ,EAAKoY,KAAKQ,YAAYP,GACtBrY,EAAKqY,OAASA,EA7BhB1d,KAAKyd,KAAKuB,OAAShf,KAAK2K,MAgCxBmU,IAIAtY,EAAOA,EAAKiE,QAAQ0S,EAAiB,QACrCnd,KAAKoe,KAAK/c,MAAQmF,EAAKiE,QAAQyS,EAAU,OAEzC,IACEld,KAAKyd,KAAKwB,SACV,MAAOzV,IAELxJ,KAAK0d,OAAO5B,YACd9b,KAAK0d,OAAOpC,mBAAqB,WACA,aAA3BjW,EAAKqY,OAAO9X,YACdiZ,KAIJ7e,KAAK0d,OAAOvC,OAAS0D,I,qCAlJvB,OAAO,O,8BApCgB1N,GA2L3BtR,EAAOD,QAAUwd,G,ytCCvMjB,IAAM3X,EAAYzB,EAAQ,GACpBwB,EAASxB,EAAQ,GACjBiN,EAAUjN,EAAQ,GAClBkN,EAAQlN,EAAQ,IACdgP,EAAShP,EAAQ,IAAjBgP,K,EAKJhP,EAAQ,IAHVkb,E,EAAAA,UACAC,E,EAAAA,sBACAC,E,EAAAA,kBAOIC,EACiB,oBAAdnB,WACsB,iBAAtBA,UAAUoB,SACmB,gBAApCpB,UAAUoB,QAAQC,cAEdC,E,sQAOJ,WAAY9Z,GAAM,a,4FAAA,UAChB,cAAMA,IAEDqM,gBAAkBrM,EAAK2U,YAHZ,E,8CAqBhB,GAAKra,KAAKyf,QAAV,CAKA,IAAM9U,EAAM3K,KAAK2K,MACX+U,EAAY1f,KAAK0F,KAAKga,UAGtBha,EAAO2Z,EACT,GACArM,EACEhT,KAAK0F,KACL,QACA,oBACA,MACA,MACA,aACA,OACA,KACA,UACA,qBACA,eACA,kBACA,SACA,aACA,SACA,uBAGF1F,KAAK0F,KAAKoV,eACZpV,EAAKia,QAAU3f,KAAK0F,KAAKoV,cAG3B,IACE9a,KAAK4f,GACHT,IAA0BE,EACtBK,EACE,IAAIR,EAAUvU,EAAK+U,GACnB,IAAIR,EAAUvU,GAChB,IAAIuU,EAAUvU,EAAK+U,EAAWha,GACpC,MAAOM,GACP,OAAOhG,KAAKwD,KAAK,QAASwC,GAG5BhG,KAAK4f,GAAG3a,WAAajF,KAAK6F,OAAOZ,YAAcma,EAE/Cpf,KAAK6f,uB,0CASL,IAAMxa,EAAOrF,KAEbA,KAAK4f,GAAG3R,OAAS,WACf5I,EAAKqM,UAEP1R,KAAK4f,GAAGhR,QAAU,WAChBvJ,EAAKgB,WAEPrG,KAAK4f,GAAGE,UAAY,SAAStL,GAC3BnP,EAAKqV,OAAOlG,EAAGhO,OAEjBxG,KAAK4f,GAAGjR,QAAU,SAASnF,GACzBnE,EAAKqT,QAAQ,kBAAmBlP,M,4BAU9BjF,GACJ,IAAMc,EAAOrF,KACbA,KAAKuG,UAAW,EAOhB,IAHA,IAAIgL,EAAQhN,EAAQnB,OAChBhD,EAAI,EACFC,EAAIkR,EACHnR,EAAIC,EAAGD,KACZ,SAAUwE,GACRY,EAAOzB,aAAaa,EAAQS,EAAK0M,gBAAgB,SAASvL,GAExD,IAAMd,EAAO,GACRyZ,IACCva,EAAOuK,UACTzJ,EAAK+O,SAAW7P,EAAOuK,QAAQsF,UAG7BpP,EAAKK,KAAKkS,oBAEV,iBAAoBpR,EAChBuZ,OAAOC,WAAWxZ,GAClBA,EAAKpD,QACDiC,EAAKK,KAAKkS,kBAAkBC,YACpCnS,EAAK+O,UAAW,IAQtB,IACM0K,EAEF9Z,EAAKua,GAAG5G,KAAKxS,GAEbnB,EAAKua,GAAG5G,KAAKxS,EAAMd,GAErB,MAAO8D,MAKP+H,IAMNlM,EAAK7B,KAAK,SAIV8K,YAAW,WACTjJ,EAAKkB,UAAW,EAChBlB,EAAK7B,KAAK,WACT,OAhDH,CAqCGe,EAAQnE,M,gCAqBbqF,EAAUzD,UAAUqE,QAAQ9F,KAAKP,Q,qCASV,IAAZA,KAAK4f,KACd5f,KAAK4f,GAAGrR,QACRvO,KAAK4f,GAAK,Q,4BAUZ,IAAIja,EAAQ3F,KAAK2F,OAAS,GACpBiM,EAAS5R,KAAK0F,KAAK8K,OAAS,MAAQ,KACtCF,EAAO,GA6BX,OAzBEtQ,KAAK0F,KAAK4K,OACR,QAAUsB,GAAqC,MAA3B7I,OAAO/I,KAAK0F,KAAK4K,OACpC,OAASsB,GAAqC,KAA3B7I,OAAO/I,KAAK0F,KAAK4K,SAEvCA,EAAO,IAAMtQ,KAAK0F,KAAK4K,MAIrBtQ,KAAK0F,KAAKmM,oBACZlM,EAAM3F,KAAK0F,KAAKoM,gBAAkBZ,KAI/BlR,KAAK+R,iBACRpM,EAAMsM,IAAM,IAGdtM,EAAQsL,EAAQvK,OAAOf,IAGbvC,SACRuC,EAAQ,IAAMA,GAKdiM,EACA,QAHgD,IAArC5R,KAAK0F,KAAK6K,SAAS/F,QAAQ,KAI9B,IAAMxK,KAAK0F,KAAK6K,SAAW,IAAMvQ,KAAK0F,KAAK6K,UACnDD,EACAtQ,KAAK0F,KAAKuF,KACVtF,I,8BAWF,SACIuZ,GACA,iBAAkBA,GAAalf,KAAKW,OAAS6e,EAAGxd,UAAUrB,Q,2BAlO9D,MAAO,iB,8BAnBM8E,GA0PjB5F,EAAOD,QAAU4f,G,gBC9QjB,IAAM3O,EAAa7M,EAAQ,GAE3BnE,EAAOD,QAAU,CACfsf,UAAWrO,EAAWqO,WAAarO,EAAWoP,aAC9Cd,uBAAuB,EACvBC,kBAAmB,gB,kQCJrBte,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,IACtDzB,EAAQuK,kBAAoBvK,EAAQyI,uBAAoB,EACxD,IAAMd,EAAcvD,EAAQ,IAgB5BpE,EAAQyI,kBARR,SAA2BzD,GACvB,IAAM2D,EAAU,GACV2X,EAAatb,EAAO4B,KACpB8B,EAAO1D,EAGb,OAFA0D,EAAK9B,KAKT,SAAS2Z,EAAmB3Z,EAAM+B,GAC9B,IAAK/B,EACD,OAAOA,EACX,GAAIe,EAAYqB,SAASpC,GAAO,CAC5B,IAAM4Z,EAAc,CAAEC,cAAc,EAAM5N,IAAKlK,EAAQnF,QAEvD,OADAmF,EAAQ3F,KAAK4D,GACN4Z,EAEN,GAAI1c,MAAMsG,QAAQxD,GAAO,CAE1B,IADA,IAAM8Z,EAAU,IAAI5c,MAAM8C,EAAKpD,QACtBhD,EAAI,EAAGA,EAAIoG,EAAKpD,OAAQhD,IAC7BkgB,EAAQlgB,GAAK+f,EAAmB3Z,EAAKpG,GAAImI,GAE7C,OAAO+X,EAEN,GAAoB,WAAhB,EAAO9Z,MAAuBA,aAAgBsM,MAAO,CAC1D,IAAMwN,EAAU,GAChB,IAAK,IAAM3e,KAAO6E,EACVA,EAAKvE,eAAeN,KACpB2e,EAAQ3e,GAAOwe,EAAmB3Z,EAAK7E,GAAM4G,IAGrD,OAAO+X,EAEX,OAAO9Z,EA7BK2Z,CAAmBD,EAAY3X,GAC3CD,EAAKP,YAAcQ,EAAQnF,OACpB,CAAEwB,OAAQ0D,EAAMC,QAASA,IA0CpC3I,EAAQuK,kBALR,SAA2BvF,EAAQ2D,GAG/B,OAFA3D,EAAO4B,KAKX,SAAS+Z,EAAmB/Z,EAAM+B,GAC9B,IAAK/B,EACD,OAAOA,EACX,GAAIA,GAAQA,EAAK6Z,aACb,OAAO9X,EAAQ/B,EAAKiM,KAEnB,GAAI/O,MAAMsG,QAAQxD,GACnB,IAAK,IAAIpG,EAAI,EAAGA,EAAIoG,EAAKpD,OAAQhD,IAC7BoG,EAAKpG,GAAKmgB,EAAmB/Z,EAAKpG,GAAImI,QAGzC,GAAoB,WAAhB,EAAO/B,GACZ,IAAK,IAAM7E,KAAO6E,EACVA,EAAKvE,eAAeN,KACpB6E,EAAK7E,GAAO4e,EAAmB/Z,EAAK7E,GAAM4G,IAItD,OAAO/B,EAvBO+Z,CAAmB3b,EAAO4B,KAAM+B,GAC9C3D,EAAOmD,iBAAckB,EACdrE,I,cCtCX,SAAS+G,EAAQjG,GACfA,EAAOA,GAAQ,GACf1F,KAAKwgB,GAAK9a,EAAK2G,KAAO,IACtBrM,KAAKsM,IAAM5G,EAAK4G,KAAO,IACvBtM,KAAKygB,OAAS/a,EAAK+a,QAAU,EAC7BzgB,KAAKuM,OAAS7G,EAAK6G,OAAS,GAAK7G,EAAK6G,QAAU,EAAI7G,EAAK6G,OAAS,EAClEvM,KAAK4N,SAAW,EApBlB/N,EAAOD,QAAU+L,EA8BjBA,EAAQ3J,UAAUyN,SAAW,WAC3B,IAAI+Q,EAAKxgB,KAAKwgB,GAAK7N,KAAK+N,IAAI1gB,KAAKygB,OAAQzgB,KAAK4N,YAC9C,GAAI5N,KAAKuM,OAAQ,CACf,IAAIoU,EAAQhO,KAAKiO,SACbC,EAAYlO,KAAKC,MAAM+N,EAAO3gB,KAAKuM,OAASiU,GAChDA,EAAoC,IAAN,EAAxB7N,KAAKC,MAAa,GAAP+N,IAAwBH,EAAKK,EAAYL,EAAKK,EAEjE,OAAgC,EAAzBlO,KAAKtG,IAAImU,EAAIxgB,KAAKsM,MAS3BX,EAAQ3J,UAAUsN,MAAQ,WACxBtP,KAAK4N,SAAW,GASlBjC,EAAQ3J,UAAUqL,OAAS,SAAShB,GAClCrM,KAAKwgB,GAAKnU,GASZV,EAAQ3J,UAAUyL,OAAS,SAASnB,GAClCtM,KAAKsM,IAAMA,GASbX,EAAQ3J,UAAUuL,UAAY,SAAShB,GACrCvM,KAAKuM,OAASA", "file": "socket.io.min.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"io\"] = factory();\n\telse\n\t\troot[\"io\"] = factory();\n})(this, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 17);\n", "\r\n/**\r\n * Expose `Emitter`.\r\n */\r\n\r\nif (typeof module !== 'undefined') {\r\n  module.exports = Emitter;\r\n}\r\n\r\n/**\r\n * Initialize a new `Emitter`.\r\n *\r\n * @api public\r\n */\r\n\r\nfunction Emitter(obj) {\r\n  if (obj) return mixin(obj);\r\n};\r\n\r\n/**\r\n * Mixin the emitter properties.\r\n *\r\n * @param {Object} obj\r\n * @return {Object}\r\n * @api private\r\n */\r\n\r\nfunction mixin(obj) {\r\n  for (var key in Emitter.prototype) {\r\n    obj[key] = Emitter.prototype[key];\r\n  }\r\n  return obj;\r\n}\r\n\r\n/**\r\n * Listen on the given `event` with `fn`.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.on =\r\nEmitter.prototype.addEventListener = function(event, fn){\r\n  this._callbacks = this._callbacks || {};\r\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\r\n    .push(fn);\r\n  return this;\r\n};\r\n\r\n/**\r\n * Adds an `event` listener that will be invoked a single\r\n * time then automatically removed.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.once = function(event, fn){\r\n  function on() {\r\n    this.off(event, on);\r\n    fn.apply(this, arguments);\r\n  }\r\n\r\n  on.fn = fn;\r\n  this.on(event, on);\r\n  return this;\r\n};\r\n\r\n/**\r\n * Remove the given callback for `event` or all\r\n * registered callbacks.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.off =\r\nEmitter.prototype.removeListener =\r\nEmitter.prototype.removeAllListeners =\r\nEmitter.prototype.removeEventListener = function(event, fn){\r\n  this._callbacks = this._callbacks || {};\r\n\r\n  // all\r\n  if (0 == arguments.length) {\r\n    this._callbacks = {};\r\n    return this;\r\n  }\r\n\r\n  // specific event\r\n  var callbacks = this._callbacks['$' + event];\r\n  if (!callbacks) return this;\r\n\r\n  // remove all handlers\r\n  if (1 == arguments.length) {\r\n    delete this._callbacks['$' + event];\r\n    return this;\r\n  }\r\n\r\n  // remove specific handler\r\n  var cb;\r\n  for (var i = 0; i < callbacks.length; i++) {\r\n    cb = callbacks[i];\r\n    if (cb === fn || cb.fn === fn) {\r\n      callbacks.splice(i, 1);\r\n      break;\r\n    }\r\n  }\r\n\r\n  // Remove event specific arrays for event types that no\r\n  // one is subscribed for to avoid memory leak.\r\n  if (callbacks.length === 0) {\r\n    delete this._callbacks['$' + event];\r\n  }\r\n\r\n  return this;\r\n};\r\n\r\n/**\r\n * Emit `event` with the given args.\r\n *\r\n * @param {String} event\r\n * @param {Mixed} ...\r\n * @return {Emitter}\r\n */\r\n\r\nEmitter.prototype.emit = function(event){\r\n  this._callbacks = this._callbacks || {};\r\n\r\n  var args = new Array(arguments.length - 1)\r\n    , callbacks = this._callbacks['$' + event];\r\n\r\n  for (var i = 1; i < arguments.length; i++) {\r\n    args[i - 1] = arguments[i];\r\n  }\r\n\r\n  if (callbacks) {\r\n    callbacks = callbacks.slice(0);\r\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\r\n      callbacks[i].apply(this, args);\r\n    }\r\n  }\r\n\r\n  return this;\r\n};\r\n\r\n/**\r\n * Return array of callbacks for `event`.\r\n *\r\n * @param {String} event\r\n * @return {Array}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.listeners = function(event){\r\n  this._callbacks = this._callbacks || {};\r\n  return this._callbacks['$' + event] || [];\r\n};\r\n\r\n/**\r\n * Check if this emitter has `event` handlers.\r\n *\r\n * @param {String} event\r\n * @return {Boolean}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.hasListeners = function(event){\r\n  return !! this.listeners(event).length;\r\n};\r\n", "const encodePacket = require(\"./encodePacket\");\nconst decodePacket = require(\"./decodePacket\");\n\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\n\nconst encodePayload = (packets, callback) => {\n  // some packets may be added to the array while encoding, so the initial length must be saved\n  const length = packets.length;\n  const encodedPackets = new Array(length);\n  let count = 0;\n\n  packets.forEach((packet, i) => {\n    // force base64 encoding for binary packets\n    encodePacket(packet, false, encodedPacket => {\n      encodedPackets[i] = encodedPacket;\n      if (++count === length) {\n        callback(encodedPackets.join(SEPARATOR));\n      }\n    });\n  });\n};\n\nconst decodePayload = (encodedPayload, binaryType) => {\n  const encodedPackets = encodedPayload.split(SEPARATOR);\n  const packets = [];\n  for (let i = 0; i < encodedPackets.length; i++) {\n    const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n    packets.push(decodedPacket);\n    if (decodedPacket.type === \"error\") {\n      break;\n    }\n  }\n  return packets;\n};\n\nmodule.exports = {\n  protocol: 4,\n  encodePacket,\n  encodePayload,\n  decodePacket,\n  decodePayload\n};\n", "module.exports = (() => {\n  if (typeof self !== \"undefined\") {\n    return self;\n  } else if (typeof window !== \"undefined\") {\n    return window;\n  } else {\n    return Function(\"return this\")();\n  }\n})();\n", "const parser = require(\"engine.io-parser\");\nconst Emitter = require(\"component-emitter\");\n\nclass Transport extends Emitter {\n  /**\n   * Transport abstract constructor.\n   *\n   * @param {Object} options.\n   * @api private\n   */\n  constructor(opts) {\n    super();\n\n    this.opts = opts;\n    this.query = opts.query;\n    this.readyState = \"\";\n    this.socket = opts.socket;\n  }\n\n  /**\n   * Emits an error.\n   *\n   * @param {String} str\n   * @return {Transport} for chaining\n   * @api public\n   */\n  onError(msg, desc) {\n    const err = new Error(msg);\n    err.type = \"TransportError\";\n    err.description = desc;\n    this.emit(\"error\", err);\n    return this;\n  }\n\n  /**\n   * Opens the transport.\n   *\n   * @api public\n   */\n  open() {\n    if (\"closed\" === this.readyState || \"\" === this.readyState) {\n      this.readyState = \"opening\";\n      this.doOpen();\n    }\n\n    return this;\n  }\n\n  /**\n   * Closes the transport.\n   *\n   * @api private\n   */\n  close() {\n    if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n      this.doClose();\n      this.onClose();\n    }\n\n    return this;\n  }\n\n  /**\n   * Sends multiple packets.\n   *\n   * @param {Array} packets\n   * @api private\n   */\n  send(packets) {\n    if (\"open\" === this.readyState) {\n      this.write(packets);\n    } else {\n      throw new Error(\"Transport not open\");\n    }\n  }\n\n  /**\n   * Called upon open\n   *\n   * @api private\n   */\n  onOpen() {\n    this.readyState = \"open\";\n    this.writable = true;\n    this.emit(\"open\");\n  }\n\n  /**\n   * Called with data.\n   *\n   * @param {String} data\n   * @api private\n   */\n  onData(data) {\n    const packet = parser.decodePacket(data, this.socket.binaryType);\n    this.onPacket(packet);\n  }\n\n  /**\n   * Called with a decoded packet.\n   */\n  onPacket(packet) {\n    this.emit(\"packet\", packet);\n  }\n\n  /**\n   * Called upon close.\n   *\n   * @api private\n   */\n  onClose() {\n    this.readyState = \"closed\";\n    this.emit(\"close\");\n  }\n}\n\nmodule.exports = Transport;\n", "/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\n\nexports.encode = function (obj) {\n  var str = '';\n\n  for (var i in obj) {\n    if (obj.hasOwnProperty(i)) {\n      if (str.length) str += '&';\n      str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n    }\n  }\n\n  return str;\n};\n\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\n\nexports.decode = function(qs){\n  var qry = {};\n  var pairs = qs.split('&');\n  for (var i = 0, l = pairs.length; i < l; i++) {\n    var pair = pairs[i].split('=');\n    qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n  }\n  return qry;\n};\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Decoder = exports.Encoder = exports.PacketType = exports.protocol = void 0;\nconst Emitter = require(\"component-emitter\");\nconst binary_1 = require(\"./binary\");\nconst is_binary_1 = require(\"./is-binary\");\n\n\n/**\n * Protocol version.\n *\n * @public\n */\nexports.protocol = 5;\nvar PacketType;\n(function (PacketType) {\n    PacketType[PacketType[\"CONNECT\"] = 0] = \"CONNECT\";\n    PacketType[PacketType[\"DISCONNECT\"] = 1] = \"DISCONNECT\";\n    PacketType[PacketType[\"EVENT\"] = 2] = \"EVENT\";\n    PacketType[PacketType[\"ACK\"] = 3] = \"ACK\";\n    PacketType[PacketType[\"CONNECT_ERROR\"] = 4] = \"CONNECT_ERROR\";\n    PacketType[PacketType[\"BINARY_EVENT\"] = 5] = \"BINARY_EVENT\";\n    PacketType[PacketType[\"BINARY_ACK\"] = 6] = \"BINARY_ACK\";\n})(PacketType = exports.PacketType || (exports.PacketType = {}));\n/**\n * A socket.io Encoder instance\n */\nclass Encoder {\n    /**\n     * Encode a packet as a single string if non-binary, or as a\n     * buffer sequence, depending on packet type.\n     *\n     * @param {Object} obj - packet object\n     */\n    encode(obj) {\n\n\n        if (obj.type === PacketType.EVENT || obj.type === PacketType.ACK) {\n            if (is_binary_1.hasBinary(obj)) {\n                obj.type =\n                    obj.type === PacketType.EVENT\n                        ? PacketType.BINARY_EVENT\n                        : PacketType.BINARY_ACK;\n                return this.encodeAsBinary(obj);\n            }\n        }\n        return [this.encodeAsString(obj)];\n    }\n    /**\n     * Encode packet as string.\n     */\n    encodeAsString(obj) {\n        // first is type\n        let str = \"\" + obj.type;\n        // attachments if we have them\n        if (obj.type === PacketType.BINARY_EVENT ||\n            obj.type === PacketType.BINARY_ACK) {\n            str += obj.attachments + \"-\";\n        }\n        // if we have a namespace other than `/`\n        // we append it followed by a comma `,`\n        if (obj.nsp && \"/\" !== obj.nsp) {\n            str += obj.nsp + \",\";\n        }\n        // immediately followed by the id\n        if (null != obj.id) {\n            str += obj.id;\n        }\n        // json data\n        if (null != obj.data) {\n            str += JSON.stringify(obj.data);\n        }\n\n\n        return str;\n    }\n    /**\n     * Encode packet as 'buffer sequence' by removing blobs, and\n     * deconstructing packet into object with placeholders and\n     * a list of buffers.\n     */\n    encodeAsBinary(obj) {\n        const deconstruction = binary_1.deconstructPacket(obj);\n        const pack = this.encodeAsString(deconstruction.packet);\n        const buffers = deconstruction.buffers;\n        buffers.unshift(pack); // add packet info to beginning of data list\n        return buffers; // write all the buffers\n    }\n}\nexports.Encoder = Encoder;\n/**\n * A socket.io Decoder instance\n *\n * @return {Object} decoder\n */\nclass Decoder extends Emitter {\n    constructor() {\n        super();\n    }\n    /**\n     * Decodes an encoded packet string into packet JSON.\n     *\n     * @param {String} obj - encoded packet\n     */\n    add(obj) {\n        let packet;\n        if (typeof obj === \"string\") {\n            packet = this.decodeString(obj);\n            if (packet.type === PacketType.BINARY_EVENT ||\n                packet.type === PacketType.BINARY_ACK) {\n                // binary packet's json\n                this.reconstructor = new BinaryReconstructor(packet);\n                // no attachments, labeled binary but no binary data to follow\n                if (packet.attachments === 0) {\n                    super.emit(\"decoded\", packet);\n                }\n            }\n            else {\n                // non-binary full packet\n                super.emit(\"decoded\", packet);\n            }\n        }\n        else if (is_binary_1.isBinary(obj) || obj.base64) {\n            // raw binary data\n            if (!this.reconstructor) {\n                throw new Error(\"got binary data when not reconstructing a packet\");\n            }\n            else {\n                packet = this.reconstructor.takeBinaryData(obj);\n                if (packet) {\n                    // received final buffer\n                    this.reconstructor = null;\n                    super.emit(\"decoded\", packet);\n                }\n            }\n        }\n        else {\n            throw new Error(\"Unknown type: \" + obj);\n        }\n    }\n    /**\n     * Decode a packet String (JSON data)\n     *\n     * @param {String} str\n     * @return {Object} packet\n     */\n    decodeString(str) {\n        let i = 0;\n        // look up type\n        const p = {\n            type: Number(str.charAt(0)),\n        };\n        if (PacketType[p.type] === undefined) {\n            throw new Error(\"unknown packet type \" + p.type);\n        }\n        // look up attachments if type binary\n        if (p.type === PacketType.BINARY_EVENT ||\n            p.type === PacketType.BINARY_ACK) {\n            const start = i + 1;\n            while (str.charAt(++i) !== \"-\" && i != str.length) { }\n            const buf = str.substring(start, i);\n            if (buf != Number(buf) || str.charAt(i) !== \"-\") {\n                throw new Error(\"Illegal attachments\");\n            }\n            p.attachments = Number(buf);\n        }\n        // look up namespace (if any)\n        if (\"/\" === str.charAt(i + 1)) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (\",\" === c)\n                    break;\n                if (i === str.length)\n                    break;\n            }\n            p.nsp = str.substring(start, i);\n        }\n        else {\n            p.nsp = \"/\";\n        }\n        // look up id\n        const next = str.charAt(i + 1);\n        if (\"\" !== next && Number(next) == next) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (null == c || Number(c) != c) {\n                    --i;\n                    break;\n                }\n                if (i === str.length)\n                    break;\n            }\n            p.id = Number(str.substring(start, i + 1));\n        }\n        // look up json data\n        if (str.charAt(++i)) {\n            const payload = tryParse(str.substr(i));\n            if (Decoder.isPayloadValid(p.type, payload)) {\n                p.data = payload;\n            }\n            else {\n                throw new Error(\"invalid payload\");\n            }\n        }\n\n\n        return p;\n    }\n    static isPayloadValid(type, payload) {\n        switch (type) {\n            case PacketType.CONNECT:\n                return typeof payload === \"object\";\n            case PacketType.DISCONNECT:\n                return payload === undefined;\n            case PacketType.CONNECT_ERROR:\n                return typeof payload === \"string\" || typeof payload === \"object\";\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                return Array.isArray(payload) && payload.length > 0;\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                return Array.isArray(payload);\n        }\n    }\n    /**\n     * Deallocates a parser's resources\n     */\n    destroy() {\n        if (this.reconstructor) {\n            this.reconstructor.finishedReconstruction();\n        }\n    }\n}\nexports.Decoder = Decoder;\nfunction tryParse(str) {\n    try {\n        return JSON.parse(str);\n    }\n    catch (e) {\n        return false;\n    }\n}\n/**\n * A manager of a binary event's 'buffer sequence'. Should\n * be constructed whenever a packet of type BINARY_EVENT is\n * decoded.\n *\n * @param {Object} packet\n * @return {BinaryReconstructor} initialized reconstructor\n */\nclass BinaryReconstructor {\n    constructor(packet) {\n        this.packet = packet;\n        this.buffers = [];\n        this.reconPack = packet;\n    }\n    /**\n     * Method to be called when binary data received from connection\n     * after a BINARY_EVENT packet.\n     *\n     * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n     * @return {null | Object} returns null if more binary data is expected or\n     *   a reconstructed packet object if all buffers have been received.\n     */\n    takeBinaryData(binData) {\n        this.buffers.push(binData);\n        if (this.buffers.length === this.reconPack.attachments) {\n            // done with buffer list\n            const packet = binary_1.reconstructPacket(this.reconPack, this.buffers);\n            this.finishedReconstruction();\n            return packet;\n        }\n        return null;\n    }\n    /**\n     * Cleans up binary packet reconstruction variables.\n     */\n    finishedReconstruction() {\n        this.reconPack = null;\n        this.buffers = [];\n    }\n}\n", "/**\n * Parses an URI\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\n\nvar re = /^(?:(?![^:@]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\n\nvar parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\n\nmodule.exports = function parseuri(str) {\n    var src = str,\n        b = str.indexOf('['),\n        e = str.indexOf(']');\n\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n\n    var m = re.exec(str || ''),\n        uri = {},\n        i = 14;\n\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n\n    return uri;\n};\n\nfunction pathNames(obj, path) {\n    var regx = /\\/{2,9}/g,\n        names = path.replace(regx, \"/\").split(\"/\");\n\n    if (path.substr(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.substr(path.length - 1, 1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n\n    return names;\n}\n\nfunction queryKey(uri, query) {\n    var data = {};\n\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n\n    return data;\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Manager = void 0;\nconst eio = require(\"engine.io-client\");\nconst socket_1 = require(\"./socket\");\nconst Emitter = require(\"component-emitter\");\nconst parser = require(\"socket.io-parser\");\nconst on_1 = require(\"./on\");\nconst Backoff = require(\"backo2\");\n\n\nclass Manager extends Emitter {\n    constructor(uri, opts) {\n        super();\n        this.nsps = {};\n        this.subs = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = undefined;\n        }\n        opts = opts || {};\n        opts.path = opts.path || \"/socket.io\";\n        this.opts = opts;\n        this.reconnection(opts.reconnection !== false);\n        this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n        this.reconnectionDelay(opts.reconnectionDelay || 1000);\n        this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n        this.randomizationFactor(opts.randomizationFactor || 0.5);\n        this.backoff = new Backoff({\n            min: this.reconnectionDelay(),\n            max: this.reconnectionDelayMax(),\n            jitter: this.randomizationFactor(),\n        });\n        this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n        this._readyState = \"closed\";\n        this.uri = uri;\n        const _parser = opts.parser || parser;\n        this.encoder = new _parser.Encoder();\n        this.decoder = new _parser.Decoder();\n        this._autoConnect = opts.autoConnect !== false;\n        if (this._autoConnect)\n            this.open();\n    }\n    reconnection(v) {\n        if (!arguments.length)\n            return this._reconnection;\n        this._reconnection = !!v;\n        return this;\n    }\n    reconnectionAttempts(v) {\n        if (v === undefined)\n            return this._reconnectionAttempts;\n        this._reconnectionAttempts = v;\n        return this;\n    }\n    reconnectionDelay(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelay;\n        this._reconnectionDelay = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMin(v);\n        return this;\n    }\n    randomizationFactor(v) {\n        var _a;\n        if (v === undefined)\n            return this._randomizationFactor;\n        this._randomizationFactor = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setJitter(v);\n        return this;\n    }\n    reconnectionDelayMax(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelayMax;\n        this._reconnectionDelayMax = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMax(v);\n        return this;\n    }\n    timeout(v) {\n        if (!arguments.length)\n            return this._timeout;\n        this._timeout = v;\n        return this;\n    }\n    /**\n     * Starts trying to reconnect if reconnection is enabled and we have not\n     * started reconnecting yet\n     *\n     * @private\n     */\n    maybeReconnectOnOpen() {\n        // Only try to reconnect if it's the first time we're connecting\n        if (!this._reconnecting &&\n            this._reconnection &&\n            this.backoff.attempts === 0) {\n            // keeps reconnection from firing twice for the same reconnection loop\n            this.reconnect();\n        }\n    }\n    /**\n     * Sets the current transport `socket`.\n     *\n     * @param {Function} fn - optional, callback\n     * @return self\n     * @public\n     */\n    open(fn) {\n\n\n        if (~this._readyState.indexOf(\"open\"))\n            return this;\n\n\n        this.engine = eio(this.uri, this.opts);\n        const socket = this.engine;\n        const self = this;\n        this._readyState = \"opening\";\n        this.skipReconnect = false;\n        // emit `open`\n        const openSubDestroy = on_1.on(socket, \"open\", function () {\n            self.onopen();\n            fn && fn();\n        });\n        // emit `error`\n        const errorSub = on_1.on(socket, \"error\", (err) => {\n\n\n            self.cleanup();\n            self._readyState = \"closed\";\n            super.emit(\"error\", err);\n            if (fn) {\n                fn(err);\n            }\n            else {\n                // Only do this if there is no fn to handle the error\n                self.maybeReconnectOnOpen();\n            }\n        });\n        if (false !== this._timeout) {\n            const timeout = this._timeout;\n\n\n            if (timeout === 0) {\n                openSubDestroy(); // prevents a race condition with the 'open' event\n            }\n            // set timer\n            const timer = setTimeout(() => {\n\n\n                openSubDestroy();\n                socket.close();\n                socket.emit(\"error\", new Error(\"timeout\"));\n            }, timeout);\n            this.subs.push(function subDestroy() {\n                clearTimeout(timer);\n            });\n        }\n        this.subs.push(openSubDestroy);\n        this.subs.push(errorSub);\n        return this;\n    }\n    /**\n     * Alias for open()\n     *\n     * @return self\n     * @public\n     */\n    connect(fn) {\n        return this.open(fn);\n    }\n    /**\n     * Called upon transport open.\n     *\n     * @private\n     */\n    onopen() {\n\n\n        // clear old subs\n        this.cleanup();\n        // mark as open\n        this._readyState = \"open\";\n        super.emit(\"open\");\n        // add new subs\n        const socket = this.engine;\n        this.subs.push(on_1.on(socket, \"ping\", this.onping.bind(this)), on_1.on(socket, \"data\", this.ondata.bind(this)), on_1.on(socket, \"error\", this.onerror.bind(this)), on_1.on(socket, \"close\", this.onclose.bind(this)), on_1.on(this.decoder, \"decoded\", this.ondecoded.bind(this)));\n    }\n    /**\n     * Called upon a ping.\n     *\n     * @private\n     */\n    onping() {\n        super.emit(\"ping\");\n    }\n    /**\n     * Called with data.\n     *\n     * @private\n     */\n    ondata(data) {\n        this.decoder.add(data);\n    }\n    /**\n     * Called when parser fully decodes a packet.\n     *\n     * @private\n     */\n    ondecoded(packet) {\n        super.emit(\"packet\", packet);\n    }\n    /**\n     * Called upon socket error.\n     *\n     * @private\n     */\n    onerror(err) {\n\n\n        super.emit(\"error\", err);\n    }\n    /**\n     * Creates a new socket for the given `nsp`.\n     *\n     * @return {Socket}\n     * @public\n     */\n    socket(nsp, opts) {\n        let socket = this.nsps[nsp];\n        if (!socket) {\n            socket = new socket_1.Socket(this, nsp, opts);\n            this.nsps[nsp] = socket;\n        }\n        return socket;\n    }\n    /**\n     * Called upon a socket close.\n     *\n     * @param socket\n     * @private\n     */\n    _destroy(socket) {\n        const nsps = Object.keys(this.nsps);\n        for (const nsp of nsps) {\n            const socket = this.nsps[nsp];\n            if (socket.active) {\n\n\n                return;\n            }\n        }\n        this._close();\n    }\n    /**\n     * Writes a packet.\n     *\n     * @param packet\n     * @private\n     */\n    _packet(packet) {\n\n\n        const encodedPackets = this.encoder.encode(packet);\n        for (let i = 0; i < encodedPackets.length; i++) {\n            this.engine.write(encodedPackets[i], packet.options);\n        }\n    }\n    /**\n     * Clean up transport subscriptions and packet buffer.\n     *\n     * @private\n     */\n    cleanup() {\n\n\n        this.subs.forEach((subDestroy) => subDestroy());\n        this.subs.length = 0;\n        this.decoder.destroy();\n    }\n    /**\n     * Close the current socket.\n     *\n     * @private\n     */\n    _close() {\n\n\n        this.skipReconnect = true;\n        this._reconnecting = false;\n        if (\"opening\" === this._readyState) {\n            // `onclose` will not fire because\n            // an open event never happened\n            this.cleanup();\n        }\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        if (this.engine)\n            this.engine.close();\n    }\n    /**\n     * Alias for close()\n     *\n     * @private\n     */\n    disconnect() {\n        return this._close();\n    }\n    /**\n     * Called upon engine close.\n     *\n     * @private\n     */\n    onclose(reason) {\n\n\n        this.cleanup();\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        super.emit(\"close\", reason);\n        if (this._reconnection && !this.skipReconnect) {\n            this.reconnect();\n        }\n    }\n    /**\n     * Attempt a reconnection.\n     *\n     * @private\n     */\n    reconnect() {\n        if (this._reconnecting || this.skipReconnect)\n            return this;\n        const self = this;\n        if (this.backoff.attempts >= this._reconnectionAttempts) {\n\n\n            this.backoff.reset();\n            super.emit(\"reconnect_failed\");\n            this._reconnecting = false;\n        }\n        else {\n            const delay = this.backoff.duration();\n\n\n            this._reconnecting = true;\n            const timer = setTimeout(() => {\n                if (self.skipReconnect)\n                    return;\n\n\n                super.emit(\"reconnect_attempt\", self.backoff.attempts);\n                // check again for the case socket closed in above events\n                if (self.skipReconnect)\n                    return;\n                self.open((err) => {\n                    if (err) {\n\n\n                        self._reconnecting = false;\n                        self.reconnect();\n                        super.emit(\"reconnect_error\", err);\n                    }\n                    else {\n\n\n                        self.onreconnect();\n                    }\n                });\n            }, delay);\n            this.subs.push(function subDestroy() {\n                clearTimeout(timer);\n            });\n        }\n    }\n    /**\n     * Called upon successful reconnect.\n     *\n     * @private\n     */\n    onreconnect() {\n        const attempt = this.backoff.attempts;\n        this._reconnecting = false;\n        this.backoff.reset();\n        super.emit(\"reconnect\", attempt);\n    }\n}\nexports.Manager = Manager;\n", "const XMLHttpRequest = require(\"xmlhttprequest-ssl\");\nconst XHR = require(\"./polling-xhr\");\nconst JSONP = require(\"./polling-jsonp\");\nconst websocket = require(\"./websocket\");\n\nexports.polling = polling;\nexports.websocket = websocket;\n\n/**\n * Polling transport polymorphic constructor.\n * Decides on xhr vs jsonp based on feature detection.\n *\n * @api private\n */\n\nfunction polling(opts) {\n  let xhr;\n  let xd = false;\n  let xs = false;\n  const jsonp = false !== opts.jsonp;\n\n  if (typeof location !== \"undefined\") {\n    const isSSL = \"https:\" === location.protocol;\n    let port = location.port;\n\n    // some user agents have empty `location.port`\n    if (!port) {\n      port = isSSL ? 443 : 80;\n    }\n\n    xd = opts.hostname !== location.hostname || port !== opts.port;\n    xs = opts.secure !== isSSL;\n  }\n\n  opts.xdomain = xd;\n  opts.xscheme = xs;\n  xhr = new XMLHttpRequest(opts);\n\n  if (\"open\" in xhr && !opts.forceJSONP) {\n    return new XHR(opts);\n  } else {\n    if (!jsonp) throw new Error(\"JSONP disabled\");\n    return new JSONP(opts);\n  }\n}\n", "// browser shim for xmlhttprequest module\n\nconst hasCORS = require(\"has-cors\");\nconst globalThis = require(\"./globalThis\");\n\nmodule.exports = function(opts) {\n  const xdomain = opts.xdomain;\n\n  // scheme must be same when usign XDomainRequest\n  // http://blogs.msdn.com/b/ieinternals/archive/2010/05/13/xdomainrequest-restrictions-limitations-and-workarounds.aspx\n  const xscheme = opts.xscheme;\n\n  // XDomainRequest has a flow of not sending cookie, therefore it should be disabled as a default.\n  // https://github.com/Automattic/engine.io-client/pull/217\n  const enablesXDR = opts.enablesXDR;\n\n  // XMLHttpRequest can be disabled on IE\n  try {\n    if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n      return new XMLHttpRequest();\n    }\n  } catch (e) {}\n\n  // Use XDomainRequest for IE8 if enablesXDR is true\n  // because loading bar keeps flashing when using jsonp-polling\n  // https://github.com/yujiosaka/socke.io-ie8-loading-example\n  try {\n    if (\"undefined\" !== typeof XDomainRequest && !xscheme && enablesXDR) {\n      return new XDomainRequest();\n    }\n  } catch (e) {}\n\n  if (!xdomain) {\n    try {\n      return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\n        \"Microsoft.XMLHTTP\"\n      );\n    } catch (e) {}\n  }\n};\n", "const Transport = require(\"../transport\");\nconst parseqs = require(\"parseqs\");\nconst parser = require(\"engine.io-parser\");\nconst yeast = require(\"yeast\");\n\n\n\n\nclass Polling extends Transport {\n  /**\n   * Transport name.\n   */\n  get name() {\n    return \"polling\";\n  }\n\n  /**\n   * Opens the socket (triggers polling). We write a PING message to determine\n   * when the transport is open.\n   *\n   * @api private\n   */\n  doOpen() {\n    this.poll();\n  }\n\n  /**\n   * Pauses polling.\n   *\n   * @param {Function} callback upon buffers are flushed and transport is paused\n   * @api private\n   */\n  pause(onPause) {\n    const self = this;\n\n    this.readyState = \"pausing\";\n\n    function pause() {\n\n\n      self.readyState = \"paused\";\n      onPause();\n    }\n\n    if (this.polling || !this.writable) {\n      let total = 0;\n\n      if (this.polling) {\n\n\n        total++;\n        this.once(\"pollComplete\", function() {\n\n\n          --total || pause();\n        });\n      }\n\n      if (!this.writable) {\n\n\n        total++;\n        this.once(\"drain\", function() {\n\n\n          --total || pause();\n        });\n      }\n    } else {\n      pause();\n    }\n  }\n\n  /**\n   * Starts polling cycle.\n   *\n   * @api public\n   */\n  poll() {\n\n\n    this.polling = true;\n    this.doPoll();\n    this.emit(\"poll\");\n  }\n\n  /**\n   * Overloads onData to detect payloads.\n   *\n   * @api private\n   */\n  onData(data) {\n    const self = this;\n\n\n    const callback = function(packet, index, total) {\n      // if its the first message we consider the transport open\n      if (\"opening\" === self.readyState && packet.type === \"open\") {\n        self.onOpen();\n      }\n\n      // if its a close packet, we close the ongoing requests\n      if (\"close\" === packet.type) {\n        self.onClose();\n        return false;\n      }\n\n      // otherwise bypass onData and handle the message\n      self.onPacket(packet);\n    };\n\n    // decode payload\n    parser.decodePayload(data, this.socket.binaryType).forEach(callback);\n\n    // if an event did not trigger closing\n    if (\"closed\" !== this.readyState) {\n      // if we got data we're not polling\n      this.polling = false;\n      this.emit(\"pollComplete\");\n\n      if (\"open\" === this.readyState) {\n        this.poll();\n      } else {\n\n\n      }\n    }\n  }\n\n  /**\n   * For polling, send a close packet.\n   *\n   * @api private\n   */\n  doClose() {\n    const self = this;\n\n    function close() {\n\n\n      self.write([{ type: \"close\" }]);\n    }\n\n    if (\"open\" === this.readyState) {\n\n\n      close();\n    } else {\n      // in case we're trying to close while\n      // handshaking is in progress (GH-164)\n\n\n      this.once(\"open\", close);\n    }\n  }\n\n  /**\n   * Writes a packets payload.\n   *\n   * @param {Array} data packets\n   * @param {Function} drain callback\n   * @api private\n   */\n  write(packets) {\n    this.writable = false;\n\n    parser.encodePayload(packets, data => {\n      this.doWrite(data, () => {\n        this.writable = true;\n        this.emit(\"drain\");\n      });\n    });\n  }\n\n  /**\n   * Generates uri for connection.\n   *\n   * @api private\n   */\n  uri() {\n    let query = this.query || {};\n    const schema = this.opts.secure ? \"https\" : \"http\";\n    let port = \"\";\n\n    // cache busting is forced\n    if (false !== this.opts.timestampRequests) {\n      query[this.opts.timestampParam] = yeast();\n    }\n\n    if (!this.supportsBinary && !query.sid) {\n      query.b64 = 1;\n    }\n\n    query = parseqs.encode(query);\n\n    // avoid port if default for schema\n    if (\n      this.opts.port &&\n      ((\"https\" === schema && Number(this.opts.port) !== 443) ||\n        (\"http\" === schema && Number(this.opts.port) !== 80))\n    ) {\n      port = \":\" + this.opts.port;\n    }\n\n    // prepend ? to query\n    if (query.length) {\n      query = \"?\" + query;\n    }\n\n    const ipv6 = this.opts.hostname.indexOf(\":\") !== -1;\n    return (\n      schema +\n      \"://\" +\n      (ipv6 ? \"[\" + this.opts.hostname + \"]\" : this.opts.hostname) +\n      port +\n      this.opts.path +\n      query\n    );\n  }\n}\n\nmodule.exports = Polling;\n", "const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\n\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach(key => {\n  PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\n\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\n\nmodule.exports = {\n  PACKET_TYPES,\n  PACKET_TYPES_REVERSE,\n  ERROR_PACKET\n};\n", "'use strict';\n\nvar alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split('')\n  , length = 64\n  , map = {}\n  , seed = 0\n  , i = 0\n  , prev;\n\n/**\n * Return a string representing the specified number.\n *\n * @param {Number} num The number to convert.\n * @returns {String} The string representation of the number.\n * @api public\n */\nfunction encode(num) {\n  var encoded = '';\n\n  do {\n    encoded = alphabet[num % length] + encoded;\n    num = Math.floor(num / length);\n  } while (num > 0);\n\n  return encoded;\n}\n\n/**\n * Return the integer value specified by the given string.\n *\n * @param {String} str The string to convert.\n * @returns {Number} The integer value represented by the string.\n * @api public\n */\nfunction decode(str) {\n  var decoded = 0;\n\n  for (i = 0; i < str.length; i++) {\n    decoded = decoded * length + map[str.charAt(i)];\n  }\n\n  return decoded;\n}\n\n/**\n * Yeast: A tiny growing id generator.\n *\n * @returns {String} A unique id.\n * @api public\n */\nfunction yeast() {\n  var now = encode(+new Date());\n\n  if (now !== prev) return seed = 0, prev = now;\n  return now +'.'+ encode(seed++);\n}\n\n//\n// Map each character to its index.\n//\nfor (; i < length; i++) map[alphabet[i]] = i;\n\n//\n// Expose the `yeast`, `encode` and `decode` functions.\n//\nyeast.encode = encode;\nyeast.decode = decode;\nmodule.exports = yeast;\n", "module.exports.pick = (obj, ...attr) => {\n  return attr.reduce((acc, k) => {\n    if (obj.hasOwnProperty(k)) {\n      acc[k] = obj[k];\n    }\n    return acc;\n  }, {});\n};\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Socket = void 0;\nconst socket_io_parser_1 = require(\"socket.io-parser\");\nconst Emitter = require(\"component-emitter\");\nconst on_1 = require(\"./on\");\n\n\n/**\n * Internal events.\n * These events can't be emitted by the user.\n */\nconst RESERVED_EVENTS = Object.freeze({\n    connect: 1,\n    connect_error: 1,\n    disconnect: 1,\n    disconnecting: 1,\n    // EventEmitter reserved events: https://nodejs.org/api/events.html#events_event_newlistener\n    newListener: 1,\n    removeListener: 1,\n});\nclass Socket extends Emitter {\n    /**\n     * `Socket` constructor.\n     *\n     * @public\n     */\n    constructor(io, nsp, opts) {\n        super();\n        this.receiveBuffer = [];\n        this.sendBuffer = [];\n        this.ids = 0;\n        this.acks = {};\n        this.flags = {};\n        this.io = io;\n        this.nsp = nsp;\n        this.ids = 0;\n        this.acks = {};\n        this.receiveBuffer = [];\n        this.sendBuffer = [];\n        this.connected = false;\n        this.disconnected = true;\n        this.flags = {};\n        if (opts && opts.auth) {\n            this.auth = opts.auth;\n        }\n        if (this.io._autoConnect)\n            this.open();\n    }\n    /**\n     * Subscribe to open, close and packet events\n     *\n     * @private\n     */\n    subEvents() {\n        if (this.subs)\n            return;\n        const io = this.io;\n        this.subs = [\n            on_1.on(io, \"open\", this.onopen.bind(this)),\n            on_1.on(io, \"packet\", this.onpacket.bind(this)),\n            on_1.on(io, \"error\", this.onerror.bind(this)),\n            on_1.on(io, \"close\", this.onclose.bind(this)),\n        ];\n    }\n    /**\n     * Whether the Socket will try to reconnect when its Manager connects or reconnects\n     */\n    get active() {\n        return !!this.subs;\n    }\n    /**\n     * \"Opens\" the socket.\n     *\n     * @public\n     */\n    connect() {\n        if (this.connected)\n            return this;\n        this.subEvents();\n        if (!this.io[\"_reconnecting\"])\n            this.io.open(); // ensure open\n        if (\"open\" === this.io._readyState)\n            this.onopen();\n        return this;\n    }\n    /**\n     * Alias for connect()\n     */\n    open() {\n        return this.connect();\n    }\n    /**\n     * Sends a `message` event.\n     *\n     * @return self\n     * @public\n     */\n    send(...args) {\n        args.unshift(\"message\");\n        this.emit.apply(this, args);\n        return this;\n    }\n    /**\n     * Override `emit`.\n     * If the event is in `events`, it's emitted normally.\n     *\n     * @param ev - event name\n     * @return self\n     * @public\n     */\n    emit(ev, ...args) {\n        if (RESERVED_EVENTS.hasOwnProperty(ev)) {\n            throw new Error('\"' + ev + '\" is a reserved event name');\n        }\n        args.unshift(ev);\n        const packet = {\n            type: socket_io_parser_1.PacketType.EVENT,\n            data: args,\n        };\n        packet.options = {};\n        packet.options.compress = this.flags.compress !== false;\n        // event ack callback\n        if (\"function\" === typeof args[args.length - 1]) {\n\n\n            this.acks[this.ids] = args.pop();\n            packet.id = this.ids++;\n        }\n        const isTransportWritable = this.io.engine &&\n            this.io.engine.transport &&\n            this.io.engine.transport.writable;\n        const discardPacket = this.flags.volatile && (!isTransportWritable || !this.connected);\n        if (discardPacket) {\n\n\n        }\n        else if (this.connected) {\n            this.packet(packet);\n        }\n        else {\n            this.sendBuffer.push(packet);\n        }\n        this.flags = {};\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param packet\n     * @private\n     */\n    packet(packet) {\n        packet.nsp = this.nsp;\n        this.io._packet(packet);\n    }\n    /**\n     * Called upon engine `open`.\n     *\n     * @private\n     */\n    onopen() {\n\n\n        if (typeof this.auth == \"function\") {\n            this.auth((data) => {\n                this.packet({ type: socket_io_parser_1.PacketType.CONNECT, data });\n            });\n        }\n        else {\n            this.packet({ type: socket_io_parser_1.PacketType.CONNECT, data: this.auth });\n        }\n    }\n    /**\n     * Called upon engine or manager `error`.\n     *\n     * @param err\n     * @private\n     */\n    onerror(err) {\n        if (!this.connected) {\n            super.emit(\"connect_error\", err);\n        }\n    }\n    /**\n     * Called upon engine `close`.\n     *\n     * @param reason\n     * @private\n     */\n    onclose(reason) {\n\n\n        this.connected = false;\n        this.disconnected = true;\n        delete this.id;\n        super.emit(\"disconnect\", reason);\n    }\n    /**\n     * Called with socket packet.\n     *\n     * @param packet\n     * @private\n     */\n    onpacket(packet) {\n        const sameNamespace = packet.nsp === this.nsp;\n        if (!sameNamespace)\n            return;\n        switch (packet.type) {\n            case socket_io_parser_1.PacketType.CONNECT:\n                if (packet.data && packet.data.sid) {\n                    const id = packet.data.sid;\n                    this.onconnect(id);\n                }\n                else {\n                    super.emit(\"connect_error\", new Error(\"It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)\"));\n                }\n                break;\n            case socket_io_parser_1.PacketType.EVENT:\n                this.onevent(packet);\n                break;\n            case socket_io_parser_1.PacketType.BINARY_EVENT:\n                this.onevent(packet);\n                break;\n            case socket_io_parser_1.PacketType.ACK:\n                this.onack(packet);\n                break;\n            case socket_io_parser_1.PacketType.BINARY_ACK:\n                this.onack(packet);\n                break;\n            case socket_io_parser_1.PacketType.DISCONNECT:\n                this.ondisconnect();\n                break;\n            case socket_io_parser_1.PacketType.CONNECT_ERROR:\n                const err = new Error(packet.data.message);\n                // @ts-ignore\n                err.data = packet.data.data;\n                super.emit(\"connect_error\", err);\n                break;\n        }\n    }\n    /**\n     * Called upon a server event.\n     *\n     * @param packet\n     * @private\n     */\n    onevent(packet) {\n        const args = packet.data || [];\n\n\n        if (null != packet.id) {\n\n\n            args.push(this.ack(packet.id));\n        }\n        if (this.connected) {\n            this.emitEvent(args);\n        }\n        else {\n            this.receiveBuffer.push(Object.freeze(args));\n        }\n    }\n    emitEvent(args) {\n        if (this._anyListeners && this._anyListeners.length) {\n            const listeners = this._anyListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, args);\n            }\n        }\n        super.emit.apply(this, args);\n    }\n    /**\n     * Produces an ack callback to emit with an event.\n     *\n     * @private\n     */\n    ack(id) {\n        const self = this;\n        let sent = false;\n        return function (...args) {\n            // prevent double callbacks\n            if (sent)\n                return;\n            sent = true;\n\n\n            self.packet({\n                type: socket_io_parser_1.PacketType.ACK,\n                id: id,\n                data: args,\n            });\n        };\n    }\n    /**\n     * Called upon a server acknowlegement.\n     *\n     * @param packet\n     * @private\n     */\n    onack(packet) {\n        const ack = this.acks[packet.id];\n        if (\"function\" === typeof ack) {\n\n\n            ack.apply(this, packet.data);\n            delete this.acks[packet.id];\n        }\n        else {\n\n\n        }\n    }\n    /**\n     * Called upon server connect.\n     *\n     * @private\n     */\n    onconnect(id) {\n\n\n        this.id = id;\n        this.connected = true;\n        this.disconnected = false;\n        super.emit(\"connect\");\n        this.emitBuffered();\n    }\n    /**\n     * Emit buffered events (received and emitted).\n     *\n     * @private\n     */\n    emitBuffered() {\n        this.receiveBuffer.forEach((args) => this.emitEvent(args));\n        this.receiveBuffer = [];\n        this.sendBuffer.forEach((packet) => this.packet(packet));\n        this.sendBuffer = [];\n    }\n    /**\n     * Called upon server disconnect.\n     *\n     * @private\n     */\n    ondisconnect() {\n\n\n        this.destroy();\n        this.onclose(\"io server disconnect\");\n    }\n    /**\n     * Called upon forced client/server side disconnections,\n     * this method ensures the manager stops tracking us and\n     * that reconnections don't get triggered for this.\n     *\n     * @private\n     */\n    destroy() {\n        if (this.subs) {\n            // clean subscriptions to avoid reconnections\n            this.subs.forEach((subDestroy) => subDestroy());\n            this.subs = undefined;\n        }\n        this.io[\"_destroy\"](this);\n    }\n    /**\n     * Disconnects the socket manually.\n     *\n     * @return self\n     * @public\n     */\n    disconnect() {\n        if (this.connected) {\n\n\n            this.packet({ type: socket_io_parser_1.PacketType.DISCONNECT });\n        }\n        // remove socket from pool\n        this.destroy();\n        if (this.connected) {\n            // fire events\n            this.onclose(\"io client disconnect\");\n        }\n        return this;\n    }\n    /**\n     * Alias for disconnect()\n     *\n     * @return self\n     * @public\n     */\n    close() {\n        return this.disconnect();\n    }\n    /**\n     * Sets the compress flag.\n     *\n     * @param compress - if `true`, compresses the sending data\n     * @return self\n     * @public\n     */\n    compress(compress) {\n        this.flags.compress = compress;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the event message will be dropped when this socket is not\n     * ready to send messages.\n     *\n     * @returns self\n     * @public\n     */\n    get volatile() {\n        this.flags.volatile = true;\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * @param listener\n     * @public\n     */\n    onAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * @param listener\n     * @public\n     */\n    prependAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @param listener\n     * @public\n     */\n    offAny(listener) {\n        if (!this._anyListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     *\n     * @public\n     */\n    listenersAny() {\n        return this._anyListeners || [];\n    }\n}\nexports.Socket = Socket;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.hasBinary = exports.isBinary = void 0;\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj.buffer instanceof ArrayBuffer;\n};\nconst toString = Object.prototype.toString;\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeFile = typeof File === \"function\" ||\n    (typeof File !== \"undefined\" &&\n        toString.call(File) === \"[object FileConstructor]\");\n/**\n * Returns true if obj is a Buffer, an ArrayBuffer, a Blob or a File.\n *\n * @private\n */\nfunction isBinary(obj) {\n    return ((withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj))) ||\n        (withNativeBlob && obj instanceof Blob) ||\n        (withNativeFile && obj instanceof File));\n}\nexports.isBinary = isBinary;\nfunction hasBinary(obj, toJSON) {\n    if (!obj || typeof obj !== \"object\") {\n        return false;\n    }\n    if (Array.isArray(obj)) {\n        for (let i = 0, l = obj.length; i < l; i++) {\n            if (hasBinary(obj[i])) {\n                return true;\n            }\n        }\n        return false;\n    }\n    if (isBinary(obj)) {\n        return true;\n    }\n    if (obj.toJSON &&\n        typeof obj.toJSON === \"function\" &&\n        arguments.length === 1) {\n        return hasBinary(obj.toJSON(), true);\n    }\n    for (const key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n            return true;\n        }\n    }\n    return false;\n}\nexports.hasBinary = hasBinary;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.on = void 0;\nfunction on(obj, ev, fn) {\n    obj.on(ev, fn);\n    return function subDestroy() {\n        obj.off(ev, fn);\n    };\n}\nexports.on = on;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Socket = exports.io = exports.Manager = exports.protocol = void 0;\nconst url_1 = require(\"./url\");\nconst manager_1 = require(\"./manager\");\nconst socket_1 = require(\"./socket\");\nObject.defineProperty(exports, \"Socket\", { enumerable: true, get: function () { return socket_1.Socket; } });\n\n\n/**\n * Module exports.\n */\nmodule.exports = exports = lookup;\n/**\n * Managers cache.\n */\nconst cache = (exports.managers = {});\nfunction lookup(uri, opts) {\n    if (typeof uri === \"object\") {\n        opts = uri;\n        uri = undefined;\n    }\n    opts = opts || {};\n    const parsed = url_1.url(uri, opts.path);\n    const source = parsed.source;\n    const id = parsed.id;\n    const path = parsed.path;\n    const sameNamespace = cache[id] && path in cache[id][\"nsps\"];\n    const newConnection = opts.forceNew ||\n        opts[\"force new connection\"] ||\n        false === opts.multiplex ||\n        sameNamespace;\n    let io;\n    if (newConnection) {\n\n\n        io = new manager_1.Manager(source, opts);\n    }\n    else {\n        if (!cache[id]) {\n\n\n            cache[id] = new manager_1.Manager(source, opts);\n        }\n        io = cache[id];\n    }\n    if (parsed.query && !opts.query) {\n        opts.query = parsed.queryKey;\n    }\n    return io.socket(parsed.path, opts);\n}\nexports.io = lookup;\n/**\n * Protocol version.\n *\n * @public\n */\nvar socket_io_parser_1 = require(\"socket.io-parser\");\nObject.defineProperty(exports, \"protocol\", { enumerable: true, get: function () { return socket_io_parser_1.protocol; } });\n/**\n * `connect`.\n *\n * @param {String} uri\n * @public\n */\nexports.connect = lookup;\n/**\n * Expose constructors for standalone build.\n *\n * @public\n */\nvar manager_2 = require(\"./manager\");\nObject.defineProperty(exports, \"Manager\", { enumerable: true, get: function () { return manager_2.Manager; } });\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.url = void 0;\nconst parseuri = require(\"parseuri\");\n\n\n/**\n * URL parser.\n *\n * @param uri - url\n * @param path - the request path of the connection\n * @param loc - An object meant to mimic window.location.\n *        Defaults to window.location.\n * @public\n */\nfunction url(uri, path = \"\", loc) {\n    let obj = uri;\n    // default to window.location\n    loc = loc || (typeof location !== \"undefined\" && location);\n    if (null == uri)\n        uri = loc.protocol + \"//\" + loc.host;\n    // relative path support\n    if (typeof uri === \"string\") {\n        if (\"/\" === uri.charAt(0)) {\n            if (\"/\" === uri.charAt(1)) {\n                uri = loc.protocol + uri;\n            }\n            else {\n                uri = loc.host + uri;\n            }\n        }\n        if (!/^(https?|wss?):\\/\\//.test(uri)) {\n\n\n            if (\"undefined\" !== typeof loc) {\n                uri = loc.protocol + \"//\" + uri;\n            }\n            else {\n                uri = \"https://\" + uri;\n            }\n        }\n        // parse\n\n\n        obj = parseuri(uri);\n    }\n    // make sure we treat `localhost:80` and `localhost` equally\n    if (!obj.port) {\n        if (/^(http|ws)$/.test(obj.protocol)) {\n            obj.port = \"80\";\n        }\n        else if (/^(http|ws)s$/.test(obj.protocol)) {\n            obj.port = \"443\";\n        }\n    }\n    obj.path = obj.path || \"/\";\n    const ipv6 = obj.host.indexOf(\":\") !== -1;\n    const host = ipv6 ? \"[\" + obj.host + \"]\" : obj.host;\n    // define unique id\n    obj.id = obj.protocol + \"://\" + host + \":\" + obj.port + path;\n    // define href\n    obj.href =\n        obj.protocol +\n            \"://\" +\n            host +\n            (loc && loc.port === obj.port ? \"\" : \":\" + obj.port);\n    return obj;\n}\nexports.url = url;\n", "const Socket = require(\"./socket\");\n\nmodule.exports = (uri, opts) => new Socket(uri, opts);\n\n/**\n * Expose deps for legacy compatibility\n * and standalone browser access.\n */\n\nmodule.exports.Socket = Socket;\nmodule.exports.protocol = Socket.protocol; // this is an int\nmodule.exports.Transport = require(\"./transport\");\nmodule.exports.transports = require(\"./transports/index\");\nmodule.exports.parser = require(\"engine.io-parser\");\n", "const transports = require(\"./transports/index\");\nconst Emitter = require(\"component-emitter\");\n\n\nconst parser = require(\"engine.io-parser\");\nconst parseuri = require(\"parseuri\");\nconst parseqs = require(\"parseqs\");\n\nclass Socket extends Emitter {\n  /**\n   * Socket constructor.\n   *\n   * @param {String|Object} uri or options\n   * @param {Object} options\n   * @api public\n   */\n  constructor(uri, opts = {}) {\n    super();\n\n    if (uri && \"object\" === typeof uri) {\n      opts = uri;\n      uri = null;\n    }\n\n    if (uri) {\n      uri = parseuri(uri);\n      opts.hostname = uri.host;\n      opts.secure = uri.protocol === \"https\" || uri.protocol === \"wss\";\n      opts.port = uri.port;\n      if (uri.query) opts.query = uri.query;\n    } else if (opts.host) {\n      opts.hostname = parseuri(opts.host).host;\n    }\n\n    this.secure =\n      null != opts.secure\n        ? opts.secure\n        : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n\n    if (opts.hostname && !opts.port) {\n      // if no port is specified manually, use the protocol default\n      opts.port = this.secure ? \"443\" : \"80\";\n    }\n\n    this.hostname =\n      opts.hostname ||\n      (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n    this.port =\n      opts.port ||\n      (typeof location !== \"undefined\" && location.port\n        ? location.port\n        : this.secure\n        ? 443\n        : 80);\n\n    this.transports = opts.transports || [\"polling\", \"websocket\"];\n    this.readyState = \"\";\n    this.writeBuffer = [];\n    this.prevBufferLen = 0;\n\n    this.opts = Object.assign(\n      {\n        path: \"/engine.io\",\n        agent: false,\n        withCredentials: false,\n        upgrade: true,\n        jsonp: true,\n        timestampParam: \"t\",\n        rememberUpgrade: false,\n        rejectUnauthorized: true,\n        perMessageDeflate: {\n          threshold: 1024\n        },\n        transportOptions: {}\n      },\n      opts\n    );\n\n    this.opts.path = this.opts.path.replace(/\\/$/, \"\") + \"/\";\n\n    if (typeof this.opts.query === \"string\") {\n      this.opts.query = parseqs.decode(this.opts.query);\n    }\n\n    // set on handshake\n    this.id = null;\n    this.upgrades = null;\n    this.pingInterval = null;\n    this.pingTimeout = null;\n\n    // set on heartbeat\n    this.pingTimeoutTimer = null;\n\n    if (typeof addEventListener === \"function\") {\n      addEventListener(\n        \"beforeunload\",\n        () => {\n          if (this.transport) {\n            // silently close the transport\n            this.transport.removeAllListeners();\n            this.transport.close();\n          }\n        },\n        false\n      );\n    }\n\n    this.open();\n  }\n\n  /**\n   * Creates transport of the given type.\n   *\n   * @param {String} transport name\n   * @return {Transport}\n   * @api private\n   */\n  createTransport(name) {\n\n\n    const query = clone(this.opts.query);\n\n    // append engine.io protocol identifier\n    query.EIO = parser.protocol;\n\n    // transport name\n    query.transport = name;\n\n    // session id if we already have one\n    if (this.id) query.sid = this.id;\n\n    const opts = Object.assign(\n      {},\n      this.opts.transportOptions[name],\n      this.opts,\n      {\n        query,\n        socket: this,\n        hostname: this.hostname,\n        secure: this.secure,\n        port: this.port\n      }\n    );\n\n\n\n\n    return new transports[name](opts);\n  }\n\n  /**\n   * Initializes transport to use and starts probe.\n   *\n   * @api private\n   */\n  open() {\n    let transport;\n    if (\n      this.opts.rememberUpgrade &&\n      Socket.priorWebsocketSuccess &&\n      this.transports.indexOf(\"websocket\") !== -1\n    ) {\n      transport = \"websocket\";\n    } else if (0 === this.transports.length) {\n      // Emit error on next tick so it can be listened to\n      const self = this;\n      setTimeout(function() {\n        self.emit(\"error\", \"No transports available\");\n      }, 0);\n      return;\n    } else {\n      transport = this.transports[0];\n    }\n    this.readyState = \"opening\";\n\n    // Retry with the next transport if the transport is disabled (jsonp: false)\n    try {\n      transport = this.createTransport(transport);\n    } catch (e) {\n\n\n      this.transports.shift();\n      this.open();\n      return;\n    }\n\n    transport.open();\n    this.setTransport(transport);\n  }\n\n  /**\n   * Sets the current transport. Disables the existing one (if any).\n   *\n   * @api private\n   */\n  setTransport(transport) {\n\n\n    const self = this;\n\n    if (this.transport) {\n\n\n      this.transport.removeAllListeners();\n    }\n\n    // set up transport\n    this.transport = transport;\n\n    // set up transport listeners\n    transport\n      .on(\"drain\", function() {\n        self.onDrain();\n      })\n      .on(\"packet\", function(packet) {\n        self.onPacket(packet);\n      })\n      .on(\"error\", function(e) {\n        self.onError(e);\n      })\n      .on(\"close\", function() {\n        self.onClose(\"transport close\");\n      });\n  }\n\n  /**\n   * Probes a transport.\n   *\n   * @param {String} transport name\n   * @api private\n   */\n  probe(name) {\n\n\n    let transport = this.createTransport(name, { probe: 1 });\n    let failed = false;\n    const self = this;\n\n    Socket.priorWebsocketSuccess = false;\n\n    function onTransportOpen() {\n      if (self.onlyBinaryUpgrades) {\n        const upgradeLosesBinary =\n          !this.supportsBinary && self.transport.supportsBinary;\n        failed = failed || upgradeLosesBinary;\n      }\n      if (failed) return;\n\n\n\n      transport.send([{ type: \"ping\", data: \"probe\" }]);\n      transport.once(\"packet\", function(msg) {\n        if (failed) return;\n        if (\"pong\" === msg.type && \"probe\" === msg.data) {\n\n\n          self.upgrading = true;\n          self.emit(\"upgrading\", transport);\n          if (!transport) return;\n          Socket.priorWebsocketSuccess = \"websocket\" === transport.name;\n\n\n\n          self.transport.pause(function() {\n            if (failed) return;\n            if (\"closed\" === self.readyState) return;\n\n\n\n            cleanup();\n\n            self.setTransport(transport);\n            transport.send([{ type: \"upgrade\" }]);\n            self.emit(\"upgrade\", transport);\n            transport = null;\n            self.upgrading = false;\n            self.flush();\n          });\n        } else {\n\n\n          const err = new Error(\"probe error\");\n          err.transport = transport.name;\n          self.emit(\"upgradeError\", err);\n        }\n      });\n    }\n\n    function freezeTransport() {\n      if (failed) return;\n\n      // Any callback called by transport should be ignored since now\n      failed = true;\n\n      cleanup();\n\n      transport.close();\n      transport = null;\n    }\n\n    // Handle any error that happens while probing\n    function onerror(err) {\n      const error = new Error(\"probe error: \" + err);\n      error.transport = transport.name;\n\n      freezeTransport();\n\n\n\n\n      self.emit(\"upgradeError\", error);\n    }\n\n    function onTransportClose() {\n      onerror(\"transport closed\");\n    }\n\n    // When the socket is closed while we're probing\n    function onclose() {\n      onerror(\"socket closed\");\n    }\n\n    // When the socket is upgraded while we're probing\n    function onupgrade(to) {\n      if (transport && to.name !== transport.name) {\n\n\n        freezeTransport();\n      }\n    }\n\n    // Remove all listeners on the transport and on self\n    function cleanup() {\n      transport.removeListener(\"open\", onTransportOpen);\n      transport.removeListener(\"error\", onerror);\n      transport.removeListener(\"close\", onTransportClose);\n      self.removeListener(\"close\", onclose);\n      self.removeListener(\"upgrading\", onupgrade);\n    }\n\n    transport.once(\"open\", onTransportOpen);\n    transport.once(\"error\", onerror);\n    transport.once(\"close\", onTransportClose);\n\n    this.once(\"close\", onclose);\n    this.once(\"upgrading\", onupgrade);\n\n    transport.open();\n  }\n\n  /**\n   * Called when connection is deemed open.\n   *\n   * @api public\n   */\n  onOpen() {\n\n\n    this.readyState = \"open\";\n    Socket.priorWebsocketSuccess = \"websocket\" === this.transport.name;\n    this.emit(\"open\");\n    this.flush();\n\n    // we check for `readyState` in case an `open`\n    // listener already closed the socket\n    if (\n      \"open\" === this.readyState &&\n      this.opts.upgrade &&\n      this.transport.pause\n    ) {\n\n\n      let i = 0;\n      const l = this.upgrades.length;\n      for (; i < l; i++) {\n        this.probe(this.upgrades[i]);\n      }\n    }\n  }\n\n  /**\n   * Handles a packet.\n   *\n   * @api private\n   */\n  onPacket(packet) {\n    if (\n      \"opening\" === this.readyState ||\n      \"open\" === this.readyState ||\n      \"closing\" === this.readyState\n    ) {\n\n\n\n      this.emit(\"packet\", packet);\n\n      // Socket is live - any packet counts\n      this.emit(\"heartbeat\");\n\n      switch (packet.type) {\n        case \"open\":\n          this.onHandshake(JSON.parse(packet.data));\n          break;\n\n        case \"ping\":\n          this.resetPingTimeout();\n          this.sendPacket(\"pong\");\n          this.emit(\"pong\");\n          break;\n\n        case \"error\":\n          const err = new Error(\"server error\");\n          err.code = packet.data;\n          this.onError(err);\n          break;\n\n        case \"message\":\n          this.emit(\"data\", packet.data);\n          this.emit(\"message\", packet.data);\n          break;\n      }\n    } else {\n\n\n    }\n  }\n\n  /**\n   * Called upon handshake completion.\n   *\n   * @param {Object} handshake obj\n   * @api private\n   */\n  onHandshake(data) {\n    this.emit(\"handshake\", data);\n    this.id = data.sid;\n    this.transport.query.sid = data.sid;\n    this.upgrades = this.filterUpgrades(data.upgrades);\n    this.pingInterval = data.pingInterval;\n    this.pingTimeout = data.pingTimeout;\n    this.onOpen();\n    // In case open handler closes socket\n    if (\"closed\" === this.readyState) return;\n    this.resetPingTimeout();\n  }\n\n  /**\n   * Sets and resets ping timeout timer based on server pings.\n   *\n   * @api private\n   */\n  resetPingTimeout() {\n    clearTimeout(this.pingTimeoutTimer);\n    this.pingTimeoutTimer = setTimeout(() => {\n      this.onClose(\"ping timeout\");\n    }, this.pingInterval + this.pingTimeout);\n  }\n\n  /**\n   * Called on `drain` event\n   *\n   * @api private\n   */\n  onDrain() {\n    this.writeBuffer.splice(0, this.prevBufferLen);\n\n    // setting prevBufferLen = 0 is very important\n    // for example, when upgrading, upgrade packet is sent over,\n    // and a nonzero prevBufferLen could cause problems on `drain`\n    this.prevBufferLen = 0;\n\n    if (0 === this.writeBuffer.length) {\n      this.emit(\"drain\");\n    } else {\n      this.flush();\n    }\n  }\n\n  /**\n   * Flush write buffers.\n   *\n   * @api private\n   */\n  flush() {\n    if (\n      \"closed\" !== this.readyState &&\n      this.transport.writable &&\n      !this.upgrading &&\n      this.writeBuffer.length\n    ) {\n\n\n      this.transport.send(this.writeBuffer);\n      // keep track of current length of writeBuffer\n      // splice writeBuffer and callbackBuffer on `drain`\n      this.prevBufferLen = this.writeBuffer.length;\n      this.emit(\"flush\");\n    }\n  }\n\n  /**\n   * Sends a message.\n   *\n   * @param {String} message.\n   * @param {Function} callback function.\n   * @param {Object} options.\n   * @return {Socket} for chaining.\n   * @api public\n   */\n  write(msg, options, fn) {\n    this.sendPacket(\"message\", msg, options, fn);\n    return this;\n  }\n\n  send(msg, options, fn) {\n    this.sendPacket(\"message\", msg, options, fn);\n    return this;\n  }\n\n  /**\n   * Sends a packet.\n   *\n   * @param {String} packet type.\n   * @param {String} data.\n   * @param {Object} options.\n   * @param {Function} callback function.\n   * @api private\n   */\n  sendPacket(type, data, options, fn) {\n    if (\"function\" === typeof data) {\n      fn = data;\n      data = undefined;\n    }\n\n    if (\"function\" === typeof options) {\n      fn = options;\n      options = null;\n    }\n\n    if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n      return;\n    }\n\n    options = options || {};\n    options.compress = false !== options.compress;\n\n    const packet = {\n      type: type,\n      data: data,\n      options: options\n    };\n    this.emit(\"packetCreate\", packet);\n    this.writeBuffer.push(packet);\n    if (fn) this.once(\"flush\", fn);\n    this.flush();\n  }\n\n  /**\n   * Closes the connection.\n   *\n   * @api private\n   */\n  close() {\n    const self = this;\n\n    if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n      this.readyState = \"closing\";\n\n      if (this.writeBuffer.length) {\n        this.once(\"drain\", function() {\n          if (this.upgrading) {\n            waitForUpgrade();\n          } else {\n            close();\n          }\n        });\n      } else if (this.upgrading) {\n        waitForUpgrade();\n      } else {\n        close();\n      }\n    }\n\n    function close() {\n      self.onClose(\"forced close\");\n\n\n      self.transport.close();\n    }\n\n    function cleanupAndClose() {\n      self.removeListener(\"upgrade\", cleanupAndClose);\n      self.removeListener(\"upgradeError\", cleanupAndClose);\n      close();\n    }\n\n    function waitForUpgrade() {\n      // wait for upgrade to finish since we can't send packets while pausing a transport\n      self.once(\"upgrade\", cleanupAndClose);\n      self.once(\"upgradeError\", cleanupAndClose);\n    }\n\n    return this;\n  }\n\n  /**\n   * Called upon transport error\n   *\n   * @api private\n   */\n  onError(err) {\n\n\n    Socket.priorWebsocketSuccess = false;\n    this.emit(\"error\", err);\n    this.onClose(\"transport error\", err);\n  }\n\n  /**\n   * Called upon transport close.\n   *\n   * @api private\n   */\n  onClose(reason, desc) {\n    if (\n      \"opening\" === this.readyState ||\n      \"open\" === this.readyState ||\n      \"closing\" === this.readyState\n    ) {\n\n\n      const self = this;\n\n      // clear timers\n      clearTimeout(this.pingIntervalTimer);\n      clearTimeout(this.pingTimeoutTimer);\n\n      // stop event from firing again for transport\n      this.transport.removeAllListeners(\"close\");\n\n      // ensure transport won't stay open\n      this.transport.close();\n\n      // ignore further transport communication\n      this.transport.removeAllListeners();\n\n      // set ready state\n      this.readyState = \"closed\";\n\n      // clear session id\n      this.id = null;\n\n      // emit close event\n      this.emit(\"close\", reason, desc);\n\n      // clean buffers after, so users can still\n      // grab the buffers on `close` event\n      self.writeBuffer = [];\n      self.prevBufferLen = 0;\n    }\n  }\n\n  /**\n   * Filters upgrades, returning only those matching client transports.\n   *\n   * @param {Array} server upgrades\n   * @api private\n   *\n   */\n  filterUpgrades(upgrades) {\n    const filteredUpgrades = [];\n    let i = 0;\n    const j = upgrades.length;\n    for (; i < j; i++) {\n      if (~this.transports.indexOf(upgrades[i]))\n        filteredUpgrades.push(upgrades[i]);\n    }\n    return filteredUpgrades;\n  }\n}\n\nSocket.priorWebsocketSuccess = false;\n\n/**\n * Protocol version.\n *\n * @api public\n */\n\nSocket.protocol = parser.protocol; // this is an int\n\nfunction clone(obj) {\n  const o = {};\n  for (let i in obj) {\n    if (obj.hasOwnProperty(i)) {\n      o[i] = obj[i];\n    }\n  }\n  return o;\n}\n\nmodule.exports = Socket;\n", "\n/**\n * Module exports.\n *\n * Logic borrowed from Modernizr:\n *\n *   - https://github.com/Modernizr/Modernizr/blob/master/feature-detects/cors.js\n */\n\ntry {\n  module.exports = typeof XMLHttpRequest !== 'undefined' &&\n    'withCredentials' in new XMLHttpRequest();\n} catch (err) {\n  // if XMLHttp support is disabled in IE then it will throw\n  // when trying to create\n  module.exports = false;\n}\n", "/* global attachEvent */\n\nconst XMLHttpRequest = require(\"xmlhttprequest-ssl\");\nconst Polling = require(\"./polling\");\nconst Emitter = require(\"component-emitter\");\nconst { pick } = require(\"../util\");\nconst globalThis = require(\"../globalThis\");\n\n\n\n\n/**\n * Empty function\n */\n\nfunction empty() {}\n\nconst hasXHR2 = (function() {\n  const xhr = new XMLHttpRequest({ xdomain: false });\n  return null != xhr.responseType;\n})();\n\nclass XHR extends Polling {\n  /**\n   * XHR Polling constructor.\n   *\n   * @param {Object} opts\n   * @api public\n   */\n  constructor(opts) {\n    super(opts);\n\n    if (typeof location !== \"undefined\") {\n      const isSSL = \"https:\" === location.protocol;\n      let port = location.port;\n\n      // some user agents have empty `location.port`\n      if (!port) {\n        port = isSSL ? 443 : 80;\n      }\n\n      this.xd =\n        (typeof location !== \"undefined\" &&\n          opts.hostname !== location.hostname) ||\n        port !== opts.port;\n      this.xs = opts.secure !== isSSL;\n    }\n    /**\n     * XHR supports binary\n     */\n    const forceBase64 = opts && opts.forceBase64;\n    this.supportsBinary = hasXHR2 && !forceBase64;\n  }\n\n  /**\n   * Creates a request.\n   *\n   * @param {String} method\n   * @api private\n   */\n  request(opts = {}) {\n    Object.assign(opts, { xd: this.xd, xs: this.xs }, this.opts);\n    return new Request(this.uri(), opts);\n  }\n\n  /**\n   * Sends data.\n   *\n   * @param {String} data to send.\n   * @param {Function} called upon flush.\n   * @api private\n   */\n  doWrite(data, fn) {\n    const req = this.request({\n      method: \"POST\",\n      data: data\n    });\n    const self = this;\n    req.on(\"success\", fn);\n    req.on(\"error\", function(err) {\n      self.onError(\"xhr post error\", err);\n    });\n  }\n\n  /**\n   * Starts a poll cycle.\n   *\n   * @api private\n   */\n  doPoll() {\n\n\n    const req = this.request();\n    const self = this;\n    req.on(\"data\", function(data) {\n      self.onData(data);\n    });\n    req.on(\"error\", function(err) {\n      self.onError(\"xhr poll error\", err);\n    });\n    this.pollXhr = req;\n  }\n}\n\nclass Request extends Emitter {\n  /**\n   * Request constructor\n   *\n   * @param {Object} options\n   * @api public\n   */\n  constructor(uri, opts) {\n    super();\n    this.opts = opts;\n\n    this.method = opts.method || \"GET\";\n    this.uri = uri;\n    this.async = false !== opts.async;\n    this.data = undefined !== opts.data ? opts.data : null;\n\n    this.create();\n  }\n\n  /**\n   * Creates the XHR object and sends the request.\n   *\n   * @api private\n   */\n  create() {\n    const opts = pick(\n      this.opts,\n      \"agent\",\n      \"enablesXDR\",\n      \"pfx\",\n      \"key\",\n      \"passphrase\",\n      \"cert\",\n      \"ca\",\n      \"ciphers\",\n      \"rejectUnauthorized\"\n    );\n    opts.xdomain = !!this.opts.xd;\n    opts.xscheme = !!this.opts.xs;\n\n    const xhr = (this.xhr = new XMLHttpRequest(opts));\n    const self = this;\n\n    try {\n\n\n      xhr.open(this.method, this.uri, this.async);\n      try {\n        if (this.opts.extraHeaders) {\n          xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n          for (let i in this.opts.extraHeaders) {\n            if (this.opts.extraHeaders.hasOwnProperty(i)) {\n              xhr.setRequestHeader(i, this.opts.extraHeaders[i]);\n            }\n          }\n        }\n      } catch (e) {}\n\n      if (\"POST\" === this.method) {\n        try {\n          xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n        } catch (e) {}\n      }\n\n      try {\n        xhr.setRequestHeader(\"Accept\", \"*/*\");\n      } catch (e) {}\n\n      // ie6 check\n      if (\"withCredentials\" in xhr) {\n        xhr.withCredentials = this.opts.withCredentials;\n      }\n\n      if (this.opts.requestTimeout) {\n        xhr.timeout = this.opts.requestTimeout;\n      }\n\n      if (this.hasXDR()) {\n        xhr.onload = function() {\n          self.onLoad();\n        };\n        xhr.onerror = function() {\n          self.onError(xhr.responseText);\n        };\n      } else {\n        xhr.onreadystatechange = function() {\n          if (4 !== xhr.readyState) return;\n          if (200 === xhr.status || 1223 === xhr.status) {\n            self.onLoad();\n          } else {\n            // make sure the `error` event handler that's user-set\n            // does not throw in the same tick and gets caught here\n            setTimeout(function() {\n              self.onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n            }, 0);\n          }\n        };\n      }\n\n\n\n      xhr.send(this.data);\n    } catch (e) {\n      // Need to defer since .create() is called directly from the constructor\n      // and thus the 'error' event can only be only bound *after* this exception\n      // occurs.  Therefore, also, we cannot throw here at all.\n      setTimeout(function() {\n        self.onError(e);\n      }, 0);\n      return;\n    }\n\n    if (typeof document !== \"undefined\") {\n      this.index = Request.requestsCount++;\n      Request.requests[this.index] = this;\n    }\n  }\n\n  /**\n   * Called upon successful response.\n   *\n   * @api private\n   */\n  onSuccess() {\n    this.emit(\"success\");\n    this.cleanup();\n  }\n\n  /**\n   * Called if we have data.\n   *\n   * @api private\n   */\n  onData(data) {\n    this.emit(\"data\", data);\n    this.onSuccess();\n  }\n\n  /**\n   * Called upon error.\n   *\n   * @api private\n   */\n  onError(err) {\n    this.emit(\"error\", err);\n    this.cleanup(true);\n  }\n\n  /**\n   * Cleans up house.\n   *\n   * @api private\n   */\n  cleanup(fromError) {\n    if (\"undefined\" === typeof this.xhr || null === this.xhr) {\n      return;\n    }\n    // xmlhttprequest\n    if (this.hasXDR()) {\n      this.xhr.onload = this.xhr.onerror = empty;\n    } else {\n      this.xhr.onreadystatechange = empty;\n    }\n\n    if (fromError) {\n      try {\n        this.xhr.abort();\n      } catch (e) {}\n    }\n\n    if (typeof document !== \"undefined\") {\n      delete Request.requests[this.index];\n    }\n\n    this.xhr = null;\n  }\n\n  /**\n   * Called upon load.\n   *\n   * @api private\n   */\n  onLoad() {\n    const data = this.xhr.responseText;\n    if (data !== null) {\n      this.onData(data);\n    }\n  }\n\n  /**\n   * Check if it has XDomainRequest.\n   *\n   * @api private\n   */\n  hasXDR() {\n    return typeof XDomainRequest !== \"undefined\" && !this.xs && this.enablesXDR;\n  }\n\n  /**\n   * Aborts the request.\n   *\n   * @api public\n   */\n  abort() {\n    this.cleanup();\n  }\n}\n\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\n\nRequest.requestsCount = 0;\nRequest.requests = {};\n\nif (typeof document !== \"undefined\") {\n  if (typeof attachEvent === \"function\") {\n    attachEvent(\"onunload\", unloadHandler);\n  } else if (typeof addEventListener === \"function\") {\n    const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n    addEventListener(terminationEvent, unloadHandler, false);\n  }\n}\n\nfunction unloadHandler() {\n  for (let i in Request.requests) {\n    if (Request.requests.hasOwnProperty(i)) {\n      Request.requests[i].abort();\n    }\n  }\n}\n\nmodule.exports = XHR;\nmodule.exports.Request = Request;\n", "const { PACKET_TYPES } = require(\"./commons\");\n\nconst withNativeBlob =\n  typeof Blob === \"function\" ||\n  (typeof Blob !== \"undefined\" &&\n    Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = obj => {\n  return typeof ArrayBuffer.isView === \"function\"\n    ? ArrayBuffer.isView(obj)\n    : obj && obj.buffer instanceof ArrayBuffer;\n};\n\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n  if (withNativeBlob && data instanceof Blob) {\n    if (supportsBinary) {\n      return callback(data);\n    } else {\n      return encodeBlobAsBase64(data, callback);\n    }\n  } else if (\n    withNativeArrayBuffer &&\n    (data instanceof ArrayBuffer || isView(data))\n  ) {\n    if (supportsBinary) {\n      return callback(data instanceof ArrayBuffer ? data : data.buffer);\n    } else {\n      return encodeBlobAsBase64(new Blob([data]), callback);\n    }\n  }\n  // plain string\n  return callback(PACKET_TYPES[type] + (data || \"\"));\n};\n\nconst encodeBlobAsBase64 = (data, callback) => {\n  const fileReader = new FileReader();\n  fileReader.onload = function() {\n    const content = fileReader.result.split(\",\")[1];\n    callback(\"b\" + content);\n  };\n  return fileReader.readAsDataURL(data);\n};\n\nmodule.exports = encodePacket;\n", "const { PACKET_TYPES_REVERSE, ERROR_PACKET } = require(\"./commons\");\n\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n\nlet base64decoder;\nif (withNativeArrayBuffer) {\n  base64decoder = require(\"base64-arraybuffer\");\n}\n\nconst decodePacket = (encodedPacket, binaryType) => {\n  if (typeof encodedPacket !== \"string\") {\n    return {\n      type: \"message\",\n      data: mapBinary(encodedPacket, binaryType)\n    };\n  }\n  const type = encodedPacket.charAt(0);\n  if (type === \"b\") {\n    return {\n      type: \"message\",\n      data: decodeBase64Packet(encodedPacket.substring(1), binaryType)\n    };\n  }\n  const packetType = PACKET_TYPES_REVERSE[type];\n  if (!packetType) {\n    return ERROR_PACKET;\n  }\n  return encodedPacket.length > 1\n    ? {\n        type: PACKET_TYPES_REVERSE[type],\n        data: encodedPacket.substring(1)\n      }\n    : {\n        type: PACKET_TYPES_REVERSE[type]\n      };\n};\n\nconst decodeBase64Packet = (data, binaryType) => {\n  if (base64decoder) {\n    const decoded = base64decoder.decode(data);\n    return mapBinary(decoded, binaryType);\n  } else {\n    return { base64: true, data }; // fallback for old browsers\n  }\n};\n\nconst mapBinary = (data, binaryType) => {\n  switch (binaryType) {\n    case \"blob\":\n      return data instanceof ArrayBuffer ? new Blob([data]) : data;\n    case \"arraybuffer\":\n    default:\n      return data; // assuming the data is already an ArrayBuffer\n  }\n};\n\nmodule.exports = decodePacket;\n", "/*\n * base64-arraybuffer\n * https://github.com/niklasvh/base64-arraybuffer\n *\n * Copyright (c) 2012 <PERSON><PERSON>\n * Licensed under the MIT license.\n */\n(function(chars){\n  \"use strict\";\n\n  exports.encode = function(arraybuffer) {\n    var bytes = new Uint8Array(arraybuffer),\n    i, len = bytes.length, base64 = \"\";\n\n    for (i = 0; i < len; i+=3) {\n      base64 += chars[bytes[i] >> 2];\n      base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n      base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n      base64 += chars[bytes[i + 2] & 63];\n    }\n\n    if ((len % 3) === 2) {\n      base64 = base64.substring(0, base64.length - 1) + \"=\";\n    } else if (len % 3 === 1) {\n      base64 = base64.substring(0, base64.length - 2) + \"==\";\n    }\n\n    return base64;\n  };\n\n  exports.decode =  function(base64) {\n    var bufferLength = base64.length * 0.75,\n    len = base64.length, i, p = 0,\n    encoded1, encoded2, encoded3, encoded4;\n\n    if (base64[base64.length - 1] === \"=\") {\n      bufferLength--;\n      if (base64[base64.length - 2] === \"=\") {\n        bufferLength--;\n      }\n    }\n\n    var arraybuffer = new ArrayBuffer(bufferLength),\n    bytes = new Uint8Array(arraybuffer);\n\n    for (i = 0; i < len; i+=4) {\n      encoded1 = chars.indexOf(base64[i]);\n      encoded2 = chars.indexOf(base64[i+1]);\n      encoded3 = chars.indexOf(base64[i+2]);\n      encoded4 = chars.indexOf(base64[i+3]);\n\n      bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n      bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n      bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n\n    return arraybuffer;\n  };\n})(\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\");\n", "const Polling = require(\"./polling\");\nconst globalThis = require(\"../globalThis\");\n\nconst rNewline = /\\n/g;\nconst rEscapedNewline = /\\\\n/g;\n\n/**\n * Global JSONP callbacks.\n */\n\nlet callbacks;\n\nclass JSONPPolling extends Polling {\n  /**\n   * JSONP Polling constructor.\n   *\n   * @param {Object} opts.\n   * @api public\n   */\n  constructor(opts) {\n    super(opts);\n\n    this.query = this.query || {};\n\n    // define global callbacks array if not present\n    // we do this here (lazily) to avoid unneeded global pollution\n    if (!callbacks) {\n      // we need to consider multiple engines in the same page\n      callbacks = globalThis.___eio = globalThis.___eio || [];\n    }\n\n    // callback identifier\n    this.index = callbacks.length;\n\n    // add callback to jsonp global\n    const self = this;\n    callbacks.push(function(msg) {\n      self.onData(msg);\n    });\n\n    // append to query string\n    this.query.j = this.index;\n  }\n\n  /**\n   * JSONP only supports binary as base64 encoded strings\n   */\n  get supportsBinary() {\n    return false;\n  }\n\n  /**\n   * Closes the socket.\n   *\n   * @api private\n   */\n  doClose() {\n    if (this.script) {\n      // prevent spurious errors from being emitted when the window is unloaded\n      this.script.onerror = () => {};\n      this.script.parentNode.removeChild(this.script);\n      this.script = null;\n    }\n\n    if (this.form) {\n      this.form.parentNode.removeChild(this.form);\n      this.form = null;\n      this.iframe = null;\n    }\n\n    super.doClose();\n  }\n\n  /**\n   * Starts a poll cycle.\n   *\n   * @api private\n   */\n  doPoll() {\n    const self = this;\n    const script = document.createElement(\"script\");\n\n    if (this.script) {\n      this.script.parentNode.removeChild(this.script);\n      this.script = null;\n    }\n\n    script.async = true;\n    script.src = this.uri();\n    script.onerror = function(e) {\n      self.onError(\"jsonp poll error\", e);\n    };\n\n    const insertAt = document.getElementsByTagName(\"script\")[0];\n    if (insertAt) {\n      insertAt.parentNode.insertBefore(script, insertAt);\n    } else {\n      (document.head || document.body).appendChild(script);\n    }\n    this.script = script;\n\n    const isUAgecko =\n      \"undefined\" !== typeof navigator && /gecko/i.test(navigator.userAgent);\n\n    if (isUAgecko) {\n      setTimeout(function() {\n        const iframe = document.createElement(\"iframe\");\n        document.body.appendChild(iframe);\n        document.body.removeChild(iframe);\n      }, 100);\n    }\n  }\n\n  /**\n   * Writes with a hidden iframe.\n   *\n   * @param {String} data to send\n   * @param {Function} called upon flush.\n   * @api private\n   */\n  doWrite(data, fn) {\n    const self = this;\n    let iframe;\n\n    if (!this.form) {\n      const form = document.createElement(\"form\");\n      const area = document.createElement(\"textarea\");\n      const id = (this.iframeId = \"eio_iframe_\" + this.index);\n\n      form.className = \"socketio\";\n      form.style.position = \"absolute\";\n      form.style.top = \"-1000px\";\n      form.style.left = \"-1000px\";\n      form.target = id;\n      form.method = \"POST\";\n      form.setAttribute(\"accept-charset\", \"utf-8\");\n      area.name = \"d\";\n      form.appendChild(area);\n      document.body.appendChild(form);\n\n      this.form = form;\n      this.area = area;\n    }\n\n    this.form.action = this.uri();\n\n    function complete() {\n      initIframe();\n      fn();\n    }\n\n    function initIframe() {\n      if (self.iframe) {\n        try {\n          self.form.removeChild(self.iframe);\n        } catch (e) {\n          self.onError(\"jsonp polling iframe removal error\", e);\n        }\n      }\n\n      try {\n        // ie6 dynamic iframes with target=\"\" support (thanks Chris Lambacher)\n        const html = '<iframe src=\"javascript:0\" name=\"' + self.iframeId + '\">';\n        iframe = document.createElement(html);\n      } catch (e) {\n        iframe = document.createElement(\"iframe\");\n        iframe.name = self.iframeId;\n        iframe.src = \"javascript:0\";\n      }\n\n      iframe.id = self.iframeId;\n\n      self.form.appendChild(iframe);\n      self.iframe = iframe;\n    }\n\n    initIframe();\n\n    // escape \\n to prevent it from being converted into \\r\\n by some UAs\n    // double escaping is required for escaped new lines because unescaping of new lines can be done safely on server-side\n    data = data.replace(rEscapedNewline, \"\\\\\\n\");\n    this.area.value = data.replace(rNewline, \"\\\\n\");\n\n    try {\n      this.form.submit();\n    } catch (e) {}\n\n    if (this.iframe.attachEvent) {\n      this.iframe.onreadystatechange = function() {\n        if (self.iframe.readyState === \"complete\") {\n          complete();\n        }\n      };\n    } else {\n      this.iframe.onload = complete;\n    }\n  }\n}\n\nmodule.exports = JSONPPolling;\n", "const Transport = require(\"../transport\");\nconst parser = require(\"engine.io-parser\");\nconst parseqs = require(\"parseqs\");\nconst yeast = require(\"yeast\");\nconst { pick } = require(\"../util\");\nconst {\n  WebSocket,\n  usingBrowserWebSocket,\n  defaultBinaryType\n} = require(\"./websocket-constructor\");\n\n\n\n\n// detect ReactNative environment\nconst isReactNative =\n  typeof navigator !== \"undefined\" &&\n  typeof navigator.product === \"string\" &&\n  navigator.product.toLowerCase() === \"reactnative\";\n\nclass WS extends Transport {\n  /**\n   * WebSocket transport constructor.\n   *\n   * @api {Object} connection options\n   * @api public\n   */\n  constructor(opts) {\n    super(opts);\n\n    this.supportsBinary = !opts.forceBase64;\n  }\n\n  /**\n   * Transport name.\n   *\n   * @api public\n   */\n  get name() {\n    return \"websocket\";\n  }\n\n  /**\n   * Opens socket.\n   *\n   * @api private\n   */\n  doOpen() {\n    if (!this.check()) {\n      // let probe timeout\n      return;\n    }\n\n    const uri = this.uri();\n    const protocols = this.opts.protocols;\n\n    // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n    const opts = isReactNative\n      ? {}\n      : pick(\n          this.opts,\n          \"agent\",\n          \"perMessageDeflate\",\n          \"pfx\",\n          \"key\",\n          \"passphrase\",\n          \"cert\",\n          \"ca\",\n          \"ciphers\",\n          \"rejectUnauthorized\",\n          \"localAddress\",\n          \"protocolVersion\",\n          \"origin\",\n          \"maxPayload\",\n          \"family\",\n          \"checkServerIdentity\"\n        );\n\n    if (this.opts.extraHeaders) {\n      opts.headers = this.opts.extraHeaders;\n    }\n\n    try {\n      this.ws =\n        usingBrowserWebSocket && !isReactNative\n          ? protocols\n            ? new WebSocket(uri, protocols)\n            : new WebSocket(uri)\n          : new WebSocket(uri, protocols, opts);\n    } catch (err) {\n      return this.emit(\"error\", err);\n    }\n\n    this.ws.binaryType = this.socket.binaryType || defaultBinaryType;\n\n    this.addEventListeners();\n  }\n\n  /**\n   * Adds event listeners to the socket\n   *\n   * @api private\n   */\n  addEventListeners() {\n    const self = this;\n\n    this.ws.onopen = function() {\n      self.onOpen();\n    };\n    this.ws.onclose = function() {\n      self.onClose();\n    };\n    this.ws.onmessage = function(ev) {\n      self.onData(ev.data);\n    };\n    this.ws.onerror = function(e) {\n      self.onError(\"websocket error\", e);\n    };\n  }\n\n  /**\n   * Writes data to socket.\n   *\n   * @param {Array} array of packets.\n   * @api private\n   */\n  write(packets) {\n    const self = this;\n    this.writable = false;\n\n    // encodePacket efficient as it uses WS framing\n    // no need for encodePayload\n    let total = packets.length;\n    let i = 0;\n    const l = total;\n    for (; i < l; i++) {\n      (function(packet) {\n        parser.encodePacket(packet, self.supportsBinary, function(data) {\n          // always create a new object (GH-437)\n          const opts = {};\n          if (!usingBrowserWebSocket) {\n            if (packet.options) {\n              opts.compress = packet.options.compress;\n            }\n\n            if (self.opts.perMessageDeflate) {\n              const len =\n                \"string\" === typeof data\n                  ? Buffer.byteLength(data)\n                  : data.length;\n              if (len < self.opts.perMessageDeflate.threshold) {\n                opts.compress = false;\n              }\n            }\n          }\n\n          // Sometimes the websocket has already been closed but the browser didn't\n          // have a chance of informing us about it yet, in that case send will\n          // throw an error\n          try {\n            if (usingBrowserWebSocket) {\n              // TypeError is thrown when passing the second argument on Safari\n              self.ws.send(data);\n            } else {\n              self.ws.send(data, opts);\n            }\n          } catch (e) {\n\n\n          }\n\n          --total || done();\n        });\n      })(packets[i]);\n    }\n\n    function done() {\n      self.emit(\"flush\");\n\n      // fake drain\n      // defer to next tick to allow Socket to clear writeBuffer\n      setTimeout(function() {\n        self.writable = true;\n        self.emit(\"drain\");\n      }, 0);\n    }\n  }\n\n  /**\n   * Called upon close\n   *\n   * @api private\n   */\n  onClose() {\n    Transport.prototype.onClose.call(this);\n  }\n\n  /**\n   * Closes socket.\n   *\n   * @api private\n   */\n  doClose() {\n    if (typeof this.ws !== \"undefined\") {\n      this.ws.close();\n      this.ws = null;\n    }\n  }\n\n  /**\n   * Generates uri for connection.\n   *\n   * @api private\n   */\n  uri() {\n    let query = this.query || {};\n    const schema = this.opts.secure ? \"wss\" : \"ws\";\n    let port = \"\";\n\n    // avoid port if default for schema\n    if (\n      this.opts.port &&\n      ((\"wss\" === schema && Number(this.opts.port) !== 443) ||\n        (\"ws\" === schema && Number(this.opts.port) !== 80))\n    ) {\n      port = \":\" + this.opts.port;\n    }\n\n    // append timestamp to URI\n    if (this.opts.timestampRequests) {\n      query[this.opts.timestampParam] = yeast();\n    }\n\n    // communicate binary support capabilities\n    if (!this.supportsBinary) {\n      query.b64 = 1;\n    }\n\n    query = parseqs.encode(query);\n\n    // prepend ? to query\n    if (query.length) {\n      query = \"?\" + query;\n    }\n\n    const ipv6 = this.opts.hostname.indexOf(\":\") !== -1;\n    return (\n      schema +\n      \"://\" +\n      (ipv6 ? \"[\" + this.opts.hostname + \"]\" : this.opts.hostname) +\n      port +\n      this.opts.path +\n      query\n    );\n  }\n\n  /**\n   * Feature detection for WebSocket.\n   *\n   * @return {Boolean} whether this transport is available.\n   * @api public\n   */\n  check() {\n    return (\n      !!WebSocket &&\n      !(\"__initialize\" in WebSocket && this.name === WS.prototype.name)\n    );\n  }\n}\n\nmodule.exports = WS;\n", "const globalThis = require(\"../globalThis\");\n\nmodule.exports = {\n  WebSocket: globalThis.WebSocket || globalThis.MozWebSocket,\n  usingBrowserWebSocket: true,\n  defaultBinaryType: \"arraybuffer\"\n};\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.reconstructPacket = exports.deconstructPacket = void 0;\nconst is_binary_1 = require(\"./is-binary\");\n/**\n * Replaces every Buffer | ArrayBuffer | Blob | File in packet with a numbered placeholder.\n *\n * @param {Object} packet - socket.io event packet\n * @return {Object} with deconstructed packet and list of buffers\n * @public\n */\nfunction deconstructPacket(packet) {\n    const buffers = [];\n    const packetData = packet.data;\n    const pack = packet;\n    pack.data = _deconstructPacket(packetData, buffers);\n    pack.attachments = buffers.length; // number of binary 'attachments'\n    return { packet: pack, buffers: buffers };\n}\nexports.deconstructPacket = deconstructPacket;\nfunction _deconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (is_binary_1.isBinary(data)) {\n        const placeholder = { _placeholder: true, num: buffers.length };\n        buffers.push(data);\n        return placeholder;\n    }\n    else if (Array.isArray(data)) {\n        const newData = new Array(data.length);\n        for (let i = 0; i < data.length; i++) {\n            newData[i] = _deconstructPacket(data[i], buffers);\n        }\n        return newData;\n    }\n    else if (typeof data === \"object\" && !(data instanceof Date)) {\n        const newData = {};\n        for (const key in data) {\n            if (data.hasOwnProperty(key)) {\n                newData[key] = _deconstructPacket(data[key], buffers);\n            }\n        }\n        return newData;\n    }\n    return data;\n}\n/**\n * Reconstructs a binary packet from its placeholder packet and buffers\n *\n * @param {Object} packet - event packet with placeholders\n * @param {Array} buffers - binary buffers to put in placeholder positions\n * @return {Object} reconstructed packet\n * @public\n */\nfunction reconstructPacket(packet, buffers) {\n    packet.data = _reconstructPacket(packet.data, buffers);\n    packet.attachments = undefined; // no longer useful\n    return packet;\n}\nexports.reconstructPacket = reconstructPacket;\nfunction _reconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (data && data._placeholder) {\n        return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n    }\n    else if (Array.isArray(data)) {\n        for (let i = 0; i < data.length; i++) {\n            data[i] = _reconstructPacket(data[i], buffers);\n        }\n    }\n    else if (typeof data === \"object\") {\n        for (const key in data) {\n            if (data.hasOwnProperty(key)) {\n                data[key] = _reconstructPacket(data[key], buffers);\n            }\n        }\n    }\n    return data;\n}\n", "\n/**\n * Expose `Backoff`.\n */\n\nmodule.exports = Backoff;\n\n/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */\n\nfunction Backoff(opts) {\n  opts = opts || {};\n  this.ms = opts.min || 100;\n  this.max = opts.max || 10000;\n  this.factor = opts.factor || 2;\n  this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n  this.attempts = 0;\n}\n\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */\n\nBackoff.prototype.duration = function(){\n  var ms = this.ms * Math.pow(this.factor, this.attempts++);\n  if (this.jitter) {\n    var rand =  Math.random();\n    var deviation = Math.floor(rand * this.jitter * ms);\n    ms = (Math.floor(rand * 10) & 1) == 0  ? ms - deviation : ms + deviation;\n  }\n  return Math.min(ms, this.max) | 0;\n};\n\n/**\n * Reset the number of attempts.\n *\n * @api public\n */\n\nBackoff.prototype.reset = function(){\n  this.attempts = 0;\n};\n\n/**\n * Set the minimum duration\n *\n * @api public\n */\n\nBackoff.prototype.setMin = function(min){\n  this.ms = min;\n};\n\n/**\n * Set the maximum duration\n *\n * @api public\n */\n\nBackoff.prototype.setMax = function(max){\n  this.max = max;\n};\n\n/**\n * Set the jitter\n *\n * @api public\n */\n\nBackoff.prototype.setJitter = function(jitter){\n  this.jitter = jitter;\n};\n\n"], "sourceRoot": ""}