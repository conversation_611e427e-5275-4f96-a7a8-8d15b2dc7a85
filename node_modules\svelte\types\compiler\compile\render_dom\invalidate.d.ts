import { Scope } from 'periscopic';
import { Node } from 'estree';
import Renderer from './Renderer';
export declare function invalidate(renderer: <PERSON>der<PERSON>, scope: Scope, node: Node, names: Set<string>, main_execution_context?: boolean): any;
export declare function renderer_invalidate(renderer: <PERSON><PERSON><PERSON>, name: string, value?: unknown, main_execution_context?: boolean): unknown;
