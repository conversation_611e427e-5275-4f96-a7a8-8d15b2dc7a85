import type { AnyFn, DefaultPluginAPIs, PartialGameState, State, Game, Plugin, ActionShape, PlayerID } from '../types';
import type { GameMethod } from '../core/game-methods';
interface PluginOpts {
    game: Game;
    isClient?: boolean;
}
/**
 * Allow plugins to intercept actions and process them.
 */
export declare const ProcessAction: (state: State, action: ActionShape.Plugin, opts: PluginOpts) => State;
/**
 * The APIs created by various plugins are stored in the plugins
 * section of the state object:
 *
 * {
 *   G: {},
 *   ctx: {},
 *   plugins: {
 *     plugin-a: {
 *       data: {},  // this is generated by the plugin at Setup / Flush.
 *       api: {},   // this is ephemeral and generated by Enhance.
 *     }
 *   }
 * }
 *
 * This function retrieves plugin APIs and returns them as an object
 * for consumption as used by move contexts.
 */
export declare const GetAPIs: ({ plugins }: PartialGameState) => DefaultPluginAPIs;
/**
 * Applies the provided plugins to the given move / flow function.
 *
 * @param methodToWrap - The move function or hook to apply the plugins to.
 * @param methodType - The type of the move or hook being wrapped.
 * @param plugins - The list of plugins.
 */
export declare const FnWrap: (methodToWrap: AnyFn, methodType: GameMethod, plugins: Plugin[]) => (context: import("../types").FnContext<any, Record<string, unknown>>, ...args: any[]) => any;
/**
 * Allows the plugin to generate its initial state.
 */
export declare const Setup: (state: PartialGameState, opts: PluginOpts) => PartialGameState;
/**
 * Invokes the plugin before a move or event.
 * The API that the plugin generates is stored inside
 * the `plugins` section of the state (which is subsequently
 * merged into ctx).
 */
export declare const Enhance: <S extends State<any> | Pick<State<any>, "G" | "ctx" | "plugins">>(state: S, opts: PluginOpts & {
    playerID: PlayerID;
}) => S;
/**
 * Allows plugins to indicate if they should not be materialized on the client.
 * This will cause the client to discard the state update and wait for the
 * master instead.
 */
export declare const NoClient: (state: State, opts: PluginOpts) => boolean;
/**
 * Update plugin state after move/event & check if plugins consider the update to be valid.
 * @returns Tuple of `[updatedState]` or `[originalState, invalidError]`.
 */
export declare const FlushAndValidate: (state: State, opts: PluginOpts) => readonly [State<any>] | readonly [State<any>, {
    plugin: string;
    message: string;
}];
/**
 * Allows plugins to customize their data for specific players.
 * For example, a plugin may want to share no data with the client, or
 * want to keep some player data secret from opponents.
 */
export declare const PlayerView: ({ G, ctx, plugins }: State, { game, playerID }: PluginOpts & {
    playerID: PlayerID;
}) => {
    [pluginName: string]: import("../types").PluginState;
};
export {};
