{"name": "engine.io-parser", "description": "Parser for the client for the realtime Engine", "license": "MIT", "version": "4.0.3", "main": "lib/index.js", "homepage": "https://github.com/socketio/engine.io-parser", "devDependencies": {"@babel/core": "~7.9.6", "@babel/preset-env": "~7.9.6", "babel-eslint": "^10.0.3", "babelify": "^10.0.0", "benchmark": "^2.1.4", "eslint": "^6.8.0", "eslint-config-prettier": "^6.9.0", "expect.js": "0.3.1", "mocha": "^5.2.0", "nyc": "~15.0.1", "prettier": "^1.19.1", "socket.io-browsers": "^1.0.4", "zuul": "3.11.1", "zuul-ngrok": "4.0.0"}, "dependencies": {"base64-arraybuffer": "0.1.4"}, "scripts": {"test": "npm run lint && npm run format:check && if test \"$BROWSERS\" = \"1\" ; then npm run test:browser; else npm run test:node; fi", "test:node": "nyc mocha test/index.js", "test:browser": "zuul test/index.js --no-coverage", "format:check": "prettier --check 'lib/**/*.js' 'test/**/*.js'", "format:fix": "prettier --write 'lib/**/*.js' 'test/**/*.js'", "lint": "eslint 'lib/**/*.js' 'test/**/*.js'"}, "repository": {"type": "git", "url": "**************:socketio/engine.io-parser.git"}, "files": ["lib/"], "browser": {"./lib/encodePacket.js": "./lib/encodePacket.browser.js", "./lib/decodePacket.js": "./lib/decodePacket.browser.js"}, "engines": {"node": ">=8.0.0"}}