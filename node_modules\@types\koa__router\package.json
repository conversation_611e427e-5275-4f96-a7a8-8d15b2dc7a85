{"name": "@types/koa__router", "version": "8.0.11", "description": "TypeScript definitions for @koa/router", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/koa__router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/hellopao", "githubUsername": "hellopao"}, {"name": "<PERSON>", "url": "https://github.com/schfkt", "githubUsername": "schfkt"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JounQin", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/romain-faust", "githubUsername": "romain-faust"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/falinor", "githubUsername": "f<PERSON>nor"}, {"name": "<PERSON>", "url": "https://github.com/jdforsythe", "githubUsername": "jdforsythe"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/koa__router"}, "scripts": {}, "dependencies": {"@types/koa": "*"}, "typesPublisherContentHash": "c1cd2d29162f48d8e297730834bdea0642b77a848bf5741606f2500ad0c394dc", "typeScriptVersion": "3.8"}