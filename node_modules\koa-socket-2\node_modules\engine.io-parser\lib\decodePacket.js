const { PACKET_TYPES_REVERSE, ERROR_PACKET } = require("./commons");

const decodePacket = (encodedPacket, binaryType) => {
  if (typeof encodedPacket !== "string") {
    return {
      type: "message",
      data: mapBinary(encodedPacket, binaryType)
    };
  }
  const type = encodedPacket.charAt(0);
  if (type === "b") {
    const buffer = Buffer.from(encodedPacket.substring(1), "base64");
    return {
      type: "message",
      data: mapBinary(buffer, binaryType)
    };
  }
  if (!PACKET_TYPES_REVERSE[type]) {
    return ERROR_PACKET;
  }
  return encodedPacket.length > 1
    ? {
        type: PACKET_TYPES_REVERSE[type],
        data: encodedPacket.substring(1)
      }
    : {
        type: PACKET_TYPES_REVERSE[type]
      };
};

const mapBinary = (data, binaryType) => {
  const isBuffer = Buffer.isBuffer(data);
  switch (binaryType) {
    case "arraybuffer":
      return isBuffer ? toArrayBuffer(data) : data;
    case "nodebuffer":
    default:
      return data; // assuming the data is already a Buffer
  }
};

const toArrayBuffer = buffer => {
  const arrayBuffer = new ArrayBuffer(buffer.length);
  const view = new Uint8Array(arrayBuffer);
  for (let i = 0; i < buffer.length; i++) {
    view[i] = buffer[i];
  }
  return arrayBuffer;
};

module.exports = decodePacket;
