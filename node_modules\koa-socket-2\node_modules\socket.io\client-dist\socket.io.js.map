{"version": 3, "sources": ["webpack://io/webpack/universalModuleDefinition", "webpack://io/webpack/bootstrap", "webpack://io/./build/index.js", "webpack://io/./build/manager.js", "webpack://io/./build/on.js", "webpack://io/./build/socket.js", "webpack://io/./build/url.js", "webpack://io/./node_modules/backo2/index.js", "webpack://io/./node_modules/component-emitter/index.js", "webpack://io/./node_modules/debug/src/browser.js", "webpack://io/./node_modules/debug/src/common.js", "webpack://io/./node_modules/engine.io-client/lib/globalThis.browser.js", "webpack://io/./node_modules/engine.io-client/lib/index.js", "webpack://io/./node_modules/engine.io-client/lib/socket.js", "webpack://io/./node_modules/engine.io-client/lib/transport.js", "webpack://io/./node_modules/engine.io-client/lib/transports/index.js", "webpack://io/./node_modules/engine.io-client/lib/transports/polling-jsonp.js", "webpack://io/./node_modules/engine.io-client/lib/transports/polling-xhr.js", "webpack://io/./node_modules/engine.io-client/lib/transports/polling.js", "webpack://io/./node_modules/engine.io-client/lib/transports/websocket-constructor.browser.js", "webpack://io/./node_modules/engine.io-client/lib/transports/websocket.js", "webpack://io/./node_modules/engine.io-client/lib/util.js", "webpack://io/./node_modules/engine.io-client/lib/xmlhttprequest.js", "webpack://io/./node_modules/engine.io-parser/lib/commons.js", "webpack://io/./node_modules/engine.io-parser/lib/decodePacket.browser.js", "webpack://io/./node_modules/engine.io-parser/lib/encodePacket.browser.js", "webpack://io/./node_modules/engine.io-parser/lib/index.js", "webpack://io/./node_modules/engine.io-parser/node_modules/base64-arraybuffer/lib/base64-arraybuffer.js", "webpack://io/./node_modules/has-cors/index.js", "webpack://io/./node_modules/ms/index.js", "webpack://io/./node_modules/parseqs/index.js", "webpack://io/./node_modules/parseuri/index.js", "webpack://io/./node_modules/socket.io-parser/dist/binary.js", "webpack://io/./node_modules/socket.io-parser/dist/index.js", "webpack://io/./node_modules/socket.io-parser/dist/is-binary.js", "webpack://io/./node_modules/yeast/index.js"], "names": ["Object", "defineProperty", "exports", "value", "Socket", "io", "Manager", "protocol", "url_1", "require", "manager_1", "socket_1", "enumerable", "get", "debug", "module", "lookup", "cache", "managers", "uri", "opts", "undefined", "parsed", "url", "path", "source", "id", "sameNamespace", "newConnection", "forceNew", "multiplex", "query", "query<PERSON><PERSON>", "socket", "socket_io_parser_1", "connect", "manager_2", "eio", "Emitter", "parser", "on_1", "Backoff", "nsps", "subs", "reconnection", "reconnectionAttempts", "Infinity", "reconnectionDelay", "reconnectionDelayMax", "randomizationFactor", "backoff", "min", "max", "jitter", "timeout", "_readyState", "_parser", "encoder", "Encoder", "decoder", "Decoder", "_autoConnect", "autoConnect", "open", "v", "arguments", "length", "_reconnection", "_reconnectionAttempts", "_a", "_reconnectionDelay", "setMin", "_randomizationFactor", "setJitter", "_reconnectionDelayMax", "setMax", "_timeout", "_reconnecting", "attempts", "reconnect", "fn", "indexOf", "engine", "self", "skipReconnect", "openSubDestroy", "on", "onopen", "errorSub", "err", "cleanup", "maybeReconnectOnOpen", "timer", "setTimeout", "close", "emit", "Error", "push", "subDestroy", "clearTimeout", "onping", "bind", "ondata", "onerror", "onclose", "ondecoded", "data", "add", "packet", "nsp", "keys", "active", "_close", "encodedPackets", "encode", "i", "write", "options", "for<PERSON>ach", "destroy", "reset", "reason", "delay", "duration", "onreconnect", "attempt", "obj", "ev", "off", "RESERVED_EVENTS", "freeze", "connect_error", "disconnect", "disconnecting", "newListener", "removeListener", "<PERSON><PERSON><PERSON><PERSON>", "send<PERSON><PERSON><PERSON>", "ids", "acks", "flags", "connected", "disconnected", "auth", "onpacket", "subEvents", "args", "unshift", "apply", "hasOwnProperty", "type", "PacketType", "EVENT", "compress", "pop", "isTransportWritable", "transport", "writable", "discardPacket", "_packet", "CONNECT", "sid", "onconnect", "onevent", "BINARY_EVENT", "ACK", "onack", "BINARY_ACK", "DISCONNECT", "ondisconnect", "CONNECT_ERROR", "message", "ack", "emitEvent", "_anyListeners", "listeners", "slice", "listener", "sent", "emitBuffered", "splice", "parseuri", "loc", "location", "host", "char<PERSON>t", "test", "port", "ipv6", "href", "ms", "factor", "prototype", "Math", "pow", "rand", "random", "deviation", "floor", "mixin", "key", "addEventListener", "event", "_callbacks", "once", "removeAllListeners", "removeEventListener", "callbacks", "cb", "Array", "len", "hasListeners", "formatArgs", "save", "load", "useColors", "storage", "localstorage", "warned", "console", "warn", "colors", "window", "process", "__nwjs", "navigator", "userAgent", "toLowerCase", "match", "document", "documentElement", "style", "WebkitAppearance", "firebug", "exception", "table", "parseInt", "RegExp", "$1", "namespace", "humanize", "diff", "c", "color", "index", "lastC", "replace", "log", "namespaces", "setItem", "removeItem", "error", "r", "getItem", "env", "DEBUG", "localStorage", "formatters", "j", "JSON", "stringify", "setup", "createDebug", "coerce", "disable", "enable", "enabled", "names", "skips", "selectColor", "hash", "charCodeAt", "abs", "prevTime", "enableOverride", "curr", "Number", "Date", "prev", "format", "formatter", "val", "call", "logFn", "extend", "configurable", "set", "init", "delimiter", "newDebug", "split", "substr", "map", "toNamespace", "join", "name", "regexp", "toString", "substring", "stack", "Function", "Transport", "transports", "parseqs", "hostname", "secure", "readyState", "writeBuffer", "prevBufferLen", "agent", "withCredentials", "upgrade", "jsonp", "timestampParam", "rememberUpgrade", "rejectUnauthorized", "perMessageDeflate", "threshold", "transportOptions", "decode", "upgrades", "pingInterval", "pingTimeout", "pingTimeoutTimer", "clone", "EIO", "priorWebsocketSuccess", "createTransport", "e", "shift", "setTransport", "onDrain", "onPacket", "onError", "onClose", "probe", "failed", "onTransportOpen", "onlyBinaryUpgrades", "upgradeLosesBinary", "supportsBinary", "send", "msg", "upgrading", "pause", "flush", "freezeTransport", "onTransportClose", "onupgrade", "to", "l", "onHandshake", "parse", "resetPingTimeout", "sendPacket", "code", "filterUpgrades", "onOpen", "waitForUpgrade", "cleanupAndClose", "desc", "pingIntervalTimer", "filteredUpgrades", "o", "description", "doOpen", "doClose", "packets", "decodePacket", "binaryType", "XMLHttpRequest", "XHR", "JSONP", "websocket", "polling", "xhr", "xd", "xs", "isSSL", "xdomain", "xscheme", "forceJSONP", "Polling", "globalThis", "rNewline", "rEscapedNewline", "JSONPPolling", "___eio", "onData", "script", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "form", "iframe", "createElement", "async", "src", "insertAt", "getElementsByTagName", "insertBefore", "head", "body", "append<PERSON><PERSON><PERSON>", "isUAgecko", "area", "iframeId", "className", "position", "top", "left", "target", "method", "setAttribute", "action", "complete", "initIframe", "html", "submit", "attachEvent", "onreadystatechange", "onload", "pick", "empty", "hasXHR2", "responseType", "forceBase64", "Request", "req", "request", "pollXhr", "create", "extraHeaders", "setDisableHeaderCheck", "setRequestHeader", "requestTimeout", "hasXDR", "onLoad", "responseText", "status", "requestsCount", "requests", "onSuccess", "fromError", "abort", "XDomainRequest", "enablesXDR", "unload<PERSON><PERSON><PERSON>", "terminationEvent", "yeast", "poll", "onPause", "total", "doPoll", "callback", "decodePayload", "encodePayload", "doWrite", "schema", "timestampRequests", "b64", "WebSocket", "MozWebSocket", "usingBrowserWebSocket", "defaultBinaryType", "isReactNative", "product", "WS", "check", "protocols", "headers", "ws", "addEventListeners", "onmessage", "encodePacket", "<PERSON><PERSON><PERSON>", "byteLength", "done", "attr", "reduce", "acc", "k", "hasCORS", "concat", "PACKET_TYPES", "PACKET_TYPES_REVERSE", "ERROR_PACKET", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "base64decoder", "encodedPacket", "mapBinary", "decodeBase64Packet", "packetType", "decoded", "base64", "Blob", "withNativeBlob", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "encodeBlobAsBase64", "fileReader", "FileReader", "content", "result", "readAsDataURL", "SEPARATOR", "String", "fromCharCode", "count", "encodedPayload", "decodedPacket", "chars", "arraybuffer", "bytes", "Uint8Array", "bufferLength", "p", "encoded1", "encoded2", "encoded3", "encoded4", "s", "m", "h", "d", "w", "y", "isFinite", "fmtLong", "fmtShort", "str", "exec", "n", "parseFloat", "msAbs", "round", "plural", "isPlural", "encodeURIComponent", "qs", "qry", "pairs", "pair", "decodeURIComponent", "re", "parts", "b", "authority", "ipv6uri", "pathNames", "regx", "$0", "$2", "reconstructPacket", "deconstructPacket", "is_binary_1", "buffers", "packetData", "pack", "_deconstructPacket", "attachments", "isBinary", "placeholder", "_placeholder", "num", "isArray", "newData", "_reconstructPacket", "binary_1", "hasBinary", "encodeAsBinary", "encodeAsString", "deconstruction", "decodeString", "reconstructor", "BinaryReconstructor", "takeBinaryData", "start", "buf", "next", "payload", "try<PERSON><PERSON><PERSON>", "isPayloadValid", "finishedReconstruction", "reconPack", "binData", "withNativeFile", "File", "toJSON", "alphabet", "seed", "encoded", "now"], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;QCVA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;;;QAGA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA,0CAA0C,gCAAgC;QAC1E;QACA;;QAEA;QACA;QACA;QACA,wDAAwD,kBAAkB;QAC1E;QACA,iDAAiD,cAAc;QAC/D;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,yCAAyC,iCAAiC;QAC1E,gHAAgH,mBAAmB,EAAE;QACrI;QACA;;QAEA;QACA;QACA;QACA,2BAA2B,0BAA0B,EAAE;QACvD,iCAAiC,eAAe;QAChD;QACA;QACA;;QAEA;QACA,sDAAsD,+DAA+D;;QAErH;QACA;;;QAGA;QACA;;;;;;;;;;;;;AClFa;;;;AACbA,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA+B,YAA/B,EAA6C;AAAEC,OAAK,EAAE;AAAT,CAA7C;AACAD,OAAO,CAACE,MAAR,GAAiBF,OAAO,CAACG,EAAR,GAAaH,OAAO,CAACI,OAAR,GAAkBJ,OAAO,CAACK,QAAR,GAAmB,KAAK,CAAxE;;AACA,IAAMC,KAAK,GAAGC,mBAAO,CAAC,6BAAD,CAArB;;AACA,IAAMC,SAAS,GAAGD,mBAAO,CAAC,qCAAD,CAAzB;;AACA,IAAME,QAAQ,GAAGF,mBAAO,CAAC,mCAAD,CAAxB;;AACAT,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA+B,QAA/B,EAAyC;AAAEU,YAAU,EAAE,IAAd;AAAoBC,KAAG,EAAE,eAAY;AAAE,WAAOF,QAAQ,CAACP,MAAhB;AAAyB;AAAhE,CAAzC;;AACA,IAAMU,KAAK,GAAGL,mBAAO,CAAC,kDAAD,CAAP,CAAiB,kBAAjB,CAAd;AACA;AACA;AACA;;;AACAM,MAAM,CAACb,OAAP,GAAiBA,OAAO,GAAGc,MAA3B;AACA;AACA;AACA;;AACA,IAAMC,KAAK,GAAIf,OAAO,CAACgB,QAAR,GAAmB,EAAlC;;AACA,SAASF,MAAT,CAAgBG,GAAhB,EAAqBC,IAArB,EAA2B;AACvB,MAAI,QAAOD,GAAP,MAAe,QAAnB,EAA6B;AACzBC,QAAI,GAAGD,GAAP;AACAA,OAAG,GAAGE,SAAN;AACH;;AACDD,MAAI,GAAGA,IAAI,IAAI,EAAf;AACA,MAAME,MAAM,GAAGd,KAAK,CAACe,GAAN,CAAUJ,GAAV,EAAeC,IAAI,CAACI,IAApB,CAAf;AACA,MAAMC,MAAM,GAAGH,MAAM,CAACG,MAAtB;AACA,MAAMC,EAAE,GAAGJ,MAAM,CAACI,EAAlB;AACA,MAAMF,IAAI,GAAGF,MAAM,CAACE,IAApB;AACA,MAAMG,aAAa,GAAGV,KAAK,CAACS,EAAD,CAAL,IAAaF,IAAI,IAAIP,KAAK,CAACS,EAAD,CAAL,CAAU,MAAV,CAA3C;AACA,MAAME,aAAa,GAAGR,IAAI,CAACS,QAAL,IAClBT,IAAI,CAAC,sBAAD,CADc,IAElB,UAAUA,IAAI,CAACU,SAFG,IAGlBH,aAHJ;AAIA,MAAItB,EAAJ;;AACA,MAAIuB,aAAJ,EAAmB;AACfd,SAAK,CAAC,8BAAD,EAAiCW,MAAjC,CAAL;AACApB,MAAE,GAAG,IAAIK,SAAS,CAACJ,OAAd,CAAsBmB,MAAtB,EAA8BL,IAA9B,CAAL;AACH,GAHD,MAIK;AACD,QAAI,CAACH,KAAK,CAACS,EAAD,CAAV,EAAgB;AACZZ,WAAK,CAAC,wBAAD,EAA2BW,MAA3B,CAAL;AACAR,WAAK,CAACS,EAAD,CAAL,GAAY,IAAIhB,SAAS,CAACJ,OAAd,CAAsBmB,MAAtB,EAA8BL,IAA9B,CAAZ;AACH;;AACDf,MAAE,GAAGY,KAAK,CAACS,EAAD,CAAV;AACH;;AACD,MAAIJ,MAAM,CAACS,KAAP,IAAgB,CAACX,IAAI,CAACW,KAA1B,EAAiC;AAC7BX,QAAI,CAACW,KAAL,GAAaT,MAAM,CAACU,QAApB;AACH;;AACD,SAAO3B,EAAE,CAAC4B,MAAH,CAAUX,MAAM,CAACE,IAAjB,EAAuBJ,IAAvB,CAAP;AACH;;AACDlB,OAAO,CAACG,EAAR,GAAaW,MAAb;AACA;AACA;AACA;AACA;AACA;;AACA,IAAIkB,kBAAkB,GAAGzB,mBAAO,CAAC,uEAAD,CAAhC;;AACAT,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA+B,UAA/B,EAA2C;AAAEU,YAAU,EAAE,IAAd;AAAoBC,KAAG,EAAE,eAAY;AAAE,WAAOqB,kBAAkB,CAAC3B,QAA1B;AAAqC;AAA5E,CAA3C;AACA;AACA;AACA;AACA;AACA;AACA;;AACAL,OAAO,CAACiC,OAAR,GAAkBnB,MAAlB;AACA;AACA;AACA;AACA;AACA;;AACA,IAAIoB,SAAS,GAAG3B,mBAAO,CAAC,qCAAD,CAAvB;;AACAT,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA+B,SAA/B,EAA0C;AAAEU,YAAU,EAAE,IAAd;AAAoBC,KAAG,EAAE,eAAY;AAAE,WAAOuB,SAAS,CAAC9B,OAAjB;AAA2B;AAAlE,CAA1C,E;;;;;;;;;;;;ACrEa;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACbN,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA+B,YAA/B,EAA6C;AAAEC,OAAK,EAAE;AAAT,CAA7C;AACAD,OAAO,CAACI,OAAR,GAAkB,KAAK,CAAvB;;AACA,IAAM+B,GAAG,GAAG5B,mBAAO,CAAC,sEAAD,CAAnB;;AACA,IAAME,QAAQ,GAAGF,mBAAO,CAAC,mCAAD,CAAxB;;AACA,IAAM6B,OAAO,GAAG7B,mBAAO,CAAC,oEAAD,CAAvB;;AACA,IAAM8B,MAAM,GAAG9B,mBAAO,CAAC,uEAAD,CAAtB;;AACA,IAAM+B,IAAI,GAAG/B,mBAAO,CAAC,2BAAD,CAApB;;AACA,IAAMgC,OAAO,GAAGhC,mBAAO,CAAC,8CAAD,CAAvB;;AACA,IAAMK,KAAK,GAAGL,mBAAO,CAAC,kDAAD,CAAP,CAAiB,0BAAjB,CAAd;;IACMH,O;;;;;AACF,mBAAYa,GAAZ,EAAiBC,IAAjB,EAAuB;AAAA;;AAAA;;AACnB;AACA,UAAKsB,IAAL,GAAY,EAAZ;AACA,UAAKC,IAAL,GAAY,EAAZ;;AACA,QAAIxB,GAAG,IAAI,qBAAoBA,GAApB,CAAX,EAAoC;AAChCC,UAAI,GAAGD,GAAP;AACAA,SAAG,GAAGE,SAAN;AACH;;AACDD,QAAI,GAAGA,IAAI,IAAI,EAAf;AACAA,QAAI,CAACI,IAAL,GAAYJ,IAAI,CAACI,IAAL,IAAa,YAAzB;AACA,UAAKJ,IAAL,GAAYA,IAAZ;;AACA,UAAKwB,YAAL,CAAkBxB,IAAI,CAACwB,YAAL,KAAsB,KAAxC;;AACA,UAAKC,oBAAL,CAA0BzB,IAAI,CAACyB,oBAAL,IAA6BC,QAAvD;;AACA,UAAKC,iBAAL,CAAuB3B,IAAI,CAAC2B,iBAAL,IAA0B,IAAjD;;AACA,UAAKC,oBAAL,CAA0B5B,IAAI,CAAC4B,oBAAL,IAA6B,IAAvD;;AACA,UAAKC,mBAAL,CAAyB7B,IAAI,CAAC6B,mBAAL,IAA4B,GAArD;;AACA,UAAKC,OAAL,GAAe,IAAIT,OAAJ,CAAY;AACvBU,SAAG,EAAE,MAAKJ,iBAAL,EADkB;AAEvBK,SAAG,EAAE,MAAKJ,oBAAL,EAFkB;AAGvBK,YAAM,EAAE,MAAKJ,mBAAL;AAHe,KAAZ,CAAf;;AAKA,UAAKK,OAAL,CAAa,QAAQlC,IAAI,CAACkC,OAAb,GAAuB,KAAvB,GAA+BlC,IAAI,CAACkC,OAAjD;;AACA,UAAKC,WAAL,GAAmB,QAAnB;AACA,UAAKpC,GAAL,GAAWA,GAAX;;AACA,QAAMqC,OAAO,GAAGpC,IAAI,CAACmB,MAAL,IAAeA,MAA/B;;AACA,UAAKkB,OAAL,GAAe,IAAID,OAAO,CAACE,OAAZ,EAAf;AACA,UAAKC,OAAL,GAAe,IAAIH,OAAO,CAACI,OAAZ,EAAf;AACA,UAAKC,YAAL,GAAoBzC,IAAI,CAAC0C,WAAL,KAAqB,KAAzC;AACA,QAAI,MAAKD,YAAT,EACI,MAAKE,IAAL;AA7Be;AA8BtB;;;;iCACYC,C,EAAG;AACZ,UAAI,CAACC,SAAS,CAACC,MAAf,EACI,OAAO,KAAKC,aAAZ;AACJ,WAAKA,aAAL,GAAqB,CAAC,CAACH,CAAvB;AACA,aAAO,IAAP;AACH;;;yCACoBA,C,EAAG;AACpB,UAAIA,CAAC,KAAK3C,SAAV,EACI,OAAO,KAAK+C,qBAAZ;AACJ,WAAKA,qBAAL,GAA6BJ,CAA7B;AACA,aAAO,IAAP;AACH;;;sCACiBA,C,EAAG;AACjB,UAAIK,EAAJ;;AACA,UAAIL,CAAC,KAAK3C,SAAV,EACI,OAAO,KAAKiD,kBAAZ;AACJ,WAAKA,kBAAL,GAA0BN,CAA1B;AACA,OAACK,EAAE,GAAG,KAAKnB,OAAX,MAAwB,IAAxB,IAAgCmB,EAAE,KAAK,KAAK,CAA5C,GAAgD,KAAK,CAArD,GAAyDA,EAAE,CAACE,MAAH,CAAUP,CAAV,CAAzD;AACA,aAAO,IAAP;AACH;;;wCACmBA,C,EAAG;AACnB,UAAIK,EAAJ;;AACA,UAAIL,CAAC,KAAK3C,SAAV,EACI,OAAO,KAAKmD,oBAAZ;AACJ,WAAKA,oBAAL,GAA4BR,CAA5B;AACA,OAACK,EAAE,GAAG,KAAKnB,OAAX,MAAwB,IAAxB,IAAgCmB,EAAE,KAAK,KAAK,CAA5C,GAAgD,KAAK,CAArD,GAAyDA,EAAE,CAACI,SAAH,CAAaT,CAAb,CAAzD;AACA,aAAO,IAAP;AACH;;;yCACoBA,C,EAAG;AACpB,UAAIK,EAAJ;;AACA,UAAIL,CAAC,KAAK3C,SAAV,EACI,OAAO,KAAKqD,qBAAZ;AACJ,WAAKA,qBAAL,GAA6BV,CAA7B;AACA,OAACK,EAAE,GAAG,KAAKnB,OAAX,MAAwB,IAAxB,IAAgCmB,EAAE,KAAK,KAAK,CAA5C,GAAgD,KAAK,CAArD,GAAyDA,EAAE,CAACM,MAAH,CAAUX,CAAV,CAAzD;AACA,aAAO,IAAP;AACH;;;4BACOA,C,EAAG;AACP,UAAI,CAACC,SAAS,CAACC,MAAf,EACI,OAAO,KAAKU,QAAZ;AACJ,WAAKA,QAAL,GAAgBZ,CAAhB;AACA,aAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;;2CAC2B;AACnB;AACA,UAAI,CAAC,KAAKa,aAAN,IACA,KAAKV,aADL,IAEA,KAAKjB,OAAL,CAAa4B,QAAb,KAA0B,CAF9B,EAEiC;AAC7B;AACA,aAAKC,SAAL;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;;yBACSC,E,EAAI;AAAA;;AACLlE,WAAK,CAAC,eAAD,EAAkB,KAAKyC,WAAvB,CAAL;AACA,UAAI,CAAC,KAAKA,WAAL,CAAiB0B,OAAjB,CAAyB,MAAzB,CAAL,EACI,OAAO,IAAP;AACJnE,WAAK,CAAC,YAAD,EAAe,KAAKK,GAApB,CAAL;AACA,WAAK+D,MAAL,GAAc7C,GAAG,CAAC,KAAKlB,GAAN,EAAW,KAAKC,IAAhB,CAAjB;AACA,UAAMa,MAAM,GAAG,KAAKiD,MAApB;AACA,UAAMC,IAAI,GAAG,IAAb;AACA,WAAK5B,WAAL,GAAmB,SAAnB;AACA,WAAK6B,aAAL,GAAqB,KAArB,CATK,CAUL;;AACA,UAAMC,cAAc,GAAG7C,IAAI,CAAC8C,EAAL,CAAQrD,MAAR,EAAgB,MAAhB,EAAwB,YAAY;AACvDkD,YAAI,CAACI,MAAL;AACAP,UAAE,IAAIA,EAAE,EAAR;AACH,OAHsB,CAAvB,CAXK,CAeL;;AACA,UAAMQ,QAAQ,GAAGhD,IAAI,CAAC8C,EAAL,CAAQrD,MAAR,EAAgB,OAAhB,EAAyB,UAACwD,GAAD,EAAS;AAC/C3E,aAAK,CAAC,OAAD,CAAL;AACAqE,YAAI,CAACO,OAAL;AACAP,YAAI,CAAC5B,WAAL,GAAmB,QAAnB;;AACA,8EAAW,OAAX,EAAoBkC,GAApB;;AACA,YAAIT,EAAJ,EAAQ;AACJA,YAAE,CAACS,GAAD,CAAF;AACH,SAFD,MAGK;AACD;AACAN,cAAI,CAACQ,oBAAL;AACH;AACJ,OAZgB,CAAjB;;AAaA,UAAI,UAAU,KAAKf,QAAnB,EAA6B;AACzB,YAAMtB,OAAO,GAAG,KAAKsB,QAArB;AACA9D,aAAK,CAAC,uCAAD,EAA0CwC,OAA1C,CAAL;;AACA,YAAIA,OAAO,KAAK,CAAhB,EAAmB;AACf+B,wBAAc,GADC,CACG;AACrB,SALwB,CAMzB;;;AACA,YAAMO,KAAK,GAAGC,UAAU,CAAC,YAAM;AAC3B/E,eAAK,CAAC,oCAAD,EAAuCwC,OAAvC,CAAL;AACA+B,wBAAc;AACdpD,gBAAM,CAAC6D,KAAP;AACA7D,gBAAM,CAAC8D,IAAP,CAAY,OAAZ,EAAqB,IAAIC,KAAJ,CAAU,SAAV,CAArB;AACH,SALuB,EAKrB1C,OALqB,CAAxB;AAMA,aAAKX,IAAL,CAAUsD,IAAV,CAAe,SAASC,UAAT,GAAsB;AACjCC,sBAAY,CAACP,KAAD,CAAZ;AACH,SAFD;AAGH;;AACD,WAAKjD,IAAL,CAAUsD,IAAV,CAAeZ,cAAf;AACA,WAAK1C,IAAL,CAAUsD,IAAV,CAAeT,QAAf;AACA,aAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;;4BACYR,E,EAAI;AACR,aAAO,KAAKjB,IAAL,CAAUiB,EAAV,CAAP;AACH;AACD;AACJ;AACA;AACA;AACA;;;;6BACa;AACLlE,WAAK,CAAC,MAAD,CAAL,CADK,CAEL;;AACA,WAAK4E,OAAL,GAHK,CAIL;;AACA,WAAKnC,WAAL,GAAmB,MAAnB;;AACA,wEAAW,MAAX,EANK,CAOL;;;AACA,UAAMtB,MAAM,GAAG,KAAKiD,MAApB;AACA,WAAKvC,IAAL,CAAUsD,IAAV,CAAezD,IAAI,CAAC8C,EAAL,CAAQrD,MAAR,EAAgB,MAAhB,EAAwB,KAAKmE,MAAL,CAAYC,IAAZ,CAAiB,IAAjB,CAAxB,CAAf,EAAgE7D,IAAI,CAAC8C,EAAL,CAAQrD,MAAR,EAAgB,MAAhB,EAAwB,KAAKqE,MAAL,CAAYD,IAAZ,CAAiB,IAAjB,CAAxB,CAAhE,EAAiH7D,IAAI,CAAC8C,EAAL,CAAQrD,MAAR,EAAgB,OAAhB,EAAyB,KAAKsE,OAAL,CAAaF,IAAb,CAAkB,IAAlB,CAAzB,CAAjH,EAAoK7D,IAAI,CAAC8C,EAAL,CAAQrD,MAAR,EAAgB,OAAhB,EAAyB,KAAKuE,OAAL,CAAaH,IAAb,CAAkB,IAAlB,CAAzB,CAApK,EAAuN7D,IAAI,CAAC8C,EAAL,CAAQ,KAAK3B,OAAb,EAAsB,SAAtB,EAAiC,KAAK8C,SAAL,CAAeJ,IAAf,CAAoB,IAApB,CAAjC,CAAvN;AACH;AACD;AACJ;AACA;AACA;AACA;;;;6BACa;AACL,wEAAW,MAAX;AACH;AACD;AACJ;AACA;AACA;AACA;;;;2BACWK,I,EAAM;AACT,WAAK/C,OAAL,CAAagD,GAAb,CAAiBD,IAAjB;AACH;AACD;AACJ;AACA;AACA;AACA;;;;8BACcE,M,EAAQ;AACd,wEAAW,QAAX,EAAqBA,MAArB;AACH;AACD;AACJ;AACA;AACA;AACA;;;;4BACYnB,G,EAAK;AACT3E,WAAK,CAAC,OAAD,EAAU2E,GAAV,CAAL;;AACA,wEAAW,OAAX,EAAoBA,GAApB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;;2BACWoB,G,EAAKzF,I,EAAM;AACd,UAAIa,MAAM,GAAG,KAAKS,IAAL,CAAUmE,GAAV,CAAb;;AACA,UAAI,CAAC5E,MAAL,EAAa;AACTA,cAAM,GAAG,IAAItB,QAAQ,CAACP,MAAb,CAAoB,IAApB,EAA0ByG,GAA1B,EAA+BzF,IAA/B,CAAT;AACA,aAAKsB,IAAL,CAAUmE,GAAV,IAAiB5E,MAAjB;AACH;;AACD,aAAOA,MAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;;6BACaA,M,EAAQ;AACb,UAAMS,IAAI,GAAG1C,MAAM,CAAC8G,IAAP,CAAY,KAAKpE,IAAjB,CAAb;;AACA,+BAAkBA,IAAlB,2BAAwB;AAAnB,YAAMmE,GAAG,YAAT;AACD,YAAM5E,OAAM,GAAG,KAAKS,IAAL,CAAUmE,GAAV,CAAf;;AACA,YAAI5E,OAAM,CAAC8E,MAAX,EAAmB;AACfjG,eAAK,CAAC,2CAAD,EAA8C+F,GAA9C,CAAL;AACA;AACH;AACJ;;AACD,WAAKG,MAAL;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;;4BACYJ,M,EAAQ;AACZ9F,WAAK,CAAC,mBAAD,EAAsB8F,MAAtB,CAAL;AACA,UAAMK,cAAc,GAAG,KAAKxD,OAAL,CAAayD,MAAb,CAAoBN,MAApB,CAAvB;;AACA,WAAK,IAAIO,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,cAAc,CAAC/C,MAAnC,EAA2CiD,CAAC,EAA5C,EAAgD;AAC5C,aAAKjC,MAAL,CAAYkC,KAAZ,CAAkBH,cAAc,CAACE,CAAD,CAAhC,EAAqCP,MAAM,CAACS,OAA5C;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;;;;8BACc;AACNvG,WAAK,CAAC,SAAD,CAAL;AACA,WAAK6B,IAAL,CAAU2E,OAAV,CAAkB,UAACpB,UAAD;AAAA,eAAgBA,UAAU,EAA1B;AAAA,OAAlB;AACA,WAAKvD,IAAL,CAAUuB,MAAV,GAAmB,CAAnB;AACA,WAAKP,OAAL,CAAa4D,OAAb;AACH;AACD;AACJ;AACA;AACA;AACA;;;;6BACa;AACLzG,WAAK,CAAC,YAAD,CAAL;AACA,WAAKsE,aAAL,GAAqB,IAArB;AACA,WAAKP,aAAL,GAAqB,KAArB;;AACA,UAAI,cAAc,KAAKtB,WAAvB,EAAoC;AAChC;AACA;AACA,aAAKmC,OAAL;AACH;;AACD,WAAKxC,OAAL,CAAasE,KAAb;AACA,WAAKjE,WAAL,GAAmB,QAAnB;AACA,UAAI,KAAK2B,MAAT,EACI,KAAKA,MAAL,CAAYY,KAAZ;AACP;AACD;AACJ;AACA;AACA;AACA;;;;iCACiB;AACT,aAAO,KAAKkB,MAAL,EAAP;AACH;AACD;AACJ;AACA;AACA;AACA;;;;4BACYS,M,EAAQ;AACZ3G,WAAK,CAAC,SAAD,CAAL;AACA,WAAK4E,OAAL;AACA,WAAKxC,OAAL,CAAasE,KAAb;AACA,WAAKjE,WAAL,GAAmB,QAAnB;;AACA,wEAAW,OAAX,EAAoBkE,MAApB;;AACA,UAAI,KAAKtD,aAAL,IAAsB,CAAC,KAAKiB,aAAhC,EAA+C;AAC3C,aAAKL,SAAL;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;;;;gCACgB;AAAA;;AACR,UAAI,KAAKF,aAAL,IAAsB,KAAKO,aAA/B,EACI,OAAO,IAAP;AACJ,UAAMD,IAAI,GAAG,IAAb;;AACA,UAAI,KAAKjC,OAAL,CAAa4B,QAAb,IAAyB,KAAKV,qBAAlC,EAAyD;AACrDtD,aAAK,CAAC,kBAAD,CAAL;AACA,aAAKoC,OAAL,CAAasE,KAAb;;AACA,0EAAW,kBAAX;;AACA,aAAK3C,aAAL,GAAqB,KAArB;AACH,OALD,MAMK;AACD,YAAM6C,KAAK,GAAG,KAAKxE,OAAL,CAAayE,QAAb,EAAd;AACA7G,aAAK,CAAC,yCAAD,EAA4C4G,KAA5C,CAAL;AACA,aAAK7C,aAAL,GAAqB,IAArB;AACA,YAAMe,KAAK,GAAGC,UAAU,CAAC,YAAM;AAC3B,cAAIV,IAAI,CAACC,aAAT,EACI;AACJtE,eAAK,CAAC,sBAAD,CAAL;;AACA,gFAAW,mBAAX,EAAgCqE,IAAI,CAACjC,OAAL,CAAa4B,QAA7C,EAJ2B,CAK3B;;;AACA,cAAIK,IAAI,CAACC,aAAT,EACI;AACJD,cAAI,CAACpB,IAAL,CAAU,UAAC0B,GAAD,EAAS;AACf,gBAAIA,GAAJ,EAAS;AACL3E,mBAAK,CAAC,yBAAD,CAAL;AACAqE,kBAAI,CAACN,aAAL,GAAqB,KAArB;AACAM,kBAAI,CAACJ,SAAL;;AACA,oFAAW,iBAAX,EAA8BU,GAA9B;AACH,aALD,MAMK;AACD3E,mBAAK,CAAC,mBAAD,CAAL;AACAqE,kBAAI,CAACyC,WAAL;AACH;AACJ,WAXD;AAYH,SApBuB,EAoBrBF,KApBqB,CAAxB;AAqBA,aAAK/E,IAAL,CAAUsD,IAAV,CAAe,SAASC,UAAT,GAAsB;AACjCC,sBAAY,CAACP,KAAD,CAAZ;AACH,SAFD;AAGH;AACJ;AACD;AACJ;AACA;AACA;AACA;;;;kCACkB;AACV,UAAMiC,OAAO,GAAG,KAAK3E,OAAL,CAAa4B,QAA7B;AACA,WAAKD,aAAL,GAAqB,KAArB;AACA,WAAK3B,OAAL,CAAasE,KAAb;;AACA,wEAAW,WAAX,EAAwBK,OAAxB;AACH;;;;EApWiBvF,O;;AAsWtBpC,OAAO,CAACI,OAAR,GAAkBA,OAAlB,C;;;;;;;;;;;;AChXa;;AACbN,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA+B,YAA/B,EAA6C;AAAEC,OAAK,EAAE;AAAT,CAA7C;AACAD,OAAO,CAACoF,EAAR,GAAa,KAAK,CAAlB;;AACA,SAASA,EAAT,CAAYwC,GAAZ,EAAiBC,EAAjB,EAAqB/C,EAArB,EAAyB;AACrB8C,KAAG,CAACxC,EAAJ,CAAOyC,EAAP,EAAW/C,EAAX;AACA,SAAO,SAASkB,UAAT,GAAsB;AACzB4B,OAAG,CAACE,GAAJ,CAAQD,EAAR,EAAY/C,EAAZ;AACH,GAFD;AAGH;;AACD9E,OAAO,CAACoF,EAAR,GAAaA,EAAb,C;;;;;;;;;;;;ACTa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACbtF,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA+B,YAA/B,EAA6C;AAAEC,OAAK,EAAE;AAAT,CAA7C;AACAD,OAAO,CAACE,MAAR,GAAiB,KAAK,CAAtB;;AACA,IAAM8B,kBAAkB,GAAGzB,mBAAO,CAAC,uEAAD,CAAlC;;AACA,IAAM6B,OAAO,GAAG7B,mBAAO,CAAC,oEAAD,CAAvB;;AACA,IAAM+B,IAAI,GAAG/B,mBAAO,CAAC,2BAAD,CAApB;;AACA,IAAMK,KAAK,GAAGL,mBAAO,CAAC,kDAAD,CAAP,CAAiB,yBAAjB,CAAd;AACA;AACA;AACA;AACA;;;AACA,IAAMwH,eAAe,GAAGjI,MAAM,CAACkI,MAAP,CAAc;AAClC/F,SAAO,EAAE,CADyB;AAElCgG,eAAa,EAAE,CAFmB;AAGlCC,YAAU,EAAE,CAHsB;AAIlCC,eAAa,EAAE,CAJmB;AAKlC;AACAC,aAAW,EAAE,CANqB;AAOlCC,gBAAc,EAAE;AAPkB,CAAd,CAAxB;;IASMnI,M;;;;;AACF;AACJ;AACA;AACA;AACA;AACI,kBAAYC,EAAZ,EAAgBwG,GAAhB,EAAqBzF,IAArB,EAA2B;AAAA;;AAAA;;AACvB;AACA,UAAKoH,aAAL,GAAqB,EAArB;AACA,UAAKC,UAAL,GAAkB,EAAlB;AACA,UAAKC,GAAL,GAAW,CAAX;AACA,UAAKC,IAAL,GAAY,EAAZ;AACA,UAAKC,KAAL,GAAa,EAAb;AACA,UAAKvI,EAAL,GAAUA,EAAV;AACA,UAAKwG,GAAL,GAAWA,GAAX;AACA,UAAK6B,GAAL,GAAW,CAAX;AACA,UAAKC,IAAL,GAAY,EAAZ;AACA,UAAKH,aAAL,GAAqB,EAArB;AACA,UAAKC,UAAL,GAAkB,EAAlB;AACA,UAAKI,SAAL,GAAiB,KAAjB;AACA,UAAKC,YAAL,GAAoB,IAApB;AACA,UAAKF,KAAL,GAAa,EAAb;;AACA,QAAIxH,IAAI,IAAIA,IAAI,CAAC2H,IAAjB,EAAuB;AACnB,YAAKA,IAAL,GAAY3H,IAAI,CAAC2H,IAAjB;AACH;;AACD,QAAI,MAAK1I,EAAL,CAAQwD,YAAZ,EACI,MAAKE,IAAL;AApBmB;AAqB1B;AACD;AACJ;AACA;AACA;AACA;;;;;gCACgB;AACR,UAAI,KAAKpB,IAAT,EACI;AACJ,UAAMtC,EAAE,GAAG,KAAKA,EAAhB;AACA,WAAKsC,IAAL,GAAY,CACRH,IAAI,CAAC8C,EAAL,CAAQjF,EAAR,EAAY,MAAZ,EAAoB,KAAKkF,MAAL,CAAYc,IAAZ,CAAiB,IAAjB,CAApB,CADQ,EAER7D,IAAI,CAAC8C,EAAL,CAAQjF,EAAR,EAAY,QAAZ,EAAsB,KAAK2I,QAAL,CAAc3C,IAAd,CAAmB,IAAnB,CAAtB,CAFQ,EAGR7D,IAAI,CAAC8C,EAAL,CAAQjF,EAAR,EAAY,OAAZ,EAAqB,KAAKkG,OAAL,CAAaF,IAAb,CAAkB,IAAlB,CAArB,CAHQ,EAIR7D,IAAI,CAAC8C,EAAL,CAAQjF,EAAR,EAAY,OAAZ,EAAqB,KAAKmG,OAAL,CAAaH,IAAb,CAAkB,IAAlB,CAArB,CAJQ,CAAZ;AAMH;AACD;AACJ;AACA;;;;;AAII;AACJ;AACA;AACA;AACA;8BACc;AACN,UAAI,KAAKwC,SAAT,EACI,OAAO,IAAP;AACJ,WAAKI,SAAL;AACA,UAAI,CAAC,KAAK5I,EAAL,CAAQ,eAAR,CAAL,EACI,KAAKA,EAAL,CAAQ0D,IAAR,GALE,CAKc;;AACpB,UAAI,WAAW,KAAK1D,EAAL,CAAQkD,WAAvB,EACI,KAAKgC,MAAL;AACJ,aAAO,IAAP;AACH;AACD;AACJ;AACA;;;;2BACW;AACH,aAAO,KAAKpD,OAAL,EAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;;2BACkB;AAAA,wCAAN+G,IAAM;AAANA,YAAM;AAAA;;AACVA,UAAI,CAACC,OAAL,CAAa,SAAb;AACA,WAAKpD,IAAL,CAAUqD,KAAV,CAAgB,IAAhB,EAAsBF,IAAtB;AACA,aAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;;yBACSnB,E,EAAa;AACd,UAAIE,eAAe,CAACoB,cAAhB,CAA+BtB,EAA/B,CAAJ,EAAwC;AACpC,cAAM,IAAI/B,KAAJ,CAAU,MAAM+B,EAAN,GAAW,4BAArB,CAAN;AACH;;AAHa,yCAANmB,IAAM;AAANA,YAAM;AAAA;;AAIdA,UAAI,CAACC,OAAL,CAAapB,EAAb;AACA,UAAMnB,MAAM,GAAG;AACX0C,YAAI,EAAEpH,kBAAkB,CAACqH,UAAnB,CAA8BC,KADzB;AAEX9C,YAAI,EAAEwC;AAFK,OAAf;AAIAtC,YAAM,CAACS,OAAP,GAAiB,EAAjB;AACAT,YAAM,CAACS,OAAP,CAAeoC,QAAf,GAA0B,KAAKb,KAAL,CAAWa,QAAX,KAAwB,KAAlD,CAVc,CAWd;;AACA,UAAI,eAAe,OAAOP,IAAI,CAACA,IAAI,CAAChF,MAAL,GAAc,CAAf,CAA9B,EAAiD;AAC7CpD,aAAK,CAAC,gCAAD,EAAmC,KAAK4H,GAAxC,CAAL;AACA,aAAKC,IAAL,CAAU,KAAKD,GAAf,IAAsBQ,IAAI,CAACQ,GAAL,EAAtB;AACA9C,cAAM,CAAClF,EAAP,GAAY,KAAKgH,GAAL,EAAZ;AACH;;AACD,UAAMiB,mBAAmB,GAAG,KAAKtJ,EAAL,CAAQ6E,MAAR,IACxB,KAAK7E,EAAL,CAAQ6E,MAAR,CAAe0E,SADS,IAExB,KAAKvJ,EAAL,CAAQ6E,MAAR,CAAe0E,SAAf,CAAyBC,QAF7B;AAGA,UAAMC,aAAa,GAAG,KAAKlB,KAAL,iBAAwB,CAACe,mBAAD,IAAwB,CAAC,KAAKd,SAAtD,CAAtB;;AACA,UAAIiB,aAAJ,EAAmB;AACfhJ,aAAK,CAAC,2DAAD,CAAL;AACH,OAFD,MAGK,IAAI,KAAK+H,SAAT,EAAoB;AACrB,aAAKjC,MAAL,CAAYA,MAAZ;AACH,OAFI,MAGA;AACD,aAAK6B,UAAL,CAAgBxC,IAAhB,CAAqBW,MAArB;AACH;;AACD,WAAKgC,KAAL,GAAa,EAAb;AACA,aAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;;2BACWhC,O,EAAQ;AACXA,aAAM,CAACC,GAAP,GAAa,KAAKA,GAAlB;;AACA,WAAKxG,EAAL,CAAQ0J,OAAR,CAAgBnD,OAAhB;AACH;AACD;AACJ;AACA;AACA;AACA;;;;6BACa;AAAA;;AACL9F,WAAK,CAAC,gCAAD,CAAL;;AACA,UAAI,OAAO,KAAKiI,IAAZ,IAAoB,UAAxB,EAAoC;AAChC,aAAKA,IAAL,CAAU,UAACrC,IAAD,EAAU;AAChB,gBAAI,CAACE,MAAL,CAAY;AAAE0C,gBAAI,EAAEpH,kBAAkB,CAACqH,UAAnB,CAA8BS,OAAtC;AAA+CtD,gBAAI,EAAJA;AAA/C,WAAZ;AACH,SAFD;AAGH,OAJD,MAKK;AACD,aAAKE,MAAL,CAAY;AAAE0C,cAAI,EAAEpH,kBAAkB,CAACqH,UAAnB,CAA8BS,OAAtC;AAA+CtD,cAAI,EAAE,KAAKqC;AAA1D,SAAZ;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;AACA;;;;4BACYtD,G,EAAK;AACT,UAAI,CAAC,KAAKoD,SAAV,EAAqB;AACjB,yEAAW,eAAX,EAA4BpD,GAA5B;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;AACA;;;;4BACYgC,M,EAAQ;AACZ3G,WAAK,CAAC,YAAD,EAAe2G,MAAf,CAAL;AACA,WAAKoB,SAAL,GAAiB,KAAjB;AACA,WAAKC,YAAL,GAAoB,IAApB;AACA,aAAO,KAAKpH,EAAZ;;AACA,uEAAW,YAAX,EAAyB+F,MAAzB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;;6BACab,M,EAAQ;AACb,UAAMjF,aAAa,GAAGiF,MAAM,CAACC,GAAP,KAAe,KAAKA,GAA1C;AACA,UAAI,CAAClF,aAAL,EACI;;AACJ,cAAQiF,MAAM,CAAC0C,IAAf;AACI,aAAKpH,kBAAkB,CAACqH,UAAnB,CAA8BS,OAAnC;AACI,cAAIpD,MAAM,CAACF,IAAP,IAAeE,MAAM,CAACF,IAAP,CAAYuD,GAA/B,EAAoC;AAChC,gBAAMvI,EAAE,GAAGkF,MAAM,CAACF,IAAP,CAAYuD,GAAvB;AACA,iBAAKC,SAAL,CAAexI,EAAf;AACH,WAHD,MAIK;AACD,6EAAW,eAAX,EAA4B,IAAIsE,KAAJ,CAAU,2LAAV,CAA5B;AACH;;AACD;;AACJ,aAAK9D,kBAAkB,CAACqH,UAAnB,CAA8BC,KAAnC;AACI,eAAKW,OAAL,CAAavD,MAAb;AACA;;AACJ,aAAK1E,kBAAkB,CAACqH,UAAnB,CAA8Ba,YAAnC;AACI,eAAKD,OAAL,CAAavD,MAAb;AACA;;AACJ,aAAK1E,kBAAkB,CAACqH,UAAnB,CAA8Bc,GAAnC;AACI,eAAKC,KAAL,CAAW1D,MAAX;AACA;;AACJ,aAAK1E,kBAAkB,CAACqH,UAAnB,CAA8BgB,UAAnC;AACI,eAAKD,KAAL,CAAW1D,MAAX;AACA;;AACJ,aAAK1E,kBAAkB,CAACqH,UAAnB,CAA8BiB,UAAnC;AACI,eAAKC,YAAL;AACA;;AACJ,aAAKvI,kBAAkB,CAACqH,UAAnB,CAA8BmB,aAAnC;AACI,cAAMjF,GAAG,GAAG,IAAIO,KAAJ,CAAUY,MAAM,CAACF,IAAP,CAAYiE,OAAtB,CAAZ,CADJ,CAEI;;AACAlF,aAAG,CAACiB,IAAJ,GAAWE,MAAM,CAACF,IAAP,CAAYA,IAAvB;;AACA,2EAAW,eAAX,EAA4BjB,GAA5B;;AACA;AA9BR;AAgCH;AACD;AACJ;AACA;AACA;AACA;AACA;;;;4BACYmB,M,EAAQ;AACZ,UAAMsC,IAAI,GAAGtC,MAAM,CAACF,IAAP,IAAe,EAA5B;AACA5F,WAAK,CAAC,mBAAD,EAAsBoI,IAAtB,CAAL;;AACA,UAAI,QAAQtC,MAAM,CAAClF,EAAnB,EAAuB;AACnBZ,aAAK,CAAC,iCAAD,CAAL;AACAoI,YAAI,CAACjD,IAAL,CAAU,KAAK2E,GAAL,CAAShE,MAAM,CAAClF,EAAhB,CAAV;AACH;;AACD,UAAI,KAAKmH,SAAT,EAAoB;AAChB,aAAKgC,SAAL,CAAe3B,IAAf;AACH,OAFD,MAGK;AACD,aAAKV,aAAL,CAAmBvC,IAAnB,CAAwBjG,MAAM,CAACkI,MAAP,CAAcgB,IAAd,CAAxB;AACH;AACJ;;;8BACSA,I,EAAM;AACZ,UAAI,KAAK4B,aAAL,IAAsB,KAAKA,aAAL,CAAmB5G,MAA7C,EAAqD;AACjD,YAAM6G,SAAS,GAAG,KAAKD,aAAL,CAAmBE,KAAnB,EAAlB;;AADiD,mDAE1BD,SAF0B;AAAA;;AAAA;AAEjD,8DAAkC;AAAA,gBAAvBE,QAAuB;AAC9BA,oBAAQ,CAAC7B,KAAT,CAAe,IAAf,EAAqBF,IAArB;AACH;AAJgD;AAAA;AAAA;AAAA;AAAA;AAKpD;;AACD,4DAAWE,KAAX,CAAiB,IAAjB,EAAuBF,IAAvB;AACH;AACD;AACJ;AACA;AACA;AACA;;;;wBACQxH,E,EAAI;AACJ,UAAMyD,IAAI,GAAG,IAAb;AACA,UAAI+F,IAAI,GAAG,KAAX;AACA,aAAO,YAAmB;AACtB;AACA,YAAIA,IAAJ,EACI;AACJA,YAAI,GAAG,IAAP;;AAJsB,2CAANhC,IAAM;AAANA,cAAM;AAAA;;AAKtBpI,aAAK,CAAC,gBAAD,EAAmBoI,IAAnB,CAAL;AACA/D,YAAI,CAACyB,MAAL,CAAY;AACR0C,cAAI,EAAEpH,kBAAkB,CAACqH,UAAnB,CAA8Bc,GAD5B;AAER3I,YAAE,EAAEA,EAFI;AAGRgF,cAAI,EAAEwC;AAHE,SAAZ;AAKH,OAXD;AAYH;AACD;AACJ;AACA;AACA;AACA;AACA;;;;0BACUtC,M,EAAQ;AACV,UAAMgE,GAAG,GAAG,KAAKjC,IAAL,CAAU/B,MAAM,CAAClF,EAAjB,CAAZ;;AACA,UAAI,eAAe,OAAOkJ,GAA1B,EAA+B;AAC3B9J,aAAK,CAAC,wBAAD,EAA2B8F,MAAM,CAAClF,EAAlC,EAAsCkF,MAAM,CAACF,IAA7C,CAAL;AACAkE,WAAG,CAACxB,KAAJ,CAAU,IAAV,EAAgBxC,MAAM,CAACF,IAAvB;AACA,eAAO,KAAKiC,IAAL,CAAU/B,MAAM,CAAClF,EAAjB,CAAP;AACH,OAJD,MAKK;AACDZ,aAAK,CAAC,YAAD,EAAe8F,MAAM,CAAClF,EAAtB,CAAL;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;;;;8BACcA,E,EAAI;AACVZ,WAAK,CAAC,6BAAD,EAAgCY,EAAhC,CAAL;AACA,WAAKA,EAAL,GAAUA,EAAV;AACA,WAAKmH,SAAL,GAAiB,IAAjB;AACA,WAAKC,YAAL,GAAoB,KAApB;;AACA,uEAAW,SAAX;;AACA,WAAKqC,YAAL;AACH;AACD;AACJ;AACA;AACA;AACA;;;;mCACmB;AAAA;;AACX,WAAK3C,aAAL,CAAmBlB,OAAnB,CAA2B,UAAC4B,IAAD;AAAA,eAAU,MAAI,CAAC2B,SAAL,CAAe3B,IAAf,CAAV;AAAA,OAA3B;AACA,WAAKV,aAAL,GAAqB,EAArB;AACA,WAAKC,UAAL,CAAgBnB,OAAhB,CAAwB,UAACV,MAAD;AAAA,eAAY,MAAI,CAACA,MAAL,CAAYA,MAAZ,CAAZ;AAAA,OAAxB;AACA,WAAK6B,UAAL,GAAkB,EAAlB;AACH;AACD;AACJ;AACA;AACA;AACA;;;;mCACmB;AACX3H,WAAK,CAAC,wBAAD,EAA2B,KAAK+F,GAAhC,CAAL;AACA,WAAKU,OAAL;AACA,WAAKf,OAAL,CAAa,sBAAb;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;;8BACc;AACN,UAAI,KAAK7D,IAAT,EAAe;AACX;AACA,aAAKA,IAAL,CAAU2E,OAAV,CAAkB,UAACpB,UAAD;AAAA,iBAAgBA,UAAU,EAA1B;AAAA,SAAlB;AACA,aAAKvD,IAAL,GAAYtB,SAAZ;AACH;;AACD,WAAKhB,EAAL,CAAQ,UAAR,EAAoB,IAApB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;;iCACiB;AACT,UAAI,KAAKwI,SAAT,EAAoB;AAChB/H,aAAK,CAAC,4BAAD,EAA+B,KAAK+F,GAApC,CAAL;AACA,aAAKD,MAAL,CAAY;AAAE0C,cAAI,EAAEpH,kBAAkB,CAACqH,UAAnB,CAA8BiB;AAAtC,SAAZ;AACH,OAJQ,CAKT;;;AACA,WAAKjD,OAAL;;AACA,UAAI,KAAKsB,SAAT,EAAoB;AAChB;AACA,aAAKrC,OAAL,CAAa,sBAAb;AACH;;AACD,aAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;;4BACY;AACJ,aAAO,KAAK4B,UAAL,EAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;;6BACaqB,S,EAAU;AACf,WAAKb,KAAL,CAAWa,QAAX,GAAsBA,SAAtB;AACA,aAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;;;AAKI;AACJ;AACA;AACA;AACA;AACA;AACA;0BACUwB,Q,EAAU;AACZ,WAAKH,aAAL,GAAqB,KAAKA,aAAL,IAAsB,EAA3C;;AACA,WAAKA,aAAL,CAAmB7E,IAAnB,CAAwBgF,QAAxB;;AACA,aAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;;+BACeA,Q,EAAU;AACjB,WAAKH,aAAL,GAAqB,KAAKA,aAAL,IAAsB,EAA3C;;AACA,WAAKA,aAAL,CAAmB3B,OAAnB,CAA2B8B,QAA3B;;AACA,aAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;;2BACWA,Q,EAAU;AACb,UAAI,CAAC,KAAKH,aAAV,EAAyB;AACrB,eAAO,IAAP;AACH;;AACD,UAAIG,QAAJ,EAAc;AACV,YAAMF,SAAS,GAAG,KAAKD,aAAvB;;AACA,aAAK,IAAI3D,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG4D,SAAS,CAAC7G,MAA9B,EAAsCiD,CAAC,EAAvC,EAA2C;AACvC,cAAI8D,QAAQ,KAAKF,SAAS,CAAC5D,CAAD,CAA1B,EAA+B;AAC3B4D,qBAAS,CAACK,MAAV,CAAiBjE,CAAjB,EAAoB,CAApB;AACA,mBAAO,IAAP;AACH;AACJ;AACJ,OARD,MASK;AACD,aAAK2D,aAAL,GAAqB,EAArB;AACH;;AACD,aAAO,IAAP;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;;mCACmB;AACX,aAAO,KAAKA,aAAL,IAAsB,EAA7B;AACH;;;wBAvYY;AACT,aAAO,CAAC,CAAC,KAAKnI,IAAd;AACH;;;wBAyUc;AACX,WAAKiG,KAAL,eAAsB,IAAtB;AACA,aAAO,IAAP;AACH;;;;EA7XgBtG,O;;AAwbrBpC,OAAO,CAACE,MAAR,GAAiBA,MAAjB,C;;;;;;;;;;;;AC5ca;;AACbJ,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA+B,YAA/B,EAA6C;AAAEC,OAAK,EAAE;AAAT,CAA7C;AACAD,OAAO,CAACqB,GAAR,GAAc,KAAK,CAAnB;;AACA,IAAM8J,QAAQ,GAAG5K,mBAAO,CAAC,kDAAD,CAAxB;;AACA,IAAMK,KAAK,GAAGL,mBAAO,CAAC,kDAAD,CAAP,CAAiB,sBAAjB,CAAd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASc,GAAT,CAAaJ,GAAb,EAAkC;AAAA,MAAhBK,IAAgB,uEAAT,EAAS;AAAA,MAAL8J,GAAK;AAC9B,MAAIxD,GAAG,GAAG3G,GAAV,CAD8B,CAE9B;;AACAmK,KAAG,GAAGA,GAAG,IAAK,OAAOC,QAAP,KAAoB,WAApB,IAAmCA,QAAjD;AACA,MAAI,QAAQpK,GAAZ,EACIA,GAAG,GAAGmK,GAAG,CAAC/K,QAAJ,GAAe,IAAf,GAAsB+K,GAAG,CAACE,IAAhC,CAL0B,CAM9B;;AACA,MAAI,OAAOrK,GAAP,KAAe,QAAnB,EAA6B;AACzB,QAAI,QAAQA,GAAG,CAACsK,MAAJ,CAAW,CAAX,CAAZ,EAA2B;AACvB,UAAI,QAAQtK,GAAG,CAACsK,MAAJ,CAAW,CAAX,CAAZ,EAA2B;AACvBtK,WAAG,GAAGmK,GAAG,CAAC/K,QAAJ,GAAeY,GAArB;AACH,OAFD,MAGK;AACDA,WAAG,GAAGmK,GAAG,CAACE,IAAJ,GAAWrK,GAAjB;AACH;AACJ;;AACD,QAAI,CAAC,sBAAsBuK,IAAtB,CAA2BvK,GAA3B,CAAL,EAAsC;AAClCL,WAAK,CAAC,sBAAD,EAAyBK,GAAzB,CAAL;;AACA,UAAI,gBAAgB,OAAOmK,GAA3B,EAAgC;AAC5BnK,WAAG,GAAGmK,GAAG,CAAC/K,QAAJ,GAAe,IAAf,GAAsBY,GAA5B;AACH,OAFD,MAGK;AACDA,WAAG,GAAG,aAAaA,GAAnB;AACH;AACJ,KAjBwB,CAkBzB;;;AACAL,SAAK,CAAC,UAAD,EAAaK,GAAb,CAAL;AACA2G,OAAG,GAAGuD,QAAQ,CAAClK,GAAD,CAAd;AACH,GA5B6B,CA6B9B;;;AACA,MAAI,CAAC2G,GAAG,CAAC6D,IAAT,EAAe;AACX,QAAI,cAAcD,IAAd,CAAmB5D,GAAG,CAACvH,QAAvB,CAAJ,EAAsC;AAClCuH,SAAG,CAAC6D,IAAJ,GAAW,IAAX;AACH,KAFD,MAGK,IAAI,eAAeD,IAAf,CAAoB5D,GAAG,CAACvH,QAAxB,CAAJ,EAAuC;AACxCuH,SAAG,CAAC6D,IAAJ,GAAW,KAAX;AACH;AACJ;;AACD7D,KAAG,CAACtG,IAAJ,GAAWsG,GAAG,CAACtG,IAAJ,IAAY,GAAvB;AACA,MAAMoK,IAAI,GAAG9D,GAAG,CAAC0D,IAAJ,CAASvG,OAAT,CAAiB,GAAjB,MAA0B,CAAC,CAAxC;AACA,MAAMuG,IAAI,GAAGI,IAAI,GAAG,MAAM9D,GAAG,CAAC0D,IAAV,GAAiB,GAApB,GAA0B1D,GAAG,CAAC0D,IAA/C,CAxC8B,CAyC9B;;AACA1D,KAAG,CAACpG,EAAJ,GAASoG,GAAG,CAACvH,QAAJ,GAAe,KAAf,GAAuBiL,IAAvB,GAA8B,GAA9B,GAAoC1D,GAAG,CAAC6D,IAAxC,GAA+CnK,IAAxD,CA1C8B,CA2C9B;;AACAsG,KAAG,CAAC+D,IAAJ,GACI/D,GAAG,CAACvH,QAAJ,GACI,KADJ,GAEIiL,IAFJ,IAGKF,GAAG,IAAIA,GAAG,CAACK,IAAJ,KAAa7D,GAAG,CAAC6D,IAAxB,GAA+B,EAA/B,GAAoC,MAAM7D,GAAG,CAAC6D,IAHnD,CADJ;AAKA,SAAO7D,GAAP;AACH;;AACD5H,OAAO,CAACqB,GAAR,GAAcA,GAAd,C;;;;;;;;;;;AChEA;AACA;AACA;AAEAR,MAAM,CAACb,OAAP,GAAiBuC,OAAjB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,OAAT,CAAiBrB,IAAjB,EAAuB;AACrBA,MAAI,GAAGA,IAAI,IAAI,EAAf;AACA,OAAK0K,EAAL,GAAU1K,IAAI,CAAC+B,GAAL,IAAY,GAAtB;AACA,OAAKC,GAAL,GAAWhC,IAAI,CAACgC,GAAL,IAAY,KAAvB;AACA,OAAK2I,MAAL,GAAc3K,IAAI,CAAC2K,MAAL,IAAe,CAA7B;AACA,OAAK1I,MAAL,GAAcjC,IAAI,CAACiC,MAAL,GAAc,CAAd,IAAmBjC,IAAI,CAACiC,MAAL,IAAe,CAAlC,GAAsCjC,IAAI,CAACiC,MAA3C,GAAoD,CAAlE;AACA,OAAKyB,QAAL,GAAgB,CAAhB;AACD;AAED;AACA;AACA;AACA;AACA;AACA;;;AAEArC,OAAO,CAACuJ,SAAR,CAAkBrE,QAAlB,GAA6B,YAAU;AACrC,MAAImE,EAAE,GAAG,KAAKA,EAAL,GAAUG,IAAI,CAACC,GAAL,CAAS,KAAKH,MAAd,EAAsB,KAAKjH,QAAL,EAAtB,CAAnB;;AACA,MAAI,KAAKzB,MAAT,EAAiB;AACf,QAAI8I,IAAI,GAAIF,IAAI,CAACG,MAAL,EAAZ;AACA,QAAIC,SAAS,GAAGJ,IAAI,CAACK,KAAL,CAAWH,IAAI,GAAG,KAAK9I,MAAZ,GAAqByI,EAAhC,CAAhB;AACAA,MAAE,GAAG,CAACG,IAAI,CAACK,KAAL,CAAWH,IAAI,GAAG,EAAlB,IAAwB,CAAzB,KAA+B,CAA/B,GAAoCL,EAAE,GAAGO,SAAzC,GAAqDP,EAAE,GAAGO,SAA/D;AACD;;AACD,SAAOJ,IAAI,CAAC9I,GAAL,CAAS2I,EAAT,EAAa,KAAK1I,GAAlB,IAAyB,CAAhC;AACD,CARD;AAUA;AACA;AACA;AACA;AACA;;;AAEAX,OAAO,CAACuJ,SAAR,CAAkBxE,KAAlB,GAA0B,YAAU;AAClC,OAAK1C,QAAL,GAAgB,CAAhB;AACD,CAFD;AAIA;AACA;AACA;AACA;AACA;;;AAEArC,OAAO,CAACuJ,SAAR,CAAkBzH,MAAlB,GAA2B,UAASpB,GAAT,EAAa;AACtC,OAAK2I,EAAL,GAAU3I,GAAV;AACD,CAFD;AAIA;AACA;AACA;AACA;AACA;;;AAEAV,OAAO,CAACuJ,SAAR,CAAkBrH,MAAlB,GAA2B,UAASvB,GAAT,EAAa;AACtC,OAAKA,GAAL,GAAWA,GAAX;AACD,CAFD;AAIA;AACA;AACA;AACA;AACA;;;AAEAX,OAAO,CAACuJ,SAAR,CAAkBvH,SAAlB,GAA8B,UAASpB,MAAT,EAAgB;AAC5C,OAAKA,MAAL,GAAcA,MAAd;AACD,CAFD,C;;;;;;;;;;;AChFA;AACA;AACA;AAEA,IAAI,IAAJ,EAAmC;AACjCtC,QAAM,CAACb,OAAP,GAAiBoC,OAAjB;AACD;AAED;AACA;AACA;AACA;AACA;;;AAEA,SAASA,OAAT,CAAiBwF,GAAjB,EAAsB;AACpB,MAAIA,GAAJ,EAAS,OAAOyE,KAAK,CAACzE,GAAD,CAAZ;AACV;;AAAA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASyE,KAAT,CAAezE,GAAf,EAAoB;AAClB,OAAK,IAAI0E,GAAT,IAAgBlK,OAAO,CAAC0J,SAAxB,EAAmC;AACjClE,OAAG,CAAC0E,GAAD,CAAH,GAAWlK,OAAO,CAAC0J,SAAR,CAAkBQ,GAAlB,CAAX;AACD;;AACD,SAAO1E,GAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEAxF,OAAO,CAAC0J,SAAR,CAAkB1G,EAAlB,GACAhD,OAAO,CAAC0J,SAAR,CAAkBS,gBAAlB,GAAqC,UAASC,KAAT,EAAgB1H,EAAhB,EAAmB;AACtD,OAAK2H,UAAL,GAAkB,KAAKA,UAAL,IAAmB,EAArC;AACA,GAAC,KAAKA,UAAL,CAAgB,MAAMD,KAAtB,IAA+B,KAAKC,UAAL,CAAgB,MAAMD,KAAtB,KAAgC,EAAhE,EACGzG,IADH,CACQjB,EADR;AAEA,SAAO,IAAP;AACD,CAND;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEA1C,OAAO,CAAC0J,SAAR,CAAkBY,IAAlB,GAAyB,UAASF,KAAT,EAAgB1H,EAAhB,EAAmB;AAC1C,WAASM,EAAT,GAAc;AACZ,SAAK0C,GAAL,CAAS0E,KAAT,EAAgBpH,EAAhB;AACAN,MAAE,CAACoE,KAAH,CAAS,IAAT,EAAenF,SAAf;AACD;;AAEDqB,IAAE,CAACN,EAAH,GAAQA,EAAR;AACA,OAAKM,EAAL,CAAQoH,KAAR,EAAepH,EAAf;AACA,SAAO,IAAP;AACD,CATD;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEAhD,OAAO,CAAC0J,SAAR,CAAkBhE,GAAlB,GACA1F,OAAO,CAAC0J,SAAR,CAAkBzD,cAAlB,GACAjG,OAAO,CAAC0J,SAAR,CAAkBa,kBAAlB,GACAvK,OAAO,CAAC0J,SAAR,CAAkBc,mBAAlB,GAAwC,UAASJ,KAAT,EAAgB1H,EAAhB,EAAmB;AACzD,OAAK2H,UAAL,GAAkB,KAAKA,UAAL,IAAmB,EAArC,CADyD,CAGzD;;AACA,MAAI,KAAK1I,SAAS,CAACC,MAAnB,EAA2B;AACzB,SAAKyI,UAAL,GAAkB,EAAlB;AACA,WAAO,IAAP;AACD,GAPwD,CASzD;;;AACA,MAAII,SAAS,GAAG,KAAKJ,UAAL,CAAgB,MAAMD,KAAtB,CAAhB;AACA,MAAI,CAACK,SAAL,EAAgB,OAAO,IAAP,CAXyC,CAazD;;AACA,MAAI,KAAK9I,SAAS,CAACC,MAAnB,EAA2B;AACzB,WAAO,KAAKyI,UAAL,CAAgB,MAAMD,KAAtB,CAAP;AACA,WAAO,IAAP;AACD,GAjBwD,CAmBzD;;;AACA,MAAIM,EAAJ;;AACA,OAAK,IAAI7F,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG4F,SAAS,CAAC7I,MAA9B,EAAsCiD,CAAC,EAAvC,EAA2C;AACzC6F,MAAE,GAAGD,SAAS,CAAC5F,CAAD,CAAd;;AACA,QAAI6F,EAAE,KAAKhI,EAAP,IAAagI,EAAE,CAAChI,EAAH,KAAUA,EAA3B,EAA+B;AAC7B+H,eAAS,CAAC3B,MAAV,CAAiBjE,CAAjB,EAAoB,CAApB;AACA;AACD;AACF,GA3BwD,CA6BzD;AACA;;;AACA,MAAI4F,SAAS,CAAC7I,MAAV,KAAqB,CAAzB,EAA4B;AAC1B,WAAO,KAAKyI,UAAL,CAAgB,MAAMD,KAAtB,CAAP;AACD;;AAED,SAAO,IAAP;AACD,CAvCD;AAyCA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEApK,OAAO,CAAC0J,SAAR,CAAkBjG,IAAlB,GAAyB,UAAS2G,KAAT,EAAe;AACtC,OAAKC,UAAL,GAAkB,KAAKA,UAAL,IAAmB,EAArC;AAEA,MAAIzD,IAAI,GAAG,IAAI+D,KAAJ,CAAUhJ,SAAS,CAACC,MAAV,GAAmB,CAA7B,CAAX;AAAA,MACI6I,SAAS,GAAG,KAAKJ,UAAL,CAAgB,MAAMD,KAAtB,CADhB;;AAGA,OAAK,IAAIvF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGlD,SAAS,CAACC,MAA9B,EAAsCiD,CAAC,EAAvC,EAA2C;AACzC+B,QAAI,CAAC/B,CAAC,GAAG,CAAL,CAAJ,GAAclD,SAAS,CAACkD,CAAD,CAAvB;AACD;;AAED,MAAI4F,SAAJ,EAAe;AACbA,aAAS,GAAGA,SAAS,CAAC/B,KAAV,CAAgB,CAAhB,CAAZ;;AACA,SAAK,IAAI7D,CAAC,GAAG,CAAR,EAAW+F,GAAG,GAAGH,SAAS,CAAC7I,MAAhC,EAAwCiD,CAAC,GAAG+F,GAA5C,EAAiD,EAAE/F,CAAnD,EAAsD;AACpD4F,eAAS,CAAC5F,CAAD,CAAT,CAAaiC,KAAb,CAAmB,IAAnB,EAAyBF,IAAzB;AACD;AACF;;AAED,SAAO,IAAP;AACD,CAlBD;AAoBA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEA5G,OAAO,CAAC0J,SAAR,CAAkBjB,SAAlB,GAA8B,UAAS2B,KAAT,EAAe;AAC3C,OAAKC,UAAL,GAAkB,KAAKA,UAAL,IAAmB,EAArC;AACA,SAAO,KAAKA,UAAL,CAAgB,MAAMD,KAAtB,KAAgC,EAAvC;AACD,CAHD;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEApK,OAAO,CAAC0J,SAAR,CAAkBmB,YAAlB,GAAiC,UAAST,KAAT,EAAe;AAC9C,SAAO,CAAC,CAAE,KAAK3B,SAAL,CAAe2B,KAAf,EAAsBxI,MAAhC;AACD,CAFD,C;;;;;;;;;;;AC5KA;;AAEA;AACA;AACA;AAEAhE,OAAO,CAACkN,UAAR,GAAqBA,UAArB;AACAlN,OAAO,CAACmN,IAAR,GAAeA,IAAf;AACAnN,OAAO,CAACoN,IAAR,GAAeA,IAAf;AACApN,OAAO,CAACqN,SAAR,GAAoBA,SAApB;AACArN,OAAO,CAACsN,OAAR,GAAkBC,YAAY,EAA9B;;AACAvN,OAAO,CAACqH,OAAR,GAAmB,YAAM;AACxB,MAAImG,MAAM,GAAG,KAAb;AAEA,SAAO,YAAM;AACZ,QAAI,CAACA,MAAL,EAAa;AACZA,YAAM,GAAG,IAAT;AACAC,aAAO,CAACC,IAAR,CAAa,uIAAb;AACA;AACD,GALD;AAMA,CATiB,EAAlB;AAWA;AACA;AACA;;;AAEA1N,OAAO,CAAC2N,MAAR,GAAiB,CAChB,SADgB,EAEhB,SAFgB,EAGhB,SAHgB,EAIhB,SAJgB,EAKhB,SALgB,EAMhB,SANgB,EAOhB,SAPgB,EAQhB,SARgB,EAShB,SATgB,EAUhB,SAVgB,EAWhB,SAXgB,EAYhB,SAZgB,EAahB,SAbgB,EAchB,SAdgB,EAehB,SAfgB,EAgBhB,SAhBgB,EAiBhB,SAjBgB,EAkBhB,SAlBgB,EAmBhB,SAnBgB,EAoBhB,SApBgB,EAqBhB,SArBgB,EAsBhB,SAtBgB,EAuBhB,SAvBgB,EAwBhB,SAxBgB,EAyBhB,SAzBgB,EA0BhB,SA1BgB,EA2BhB,SA3BgB,EA4BhB,SA5BgB,EA6BhB,SA7BgB,EA8BhB,SA9BgB,EA+BhB,SA/BgB,EAgChB,SAhCgB,EAiChB,SAjCgB,EAkChB,SAlCgB,EAmChB,SAnCgB,EAoChB,SApCgB,EAqChB,SArCgB,EAsChB,SAtCgB,EAuChB,SAvCgB,EAwChB,SAxCgB,EAyChB,SAzCgB,EA0ChB,SA1CgB,EA2ChB,SA3CgB,EA4ChB,SA5CgB,EA6ChB,SA7CgB,EA8ChB,SA9CgB,EA+ChB,SA/CgB,EAgDhB,SAhDgB,EAiDhB,SAjDgB,EAkDhB,SAlDgB,EAmDhB,SAnDgB,EAoDhB,SApDgB,EAqDhB,SArDgB,EAsDhB,SAtDgB,EAuDhB,SAvDgB,EAwDhB,SAxDgB,EAyDhB,SAzDgB,EA0DhB,SA1DgB,EA2DhB,SA3DgB,EA4DhB,SA5DgB,EA6DhB,SA7DgB,EA8DhB,SA9DgB,EA+DhB,SA/DgB,EAgEhB,SAhEgB,EAiEhB,SAjEgB,EAkEhB,SAlEgB,EAmEhB,SAnEgB,EAoEhB,SApEgB,EAqEhB,SArEgB,EAsEhB,SAtEgB,EAuEhB,SAvEgB,EAwEhB,SAxEgB,EAyEhB,SAzEgB,EA0EhB,SA1EgB,EA2EhB,SA3EgB,EA4EhB,SA5EgB,CAAjB;AA+EA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;AACA,SAASN,SAAT,GAAqB;AACpB;AACA;AACA;AACA,MAAI,OAAOO,MAAP,KAAkB,WAAlB,IAAiCA,MAAM,CAACC,OAAxC,KAAoDD,MAAM,CAACC,OAAP,CAAezE,IAAf,KAAwB,UAAxB,IAAsCwE,MAAM,CAACC,OAAP,CAAeC,MAAzG,CAAJ,EAAsH;AACrH,WAAO,IAAP;AACA,GANmB,CAQpB;;;AACA,MAAI,OAAOC,SAAP,KAAqB,WAArB,IAAoCA,SAAS,CAACC,SAA9C,IAA2DD,SAAS,CAACC,SAAV,CAAoBC,WAApB,GAAkCC,KAAlC,CAAwC,uBAAxC,CAA/D,EAAiI;AAChI,WAAO,KAAP;AACA,GAXmB,CAapB;AACA;;;AACA,SAAQ,OAAOC,QAAP,KAAoB,WAApB,IAAmCA,QAAQ,CAACC,eAA5C,IAA+DD,QAAQ,CAACC,eAAT,CAAyBC,KAAxF,IAAiGF,QAAQ,CAACC,eAAT,CAAyBC,KAAzB,CAA+BC,gBAAjI,IACN;AACC,SAAOV,MAAP,KAAkB,WAAlB,IAAiCA,MAAM,CAACH,OAAxC,KAAoDG,MAAM,CAACH,OAAP,CAAec,OAAf,IAA2BX,MAAM,CAACH,OAAP,CAAee,SAAf,IAA4BZ,MAAM,CAACH,OAAP,CAAegB,KAA1H,CAFK,IAGN;AACA;AACC,SAAOV,SAAP,KAAqB,WAArB,IAAoCA,SAAS,CAACC,SAA9C,IAA2DD,SAAS,CAACC,SAAV,CAAoBC,WAApB,GAAkCC,KAAlC,CAAwC,gBAAxC,CAA3D,IAAwHQ,QAAQ,CAACC,MAAM,CAACC,EAAR,EAAY,EAAZ,CAAR,IAA2B,EAL9I,IAMN;AACC,SAAOb,SAAP,KAAqB,WAArB,IAAoCA,SAAS,CAACC,SAA9C,IAA2DD,SAAS,CAACC,SAAV,CAAoBC,WAApB,GAAkCC,KAAlC,CAAwC,oBAAxC,CAP7D;AAQA;AAED;AACA;AACA;AACA;AACA;;;AAEA,SAAShB,UAAT,CAAoBlE,IAApB,EAA0B;AACzBA,MAAI,CAAC,CAAD,CAAJ,GAAU,CAAC,KAAKqE,SAAL,GAAiB,IAAjB,GAAwB,EAAzB,IACT,KAAKwB,SADI,IAER,KAAKxB,SAAL,GAAiB,KAAjB,GAAyB,GAFjB,IAGTrE,IAAI,CAAC,CAAD,CAHK,IAIR,KAAKqE,SAAL,GAAiB,KAAjB,GAAyB,GAJjB,IAKT,GALS,GAKHxM,MAAM,CAACb,OAAP,CAAe8O,QAAf,CAAwB,KAAKC,IAA7B,CALP;;AAOA,MAAI,CAAC,KAAK1B,SAAV,EAAqB;AACpB;AACA;;AAED,MAAM2B,CAAC,GAAG,YAAY,KAAKC,KAA3B;AACAjG,MAAI,CAACkC,MAAL,CAAY,CAAZ,EAAe,CAAf,EAAkB8D,CAAlB,EAAqB,gBAArB,EAbyB,CAezB;AACA;AACA;;AACA,MAAIE,KAAK,GAAG,CAAZ;AACA,MAAIC,KAAK,GAAG,CAAZ;AACAnG,MAAI,CAAC,CAAD,CAAJ,CAAQoG,OAAR,CAAgB,aAAhB,EAA+B,UAAAlB,KAAK,EAAI;AACvC,QAAIA,KAAK,KAAK,IAAd,EAAoB;AACnB;AACA;;AACDgB,SAAK;;AACL,QAAIhB,KAAK,KAAK,IAAd,EAAoB;AACnB;AACA;AACAiB,WAAK,GAAGD,KAAR;AACA;AACD,GAVD;AAYAlG,MAAI,CAACkC,MAAL,CAAYiE,KAAZ,EAAmB,CAAnB,EAAsBH,CAAtB;AACA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAhP,OAAO,CAACqP,GAAR,GAAc5B,OAAO,CAAC7M,KAAR,IAAiB6M,OAAO,CAAC4B,GAAzB,IAAiC,YAAM,CAAE,CAAvD;AAEA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASlC,IAAT,CAAcmC,UAAd,EAA0B;AACzB,MAAI;AACH,QAAIA,UAAJ,EAAgB;AACftP,aAAO,CAACsN,OAAR,CAAgBiC,OAAhB,CAAwB,OAAxB,EAAiCD,UAAjC;AACA,KAFD,MAEO;AACNtP,aAAO,CAACsN,OAAR,CAAgBkC,UAAhB,CAA2B,OAA3B;AACA;AACD,GAND,CAME,OAAOC,KAAP,EAAc,CACf;AACA;AACA;AACD;AAED;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASrC,IAAT,GAAgB;AACf,MAAIsC,CAAJ;;AACA,MAAI;AACHA,KAAC,GAAG1P,OAAO,CAACsN,OAAR,CAAgBqC,OAAhB,CAAwB,OAAxB,CAAJ;AACA,GAFD,CAEE,OAAOF,KAAP,EAAc,CACf;AACA;AACA,GAPc,CASf;;;AACA,MAAI,CAACC,CAAD,IAAM,OAAO7B,OAAP,KAAmB,WAAzB,IAAwC,SAASA,OAArD,EAA8D;AAC7D6B,KAAC,GAAG7B,OAAO,CAAC+B,GAAR,CAAYC,KAAhB;AACA;;AAED,SAAOH,CAAP;AACA;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEA,SAASnC,YAAT,GAAwB;AACvB,MAAI;AACH;AACA;AACA,WAAOuC,YAAP;AACA,GAJD,CAIE,OAAOL,KAAP,EAAc,CACf;AACA;AACA;AACD;;AAED5O,MAAM,CAACb,OAAP,GAAiBO,mBAAO,CAAC,oDAAD,CAAP,CAAoBP,OAApB,CAAjB;IAEO+P,U,GAAclP,MAAM,CAACb,O,CAArB+P,U;AAEP;AACA;AACA;;AAEAA,UAAU,CAACC,CAAX,GAAe,UAAUlM,CAAV,EAAa;AAC3B,MAAI;AACH,WAAOmM,IAAI,CAACC,SAAL,CAAepM,CAAf,CAAP;AACA,GAFD,CAEE,OAAO2L,KAAP,EAAc;AACf,WAAO,iCAAiCA,KAAK,CAAChF,OAA9C;AACA;AACD,CAND,C;;;;;;;;;;;;;;;;;;;;;;;ACrQA;AACA;AACA;AACA;AAEA,SAAS0F,KAAT,CAAeP,GAAf,EAAoB;AACnBQ,aAAW,CAACxP,KAAZ,GAAoBwP,WAApB;AACAA,aAAW,WAAX,GAAsBA,WAAtB;AACAA,aAAW,CAACC,MAAZ,GAAqBA,MAArB;AACAD,aAAW,CAACE,OAAZ,GAAsBA,OAAtB;AACAF,aAAW,CAACG,MAAZ,GAAqBA,MAArB;AACAH,aAAW,CAACI,OAAZ,GAAsBA,OAAtB;AACAJ,aAAW,CAACtB,QAAZ,GAAuBvO,mBAAO,CAAC,sCAAD,CAA9B;AACA6P,aAAW,CAAC/I,OAAZ,GAAsBA,OAAtB;AAEAvH,QAAM,CAAC8G,IAAP,CAAYgJ,GAAZ,EAAiBxI,OAAjB,CAAyB,UAAAkF,GAAG,EAAI;AAC/B8D,eAAW,CAAC9D,GAAD,CAAX,GAAmBsD,GAAG,CAACtD,GAAD,CAAtB;AACA,GAFD;AAIA;AACD;AACA;;AAEC8D,aAAW,CAACK,KAAZ,GAAoB,EAApB;AACAL,aAAW,CAACM,KAAZ,GAAoB,EAApB;AAEA;AACD;AACA;AACA;AACA;;AACCN,aAAW,CAACL,UAAZ,GAAyB,EAAzB;AAEA;AACD;AACA;AACA;AACA;AACA;;AACC,WAASY,WAAT,CAAqB9B,SAArB,EAAgC;AAC/B,QAAI+B,IAAI,GAAG,CAAX;;AAEA,SAAK,IAAI3J,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG4H,SAAS,CAAC7K,MAA9B,EAAsCiD,CAAC,EAAvC,EAA2C;AAC1C2J,UAAI,GAAI,CAACA,IAAI,IAAI,CAAT,IAAcA,IAAf,GAAuB/B,SAAS,CAACgC,UAAV,CAAqB5J,CAArB,CAA9B;AACA2J,UAAI,IAAI,CAAR,CAF0C,CAE/B;AACX;;AAED,WAAOR,WAAW,CAACzC,MAAZ,CAAmB5B,IAAI,CAAC+E,GAAL,CAASF,IAAT,IAAiBR,WAAW,CAACzC,MAAZ,CAAmB3J,MAAvD,CAAP;AACA;;AACDoM,aAAW,CAACO,WAAZ,GAA0BA,WAA1B;AAEA;AACD;AACA;AACA;AACA;AACA;AACA;;AACC,WAASP,WAAT,CAAqBvB,SAArB,EAAgC;AAC/B,QAAIkC,QAAJ;AACA,QAAIC,cAAc,GAAG,IAArB;;AAEA,aAASpQ,KAAT,GAAwB;AAAA,wCAANoI,IAAM;AAANA,YAAM;AAAA;;AACvB;AACA,UAAI,CAACpI,KAAK,CAAC4P,OAAX,EAAoB;AACnB;AACA;;AAED,UAAMvL,IAAI,GAAGrE,KAAb,CANuB,CAQvB;;AACA,UAAMqQ,IAAI,GAAGC,MAAM,CAAC,IAAIC,IAAJ,EAAD,CAAnB;AACA,UAAMvF,EAAE,GAAGqF,IAAI,IAAIF,QAAQ,IAAIE,IAAhB,CAAf;AACAhM,UAAI,CAAC8J,IAAL,GAAYnD,EAAZ;AACA3G,UAAI,CAACmM,IAAL,GAAYL,QAAZ;AACA9L,UAAI,CAACgM,IAAL,GAAYA,IAAZ;AACAF,cAAQ,GAAGE,IAAX;AAEAjI,UAAI,CAAC,CAAD,CAAJ,GAAUoH,WAAW,CAACC,MAAZ,CAAmBrH,IAAI,CAAC,CAAD,CAAvB,CAAV;;AAEA,UAAI,OAAOA,IAAI,CAAC,CAAD,CAAX,KAAmB,QAAvB,EAAiC;AAChC;AACAA,YAAI,CAACC,OAAL,CAAa,IAAb;AACA,OArBsB,CAuBvB;;;AACA,UAAIiG,KAAK,GAAG,CAAZ;AACAlG,UAAI,CAAC,CAAD,CAAJ,GAAUA,IAAI,CAAC,CAAD,CAAJ,CAAQoG,OAAR,CAAgB,eAAhB,EAAiC,UAAClB,KAAD,EAAQmD,MAAR,EAAmB;AAC7D;AACA,YAAInD,KAAK,KAAK,IAAd,EAAoB;AACnB,iBAAO,GAAP;AACA;;AACDgB,aAAK;AACL,YAAMoC,SAAS,GAAGlB,WAAW,CAACL,UAAZ,CAAuBsB,MAAvB,CAAlB;;AACA,YAAI,OAAOC,SAAP,KAAqB,UAAzB,EAAqC;AACpC,cAAMC,GAAG,GAAGvI,IAAI,CAACkG,KAAD,CAAhB;AACAhB,eAAK,GAAGoD,SAAS,CAACE,IAAV,CAAevM,IAAf,EAAqBsM,GAArB,CAAR,CAFoC,CAIpC;;AACAvI,cAAI,CAACkC,MAAL,CAAYgE,KAAZ,EAAmB,CAAnB;AACAA,eAAK;AACL;;AACD,eAAOhB,KAAP;AACA,OAhBS,CAAV,CAzBuB,CA2CvB;;AACAkC,iBAAW,CAAClD,UAAZ,CAAuBsE,IAAvB,CAA4BvM,IAA5B,EAAkC+D,IAAlC;AAEA,UAAMyI,KAAK,GAAGxM,IAAI,CAACoK,GAAL,IAAYe,WAAW,CAACf,GAAtC;AACAoC,WAAK,CAACvI,KAAN,CAAYjE,IAAZ,EAAkB+D,IAAlB;AACA;;AAEDpI,SAAK,CAACiO,SAAN,GAAkBA,SAAlB;AACAjO,SAAK,CAACyM,SAAN,GAAkB+C,WAAW,CAAC/C,SAAZ,EAAlB;AACAzM,SAAK,CAACqO,KAAN,GAAcmB,WAAW,CAACO,WAAZ,CAAwB9B,SAAxB,CAAd;AACAjO,SAAK,CAAC8Q,MAAN,GAAeA,MAAf;AACA9Q,SAAK,CAACyG,OAAN,GAAgB+I,WAAW,CAAC/I,OAA5B,CA1D+B,CA0DM;;AAErCvH,UAAM,CAACC,cAAP,CAAsBa,KAAtB,EAA6B,SAA7B,EAAwC;AACvCF,gBAAU,EAAE,IAD2B;AAEvCiR,kBAAY,EAAE,KAFyB;AAGvChR,SAAG,EAAE;AAAA,eAAMqQ,cAAc,KAAK,IAAnB,GAA0BZ,WAAW,CAACI,OAAZ,CAAoB3B,SAApB,CAA1B,GAA2DmC,cAAjE;AAAA,OAHkC;AAIvCY,SAAG,EAAE,aAAA9N,CAAC,EAAI;AACTkN,sBAAc,GAAGlN,CAAjB;AACA;AANsC,KAAxC,EA5D+B,CAqE/B;;AACA,QAAI,OAAOsM,WAAW,CAACyB,IAAnB,KAA4B,UAAhC,EAA4C;AAC3CzB,iBAAW,CAACyB,IAAZ,CAAiBjR,KAAjB;AACA;;AAED,WAAOA,KAAP;AACA;;AAED,WAAS8Q,MAAT,CAAgB7C,SAAhB,EAA2BiD,SAA3B,EAAsC;AACrC,QAAMC,QAAQ,GAAG3B,WAAW,CAAC,KAAKvB,SAAL,IAAkB,OAAOiD,SAAP,KAAqB,WAArB,GAAmC,GAAnC,GAAyCA,SAA3D,IAAwEjD,SAAzE,CAA5B;AACAkD,YAAQ,CAAC1C,GAAT,GAAe,KAAKA,GAApB;AACA,WAAO0C,QAAP;AACA;AAED;AACD;AACA;AACA;AACA;AACA;AACA;;;AACC,WAASxB,MAAT,CAAgBjB,UAAhB,EAA4B;AAC3Bc,eAAW,CAACjD,IAAZ,CAAiBmC,UAAjB;AAEAc,eAAW,CAACK,KAAZ,GAAoB,EAApB;AACAL,eAAW,CAACM,KAAZ,GAAoB,EAApB;AAEA,QAAIzJ,CAAJ;AACA,QAAM+K,KAAK,GAAG,CAAC,OAAO1C,UAAP,KAAsB,QAAtB,GAAiCA,UAAjC,GAA8C,EAA/C,EAAmD0C,KAAnD,CAAyD,QAAzD,CAAd;AACA,QAAMhF,GAAG,GAAGgF,KAAK,CAAChO,MAAlB;;AAEA,SAAKiD,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG+F,GAAhB,EAAqB/F,CAAC,EAAtB,EAA0B;AACzB,UAAI,CAAC+K,KAAK,CAAC/K,CAAD,CAAV,EAAe;AACd;AACA;AACA;;AAEDqI,gBAAU,GAAG0C,KAAK,CAAC/K,CAAD,CAAL,CAASmI,OAAT,CAAiB,KAAjB,EAAwB,KAAxB,CAAb;;AAEA,UAAIE,UAAU,CAAC,CAAD,CAAV,KAAkB,GAAtB,EAA2B;AAC1Bc,mBAAW,CAACM,KAAZ,CAAkB3K,IAAlB,CAAuB,IAAI4I,MAAJ,CAAW,MAAMW,UAAU,CAAC2C,MAAX,CAAkB,CAAlB,CAAN,GAA6B,GAAxC,CAAvB;AACA,OAFD,MAEO;AACN7B,mBAAW,CAACK,KAAZ,CAAkB1K,IAAlB,CAAuB,IAAI4I,MAAJ,CAAW,MAAMW,UAAN,GAAmB,GAA9B,CAAvB;AACA;AACD;AACD;AAED;AACD;AACA;AACA;AACA;AACA;;;AACC,WAASgB,OAAT,GAAmB;AAClB,QAAMhB,UAAU,GAAG,6BACfc,WAAW,CAACK,KAAZ,CAAkByB,GAAlB,CAAsBC,WAAtB,CADe,sBAEf/B,WAAW,CAACM,KAAZ,CAAkBwB,GAAlB,CAAsBC,WAAtB,EAAmCD,GAAnC,CAAuC,UAAArD,SAAS;AAAA,aAAI,MAAMA,SAAV;AAAA,KAAhD,CAFe,GAGjBuD,IAHiB,CAGZ,GAHY,CAAnB;AAIAhC,eAAW,CAACG,MAAZ,CAAmB,EAAnB;AACA,WAAOjB,UAAP;AACA;AAED;AACD;AACA;AACA;AACA;AACA;AACA;;;AACC,WAASkB,OAAT,CAAiB6B,IAAjB,EAAuB;AACtB,QAAIA,IAAI,CAACA,IAAI,CAACrO,MAAL,GAAc,CAAf,CAAJ,KAA0B,GAA9B,EAAmC;AAClC,aAAO,IAAP;AACA;;AAED,QAAIiD,CAAJ;AACA,QAAI+F,GAAJ;;AAEA,SAAK/F,CAAC,GAAG,CAAJ,EAAO+F,GAAG,GAAGoD,WAAW,CAACM,KAAZ,CAAkB1M,MAApC,EAA4CiD,CAAC,GAAG+F,GAAhD,EAAqD/F,CAAC,EAAtD,EAA0D;AACzD,UAAImJ,WAAW,CAACM,KAAZ,CAAkBzJ,CAAlB,EAAqBuE,IAArB,CAA0B6G,IAA1B,CAAJ,EAAqC;AACpC,eAAO,KAAP;AACA;AACD;;AAED,SAAKpL,CAAC,GAAG,CAAJ,EAAO+F,GAAG,GAAGoD,WAAW,CAACK,KAAZ,CAAkBzM,MAApC,EAA4CiD,CAAC,GAAG+F,GAAhD,EAAqD/F,CAAC,EAAtD,EAA0D;AACzD,UAAImJ,WAAW,CAACK,KAAZ,CAAkBxJ,CAAlB,EAAqBuE,IAArB,CAA0B6G,IAA1B,CAAJ,EAAqC;AACpC,eAAO,IAAP;AACA;AACD;;AAED,WAAO,KAAP;AACA;AAED;AACD;AACA;AACA;AACA;AACA;AACA;;;AACC,WAASF,WAAT,CAAqBG,MAArB,EAA6B;AAC5B,WAAOA,MAAM,CAACC,QAAP,GACLC,SADK,CACK,CADL,EACQF,MAAM,CAACC,QAAP,GAAkBvO,MAAlB,GAA2B,CADnC,EAELoL,OAFK,CAEG,SAFH,EAEc,GAFd,CAAP;AAGA;AAED;AACD;AACA;AACA;AACA;AACA;AACA;;;AACC,WAASiB,MAAT,CAAgBkB,GAAhB,EAAqB;AACpB,QAAIA,GAAG,YAAYzL,KAAnB,EAA0B;AACzB,aAAOyL,GAAG,CAACkB,KAAJ,IAAalB,GAAG,CAAC9G,OAAxB;AACA;;AACD,WAAO8G,GAAP;AACA;AAED;AACD;AACA;AACA;;;AACC,WAASlK,OAAT,GAAmB;AAClBoG,WAAO,CAACC,IAAR,CAAa,uIAAb;AACA;;AAED0C,aAAW,CAACG,MAAZ,CAAmBH,WAAW,CAAChD,IAAZ,EAAnB;AAEA,SAAOgD,WAAP;AACA;;AAEDvP,MAAM,CAACb,OAAP,GAAiBmQ,KAAjB,C;;;;;;;;;;;ACpQAtP,MAAM,CAACb,OAAP,GAAkB,YAAM;AACtB,MAAI,OAAOiF,IAAP,KAAgB,WAApB,EAAiC;AAC/B,WAAOA,IAAP;AACD,GAFD,MAEO,IAAI,OAAO2I,MAAP,KAAkB,WAAtB,EAAmC;AACxC,WAAOA,MAAP;AACD,GAFM,MAEA;AACL,WAAO8E,QAAQ,CAAC,aAAD,CAAR,EAAP;AACD;AACF,CARgB,EAAjB,C;;;;;;;;;;;ACAA,IAAMxS,MAAM,GAAGK,mBAAO,CAAC,+DAAD,CAAtB;;AAEAM,MAAM,CAACb,OAAP,GAAiB,UAACiB,GAAD,EAAMC,IAAN;AAAA,SAAe,IAAIhB,MAAJ,CAAWe,GAAX,EAAgBC,IAAhB,CAAf;AAAA,CAAjB;AAEA;AACA;AACA;AACA;;;AAEAL,MAAM,CAACb,OAAP,CAAeE,MAAf,GAAwBA,MAAxB;AACAW,MAAM,CAACb,OAAP,CAAeK,QAAf,GAA0BH,MAAM,CAACG,QAAjC,C,CAA2C;;AAC3CQ,MAAM,CAACb,OAAP,CAAe2S,SAAf,GAA2BpS,mBAAO,CAAC,qEAAD,CAAlC;AACAM,MAAM,CAACb,OAAP,CAAe4S,UAAf,GAA4BrS,mBAAO,CAAC,mFAAD,CAAnC;AACAM,MAAM,CAACb,OAAP,CAAeqC,MAAf,GAAwB9B,mBAAO,CAAC,sEAAD,CAA/B,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACbA,IAAMqS,UAAU,GAAGrS,mBAAO,CAAC,mFAAD,CAA1B;;AACA,IAAM6B,OAAO,GAAG7B,mBAAO,CAAC,oEAAD,CAAvB;;AACA,IAAMK,KAAK,GAAGL,mBAAO,CAAC,kDAAD,CAAP,CAAiB,yBAAjB,CAAd;;AACA,IAAM8B,MAAM,GAAG9B,mBAAO,CAAC,sEAAD,CAAtB;;AACA,IAAM4K,QAAQ,GAAG5K,mBAAO,CAAC,kDAAD,CAAxB;;AACA,IAAMsS,OAAO,GAAGtS,mBAAO,CAAC,gDAAD,CAAvB;;IAEML,M;;;;;AACJ;AACF;AACA;AACA;AACA;AACA;AACA;AACE,kBAAYe,GAAZ,EAA4B;AAAA;;AAAA,QAAXC,IAAW,uEAAJ,EAAI;;AAAA;;AAC1B;;AAEA,QAAID,GAAG,IAAI,qBAAoBA,GAApB,CAAX,EAAoC;AAClCC,UAAI,GAAGD,GAAP;AACAA,SAAG,GAAG,IAAN;AACD;;AAED,QAAIA,GAAJ,EAAS;AACPA,SAAG,GAAGkK,QAAQ,CAAClK,GAAD,CAAd;AACAC,UAAI,CAAC4R,QAAL,GAAgB7R,GAAG,CAACqK,IAApB;AACApK,UAAI,CAAC6R,MAAL,GAAc9R,GAAG,CAACZ,QAAJ,KAAiB,OAAjB,IAA4BY,GAAG,CAACZ,QAAJ,KAAiB,KAA3D;AACAa,UAAI,CAACuK,IAAL,GAAYxK,GAAG,CAACwK,IAAhB;AACA,UAAIxK,GAAG,CAACY,KAAR,EAAeX,IAAI,CAACW,KAAL,GAAaZ,GAAG,CAACY,KAAjB;AAChB,KAND,MAMO,IAAIX,IAAI,CAACoK,IAAT,EAAe;AACpBpK,UAAI,CAAC4R,QAAL,GAAgB3H,QAAQ,CAACjK,IAAI,CAACoK,IAAN,CAAR,CAAoBA,IAApC;AACD;;AAED,UAAKyH,MAAL,GACE,QAAQ7R,IAAI,CAAC6R,MAAb,GACI7R,IAAI,CAAC6R,MADT,GAEI,OAAO1H,QAAP,KAAoB,WAApB,IAAmC,aAAaA,QAAQ,CAAChL,QAH/D;;AAKA,QAAIa,IAAI,CAAC4R,QAAL,IAAiB,CAAC5R,IAAI,CAACuK,IAA3B,EAAiC;AAC/B;AACAvK,UAAI,CAACuK,IAAL,GAAY,MAAKsH,MAAL,GAAc,KAAd,GAAsB,IAAlC;AACD;;AAED,UAAKD,QAAL,GACE5R,IAAI,CAAC4R,QAAL,KACC,OAAOzH,QAAP,KAAoB,WAApB,GAAkCA,QAAQ,CAACyH,QAA3C,GAAsD,WADvD,CADF;AAGA,UAAKrH,IAAL,GACEvK,IAAI,CAACuK,IAAL,KACC,OAAOJ,QAAP,KAAoB,WAApB,IAAmCA,QAAQ,CAACI,IAA5C,GACGJ,QAAQ,CAACI,IADZ,GAEG,MAAKsH,MAAL,GACA,GADA,GAEA,EALJ,CADF;AAQA,UAAKH,UAAL,GAAkB1R,IAAI,CAAC0R,UAAL,IAAmB,CAAC,SAAD,EAAY,WAAZ,CAArC;AACA,UAAKI,UAAL,GAAkB,EAAlB;AACA,UAAKC,WAAL,GAAmB,EAAnB;AACA,UAAKC,aAAL,GAAqB,CAArB;AAEA,UAAKhS,IAAL,GAAY,SACV;AACEI,UAAI,EAAE,YADR;AAEE6R,WAAK,EAAE,KAFT;AAGEC,qBAAe,EAAE,KAHnB;AAIEC,aAAO,EAAE,IAJX;AAKEC,WAAK,EAAE,IALT;AAMEC,oBAAc,EAAE,GANlB;AAOEC,qBAAe,EAAE,KAPnB;AAQEC,wBAAkB,EAAE,IARtB;AASEC,uBAAiB,EAAE;AACjBC,iBAAS,EAAE;AADM,OATrB;AAYEC,sBAAgB,EAAE;AAZpB,KADU,EAeV1S,IAfU,CAAZ;AAkBA,UAAKA,IAAL,CAAUI,IAAV,GAAiB,MAAKJ,IAAL,CAAUI,IAAV,CAAe8N,OAAf,CAAuB,KAAvB,EAA8B,EAA9B,IAAoC,GAArD;;AAEA,QAAI,OAAO,MAAKlO,IAAL,CAAUW,KAAjB,KAA2B,QAA/B,EAAyC;AACvC,YAAKX,IAAL,CAAUW,KAAV,GAAkBgR,OAAO,CAACgB,MAAR,CAAe,MAAK3S,IAAL,CAAUW,KAAzB,CAAlB;AACD,KAlEyB,CAoE1B;;;AACA,UAAKL,EAAL,GAAU,IAAV;AACA,UAAKsS,QAAL,GAAgB,IAAhB;AACA,UAAKC,YAAL,GAAoB,IAApB;AACA,UAAKC,WAAL,GAAmB,IAAnB,CAxE0B,CA0E1B;;AACA,UAAKC,gBAAL,GAAwB,IAAxB;;AAEA,QAAI,OAAO1H,gBAAP,KAA4B,UAAhC,EAA4C;AAC1CA,sBAAgB,CACd,cADc,EAEd,YAAM;AACJ,YAAI,MAAK7C,SAAT,EAAoB;AAClB;AACA,gBAAKA,SAAL,CAAeiD,kBAAf;;AACA,gBAAKjD,SAAL,CAAe9D,KAAf;AACD;AACF,OARa,EASd,KATc,CAAhB;AAWD;;AAED,UAAK/B,IAAL;;AA3F0B;AA4F3B;AAED;AACF;AACA;AACA;AACA;AACA;AACA;;;;;oCACkBwO,I,EAAM;AACpBzR,WAAK,CAAC,yBAAD,EAA4ByR,IAA5B,CAAL;AACA,UAAMxQ,KAAK,GAAGqS,KAAK,CAAC,KAAKhT,IAAL,CAAUW,KAAX,CAAnB,CAFoB,CAIpB;;AACAA,WAAK,CAACsS,GAAN,GAAY9R,MAAM,CAAChC,QAAnB,CALoB,CAOpB;;AACAwB,WAAK,CAAC6H,SAAN,GAAkB2I,IAAlB,CARoB,CAUpB;;AACA,UAAI,KAAK7Q,EAAT,EAAaK,KAAK,CAACkI,GAAN,GAAY,KAAKvI,EAAjB;;AAEb,UAAMN,IAAI,GAAG,SACX,EADW,EAEX,KAAKA,IAAL,CAAU0S,gBAAV,CAA2BvB,IAA3B,CAFW,EAGX,KAAKnR,IAHM,EAIX;AACEW,aAAK,EAALA,KADF;AAEEE,cAAM,EAAE,IAFV;AAGE+Q,gBAAQ,EAAE,KAAKA,QAHjB;AAIEC,cAAM,EAAE,KAAKA,MAJf;AAKEtH,YAAI,EAAE,KAAKA;AALb,OAJW,CAAb;;AAaA7K,WAAK,CAAC,aAAD,EAAgBM,IAAhB,CAAL;AAEA,aAAO,IAAI0R,UAAU,CAACP,IAAD,CAAd,CAAqBnR,IAArB,CAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;;2BACS;AACL,UAAIwI,SAAJ;;AACA,UACE,KAAKxI,IAAL,CAAUsS,eAAV,IACAtT,MAAM,CAACkU,qBADP,IAEA,KAAKxB,UAAL,CAAgB7N,OAAhB,CAAwB,WAAxB,MAAyC,CAAC,CAH5C,EAIE;AACA2E,iBAAS,GAAG,WAAZ;AACD,OAND,MAMO,IAAI,MAAM,KAAKkJ,UAAL,CAAgB5O,MAA1B,EAAkC;AACvC;AACA,YAAMiB,IAAI,GAAG,IAAb;AACAU,kBAAU,CAAC,YAAW;AACpBV,cAAI,CAACY,IAAL,CAAU,OAAV,EAAmB,yBAAnB;AACD,SAFS,EAEP,CAFO,CAAV;AAGA;AACD,OAPM,MAOA;AACL6D,iBAAS,GAAG,KAAKkJ,UAAL,CAAgB,CAAhB,CAAZ;AACD;;AACD,WAAKI,UAAL,GAAkB,SAAlB,CAlBK,CAoBL;;AACA,UAAI;AACFtJ,iBAAS,GAAG,KAAK2K,eAAL,CAAqB3K,SAArB,CAAZ;AACD,OAFD,CAEE,OAAO4K,CAAP,EAAU;AACV1T,aAAK,CAAC,oCAAD,EAAuC0T,CAAvC,CAAL;AACA,aAAK1B,UAAL,CAAgB2B,KAAhB;AACA,aAAK1Q,IAAL;AACA;AACD;;AAED6F,eAAS,CAAC7F,IAAV;AACA,WAAK2Q,YAAL,CAAkB9K,SAAlB;AACD;AAED;AACF;AACA;AACA;AACA;;;;iCACeA,S,EAAW;AACtB9I,WAAK,CAAC,sBAAD,EAAyB8I,SAAS,CAAC2I,IAAnC,CAAL;AACA,UAAMpN,IAAI,GAAG,IAAb;;AAEA,UAAI,KAAKyE,SAAT,EAAoB;AAClB9I,aAAK,CAAC,gCAAD,EAAmC,KAAK8I,SAAL,CAAe2I,IAAlD,CAAL;AACA,aAAK3I,SAAL,CAAeiD,kBAAf;AACD,OAPqB,CAStB;;;AACA,WAAKjD,SAAL,GAAiBA,SAAjB,CAVsB,CAYtB;;AACAA,eAAS,CACNtE,EADH,CACM,OADN,EACe,YAAW;AACtBH,YAAI,CAACwP,OAAL;AACD,OAHH,EAIGrP,EAJH,CAIM,QAJN,EAIgB,UAASsB,MAAT,EAAiB;AAC7BzB,YAAI,CAACyP,QAAL,CAAchO,MAAd;AACD,OANH,EAOGtB,EAPH,CAOM,OAPN,EAOe,UAASkP,CAAT,EAAY;AACvBrP,YAAI,CAAC0P,OAAL,CAAaL,CAAb;AACD,OATH,EAUGlP,EAVH,CAUM,OAVN,EAUe,YAAW;AACtBH,YAAI,CAAC2P,OAAL,CAAa,iBAAb;AACD,OAZH;AAaD;AAED;AACF;AACA;AACA;AACA;AACA;;;;0BACQvC,I,EAAM;AACVzR,WAAK,CAAC,wBAAD,EAA2ByR,IAA3B,CAAL;AACA,UAAI3I,SAAS,GAAG,KAAK2K,eAAL,CAAqBhC,IAArB,EAA2B;AAAEwC,aAAK,EAAE;AAAT,OAA3B,CAAhB;AACA,UAAIC,MAAM,GAAG,KAAb;AACA,UAAM7P,IAAI,GAAG,IAAb;AAEA/E,YAAM,CAACkU,qBAAP,GAA+B,KAA/B;;AAEA,eAASW,eAAT,GAA2B;AACzB,YAAI9P,IAAI,CAAC+P,kBAAT,EAA6B;AAC3B,cAAMC,kBAAkB,GACtB,CAAC,KAAKC,cAAN,IAAwBjQ,IAAI,CAACyE,SAAL,CAAewL,cADzC;AAEAJ,gBAAM,GAAGA,MAAM,IAAIG,kBAAnB;AACD;;AACD,YAAIH,MAAJ,EAAY;AAEZlU,aAAK,CAAC,6BAAD,EAAgCyR,IAAhC,CAAL;AACA3I,iBAAS,CAACyL,IAAV,CAAe,CAAC;AAAE/L,cAAI,EAAE,MAAR;AAAgB5C,cAAI,EAAE;AAAtB,SAAD,CAAf;AACAkD,iBAAS,CAACgD,IAAV,CAAe,QAAf,EAAyB,UAAS0I,GAAT,EAAc;AACrC,cAAIN,MAAJ,EAAY;;AACZ,cAAI,WAAWM,GAAG,CAAChM,IAAf,IAAuB,YAAYgM,GAAG,CAAC5O,IAA3C,EAAiD;AAC/C5F,iBAAK,CAAC,2BAAD,EAA8ByR,IAA9B,CAAL;AACApN,gBAAI,CAACoQ,SAAL,GAAiB,IAAjB;AACApQ,gBAAI,CAACY,IAAL,CAAU,WAAV,EAAuB6D,SAAvB;AACA,gBAAI,CAACA,SAAL,EAAgB;AAChBxJ,kBAAM,CAACkU,qBAAP,GAA+B,gBAAgB1K,SAAS,CAAC2I,IAAzD;AAEAzR,iBAAK,CAAC,gCAAD,EAAmCqE,IAAI,CAACyE,SAAL,CAAe2I,IAAlD,CAAL;AACApN,gBAAI,CAACyE,SAAL,CAAe4L,KAAf,CAAqB,YAAW;AAC9B,kBAAIR,MAAJ,EAAY;AACZ,kBAAI,aAAa7P,IAAI,CAAC+N,UAAtB,EAAkC;AAClCpS,mBAAK,CAAC,+CAAD,CAAL;AAEA4E,qBAAO;AAEPP,kBAAI,CAACuP,YAAL,CAAkB9K,SAAlB;AACAA,uBAAS,CAACyL,IAAV,CAAe,CAAC;AAAE/L,oBAAI,EAAE;AAAR,eAAD,CAAf;AACAnE,kBAAI,CAACY,IAAL,CAAU,SAAV,EAAqB6D,SAArB;AACAA,uBAAS,GAAG,IAAZ;AACAzE,kBAAI,CAACoQ,SAAL,GAAiB,KAAjB;AACApQ,kBAAI,CAACsQ,KAAL;AACD,aAbD;AAcD,WAtBD,MAsBO;AACL3U,iBAAK,CAAC,6BAAD,EAAgCyR,IAAhC,CAAL;AACA,gBAAM9M,GAAG,GAAG,IAAIO,KAAJ,CAAU,aAAV,CAAZ;AACAP,eAAG,CAACmE,SAAJ,GAAgBA,SAAS,CAAC2I,IAA1B;AACApN,gBAAI,CAACY,IAAL,CAAU,cAAV,EAA0BN,GAA1B;AACD;AACF,SA9BD;AA+BD;;AAED,eAASiQ,eAAT,GAA2B;AACzB,YAAIV,MAAJ,EAAY,OADa,CAGzB;;AACAA,cAAM,GAAG,IAAT;AAEAtP,eAAO;AAEPkE,iBAAS,CAAC9D,KAAV;AACA8D,iBAAS,GAAG,IAAZ;AACD,OA7DS,CA+DV;;;AACA,eAASrD,OAAT,CAAiBd,GAAjB,EAAsB;AACpB,YAAMkK,KAAK,GAAG,IAAI3J,KAAJ,CAAU,kBAAkBP,GAA5B,CAAd;AACAkK,aAAK,CAAC/F,SAAN,GAAkBA,SAAS,CAAC2I,IAA5B;AAEAmD,uBAAe;AAEf5U,aAAK,CAAC,kDAAD,EAAqDyR,IAArD,EAA2D9M,GAA3D,CAAL;AAEAN,YAAI,CAACY,IAAL,CAAU,cAAV,EAA0B4J,KAA1B;AACD;;AAED,eAASgG,gBAAT,GAA4B;AAC1BpP,eAAO,CAAC,kBAAD,CAAP;AACD,OA7ES,CA+EV;;;AACA,eAASC,OAAT,GAAmB;AACjBD,eAAO,CAAC,eAAD,CAAP;AACD,OAlFS,CAoFV;;;AACA,eAASqP,SAAT,CAAmBC,EAAnB,EAAuB;AACrB,YAAIjM,SAAS,IAAIiM,EAAE,CAACtD,IAAH,KAAY3I,SAAS,CAAC2I,IAAvC,EAA6C;AAC3CzR,eAAK,CAAC,4BAAD,EAA+B+U,EAAE,CAACtD,IAAlC,EAAwC3I,SAAS,CAAC2I,IAAlD,CAAL;AACAmD,yBAAe;AAChB;AACF,OA1FS,CA4FV;;;AACA,eAAShQ,OAAT,GAAmB;AACjBkE,iBAAS,CAACrB,cAAV,CAAyB,MAAzB,EAAiC0M,eAAjC;AACArL,iBAAS,CAACrB,cAAV,CAAyB,OAAzB,EAAkChC,OAAlC;AACAqD,iBAAS,CAACrB,cAAV,CAAyB,OAAzB,EAAkCoN,gBAAlC;AACAxQ,YAAI,CAACoD,cAAL,CAAoB,OAApB,EAA6B/B,OAA7B;AACArB,YAAI,CAACoD,cAAL,CAAoB,WAApB,EAAiCqN,SAAjC;AACD;;AAEDhM,eAAS,CAACgD,IAAV,CAAe,MAAf,EAAuBqI,eAAvB;AACArL,eAAS,CAACgD,IAAV,CAAe,OAAf,EAAwBrG,OAAxB;AACAqD,eAAS,CAACgD,IAAV,CAAe,OAAf,EAAwB+I,gBAAxB;AAEA,WAAK/I,IAAL,CAAU,OAAV,EAAmBpG,OAAnB;AACA,WAAKoG,IAAL,CAAU,WAAV,EAAuBgJ,SAAvB;AAEAhM,eAAS,CAAC7F,IAAV;AACD;AAED;AACF;AACA;AACA;AACA;;;;6BACW;AACPjD,WAAK,CAAC,aAAD,CAAL;AACA,WAAKoS,UAAL,GAAkB,MAAlB;AACA9S,YAAM,CAACkU,qBAAP,GAA+B,gBAAgB,KAAK1K,SAAL,CAAe2I,IAA9D;AACA,WAAKxM,IAAL,CAAU,MAAV;AACA,WAAK0P,KAAL,GALO,CAOP;AACA;;AACA,UACE,WAAW,KAAKvC,UAAhB,IACA,KAAK9R,IAAL,CAAUmS,OADV,IAEA,KAAK3J,SAAL,CAAe4L,KAHjB,EAIE;AACA1U,aAAK,CAAC,yBAAD,CAAL;AACA,YAAIqG,CAAC,GAAG,CAAR;AACA,YAAM2O,CAAC,GAAG,KAAK9B,QAAL,CAAc9P,MAAxB;;AACA,eAAOiD,CAAC,GAAG2O,CAAX,EAAc3O,CAAC,EAAf,EAAmB;AACjB,eAAK4N,KAAL,CAAW,KAAKf,QAAL,CAAc7M,CAAd,CAAX;AACD;AACF;AACF;AAED;AACF;AACA;AACA;AACA;;;;6BACWP,M,EAAQ;AACf,UACE,cAAc,KAAKsM,UAAnB,IACA,WAAW,KAAKA,UADhB,IAEA,cAAc,KAAKA,UAHrB,EAIE;AACApS,aAAK,CAAC,sCAAD,EAAyC8F,MAAM,CAAC0C,IAAhD,EAAsD1C,MAAM,CAACF,IAA7D,CAAL;AAEA,aAAKX,IAAL,CAAU,QAAV,EAAoBa,MAApB,EAHA,CAKA;;AACA,aAAKb,IAAL,CAAU,WAAV;;AAEA,gBAAQa,MAAM,CAAC0C,IAAf;AACE,eAAK,MAAL;AACE,iBAAKyM,WAAL,CAAiB5F,IAAI,CAAC6F,KAAL,CAAWpP,MAAM,CAACF,IAAlB,CAAjB;AACA;;AAEF,eAAK,MAAL;AACE,iBAAKuP,gBAAL;AACA,iBAAKC,UAAL,CAAgB,MAAhB;AACA,iBAAKnQ,IAAL,CAAU,MAAV;AACA;;AAEF,eAAK,OAAL;AACE,gBAAMN,GAAG,GAAG,IAAIO,KAAJ,CAAU,cAAV,CAAZ;AACAP,eAAG,CAAC0Q,IAAJ,GAAWvP,MAAM,CAACF,IAAlB;AACA,iBAAKmO,OAAL,CAAapP,GAAb;AACA;;AAEF,eAAK,SAAL;AACE,iBAAKM,IAAL,CAAU,MAAV,EAAkBa,MAAM,CAACF,IAAzB;AACA,iBAAKX,IAAL,CAAU,SAAV,EAAqBa,MAAM,CAACF,IAA5B;AACA;AApBJ;AAsBD,OAlCD,MAkCO;AACL5F,aAAK,CAAC,6CAAD,EAAgD,KAAKoS,UAArD,CAAL;AACD;AACF;AAED;AACF;AACA;AACA;AACA;AACA;;;;gCACcxM,I,EAAM;AAChB,WAAKX,IAAL,CAAU,WAAV,EAAuBW,IAAvB;AACA,WAAKhF,EAAL,GAAUgF,IAAI,CAACuD,GAAf;AACA,WAAKL,SAAL,CAAe7H,KAAf,CAAqBkI,GAArB,GAA2BvD,IAAI,CAACuD,GAAhC;AACA,WAAK+J,QAAL,GAAgB,KAAKoC,cAAL,CAAoB1P,IAAI,CAACsN,QAAzB,CAAhB;AACA,WAAKC,YAAL,GAAoBvN,IAAI,CAACuN,YAAzB;AACA,WAAKC,WAAL,GAAmBxN,IAAI,CAACwN,WAAxB;AACA,WAAKmC,MAAL,GAPgB,CAQhB;;AACA,UAAI,aAAa,KAAKnD,UAAtB,EAAkC;AAClC,WAAK+C,gBAAL;AACD;AAED;AACF;AACA;AACA;AACA;;;;uCACqB;AAAA;;AACjB9P,kBAAY,CAAC,KAAKgO,gBAAN,CAAZ;AACA,WAAKA,gBAAL,GAAwBtO,UAAU,CAAC,YAAM;AACvC,cAAI,CAACiP,OAAL,CAAa,cAAb;AACD,OAFiC,EAE/B,KAAKb,YAAL,GAAoB,KAAKC,WAFM,CAAlC;AAGD;AAED;AACF;AACA;AACA;AACA;;;;8BACY;AACR,WAAKf,WAAL,CAAiB/H,MAAjB,CAAwB,CAAxB,EAA2B,KAAKgI,aAAhC,EADQ,CAGR;AACA;AACA;;AACA,WAAKA,aAAL,GAAqB,CAArB;;AAEA,UAAI,MAAM,KAAKD,WAAL,CAAiBjP,MAA3B,EAAmC;AACjC,aAAK6B,IAAL,CAAU,OAAV;AACD,OAFD,MAEO;AACL,aAAK0P,KAAL;AACD;AACF;AAED;AACF;AACA;AACA;AACA;;;;4BACU;AACN,UACE,aAAa,KAAKvC,UAAlB,IACA,KAAKtJ,SAAL,CAAeC,QADf,IAEA,CAAC,KAAK0L,SAFN,IAGA,KAAKpC,WAAL,CAAiBjP,MAJnB,EAKE;AACApD,aAAK,CAAC,+BAAD,EAAkC,KAAKqS,WAAL,CAAiBjP,MAAnD,CAAL;AACA,aAAK0F,SAAL,CAAeyL,IAAf,CAAoB,KAAKlC,WAAzB,EAFA,CAGA;AACA;;AACA,aAAKC,aAAL,GAAqB,KAAKD,WAAL,CAAiBjP,MAAtC;AACA,aAAK6B,IAAL,CAAU,OAAV;AACD;AACF;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;0BACQuP,G,EAAKjO,O,EAASrC,E,EAAI;AACtB,WAAKkR,UAAL,CAAgB,SAAhB,EAA2BZ,GAA3B,EAAgCjO,OAAhC,EAAyCrC,EAAzC;AACA,aAAO,IAAP;AACD;;;yBAEIsQ,G,EAAKjO,O,EAASrC,E,EAAI;AACrB,WAAKkR,UAAL,CAAgB,SAAhB,EAA2BZ,GAA3B,EAAgCjO,OAAhC,EAAyCrC,EAAzC;AACA,aAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;+BACasE,I,EAAM5C,I,EAAMW,O,EAASrC,E,EAAI;AAClC,UAAI,eAAe,OAAO0B,IAA1B,EAAgC;AAC9B1B,UAAE,GAAG0B,IAAL;AACAA,YAAI,GAAGrF,SAAP;AACD;;AAED,UAAI,eAAe,OAAOgG,OAA1B,EAAmC;AACjCrC,UAAE,GAAGqC,OAAL;AACAA,eAAO,GAAG,IAAV;AACD;;AAED,UAAI,cAAc,KAAK6L,UAAnB,IAAiC,aAAa,KAAKA,UAAvD,EAAmE;AACjE;AACD;;AAED7L,aAAO,GAAGA,OAAO,IAAI,EAArB;AACAA,aAAO,CAACoC,QAAR,GAAmB,UAAUpC,OAAO,CAACoC,QAArC;AAEA,UAAM7C,MAAM,GAAG;AACb0C,YAAI,EAAEA,IADO;AAEb5C,YAAI,EAAEA,IAFO;AAGbW,eAAO,EAAEA;AAHI,OAAf;AAKA,WAAKtB,IAAL,CAAU,cAAV,EAA0Ba,MAA1B;AACA,WAAKuM,WAAL,CAAiBlN,IAAjB,CAAsBW,MAAtB;AACA,UAAI5B,EAAJ,EAAQ,KAAK4H,IAAL,CAAU,OAAV,EAAmB5H,EAAnB;AACR,WAAKyQ,KAAL;AACD;AAED;AACF;AACA;AACA;AACA;;;;4BACU;AACN,UAAMtQ,IAAI,GAAG,IAAb;;AAEA,UAAI,cAAc,KAAK+N,UAAnB,IAAiC,WAAW,KAAKA,UAArD,EAAiE;AAC/D,aAAKA,UAAL,GAAkB,SAAlB;;AAEA,YAAI,KAAKC,WAAL,CAAiBjP,MAArB,EAA6B;AAC3B,eAAK0I,IAAL,CAAU,OAAV,EAAmB,YAAW;AAC5B,gBAAI,KAAK2I,SAAT,EAAoB;AAClBe,4BAAc;AACf,aAFD,MAEO;AACLxQ,mBAAK;AACN;AACF,WAND;AAOD,SARD,MAQO,IAAI,KAAKyP,SAAT,EAAoB;AACzBe,wBAAc;AACf,SAFM,MAEA;AACLxQ,eAAK;AACN;AACF;;AAED,eAASA,KAAT,GAAiB;AACfX,YAAI,CAAC2P,OAAL,CAAa,cAAb;AACAhU,aAAK,CAAC,6CAAD,CAAL;AACAqE,YAAI,CAACyE,SAAL,CAAe9D,KAAf;AACD;;AAED,eAASyQ,eAAT,GAA2B;AACzBpR,YAAI,CAACoD,cAAL,CAAoB,SAApB,EAA+BgO,eAA/B;AACApR,YAAI,CAACoD,cAAL,CAAoB,cAApB,EAAoCgO,eAApC;AACAzQ,aAAK;AACN;;AAED,eAASwQ,cAAT,GAA0B;AACxB;AACAnR,YAAI,CAACyH,IAAL,CAAU,SAAV,EAAqB2J,eAArB;AACApR,YAAI,CAACyH,IAAL,CAAU,cAAV,EAA0B2J,eAA1B;AACD;;AAED,aAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;;4BACU9Q,G,EAAK;AACX3E,WAAK,CAAC,iBAAD,EAAoB2E,GAApB,CAAL;AACArF,YAAM,CAACkU,qBAAP,GAA+B,KAA/B;AACA,WAAKvO,IAAL,CAAU,OAAV,EAAmBN,GAAnB;AACA,WAAKqP,OAAL,CAAa,iBAAb,EAAgCrP,GAAhC;AACD;AAED;AACF;AACA;AACA;AACA;;;;4BACUgC,M,EAAQ+O,I,EAAM;AACpB,UACE,cAAc,KAAKtD,UAAnB,IACA,WAAW,KAAKA,UADhB,IAEA,cAAc,KAAKA,UAHrB,EAIE;AACApS,aAAK,CAAC,gCAAD,EAAmC2G,MAAnC,CAAL;AACA,YAAMtC,IAAI,GAAG,IAAb,CAFA,CAIA;;AACAgB,oBAAY,CAAC,KAAKsQ,iBAAN,CAAZ;AACAtQ,oBAAY,CAAC,KAAKgO,gBAAN,CAAZ,CANA,CAQA;;AACA,aAAKvK,SAAL,CAAeiD,kBAAf,CAAkC,OAAlC,EATA,CAWA;;AACA,aAAKjD,SAAL,CAAe9D,KAAf,GAZA,CAcA;;AACA,aAAK8D,SAAL,CAAeiD,kBAAf,GAfA,CAiBA;;AACA,aAAKqG,UAAL,GAAkB,QAAlB,CAlBA,CAoBA;;AACA,aAAKxR,EAAL,GAAU,IAAV,CArBA,CAuBA;;AACA,aAAKqE,IAAL,CAAU,OAAV,EAAmB0B,MAAnB,EAA2B+O,IAA3B,EAxBA,CA0BA;AACA;;AACArR,YAAI,CAACgO,WAAL,GAAmB,EAAnB;AACAhO,YAAI,CAACiO,aAAL,GAAqB,CAArB;AACD;AACF;AAED;AACF;AACA;AACA;AACA;AACA;AACA;;;;mCACiBY,Q,EAAU;AACvB,UAAM0C,gBAAgB,GAAG,EAAzB;AACA,UAAIvP,CAAC,GAAG,CAAR;AACA,UAAM+I,CAAC,GAAG8D,QAAQ,CAAC9P,MAAnB;;AACA,aAAOiD,CAAC,GAAG+I,CAAX,EAAc/I,CAAC,EAAf,EAAmB;AACjB,YAAI,CAAC,KAAK2L,UAAL,CAAgB7N,OAAhB,CAAwB+O,QAAQ,CAAC7M,CAAD,CAAhC,CAAL,EACEuP,gBAAgB,CAACzQ,IAAjB,CAAsB+N,QAAQ,CAAC7M,CAAD,CAA9B;AACH;;AACD,aAAOuP,gBAAP;AACD;;;;EAzoBkBpU,O;;AA4oBrBlC,MAAM,CAACkU,qBAAP,GAA+B,KAA/B;AAEA;AACA;AACA;AACA;AACA;;AAEAlU,MAAM,CAACG,QAAP,GAAkBgC,MAAM,CAAChC,QAAzB,C,CAAmC;;AAEnC,SAAS6T,KAAT,CAAetM,GAAf,EAAoB;AAClB,MAAM6O,CAAC,GAAG,EAAV;;AACA,OAAK,IAAIxP,CAAT,IAAcW,GAAd,EAAmB;AACjB,QAAIA,GAAG,CAACuB,cAAJ,CAAmBlC,CAAnB,CAAJ,EAA2B;AACzBwP,OAAC,CAACxP,CAAD,CAAD,GAAOW,GAAG,CAACX,CAAD,CAAV;AACD;AACF;;AACD,SAAOwP,CAAP;AACD;;AAED5V,MAAM,CAACb,OAAP,GAAiBE,MAAjB,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvqBA,IAAMmC,MAAM,GAAG9B,mBAAO,CAAC,sEAAD,CAAtB;;AACA,IAAM6B,OAAO,GAAG7B,mBAAO,CAAC,oEAAD,CAAvB;;IAEMoS,S;;;;;AACJ;AACF;AACA;AACA;AACA;AACA;AACE,qBAAYzR,IAAZ,EAAkB;AAAA;;AAAA;;AAChB;AAEA,UAAKA,IAAL,GAAYA,IAAZ;AACA,UAAKW,KAAL,GAAaX,IAAI,CAACW,KAAlB;AACA,UAAKmR,UAAL,GAAkB,EAAlB;AACA,UAAKjR,MAAL,GAAcb,IAAI,CAACa,MAAnB;AANgB;AAOjB;AAED;AACF;AACA;AACA;AACA;AACA;AACA;;;;;4BACUqT,G,EAAKkB,I,EAAM;AACjB,UAAM/Q,GAAG,GAAG,IAAIO,KAAJ,CAAUsP,GAAV,CAAZ;AACA7P,SAAG,CAAC6D,IAAJ,GAAW,gBAAX;AACA7D,SAAG,CAACmR,WAAJ,GAAkBJ,IAAlB;AACA,WAAKzQ,IAAL,CAAU,OAAV,EAAmBN,GAAnB;AACA,aAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;;2BACS;AACL,UAAI,aAAa,KAAKyN,UAAlB,IAAgC,OAAO,KAAKA,UAAhD,EAA4D;AAC1D,aAAKA,UAAL,GAAkB,SAAlB;AACA,aAAK2D,MAAL;AACD;;AAED,aAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;;;;4BACU;AACN,UAAI,cAAc,KAAK3D,UAAnB,IAAiC,WAAW,KAAKA,UAArD,EAAiE;AAC/D,aAAK4D,OAAL;AACA,aAAKhC,OAAL;AACD;;AAED,aAAO,IAAP;AACD;AAED;AACF;AACA;AACA;AACA;AACA;;;;yBACOiC,O,EAAS;AACZ,UAAI,WAAW,KAAK7D,UAApB,EAAgC;AAC9B,aAAK9L,KAAL,CAAW2P,OAAX;AACD,OAFD,MAEO;AACL,cAAM,IAAI/Q,KAAJ,CAAU,oBAAV,CAAN;AACD;AACF;AAED;AACF;AACA;AACA;AACA;;;;6BACW;AACP,WAAKkN,UAAL,GAAkB,MAAlB;AACA,WAAKrJ,QAAL,GAAgB,IAAhB;AACA,WAAK9D,IAAL,CAAU,MAAV;AACD;AAED;AACF;AACA;AACA;AACA;AACA;;;;2BACSW,I,EAAM;AACX,UAAME,MAAM,GAAGrE,MAAM,CAACyU,YAAP,CAAoBtQ,IAApB,EAA0B,KAAKzE,MAAL,CAAYgV,UAAtC,CAAf;AACA,WAAKrC,QAAL,CAAchO,MAAd;AACD;AAED;AACF;AACA;;;;6BACWA,M,EAAQ;AACf,WAAKb,IAAL,CAAU,QAAV,EAAoBa,MAApB;AACD;AAED;AACF;AACA;AACA;AACA;;;;8BACY;AACR,WAAKsM,UAAL,GAAkB,QAAlB;AACA,WAAKnN,IAAL,CAAU,OAAV;AACD;;;;EA9GqBzD,O;;AAiHxBvB,MAAM,CAACb,OAAP,GAAiB2S,SAAjB,C;;;;;;;;;;;ACpHA,IAAMqE,cAAc,GAAGzW,mBAAO,CAAC,iFAAD,CAA9B;;AACA,IAAM0W,GAAG,GAAG1W,mBAAO,CAAC,oFAAD,CAAnB;;AACA,IAAM2W,KAAK,GAAG3W,mBAAO,CAAC,wFAAD,CAArB;;AACA,IAAM4W,SAAS,GAAG5W,mBAAO,CAAC,gFAAD,CAAzB;;AAEAP,OAAO,CAACoX,OAAR,GAAkBA,OAAlB;AACApX,OAAO,CAACmX,SAAR,GAAoBA,SAApB;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,OAAT,CAAiBlW,IAAjB,EAAuB;AACrB,MAAImW,GAAJ;AACA,MAAIC,EAAE,GAAG,KAAT;AACA,MAAIC,EAAE,GAAG,KAAT;AACA,MAAMjE,KAAK,GAAG,UAAUpS,IAAI,CAACoS,KAA7B;;AAEA,MAAI,OAAOjI,QAAP,KAAoB,WAAxB,EAAqC;AACnC,QAAMmM,KAAK,GAAG,aAAanM,QAAQ,CAAChL,QAApC;AACA,QAAIoL,IAAI,GAAGJ,QAAQ,CAACI,IAApB,CAFmC,CAInC;;AACA,QAAI,CAACA,IAAL,EAAW;AACTA,UAAI,GAAG+L,KAAK,GAAG,GAAH,GAAS,EAArB;AACD;;AAEDF,MAAE,GAAGpW,IAAI,CAAC4R,QAAL,KAAkBzH,QAAQ,CAACyH,QAA3B,IAAuCrH,IAAI,KAAKvK,IAAI,CAACuK,IAA1D;AACA8L,MAAE,GAAGrW,IAAI,CAAC6R,MAAL,KAAgByE,KAArB;AACD;;AAEDtW,MAAI,CAACuW,OAAL,GAAeH,EAAf;AACApW,MAAI,CAACwW,OAAL,GAAeH,EAAf;AACAF,KAAG,GAAG,IAAIL,cAAJ,CAAmB9V,IAAnB,CAAN;;AAEA,MAAI,UAAUmW,GAAV,IAAiB,CAACnW,IAAI,CAACyW,UAA3B,EAAuC;AACrC,WAAO,IAAIV,GAAJ,CAAQ/V,IAAR,CAAP;AACD,GAFD,MAEO;AACL,QAAI,CAACoS,KAAL,EAAY,MAAM,IAAIxN,KAAJ,CAAU,gBAAV,CAAN;AACZ,WAAO,IAAIoR,KAAJ,CAAUhW,IAAV,CAAP;AACD;AACF,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5CD,IAAM0W,OAAO,GAAGrX,mBAAO,CAAC,4EAAD,CAAvB;;AACA,IAAMsX,UAAU,GAAGtX,mBAAO,CAAC,gFAAD,CAA1B;;AAEA,IAAMuX,QAAQ,GAAG,KAAjB;AACA,IAAMC,eAAe,GAAG,MAAxB;AAEA;AACA;AACA;;AAEA,IAAIlL,SAAJ;;IAEMmL,Y;;;;;AACJ;AACF;AACA;AACA;AACA;AACA;AACE,wBAAY9W,IAAZ,EAAkB;AAAA;;AAAA;;AAChB,8BAAMA,IAAN;AAEA,UAAKW,KAAL,GAAa,MAAKA,KAAL,IAAc,EAA3B,CAHgB,CAKhB;AACA;;AACA,QAAI,CAACgL,SAAL,EAAgB;AACd;AACAA,eAAS,GAAGgL,UAAU,CAACI,MAAX,GAAoBJ,UAAU,CAACI,MAAX,IAAqB,EAArD;AACD,KAVe,CAYhB;;;AACA,UAAK/I,KAAL,GAAarC,SAAS,CAAC7I,MAAvB,CAbgB,CAehB;;AACA,QAAMiB,IAAI,gCAAV;;AACA4H,aAAS,CAAC9G,IAAV,CAAe,UAASqP,GAAT,EAAc;AAC3BnQ,UAAI,CAACiT,MAAL,CAAY9C,GAAZ;AACD,KAFD,EAjBgB,CAqBhB;;AACA,UAAKvT,KAAL,CAAWmO,CAAX,GAAe,MAAKd,KAApB;AAtBgB;AAuBjB;AAED;AACF;AACA;;;;;;AAKE;AACF;AACA;AACA;AACA;8BACY;AACR,UAAI,KAAKiJ,MAAT,EAAiB;AACf;AACA,aAAKA,MAAL,CAAY9R,OAAZ,GAAsB,YAAM,CAAE,CAA9B;;AACA,aAAK8R,MAAL,CAAYC,UAAZ,CAAuBC,WAAvB,CAAmC,KAAKF,MAAxC;AACA,aAAKA,MAAL,GAAc,IAAd;AACD;;AAED,UAAI,KAAKG,IAAT,EAAe;AACb,aAAKA,IAAL,CAAUF,UAAV,CAAqBC,WAArB,CAAiC,KAAKC,IAAtC;AACA,aAAKA,IAAL,GAAY,IAAZ;AACA,aAAKC,MAAL,GAAc,IAAd;AACD;;AAED;AACD;AAED;AACF;AACA;AACA;AACA;;;;6BACW;AACP,UAAMtT,IAAI,GAAG,IAAb;AACA,UAAMkT,MAAM,GAAGhK,QAAQ,CAACqK,aAAT,CAAuB,QAAvB,CAAf;;AAEA,UAAI,KAAKL,MAAT,EAAiB;AACf,aAAKA,MAAL,CAAYC,UAAZ,CAAuBC,WAAvB,CAAmC,KAAKF,MAAxC;AACA,aAAKA,MAAL,GAAc,IAAd;AACD;;AAEDA,YAAM,CAACM,KAAP,GAAe,IAAf;AACAN,YAAM,CAACO,GAAP,GAAa,KAAKzX,GAAL,EAAb;;AACAkX,YAAM,CAAC9R,OAAP,GAAiB,UAASiO,CAAT,EAAY;AAC3BrP,YAAI,CAAC0P,OAAL,CAAa,kBAAb,EAAiCL,CAAjC;AACD,OAFD;;AAIA,UAAMqE,QAAQ,GAAGxK,QAAQ,CAACyK,oBAAT,CAA8B,QAA9B,EAAwC,CAAxC,CAAjB;;AACA,UAAID,QAAJ,EAAc;AACZA,gBAAQ,CAACP,UAAT,CAAoBS,YAApB,CAAiCV,MAAjC,EAAyCQ,QAAzC;AACD,OAFD,MAEO;AACL,SAACxK,QAAQ,CAAC2K,IAAT,IAAiB3K,QAAQ,CAAC4K,IAA3B,EAAiCC,WAAjC,CAA6Cb,MAA7C;AACD;;AACD,WAAKA,MAAL,GAAcA,MAAd;AAEA,UAAMc,SAAS,GACb,gBAAgB,OAAOlL,SAAvB,IAAoC,SAASvC,IAAT,CAAcuC,SAAS,CAACC,SAAxB,CADtC;;AAGA,UAAIiL,SAAJ,EAAe;AACbtT,kBAAU,CAAC,YAAW;AACpB,cAAM4S,MAAM,GAAGpK,QAAQ,CAACqK,aAAT,CAAuB,QAAvB,CAAf;AACArK,kBAAQ,CAAC4K,IAAT,CAAcC,WAAd,CAA0BT,MAA1B;AACApK,kBAAQ,CAAC4K,IAAT,CAAcV,WAAd,CAA0BE,MAA1B;AACD,SAJS,EAIP,GAJO,CAAV;AAKD;AACF;AAED;AACF;AACA;AACA;AACA;AACA;AACA;;;;4BACU/R,I,EAAM1B,E,EAAI;AAChB,UAAMG,IAAI,GAAG,IAAb;AACA,UAAIsT,MAAJ;;AAEA,UAAI,CAAC,KAAKD,IAAV,EAAgB;AACd,YAAMA,IAAI,GAAGnK,QAAQ,CAACqK,aAAT,CAAuB,MAAvB,CAAb;AACA,YAAMU,IAAI,GAAG/K,QAAQ,CAACqK,aAAT,CAAuB,UAAvB,CAAb;AACA,YAAMhX,EAAE,GAAI,KAAK2X,QAAL,GAAgB,gBAAgB,KAAKjK,KAAjD;AAEAoJ,YAAI,CAACc,SAAL,GAAiB,UAAjB;AACAd,YAAI,CAACjK,KAAL,CAAWgL,QAAX,GAAsB,UAAtB;AACAf,YAAI,CAACjK,KAAL,CAAWiL,GAAX,GAAiB,SAAjB;AACAhB,YAAI,CAACjK,KAAL,CAAWkL,IAAX,GAAkB,SAAlB;AACAjB,YAAI,CAACkB,MAAL,GAAchY,EAAd;AACA8W,YAAI,CAACmB,MAAL,GAAc,MAAd;AACAnB,YAAI,CAACoB,YAAL,CAAkB,gBAAlB,EAAoC,OAApC;AACAR,YAAI,CAAC7G,IAAL,GAAY,GAAZ;AACAiG,YAAI,CAACU,WAAL,CAAiBE,IAAjB;AACA/K,gBAAQ,CAAC4K,IAAT,CAAcC,WAAd,CAA0BV,IAA1B;AAEA,aAAKA,IAAL,GAAYA,IAAZ;AACA,aAAKY,IAAL,GAAYA,IAAZ;AACD;;AAED,WAAKZ,IAAL,CAAUqB,MAAV,GAAmB,KAAK1Y,GAAL,EAAnB;;AAEA,eAAS2Y,QAAT,GAAoB;AAClBC,kBAAU;AACV/U,UAAE;AACH;;AAED,eAAS+U,UAAT,GAAsB;AACpB,YAAI5U,IAAI,CAACsT,MAAT,EAAiB;AACf,cAAI;AACFtT,gBAAI,CAACqT,IAAL,CAAUD,WAAV,CAAsBpT,IAAI,CAACsT,MAA3B;AACD,WAFD,CAEE,OAAOjE,CAAP,EAAU;AACVrP,gBAAI,CAAC0P,OAAL,CAAa,oCAAb,EAAmDL,CAAnD;AACD;AACF;;AAED,YAAI;AACF;AACA,cAAMwF,IAAI,GAAG,sCAAsC7U,IAAI,CAACkU,QAA3C,GAAsD,IAAnE;AACAZ,gBAAM,GAAGpK,QAAQ,CAACqK,aAAT,CAAuBsB,IAAvB,CAAT;AACD,SAJD,CAIE,OAAOxF,CAAP,EAAU;AACViE,gBAAM,GAAGpK,QAAQ,CAACqK,aAAT,CAAuB,QAAvB,CAAT;AACAD,gBAAM,CAAClG,IAAP,GAAcpN,IAAI,CAACkU,QAAnB;AACAZ,gBAAM,CAACG,GAAP,GAAa,cAAb;AACD;;AAEDH,cAAM,CAAC/W,EAAP,GAAYyD,IAAI,CAACkU,QAAjB;AAEAlU,YAAI,CAACqT,IAAL,CAAUU,WAAV,CAAsBT,MAAtB;AACAtT,YAAI,CAACsT,MAAL,GAAcA,MAAd;AACD;;AAEDsB,gBAAU,GAxDM,CA0DhB;AACA;;AACArT,UAAI,GAAGA,IAAI,CAAC4I,OAAL,CAAa2I,eAAb,EAA8B,MAA9B,CAAP;AACA,WAAKmB,IAAL,CAAUjZ,KAAV,GAAkBuG,IAAI,CAAC4I,OAAL,CAAa0I,QAAb,EAAuB,KAAvB,CAAlB;;AAEA,UAAI;AACF,aAAKQ,IAAL,CAAUyB,MAAV;AACD,OAFD,CAEE,OAAOzF,CAAP,EAAU,CAAE;;AAEd,UAAI,KAAKiE,MAAL,CAAYyB,WAAhB,EAA6B;AAC3B,aAAKzB,MAAL,CAAY0B,kBAAZ,GAAiC,YAAW;AAC1C,cAAIhV,IAAI,CAACsT,MAAL,CAAYvF,UAAZ,KAA2B,UAA/B,EAA2C;AACzC4G,oBAAQ;AACT;AACF,SAJD;AAKD,OAND,MAMO;AACL,aAAKrB,MAAL,CAAY2B,MAAZ,GAAqBN,QAArB;AACD;AACF;;;wBArJoB;AACnB,aAAO,KAAP;AACD;;;;EArCwBhC,O;;AA2L3B/W,MAAM,CAACb,OAAP,GAAiBgY,YAAjB,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvMA;AAEA,IAAMhB,cAAc,GAAGzW,mBAAO,CAAC,iFAAD,CAA9B;;AACA,IAAMqX,OAAO,GAAGrX,mBAAO,CAAC,4EAAD,CAAvB;;AACA,IAAM6B,OAAO,GAAG7B,mBAAO,CAAC,oEAAD,CAAvB;;eACiBA,mBAAO,CAAC,4DAAD,C;IAAhB4Z,I,YAAAA,I;;AACR,IAAMtC,UAAU,GAAGtX,mBAAO,CAAC,gFAAD,CAA1B;;AAEA,IAAMK,KAAK,GAAGL,mBAAO,CAAC,kDAAD,CAAP,CAAiB,8BAAjB,CAAd;AAEA;AACA;AACA;;;AAEA,SAAS6Z,KAAT,GAAiB,CAAE;;AAEnB,IAAMC,OAAO,GAAI,YAAW;AAC1B,MAAMhD,GAAG,GAAG,IAAIL,cAAJ,CAAmB;AAAES,WAAO,EAAE;AAAX,GAAnB,CAAZ;AACA,SAAO,QAAQJ,GAAG,CAACiD,YAAnB;AACD,CAHe,EAAhB;;IAKMrD,G;;;;;AACJ;AACF;AACA;AACA;AACA;AACA;AACE,eAAY/V,IAAZ,EAAkB;AAAA;;AAAA;;AAChB,8BAAMA,IAAN;;AAEA,QAAI,OAAOmK,QAAP,KAAoB,WAAxB,EAAqC;AACnC,UAAMmM,KAAK,GAAG,aAAanM,QAAQ,CAAChL,QAApC;AACA,UAAIoL,IAAI,GAAGJ,QAAQ,CAACI,IAApB,CAFmC,CAInC;;AACA,UAAI,CAACA,IAAL,EAAW;AACTA,YAAI,GAAG+L,KAAK,GAAG,GAAH,GAAS,EAArB;AACD;;AAED,YAAKF,EAAL,GACG,OAAOjM,QAAP,KAAoB,WAApB,IACCnK,IAAI,CAAC4R,QAAL,KAAkBzH,QAAQ,CAACyH,QAD7B,IAEArH,IAAI,KAAKvK,IAAI,CAACuK,IAHhB;AAIA,YAAK8L,EAAL,GAAUrW,IAAI,CAAC6R,MAAL,KAAgByE,KAA1B;AACD;AACD;AACJ;AACA;;;AACI,QAAM+C,WAAW,GAAGrZ,IAAI,IAAIA,IAAI,CAACqZ,WAAjC;AACA,UAAKrF,cAAL,GAAsBmF,OAAO,IAAI,CAACE,WAAlC;AAtBgB;AAuBjB;AAED;AACF;AACA;AACA;AACA;AACA;;;;;8BACqB;AAAA,UAAXrZ,IAAW,uEAAJ,EAAI;;AACjB,eAAcA,IAAd,EAAoB;AAAEoW,UAAE,EAAE,KAAKA,EAAX;AAAeC,UAAE,EAAE,KAAKA;AAAxB,OAApB,EAAkD,KAAKrW,IAAvD;;AACA,aAAO,IAAIsZ,OAAJ,CAAY,KAAKvZ,GAAL,EAAZ,EAAwBC,IAAxB,CAAP;AACD;AAED;AACF;AACA;AACA;AACA;AACA;AACA;;;;4BACUsF,I,EAAM1B,E,EAAI;AAChB,UAAM2V,GAAG,GAAG,KAAKC,OAAL,CAAa;AACvBjB,cAAM,EAAE,MADe;AAEvBjT,YAAI,EAAEA;AAFiB,OAAb,CAAZ;AAIA,UAAMvB,IAAI,GAAG,IAAb;AACAwV,SAAG,CAACrV,EAAJ,CAAO,SAAP,EAAkBN,EAAlB;AACA2V,SAAG,CAACrV,EAAJ,CAAO,OAAP,EAAgB,UAASG,GAAT,EAAc;AAC5BN,YAAI,CAAC0P,OAAL,CAAa,gBAAb,EAA+BpP,GAA/B;AACD,OAFD;AAGD;AAED;AACF;AACA;AACA;AACA;;;;6BACW;AACP3E,WAAK,CAAC,UAAD,CAAL;AACA,UAAM6Z,GAAG,GAAG,KAAKC,OAAL,EAAZ;AACA,UAAMzV,IAAI,GAAG,IAAb;AACAwV,SAAG,CAACrV,EAAJ,CAAO,MAAP,EAAe,UAASoB,IAAT,EAAe;AAC5BvB,YAAI,CAACiT,MAAL,CAAY1R,IAAZ;AACD,OAFD;AAGAiU,SAAG,CAACrV,EAAJ,CAAO,OAAP,EAAgB,UAASG,GAAT,EAAc;AAC5BN,YAAI,CAAC0P,OAAL,CAAa,gBAAb,EAA+BpP,GAA/B;AACD,OAFD;AAGA,WAAKoV,OAAL,GAAeF,GAAf;AACD;;;;EA9Ee7C,O;;IAiFZ4C,O;;;;;AACJ;AACF;AACA;AACA;AACA;AACA;AACE,mBAAYvZ,GAAZ,EAAiBC,IAAjB,EAAuB;AAAA;;AAAA;;AACrB;AACA,WAAKA,IAAL,GAAYA,IAAZ;AAEA,WAAKuY,MAAL,GAAcvY,IAAI,CAACuY,MAAL,IAAe,KAA7B;AACA,WAAKxY,GAAL,GAAWA,GAAX;AACA,WAAKwX,KAAL,GAAa,UAAUvX,IAAI,CAACuX,KAA5B;AACA,WAAKjS,IAAL,GAAYrF,SAAS,KAAKD,IAAI,CAACsF,IAAnB,GAA0BtF,IAAI,CAACsF,IAA/B,GAAsC,IAAlD;;AAEA,WAAKoU,MAAL;;AATqB;AAUtB;AAED;AACF;AACA;AACA;AACA;;;;;6BACW;AACP,UAAM1Z,IAAI,GAAGiZ,IAAI,CACf,KAAKjZ,IADU,EAEf,OAFe,EAGf,YAHe,EAIf,KAJe,EAKf,KALe,EAMf,YANe,EAOf,MAPe,EAQf,IARe,EASf,SATe,EAUf,oBAVe,CAAjB;AAYAA,UAAI,CAACuW,OAAL,GAAe,CAAC,CAAC,KAAKvW,IAAL,CAAUoW,EAA3B;AACApW,UAAI,CAACwW,OAAL,GAAe,CAAC,CAAC,KAAKxW,IAAL,CAAUqW,EAA3B;AAEA,UAAMF,GAAG,GAAI,KAAKA,GAAL,GAAW,IAAIL,cAAJ,CAAmB9V,IAAnB,CAAxB;AACA,UAAM+D,IAAI,GAAG,IAAb;;AAEA,UAAI;AACFrE,aAAK,CAAC,iBAAD,EAAoB,KAAK6Y,MAAzB,EAAiC,KAAKxY,GAAtC,CAAL;AACAoW,WAAG,CAACxT,IAAJ,CAAS,KAAK4V,MAAd,EAAsB,KAAKxY,GAA3B,EAAgC,KAAKwX,KAArC;;AACA,YAAI;AACF,cAAI,KAAKvX,IAAL,CAAU2Z,YAAd,EAA4B;AAC1BxD,eAAG,CAACyD,qBAAJ,IAA6BzD,GAAG,CAACyD,qBAAJ,CAA0B,IAA1B,CAA7B;;AACA,iBAAK,IAAI7T,CAAT,IAAc,KAAK/F,IAAL,CAAU2Z,YAAxB,EAAsC;AACpC,kBAAI,KAAK3Z,IAAL,CAAU2Z,YAAV,CAAuB1R,cAAvB,CAAsClC,CAAtC,CAAJ,EAA8C;AAC5CoQ,mBAAG,CAAC0D,gBAAJ,CAAqB9T,CAArB,EAAwB,KAAK/F,IAAL,CAAU2Z,YAAV,CAAuB5T,CAAvB,CAAxB;AACD;AACF;AACF;AACF,SATD,CASE,OAAOqN,CAAP,EAAU,CAAE;;AAEd,YAAI,WAAW,KAAKmF,MAApB,EAA4B;AAC1B,cAAI;AACFpC,eAAG,CAAC0D,gBAAJ,CAAqB,cAArB,EAAqC,0BAArC;AACD,WAFD,CAEE,OAAOzG,CAAP,EAAU,CAAE;AACf;;AAED,YAAI;AACF+C,aAAG,CAAC0D,gBAAJ,CAAqB,QAArB,EAA+B,KAA/B;AACD,SAFD,CAEE,OAAOzG,CAAP,EAAU,CAAE,CAtBZ,CAwBF;;;AACA,YAAI,qBAAqB+C,GAAzB,EAA8B;AAC5BA,aAAG,CAACjE,eAAJ,GAAsB,KAAKlS,IAAL,CAAUkS,eAAhC;AACD;;AAED,YAAI,KAAKlS,IAAL,CAAU8Z,cAAd,EAA8B;AAC5B3D,aAAG,CAACjU,OAAJ,GAAc,KAAKlC,IAAL,CAAU8Z,cAAxB;AACD;;AAED,YAAI,KAAKC,MAAL,EAAJ,EAAmB;AACjB5D,aAAG,CAAC6C,MAAJ,GAAa,YAAW;AACtBjV,gBAAI,CAACiW,MAAL;AACD,WAFD;;AAGA7D,aAAG,CAAChR,OAAJ,GAAc,YAAW;AACvBpB,gBAAI,CAAC0P,OAAL,CAAa0C,GAAG,CAAC8D,YAAjB;AACD,WAFD;AAGD,SAPD,MAOO;AACL9D,aAAG,CAAC4C,kBAAJ,GAAyB,YAAW;AAClC,gBAAI,MAAM5C,GAAG,CAACrE,UAAd,EAA0B;;AAC1B,gBAAI,QAAQqE,GAAG,CAAC+D,MAAZ,IAAsB,SAAS/D,GAAG,CAAC+D,MAAvC,EAA+C;AAC7CnW,kBAAI,CAACiW,MAAL;AACD,aAFD,MAEO;AACL;AACA;AACAvV,wBAAU,CAAC,YAAW;AACpBV,oBAAI,CAAC0P,OAAL,CAAa,OAAO0C,GAAG,CAAC+D,MAAX,KAAsB,QAAtB,GAAiC/D,GAAG,CAAC+D,MAArC,GAA8C,CAA3D;AACD,eAFS,EAEP,CAFO,CAAV;AAGD;AACF,WAXD;AAYD;;AAEDxa,aAAK,CAAC,aAAD,EAAgB,KAAK4F,IAArB,CAAL;AACA6Q,WAAG,CAAClC,IAAJ,CAAS,KAAK3O,IAAd;AACD,OAzDD,CAyDE,OAAO8N,CAAP,EAAU;AACV;AACA;AACA;AACA3O,kBAAU,CAAC,YAAW;AACpBV,cAAI,CAAC0P,OAAL,CAAaL,CAAb;AACD,SAFS,EAEP,CAFO,CAAV;AAGA;AACD;;AAED,UAAI,OAAOnG,QAAP,KAAoB,WAAxB,EAAqC;AACnC,aAAKe,KAAL,GAAasL,OAAO,CAACa,aAAR,EAAb;AACAb,eAAO,CAACc,QAAR,CAAiB,KAAKpM,KAAtB,IAA+B,IAA/B;AACD;AACF;AAED;AACF;AACA;AACA;AACA;;;;gCACc;AACV,WAAKrJ,IAAL,CAAU,SAAV;AACA,WAAKL,OAAL;AACD;AAED;AACF;AACA;AACA;AACA;;;;2BACSgB,I,EAAM;AACX,WAAKX,IAAL,CAAU,MAAV,EAAkBW,IAAlB;AACA,WAAK+U,SAAL;AACD;AAED;AACF;AACA;AACA;AACA;;;;4BACUhW,G,EAAK;AACX,WAAKM,IAAL,CAAU,OAAV,EAAmBN,GAAnB;AACA,WAAKC,OAAL,CAAa,IAAb;AACD;AAED;AACF;AACA;AACA;AACA;;;;4BACUgW,S,EAAW;AACjB,UAAI,gBAAgB,OAAO,KAAKnE,GAA5B,IAAmC,SAAS,KAAKA,GAArD,EAA0D;AACxD;AACD,OAHgB,CAIjB;;;AACA,UAAI,KAAK4D,MAAL,EAAJ,EAAmB;AACjB,aAAK5D,GAAL,CAAS6C,MAAT,GAAkB,KAAK7C,GAAL,CAAShR,OAAT,GAAmB+T,KAArC;AACD,OAFD,MAEO;AACL,aAAK/C,GAAL,CAAS4C,kBAAT,GAA8BG,KAA9B;AACD;;AAED,UAAIoB,SAAJ,EAAe;AACb,YAAI;AACF,eAAKnE,GAAL,CAASoE,KAAT;AACD,SAFD,CAEE,OAAOnH,CAAP,EAAU,CAAE;AACf;;AAED,UAAI,OAAOnG,QAAP,KAAoB,WAAxB,EAAqC;AACnC,eAAOqM,OAAO,CAACc,QAAR,CAAiB,KAAKpM,KAAtB,CAAP;AACD;;AAED,WAAKmI,GAAL,GAAW,IAAX;AACD;AAED;AACF;AACA;AACA;AACA;;;;6BACW;AACP,UAAM7Q,IAAI,GAAG,KAAK6Q,GAAL,CAAS8D,YAAtB;;AACA,UAAI3U,IAAI,KAAK,IAAb,EAAmB;AACjB,aAAK0R,MAAL,CAAY1R,IAAZ;AACD;AACF;AAED;AACF;AACA;AACA;AACA;;;;6BACW;AACP,aAAO,OAAOkV,cAAP,KAA0B,WAA1B,IAAyC,CAAC,KAAKnE,EAA/C,IAAqD,KAAKoE,UAAjE;AACD;AAED;AACF;AACA;AACA;AACA;;;;4BACU;AACN,WAAKnW,OAAL;AACD;;;;EA3MmBpD,O;AA8MtB;AACA;AACA;AACA;AACA;;;AAEAoY,OAAO,CAACa,aAAR,GAAwB,CAAxB;AACAb,OAAO,CAACc,QAAR,GAAmB,EAAnB;;AAEA,IAAI,OAAOnN,QAAP,KAAoB,WAAxB,EAAqC;AACnC,MAAI,OAAO6L,WAAP,KAAuB,UAA3B,EAAuC;AACrCA,eAAW,CAAC,UAAD,EAAa4B,aAAb,CAAX;AACD,GAFD,MAEO,IAAI,OAAOrP,gBAAP,KAA4B,UAAhC,EAA4C;AACjD,QAAMsP,gBAAgB,GAAG,gBAAgBhE,UAAhB,GAA6B,UAA7B,GAA0C,QAAnE;AACAtL,oBAAgB,CAACsP,gBAAD,EAAmBD,aAAnB,EAAkC,KAAlC,CAAhB;AACD;AACF;;AAED,SAASA,aAAT,GAAyB;AACvB,OAAK,IAAI3U,CAAT,IAAcuT,OAAO,CAACc,QAAtB,EAAgC;AAC9B,QAAId,OAAO,CAACc,QAAR,CAAiBnS,cAAjB,CAAgClC,CAAhC,CAAJ,EAAwC;AACtCuT,aAAO,CAACc,QAAR,CAAiBrU,CAAjB,EAAoBwU,KAApB;AACD;AACF;AACF;;AAED5a,MAAM,CAACb,OAAP,GAAiBiX,GAAjB;AACApW,MAAM,CAACb,OAAP,CAAewa,OAAf,GAAyBA,OAAzB,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/UA,IAAM7H,SAAS,GAAGpS,mBAAO,CAAC,sEAAD,CAAzB;;AACA,IAAMsS,OAAO,GAAGtS,mBAAO,CAAC,gDAAD,CAAvB;;AACA,IAAM8B,MAAM,GAAG9B,mBAAO,CAAC,sEAAD,CAAtB;;AACA,IAAMub,KAAK,GAAGvb,mBAAO,CAAC,4CAAD,CAArB;;AAEA,IAAMK,KAAK,GAAGL,mBAAO,CAAC,kDAAD,CAAP,CAAiB,0BAAjB,CAAd;;IAEMqX,O;;;;;;;;;;;;;;AAQJ;AACF;AACA;AACA;AACA;AACA;6BACW;AACP,WAAKmE,IAAL;AACD;AAED;AACF;AACA;AACA;AACA;AACA;;;;0BACQC,O,EAAS;AACb,UAAM/W,IAAI,GAAG,IAAb;AAEA,WAAK+N,UAAL,GAAkB,SAAlB;;AAEA,eAASsC,KAAT,GAAiB;AACf1U,aAAK,CAAC,QAAD,CAAL;AACAqE,YAAI,CAAC+N,UAAL,GAAkB,QAAlB;AACAgJ,eAAO;AACR;;AAED,UAAI,KAAK5E,OAAL,IAAgB,CAAC,KAAKzN,QAA1B,EAAoC;AAClC,YAAIsS,KAAK,GAAG,CAAZ;;AAEA,YAAI,KAAK7E,OAAT,EAAkB;AAChBxW,eAAK,CAAC,6CAAD,CAAL;AACAqb,eAAK;AACL,eAAKvP,IAAL,CAAU,cAAV,EAA0B,YAAW;AACnC9L,iBAAK,CAAC,4BAAD,CAAL;AACA,cAAEqb,KAAF,IAAW3G,KAAK,EAAhB;AACD,WAHD;AAID;;AAED,YAAI,CAAC,KAAK3L,QAAV,EAAoB;AAClB/I,eAAK,CAAC,6CAAD,CAAL;AACAqb,eAAK;AACL,eAAKvP,IAAL,CAAU,OAAV,EAAmB,YAAW;AAC5B9L,iBAAK,CAAC,4BAAD,CAAL;AACA,cAAEqb,KAAF,IAAW3G,KAAK,EAAhB;AACD,WAHD;AAID;AACF,OApBD,MAoBO;AACLA,aAAK;AACN;AACF;AAED;AACF;AACA;AACA;AACA;;;;2BACS;AACL1U,WAAK,CAAC,SAAD,CAAL;AACA,WAAKwW,OAAL,GAAe,IAAf;AACA,WAAK8E,MAAL;AACA,WAAKrW,IAAL,CAAU,MAAV;AACD;AAED;AACF;AACA;AACA;AACA;;;;2BACSW,I,EAAM;AACX,UAAMvB,IAAI,GAAG,IAAb;AACArE,WAAK,CAAC,qBAAD,EAAwB4F,IAAxB,CAAL;;AACA,UAAM2V,QAAQ,GAAG,SAAXA,QAAW,CAASzV,MAAT,EAAiBwI,KAAjB,EAAwB+M,KAAxB,EAA+B;AAC9C;AACA,YAAI,cAAchX,IAAI,CAAC+N,UAAnB,IAAiCtM,MAAM,CAAC0C,IAAP,KAAgB,MAArD,EAA6D;AAC3DnE,cAAI,CAACkR,MAAL;AACD,SAJ6C,CAM9C;;;AACA,YAAI,YAAYzP,MAAM,CAAC0C,IAAvB,EAA6B;AAC3BnE,cAAI,CAAC2P,OAAL;AACA,iBAAO,KAAP;AACD,SAV6C,CAY9C;;;AACA3P,YAAI,CAACyP,QAAL,CAAchO,MAAd;AACD,OAdD,CAHW,CAmBX;;;AACArE,YAAM,CAAC+Z,aAAP,CAAqB5V,IAArB,EAA2B,KAAKzE,MAAL,CAAYgV,UAAvC,EAAmD3P,OAAnD,CAA2D+U,QAA3D,EApBW,CAsBX;;AACA,UAAI,aAAa,KAAKnJ,UAAtB,EAAkC;AAChC;AACA,aAAKoE,OAAL,GAAe,KAAf;AACA,aAAKvR,IAAL,CAAU,cAAV;;AAEA,YAAI,WAAW,KAAKmN,UAApB,EAAgC;AAC9B,eAAK+I,IAAL;AACD,SAFD,MAEO;AACLnb,eAAK,CAAC,sCAAD,EAAyC,KAAKoS,UAA9C,CAAL;AACD;AACF;AACF;AAED;AACF;AACA;AACA;AACA;;;;8BACY;AACR,UAAM/N,IAAI,GAAG,IAAb;;AAEA,eAASW,KAAT,GAAiB;AACfhF,aAAK,CAAC,sBAAD,CAAL;AACAqE,YAAI,CAACiC,KAAL,CAAW,CAAC;AAAEkC,cAAI,EAAE;AAAR,SAAD,CAAX;AACD;;AAED,UAAI,WAAW,KAAK4J,UAApB,EAAgC;AAC9BpS,aAAK,CAAC,0BAAD,CAAL;AACAgF,aAAK;AACN,OAHD,MAGO;AACL;AACA;AACAhF,aAAK,CAAC,sCAAD,CAAL;AACA,aAAK8L,IAAL,CAAU,MAAV,EAAkB9G,KAAlB;AACD;AACF;AAED;AACF;AACA;AACA;AACA;AACA;AACA;;;;0BACQiR,O,EAAS;AAAA;;AACb,WAAKlN,QAAL,GAAgB,KAAhB;AAEAtH,YAAM,CAACga,aAAP,CAAqBxF,OAArB,EAA8B,UAAArQ,IAAI,EAAI;AACpC,aAAI,CAAC8V,OAAL,CAAa9V,IAAb,EAAmB,YAAM;AACvB,eAAI,CAACmD,QAAL,GAAgB,IAAhB;;AACA,eAAI,CAAC9D,IAAL,CAAU,OAAV;AACD,SAHD;AAID,OALD;AAMD;AAED;AACF;AACA;AACA;AACA;;;;0BACQ;AACJ,UAAIhE,KAAK,GAAG,KAAKA,KAAL,IAAc,EAA1B;AACA,UAAM0a,MAAM,GAAG,KAAKrb,IAAL,CAAU6R,MAAV,GAAmB,OAAnB,GAA6B,MAA5C;AACA,UAAItH,IAAI,GAAG,EAAX,CAHI,CAKJ;;AACA,UAAI,UAAU,KAAKvK,IAAL,CAAUsb,iBAAxB,EAA2C;AACzC3a,aAAK,CAAC,KAAKX,IAAL,CAAUqS,cAAX,CAAL,GAAkCuI,KAAK,EAAvC;AACD;;AAED,UAAI,CAAC,KAAK5G,cAAN,IAAwB,CAACrT,KAAK,CAACkI,GAAnC,EAAwC;AACtClI,aAAK,CAAC4a,GAAN,GAAY,CAAZ;AACD;;AAED5a,WAAK,GAAGgR,OAAO,CAAC7L,MAAR,CAAenF,KAAf,CAAR,CAdI,CAgBJ;;AACA,UACE,KAAKX,IAAL,CAAUuK,IAAV,KACE,YAAY8Q,MAAZ,IAAsBrL,MAAM,CAAC,KAAKhQ,IAAL,CAAUuK,IAAX,CAAN,KAA2B,GAAlD,IACE,WAAW8Q,MAAX,IAAqBrL,MAAM,CAAC,KAAKhQ,IAAL,CAAUuK,IAAX,CAAN,KAA2B,EAFnD,CADF,EAIE;AACAA,YAAI,GAAG,MAAM,KAAKvK,IAAL,CAAUuK,IAAvB;AACD,OAvBG,CAyBJ;;;AACA,UAAI5J,KAAK,CAACmC,MAAV,EAAkB;AAChBnC,aAAK,GAAG,MAAMA,KAAd;AACD;;AAED,UAAM6J,IAAI,GAAG,KAAKxK,IAAL,CAAU4R,QAAV,CAAmB/N,OAAnB,CAA2B,GAA3B,MAAoC,CAAC,CAAlD;AACA,aACEwX,MAAM,GACN,KADA,IAEC7Q,IAAI,GAAG,MAAM,KAAKxK,IAAL,CAAU4R,QAAhB,GAA2B,GAA9B,GAAoC,KAAK5R,IAAL,CAAU4R,QAFnD,IAGArH,IAHA,GAIA,KAAKvK,IAAL,CAAUI,IAJV,GAKAO,KANF;AAQD;;;;AAtMD;AACF;AACA;wBACa;AACT,aAAO,SAAP;AACD;;;;EANmB8Q,S;;AA0MtB9R,MAAM,CAACb,OAAP,GAAiB4X,OAAjB,C;;;;;;;;;;;ACjNA,IAAMC,UAAU,GAAGtX,mBAAO,CAAC,gFAAD,CAA1B;;AAEAM,MAAM,CAACb,OAAP,GAAiB;AACf0c,WAAS,EAAE7E,UAAU,CAAC6E,SAAX,IAAwB7E,UAAU,CAAC8E,YAD/B;AAEfC,uBAAqB,EAAE,IAFR;AAGfC,mBAAiB,EAAE;AAHJ,CAAjB,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACFA,IAAMlK,SAAS,GAAGpS,mBAAO,CAAC,sEAAD,CAAzB;;AACA,IAAM8B,MAAM,GAAG9B,mBAAO,CAAC,sEAAD,CAAtB;;AACA,IAAMsS,OAAO,GAAGtS,mBAAO,CAAC,gDAAD,CAAvB;;AACA,IAAMub,KAAK,GAAGvb,mBAAO,CAAC,4CAAD,CAArB;;eACiBA,mBAAO,CAAC,4DAAD,C;IAAhB4Z,I,YAAAA,I;;gBAKJ5Z,mBAAO,CAAC,gHAAD,C;IAHTmc,S,aAAAA,S;IACAE,qB,aAAAA,qB;IACAC,iB,aAAAA,iB;;AAGF,IAAMjc,KAAK,GAAGL,mBAAO,CAAC,kDAAD,CAAP,CAAiB,4BAAjB,CAAd,C,CAEA;;;AACA,IAAMuc,aAAa,GACjB,OAAO/O,SAAP,KAAqB,WAArB,IACA,OAAOA,SAAS,CAACgP,OAAjB,KAA6B,QAD7B,IAEAhP,SAAS,CAACgP,OAAV,CAAkB9O,WAAlB,OAAoC,aAHtC;;IAKM+O,E;;;;;AACJ;AACF;AACA;AACA;AACA;AACA;AACE,cAAY9b,IAAZ,EAAkB;AAAA;;AAAA;;AAChB,8BAAMA,IAAN;AAEA,UAAKgU,cAAL,GAAsB,CAAChU,IAAI,CAACqZ,WAA5B;AAHgB;AAIjB;AAED;AACF;AACA;AACA;AACA;;;;;;AAKE;AACF;AACA;AACA;AACA;6BACW;AACP,UAAI,CAAC,KAAK0C,KAAL,EAAL,EAAmB;AACjB;AACA;AACD;;AAED,UAAMhc,GAAG,GAAG,KAAKA,GAAL,EAAZ;AACA,UAAMic,SAAS,GAAG,KAAKhc,IAAL,CAAUgc,SAA5B,CAPO,CASP;;AACA,UAAMhc,IAAI,GAAG4b,aAAa,GACtB,EADsB,GAEtB3C,IAAI,CACF,KAAKjZ,IADH,EAEF,OAFE,EAGF,mBAHE,EAIF,KAJE,EAKF,KALE,EAMF,YANE,EAOF,MAPE,EAQF,IARE,EASF,SATE,EAUF,oBAVE,EAWF,cAXE,EAYF,iBAZE,EAaF,QAbE,EAcF,YAdE,EAeF,QAfE,EAgBF,qBAhBE,CAFR;;AAqBA,UAAI,KAAKA,IAAL,CAAU2Z,YAAd,EAA4B;AAC1B3Z,YAAI,CAACic,OAAL,GAAe,KAAKjc,IAAL,CAAU2Z,YAAzB;AACD;;AAED,UAAI;AACF,aAAKuC,EAAL,GACER,qBAAqB,IAAI,CAACE,aAA1B,GACII,SAAS,GACP,IAAIR,SAAJ,CAAczb,GAAd,EAAmBic,SAAnB,CADO,GAEP,IAAIR,SAAJ,CAAczb,GAAd,CAHN,GAII,IAAIyb,SAAJ,CAAczb,GAAd,EAAmBic,SAAnB,EAA8Bhc,IAA9B,CALN;AAMD,OAPD,CAOE,OAAOqE,GAAP,EAAY;AACZ,eAAO,KAAKM,IAAL,CAAU,OAAV,EAAmBN,GAAnB,CAAP;AACD;;AAED,WAAK6X,EAAL,CAAQrG,UAAR,GAAqB,KAAKhV,MAAL,CAAYgV,UAAZ,IAA0B8F,iBAA/C;AAEA,WAAKQ,iBAAL;AACD;AAED;AACF;AACA;AACA;AACA;;;;wCACsB;AAClB,UAAMpY,IAAI,GAAG,IAAb;;AAEA,WAAKmY,EAAL,CAAQ/X,MAAR,GAAiB,YAAW;AAC1BJ,YAAI,CAACkR,MAAL;AACD,OAFD;;AAGA,WAAKiH,EAAL,CAAQ9W,OAAR,GAAkB,YAAW;AAC3BrB,YAAI,CAAC2P,OAAL;AACD,OAFD;;AAGA,WAAKwI,EAAL,CAAQE,SAAR,GAAoB,UAASzV,EAAT,EAAa;AAC/B5C,YAAI,CAACiT,MAAL,CAAYrQ,EAAE,CAACrB,IAAf;AACD,OAFD;;AAGA,WAAK4W,EAAL,CAAQ/W,OAAR,GAAkB,UAASiO,CAAT,EAAY;AAC5BrP,YAAI,CAAC0P,OAAL,CAAa,iBAAb,EAAgCL,CAAhC;AACD,OAFD;AAGD;AAED;AACF;AACA;AACA;AACA;AACA;;;;0BACQuC,O,EAAS;AACb,UAAM5R,IAAI,GAAG,IAAb;AACA,WAAK0E,QAAL,GAAgB,KAAhB,CAFa,CAIb;AACA;;AACA,UAAIsS,KAAK,GAAGpF,OAAO,CAAC7S,MAApB;AACA,UAAIiD,CAAC,GAAG,CAAR;AACA,UAAM2O,CAAC,GAAGqG,KAAV;;AACA,aAAOhV,CAAC,GAAG2O,CAAX,EAAc3O,CAAC,EAAf,EAAmB;AACjB,SAAC,UAASP,MAAT,EAAiB;AAChBrE,gBAAM,CAACkb,YAAP,CAAoB7W,MAApB,EAA4BzB,IAAI,CAACiQ,cAAjC,EAAiD,UAAS1O,IAAT,EAAe;AAC9D;AACA,gBAAMtF,IAAI,GAAG,EAAb;;AACA,gBAAI,CAAC0b,qBAAL,EAA4B;AAC1B,kBAAIlW,MAAM,CAACS,OAAX,EAAoB;AAClBjG,oBAAI,CAACqI,QAAL,GAAgB7C,MAAM,CAACS,OAAP,CAAeoC,QAA/B;AACD;;AAED,kBAAItE,IAAI,CAAC/D,IAAL,CAAUwS,iBAAd,EAAiC;AAC/B,oBAAM1G,GAAG,GACP,aAAa,OAAOxG,IAApB,GACIgX,MAAM,CAACC,UAAP,CAAkBjX,IAAlB,CADJ,GAEIA,IAAI,CAACxC,MAHX;;AAIA,oBAAIgJ,GAAG,GAAG/H,IAAI,CAAC/D,IAAL,CAAUwS,iBAAV,CAA4BC,SAAtC,EAAiD;AAC/CzS,sBAAI,CAACqI,QAAL,GAAgB,KAAhB;AACD;AACF;AACF,aAjB6D,CAmB9D;AACA;AACA;;;AACA,gBAAI;AACF,kBAAIqT,qBAAJ,EAA2B;AACzB;AACA3X,oBAAI,CAACmY,EAAL,CAAQjI,IAAR,CAAa3O,IAAb;AACD,eAHD,MAGO;AACLvB,oBAAI,CAACmY,EAAL,CAAQjI,IAAR,CAAa3O,IAAb,EAAmBtF,IAAnB;AACD;AACF,aAPD,CAOE,OAAOoT,CAAP,EAAU;AACV1T,mBAAK,CAAC,uCAAD,CAAL;AACD;;AAED,cAAEqb,KAAF,IAAWyB,IAAI,EAAf;AACD,WAlCD;AAmCD,SApCD,EAoCG7G,OAAO,CAAC5P,CAAD,CApCV;AAqCD;;AAED,eAASyW,IAAT,GAAgB;AACdzY,YAAI,CAACY,IAAL,CAAU,OAAV,EADc,CAGd;AACA;;AACAF,kBAAU,CAAC,YAAW;AACpBV,cAAI,CAAC0E,QAAL,GAAgB,IAAhB;AACA1E,cAAI,CAACY,IAAL,CAAU,OAAV;AACD,SAHS,EAGP,CAHO,CAAV;AAID;AACF;AAED;AACF;AACA;AACA;AACA;;;;8BACY;AACR8M,eAAS,CAAC7G,SAAV,CAAoB8I,OAApB,CAA4BpD,IAA5B,CAAiC,IAAjC;AACD;AAED;AACF;AACA;AACA;AACA;;;;8BACY;AACR,UAAI,OAAO,KAAK4L,EAAZ,KAAmB,WAAvB,EAAoC;AAClC,aAAKA,EAAL,CAAQxX,KAAR;AACA,aAAKwX,EAAL,GAAU,IAAV;AACD;AACF;AAED;AACF;AACA;AACA;AACA;;;;0BACQ;AACJ,UAAIvb,KAAK,GAAG,KAAKA,KAAL,IAAc,EAA1B;AACA,UAAM0a,MAAM,GAAG,KAAKrb,IAAL,CAAU6R,MAAV,GAAmB,KAAnB,GAA2B,IAA1C;AACA,UAAItH,IAAI,GAAG,EAAX,CAHI,CAKJ;;AACA,UACE,KAAKvK,IAAL,CAAUuK,IAAV,KACE,UAAU8Q,MAAV,IAAoBrL,MAAM,CAAC,KAAKhQ,IAAL,CAAUuK,IAAX,CAAN,KAA2B,GAAhD,IACE,SAAS8Q,MAAT,IAAmBrL,MAAM,CAAC,KAAKhQ,IAAL,CAAUuK,IAAX,CAAN,KAA2B,EAFjD,CADF,EAIE;AACAA,YAAI,GAAG,MAAM,KAAKvK,IAAL,CAAUuK,IAAvB;AACD,OAZG,CAcJ;;;AACA,UAAI,KAAKvK,IAAL,CAAUsb,iBAAd,EAAiC;AAC/B3a,aAAK,CAAC,KAAKX,IAAL,CAAUqS,cAAX,CAAL,GAAkCuI,KAAK,EAAvC;AACD,OAjBG,CAmBJ;;;AACA,UAAI,CAAC,KAAK5G,cAAV,EAA0B;AACxBrT,aAAK,CAAC4a,GAAN,GAAY,CAAZ;AACD;;AAED5a,WAAK,GAAGgR,OAAO,CAAC7L,MAAR,CAAenF,KAAf,CAAR,CAxBI,CA0BJ;;AACA,UAAIA,KAAK,CAACmC,MAAV,EAAkB;AAChBnC,aAAK,GAAG,MAAMA,KAAd;AACD;;AAED,UAAM6J,IAAI,GAAG,KAAKxK,IAAL,CAAU4R,QAAV,CAAmB/N,OAAnB,CAA2B,GAA3B,MAAoC,CAAC,CAAlD;AACA,aACEwX,MAAM,GACN,KADA,IAEC7Q,IAAI,GAAG,MAAM,KAAKxK,IAAL,CAAU4R,QAAhB,GAA2B,GAA9B,GAAoC,KAAK5R,IAAL,CAAU4R,QAFnD,IAGArH,IAHA,GAIA,KAAKvK,IAAL,CAAUI,IAJV,GAKAO,KANF;AAQD;AAED;AACF;AACA;AACA;AACA;AACA;;;;4BACU;AACN,aACE,CAAC,CAAC6a,SAAF,IACA,EAAE,kBAAkBA,SAAlB,IAA+B,KAAKrK,IAAL,KAAc2K,EAAE,CAAClR,SAAH,CAAauG,IAA5D,CAFF;AAID;;;wBApOU;AACT,aAAO,WAAP;AACD;;;;EApBcM,S;;AAyPjB9R,MAAM,CAACb,OAAP,GAAiBgd,EAAjB,C;;;;;;;;;;;AC5QAnc,MAAM,CAACb,OAAP,CAAema,IAAf,GAAsB,UAACvS,GAAD,EAAkB;AAAA,oCAAT+V,IAAS;AAATA,QAAS;AAAA;;AACtC,SAAOA,IAAI,CAACC,MAAL,CAAY,UAACC,GAAD,EAAMC,CAAN,EAAY;AAC7B,QAAIlW,GAAG,CAACuB,cAAJ,CAAmB2U,CAAnB,CAAJ,EAA2B;AACzBD,SAAG,CAACC,CAAD,CAAH,GAASlW,GAAG,CAACkW,CAAD,CAAZ;AACD;;AACD,WAAOD,GAAP;AACD,GALM,EAKJ,EALI,CAAP;AAMD,CAPD,C;;;;;;;;;;;ACAA;AAEA,IAAME,OAAO,GAAGxd,mBAAO,CAAC,kDAAD,CAAvB;;AACA,IAAMsX,UAAU,GAAGtX,mBAAO,CAAC,+EAAD,CAA1B;;AAEAM,MAAM,CAACb,OAAP,GAAiB,UAASkB,IAAT,EAAe;AAC9B,MAAMuW,OAAO,GAAGvW,IAAI,CAACuW,OAArB,CAD8B,CAG9B;AACA;;AACA,MAAMC,OAAO,GAAGxW,IAAI,CAACwW,OAArB,CAL8B,CAO9B;AACA;;AACA,MAAMiE,UAAU,GAAGza,IAAI,CAACya,UAAxB,CAT8B,CAW9B;;AACA,MAAI;AACF,QAAI,gBAAgB,OAAO3E,cAAvB,KAA0C,CAACS,OAAD,IAAYsG,OAAtD,CAAJ,EAAoE;AAClE,aAAO,IAAI/G,cAAJ,EAAP;AACD;AACF,GAJD,CAIE,OAAO1C,CAAP,EAAU,CAAE,CAhBgB,CAkB9B;AACA;AACA;;;AACA,MAAI;AACF,QAAI,gBAAgB,OAAOoH,cAAvB,IAAyC,CAAChE,OAA1C,IAAqDiE,UAAzD,EAAqE;AACnE,aAAO,IAAID,cAAJ,EAAP;AACD;AACF,GAJD,CAIE,OAAOpH,CAAP,EAAU,CAAE;;AAEd,MAAI,CAACmD,OAAL,EAAc;AACZ,QAAI;AACF,aAAO,IAAII,UAAU,CAAC,CAAC,QAAD,EAAWmG,MAAX,CAAkB,QAAlB,EAA4B5L,IAA5B,CAAiC,GAAjC,CAAD,CAAd,CACL,mBADK,CAAP;AAGD,KAJD,CAIE,OAAOkC,CAAP,EAAU,CAAE;AACf;AACF,CAlCD,C;;;;;;;;;;;ACLA,IAAM2J,YAAY,GAAGne,MAAM,CAAC8a,MAAP,CAAc,IAAd,CAArB,C,CAA0C;;AAC1CqD,YAAY,CAAC,MAAD,CAAZ,GAAuB,GAAvB;AACAA,YAAY,CAAC,OAAD,CAAZ,GAAwB,GAAxB;AACAA,YAAY,CAAC,MAAD,CAAZ,GAAuB,GAAvB;AACAA,YAAY,CAAC,MAAD,CAAZ,GAAuB,GAAvB;AACAA,YAAY,CAAC,SAAD,CAAZ,GAA0B,GAA1B;AACAA,YAAY,CAAC,SAAD,CAAZ,GAA0B,GAA1B;AACAA,YAAY,CAAC,MAAD,CAAZ,GAAuB,GAAvB;AAEA,IAAMC,oBAAoB,GAAGpe,MAAM,CAAC8a,MAAP,CAAc,IAAd,CAA7B;AACA9a,MAAM,CAAC8G,IAAP,CAAYqX,YAAZ,EAA0B7W,OAA1B,CAAkC,UAAAkF,GAAG,EAAI;AACvC4R,sBAAoB,CAACD,YAAY,CAAC3R,GAAD,CAAb,CAApB,GAA0CA,GAA1C;AACD,CAFD;AAIA,IAAM6R,YAAY,GAAG;AAAE/U,MAAI,EAAE,OAAR;AAAiB5C,MAAI,EAAE;AAAvB,CAArB;AAEA3F,MAAM,CAACb,OAAP,GAAiB;AACfie,cAAY,EAAZA,YADe;AAEfC,sBAAoB,EAApBA,oBAFe;AAGfC,cAAY,EAAZA;AAHe,CAAjB,C;;;;;;;;;;;eChB+C5d,mBAAO,CAAC,iEAAD,C;IAA9C2d,oB,YAAAA,oB;IAAsBC,Y,YAAAA,Y;;AAE9B,IAAMC,qBAAqB,GAAG,OAAOC,WAAP,KAAuB,UAArD;AAEA,IAAIC,aAAJ;;AACA,IAAIF,qBAAJ,EAA2B;AACzBE,eAAa,GAAG/d,mBAAO,CAAC,qHAAD,CAAvB;AACD;;AAED,IAAMuW,YAAY,GAAG,SAAfA,YAAe,CAACyH,aAAD,EAAgBxH,UAAhB,EAA+B;AAClD,MAAI,OAAOwH,aAAP,KAAyB,QAA7B,EAAuC;AACrC,WAAO;AACLnV,UAAI,EAAE,SADD;AAEL5C,UAAI,EAAEgY,SAAS,CAACD,aAAD,EAAgBxH,UAAhB;AAFV,KAAP;AAID;;AACD,MAAM3N,IAAI,GAAGmV,aAAa,CAAChT,MAAd,CAAqB,CAArB,CAAb;;AACA,MAAInC,IAAI,KAAK,GAAb,EAAkB;AAChB,WAAO;AACLA,UAAI,EAAE,SADD;AAEL5C,UAAI,EAAEiY,kBAAkB,CAACF,aAAa,CAAC/L,SAAd,CAAwB,CAAxB,CAAD,EAA6BuE,UAA7B;AAFnB,KAAP;AAID;;AACD,MAAM2H,UAAU,GAAGR,oBAAoB,CAAC9U,IAAD,CAAvC;;AACA,MAAI,CAACsV,UAAL,EAAiB;AACf,WAAOP,YAAP;AACD;;AACD,SAAOI,aAAa,CAACva,MAAd,GAAuB,CAAvB,GACH;AACEoF,QAAI,EAAE8U,oBAAoB,CAAC9U,IAAD,CAD5B;AAEE5C,QAAI,EAAE+X,aAAa,CAAC/L,SAAd,CAAwB,CAAxB;AAFR,GADG,GAKH;AACEpJ,QAAI,EAAE8U,oBAAoB,CAAC9U,IAAD;AAD5B,GALJ;AAQD,CA1BD;;AA4BA,IAAMqV,kBAAkB,GAAG,SAArBA,kBAAqB,CAACjY,IAAD,EAAOuQ,UAAP,EAAsB;AAC/C,MAAIuH,aAAJ,EAAmB;AACjB,QAAMK,OAAO,GAAGL,aAAa,CAACzK,MAAd,CAAqBrN,IAArB,CAAhB;AACA,WAAOgY,SAAS,CAACG,OAAD,EAAU5H,UAAV,CAAhB;AACD,GAHD,MAGO;AACL,WAAO;AAAE6H,YAAM,EAAE,IAAV;AAAgBpY,UAAI,EAAJA;AAAhB,KAAP,CADK,CAC0B;AAChC;AACF,CAPD;;AASA,IAAMgY,SAAS,GAAG,SAAZA,SAAY,CAAChY,IAAD,EAAOuQ,UAAP,EAAsB;AACtC,UAAQA,UAAR;AACE,SAAK,MAAL;AACE,aAAOvQ,IAAI,YAAY6X,WAAhB,GAA8B,IAAIQ,IAAJ,CAAS,CAACrY,IAAD,CAAT,CAA9B,GAAiDA,IAAxD;;AACF,SAAK,aAAL;AACA;AACE,aAAOA,IAAP;AAAa;AALjB;AAOD,CARD;;AAUA3F,MAAM,CAACb,OAAP,GAAiB8W,YAAjB,C;;;;;;;;;;;eCxDyBvW,mBAAO,CAAC,iEAAD,C;IAAxB0d,Y,YAAAA,Y;;AAER,IAAMa,cAAc,GAClB,OAAOD,IAAP,KAAgB,UAAhB,IACC,OAAOA,IAAP,KAAgB,WAAhB,IACC/e,MAAM,CAACgM,SAAP,CAAiByG,QAAjB,CAA0Bf,IAA1B,CAA+BqN,IAA/B,MAAyC,0BAH7C;AAIA,IAAMT,qBAAqB,GAAG,OAAOC,WAAP,KAAuB,UAArD,C,CAEA;;AACA,IAAMU,MAAM,GAAG,SAATA,MAAS,CAAAnX,GAAG,EAAI;AACpB,SAAO,OAAOyW,WAAW,CAACU,MAAnB,KAA8B,UAA9B,GACHV,WAAW,CAACU,MAAZ,CAAmBnX,GAAnB,CADG,GAEHA,GAAG,IAAIA,GAAG,CAACoX,MAAJ,YAAsBX,WAFjC;AAGD,CAJD;;AAMA,IAAMd,YAAY,GAAG,SAAfA,YAAe,OAAiBrI,cAAjB,EAAiCiH,QAAjC,EAA8C;AAAA,MAA3C/S,IAA2C,QAA3CA,IAA2C;AAAA,MAArC5C,IAAqC,QAArCA,IAAqC;;AACjE,MAAIsY,cAAc,IAAItY,IAAI,YAAYqY,IAAtC,EAA4C;AAC1C,QAAI3J,cAAJ,EAAoB;AAClB,aAAOiH,QAAQ,CAAC3V,IAAD,CAAf;AACD,KAFD,MAEO;AACL,aAAOyY,kBAAkB,CAACzY,IAAD,EAAO2V,QAAP,CAAzB;AACD;AACF,GAND,MAMO,IACLiC,qBAAqB,KACpB5X,IAAI,YAAY6X,WAAhB,IAA+BU,MAAM,CAACvY,IAAD,CADjB,CADhB,EAGL;AACA,QAAI0O,cAAJ,EAAoB;AAClB,aAAOiH,QAAQ,CAAC3V,IAAI,YAAY6X,WAAhB,GAA8B7X,IAA9B,GAAqCA,IAAI,CAACwY,MAA3C,CAAf;AACD,KAFD,MAEO;AACL,aAAOC,kBAAkB,CAAC,IAAIJ,IAAJ,CAAS,CAACrY,IAAD,CAAT,CAAD,EAAmB2V,QAAnB,CAAzB;AACD;AACF,GAhBgE,CAiBjE;;;AACA,SAAOA,QAAQ,CAAC8B,YAAY,CAAC7U,IAAD,CAAZ,IAAsB5C,IAAI,IAAI,EAA9B,CAAD,CAAf;AACD,CAnBD;;AAqBA,IAAMyY,kBAAkB,GAAG,SAArBA,kBAAqB,CAACzY,IAAD,EAAO2V,QAAP,EAAoB;AAC7C,MAAM+C,UAAU,GAAG,IAAIC,UAAJ,EAAnB;;AACAD,YAAU,CAAChF,MAAX,GAAoB,YAAW;AAC7B,QAAMkF,OAAO,GAAGF,UAAU,CAACG,MAAX,CAAkBrN,KAAlB,CAAwB,GAAxB,EAA6B,CAA7B,CAAhB;AACAmK,YAAQ,CAAC,MAAMiD,OAAP,CAAR;AACD,GAHD;;AAIA,SAAOF,UAAU,CAACI,aAAX,CAAyB9Y,IAAzB,CAAP;AACD,CAPD;;AASA3F,MAAM,CAACb,OAAP,GAAiBud,YAAjB,C;;;;;;;;;;;AC7CA,IAAMA,YAAY,GAAGhd,mBAAO,CAAC,mFAAD,CAA5B;;AACA,IAAMuW,YAAY,GAAGvW,mBAAO,CAAC,mFAAD,CAA5B;;AAEA,IAAMgf,SAAS,GAAGC,MAAM,CAACC,YAAP,CAAoB,EAApB,CAAlB,C,CAA2C;;AAE3C,IAAMpD,aAAa,GAAG,SAAhBA,aAAgB,CAACxF,OAAD,EAAUsF,QAAV,EAAuB;AAC3C;AACA,MAAMnY,MAAM,GAAG6S,OAAO,CAAC7S,MAAvB;AACA,MAAM+C,cAAc,GAAG,IAAIgG,KAAJ,CAAU/I,MAAV,CAAvB;AACA,MAAI0b,KAAK,GAAG,CAAZ;AAEA7I,SAAO,CAACzP,OAAR,CAAgB,UAACV,MAAD,EAASO,CAAT,EAAe;AAC7B;AACAsW,gBAAY,CAAC7W,MAAD,EAAS,KAAT,EAAgB,UAAA6X,aAAa,EAAI;AAC3CxX,oBAAc,CAACE,CAAD,CAAd,GAAoBsX,aAApB;;AACA,UAAI,EAAEmB,KAAF,KAAY1b,MAAhB,EAAwB;AACtBmY,gBAAQ,CAACpV,cAAc,CAACqL,IAAf,CAAoBmN,SAApB,CAAD,CAAR;AACD;AACF,KALW,CAAZ;AAMD,GARD;AASD,CAfD;;AAiBA,IAAMnD,aAAa,GAAG,SAAhBA,aAAgB,CAACuD,cAAD,EAAiB5I,UAAjB,EAAgC;AACpD,MAAMhQ,cAAc,GAAG4Y,cAAc,CAAC3N,KAAf,CAAqBuN,SAArB,CAAvB;AACA,MAAM1I,OAAO,GAAG,EAAhB;;AACA,OAAK,IAAI5P,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,cAAc,CAAC/C,MAAnC,EAA2CiD,CAAC,EAA5C,EAAgD;AAC9C,QAAM2Y,aAAa,GAAG9I,YAAY,CAAC/P,cAAc,CAACE,CAAD,CAAf,EAAoB8P,UAApB,CAAlC;AACAF,WAAO,CAAC9Q,IAAR,CAAa6Z,aAAb;;AACA,QAAIA,aAAa,CAACxW,IAAd,KAAuB,OAA3B,EAAoC;AAClC;AACD;AACF;;AACD,SAAOyN,OAAP;AACD,CAXD;;AAaAhW,MAAM,CAACb,OAAP,GAAiB;AACfK,UAAQ,EAAE,CADK;AAEfkd,cAAY,EAAZA,YAFe;AAGflB,eAAa,EAAbA,aAHe;AAIfvF,cAAY,EAAZA,YAJe;AAKfsF,eAAa,EAAbA;AALe,CAAjB,C;;;;;;;;;;;ACnCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,UAASyD,KAAT,EAAe;AACd;;AAEA7f,SAAO,CAACgH,MAAR,GAAiB,UAAS8Y,WAAT,EAAsB;AACrC,QAAIC,KAAK,GAAG,IAAIC,UAAJ,CAAeF,WAAf,CAAZ;AAAA,QACA7Y,CADA;AAAA,QACG+F,GAAG,GAAG+S,KAAK,CAAC/b,MADf;AAAA,QACuB4a,MAAM,GAAG,EADhC;;AAGA,SAAK3X,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG+F,GAAhB,EAAqB/F,CAAC,IAAE,CAAxB,EAA2B;AACzB2X,YAAM,IAAIiB,KAAK,CAACE,KAAK,CAAC9Y,CAAD,CAAL,IAAY,CAAb,CAAf;AACA2X,YAAM,IAAIiB,KAAK,CAAE,CAACE,KAAK,CAAC9Y,CAAD,CAAL,GAAW,CAAZ,KAAkB,CAAnB,GAAyB8Y,KAAK,CAAC9Y,CAAC,GAAG,CAAL,CAAL,IAAgB,CAA1C,CAAf;AACA2X,YAAM,IAAIiB,KAAK,CAAE,CAACE,KAAK,CAAC9Y,CAAC,GAAG,CAAL,CAAL,GAAe,EAAhB,KAAuB,CAAxB,GAA8B8Y,KAAK,CAAC9Y,CAAC,GAAG,CAAL,CAAL,IAAgB,CAA/C,CAAf;AACA2X,YAAM,IAAIiB,KAAK,CAACE,KAAK,CAAC9Y,CAAC,GAAG,CAAL,CAAL,GAAe,EAAhB,CAAf;AACD;;AAED,QAAK+F,GAAG,GAAG,CAAP,KAAc,CAAlB,EAAqB;AACnB4R,YAAM,GAAGA,MAAM,CAACpM,SAAP,CAAiB,CAAjB,EAAoBoM,MAAM,CAAC5a,MAAP,GAAgB,CAApC,IAAyC,GAAlD;AACD,KAFD,MAEO,IAAIgJ,GAAG,GAAG,CAAN,KAAY,CAAhB,EAAmB;AACxB4R,YAAM,GAAGA,MAAM,CAACpM,SAAP,CAAiB,CAAjB,EAAoBoM,MAAM,CAAC5a,MAAP,GAAgB,CAApC,IAAyC,IAAlD;AACD;;AAED,WAAO4a,MAAP;AACD,GAlBD;;AAoBA5e,SAAO,CAAC6T,MAAR,GAAkB,UAAS+K,MAAT,EAAiB;AACjC,QAAIqB,YAAY,GAAGrB,MAAM,CAAC5a,MAAP,GAAgB,IAAnC;AAAA,QACAgJ,GAAG,GAAG4R,MAAM,CAAC5a,MADb;AAAA,QACqBiD,CADrB;AAAA,QACwBiZ,CAAC,GAAG,CAD5B;AAAA,QAEAC,QAFA;AAAA,QAEUC,QAFV;AAAA,QAEoBC,QAFpB;AAAA,QAE8BC,QAF9B;;AAIA,QAAI1B,MAAM,CAACA,MAAM,CAAC5a,MAAP,GAAgB,CAAjB,CAAN,KAA8B,GAAlC,EAAuC;AACrCic,kBAAY;;AACZ,UAAIrB,MAAM,CAACA,MAAM,CAAC5a,MAAP,GAAgB,CAAjB,CAAN,KAA8B,GAAlC,EAAuC;AACrCic,oBAAY;AACb;AACF;;AAED,QAAIH,WAAW,GAAG,IAAIzB,WAAJ,CAAgB4B,YAAhB,CAAlB;AAAA,QACAF,KAAK,GAAG,IAAIC,UAAJ,CAAeF,WAAf,CADR;;AAGA,SAAK7Y,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG+F,GAAhB,EAAqB/F,CAAC,IAAE,CAAxB,EAA2B;AACzBkZ,cAAQ,GAAGN,KAAK,CAAC9a,OAAN,CAAc6Z,MAAM,CAAC3X,CAAD,CAApB,CAAX;AACAmZ,cAAQ,GAAGP,KAAK,CAAC9a,OAAN,CAAc6Z,MAAM,CAAC3X,CAAC,GAAC,CAAH,CAApB,CAAX;AACAoZ,cAAQ,GAAGR,KAAK,CAAC9a,OAAN,CAAc6Z,MAAM,CAAC3X,CAAC,GAAC,CAAH,CAApB,CAAX;AACAqZ,cAAQ,GAAGT,KAAK,CAAC9a,OAAN,CAAc6Z,MAAM,CAAC3X,CAAC,GAAC,CAAH,CAApB,CAAX;AAEA8Y,WAAK,CAACG,CAAC,EAAF,CAAL,GAAcC,QAAQ,IAAI,CAAb,GAAmBC,QAAQ,IAAI,CAA5C;AACAL,WAAK,CAACG,CAAC,EAAF,CAAL,GAAc,CAACE,QAAQ,GAAG,EAAZ,KAAmB,CAApB,GAA0BC,QAAQ,IAAI,CAAnD;AACAN,WAAK,CAACG,CAAC,EAAF,CAAL,GAAc,CAACG,QAAQ,GAAG,CAAZ,KAAkB,CAAnB,GAAyBC,QAAQ,GAAG,EAAjD;AACD;;AAED,WAAOR,WAAP;AACD,GA3BD;AA4BD,CAnDD,EAmDG,kEAnDH,E;;;;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA,IAAI;AACFjf,QAAM,CAACb,OAAP,GAAiB,OAAOgX,cAAP,KAA0B,WAA1B,IACf,qBAAqB,IAAIA,cAAJ,EADvB;AAED,CAHD,CAGE,OAAOzR,GAAP,EAAY;AACZ;AACA;AACA1E,QAAM,CAACb,OAAP,GAAiB,KAAjB;AACD,C;;;;;;;;;;;;;AChBD;AACA;AACA;AAEA,IAAIugB,CAAC,GAAG,IAAR;AACA,IAAIC,CAAC,GAAGD,CAAC,GAAG,EAAZ;AACA,IAAIE,CAAC,GAAGD,CAAC,GAAG,EAAZ;AACA,IAAIE,CAAC,GAAGD,CAAC,GAAG,EAAZ;AACA,IAAIE,CAAC,GAAGD,CAAC,GAAG,CAAZ;AACA,IAAIE,CAAC,GAAGF,CAAC,GAAG,MAAZ;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA7f,MAAM,CAACb,OAAP,GAAiB,UAASuR,GAAT,EAAcpK,OAAd,EAAuB;AACtCA,SAAO,GAAGA,OAAO,IAAI,EAArB;;AACA,MAAIiC,IAAI,WAAUmI,GAAV,CAAR;;AACA,MAAInI,IAAI,KAAK,QAAT,IAAqBmI,GAAG,CAACvN,MAAJ,GAAa,CAAtC,EAAyC;AACvC,WAAO8R,KAAK,CAACvE,GAAD,CAAZ;AACD,GAFD,MAEO,IAAInI,IAAI,KAAK,QAAT,IAAqByX,QAAQ,CAACtP,GAAD,CAAjC,EAAwC;AAC7C,WAAOpK,OAAO,QAAP,GAAe2Z,OAAO,CAACvP,GAAD,CAAtB,GAA8BwP,QAAQ,CAACxP,GAAD,CAA7C;AACD;;AACD,QAAM,IAAIzL,KAAJ,CACJ,0DACEmK,IAAI,CAACC,SAAL,CAAeqB,GAAf,CAFE,CAAN;AAID,CAZD;AAcA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEA,SAASuE,KAAT,CAAekL,GAAf,EAAoB;AAClBA,KAAG,GAAGxB,MAAM,CAACwB,GAAD,CAAZ;;AACA,MAAIA,GAAG,CAAChd,MAAJ,GAAa,GAAjB,EAAsB;AACpB;AACD;;AACD,MAAIkK,KAAK,GAAG,mIAAmI+S,IAAnI,CACVD,GADU,CAAZ;;AAGA,MAAI,CAAC9S,KAAL,EAAY;AACV;AACD;;AACD,MAAIgT,CAAC,GAAGC,UAAU,CAACjT,KAAK,CAAC,CAAD,CAAN,CAAlB;AACA,MAAI9E,IAAI,GAAG,CAAC8E,KAAK,CAAC,CAAD,CAAL,IAAY,IAAb,EAAmBD,WAAnB,EAAX;;AACA,UAAQ7E,IAAR;AACE,SAAK,OAAL;AACA,SAAK,MAAL;AACA,SAAK,KAAL;AACA,SAAK,IAAL;AACA,SAAK,GAAL;AACE,aAAO8X,CAAC,GAAGN,CAAX;;AACF,SAAK,OAAL;AACA,SAAK,MAAL;AACA,SAAK,GAAL;AACE,aAAOM,CAAC,GAAGP,CAAX;;AACF,SAAK,MAAL;AACA,SAAK,KAAL;AACA,SAAK,GAAL;AACE,aAAOO,CAAC,GAAGR,CAAX;;AACF,SAAK,OAAL;AACA,SAAK,MAAL;AACA,SAAK,KAAL;AACA,SAAK,IAAL;AACA,SAAK,GAAL;AACE,aAAOQ,CAAC,GAAGT,CAAX;;AACF,SAAK,SAAL;AACA,SAAK,QAAL;AACA,SAAK,MAAL;AACA,SAAK,KAAL;AACA,SAAK,GAAL;AACE,aAAOS,CAAC,GAAGV,CAAX;;AACF,SAAK,SAAL;AACA,SAAK,QAAL;AACA,SAAK,MAAL;AACA,SAAK,KAAL;AACA,SAAK,GAAL;AACE,aAAOU,CAAC,GAAGX,CAAX;;AACF,SAAK,cAAL;AACA,SAAK,aAAL;AACA,SAAK,OAAL;AACA,SAAK,MAAL;AACA,SAAK,IAAL;AACE,aAAOW,CAAP;;AACF;AACE,aAAO/f,SAAP;AAxCJ;AA0CD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEA,SAAS4f,QAAT,CAAkBnV,EAAlB,EAAsB;AACpB,MAAIwV,KAAK,GAAGrV,IAAI,CAAC+E,GAAL,CAASlF,EAAT,CAAZ;;AACA,MAAIwV,KAAK,IAAIV,CAAb,EAAgB;AACd,WAAO3U,IAAI,CAACsV,KAAL,CAAWzV,EAAE,GAAG8U,CAAhB,IAAqB,GAA5B;AACD;;AACD,MAAIU,KAAK,IAAIX,CAAb,EAAgB;AACd,WAAO1U,IAAI,CAACsV,KAAL,CAAWzV,EAAE,GAAG6U,CAAhB,IAAqB,GAA5B;AACD;;AACD,MAAIW,KAAK,IAAIZ,CAAb,EAAgB;AACd,WAAOzU,IAAI,CAACsV,KAAL,CAAWzV,EAAE,GAAG4U,CAAhB,IAAqB,GAA5B;AACD;;AACD,MAAIY,KAAK,IAAIb,CAAb,EAAgB;AACd,WAAOxU,IAAI,CAACsV,KAAL,CAAWzV,EAAE,GAAG2U,CAAhB,IAAqB,GAA5B;AACD;;AACD,SAAO3U,EAAE,GAAG,IAAZ;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEA,SAASkV,OAAT,CAAiBlV,EAAjB,EAAqB;AACnB,MAAIwV,KAAK,GAAGrV,IAAI,CAAC+E,GAAL,CAASlF,EAAT,CAAZ;;AACA,MAAIwV,KAAK,IAAIV,CAAb,EAAgB;AACd,WAAOY,MAAM,CAAC1V,EAAD,EAAKwV,KAAL,EAAYV,CAAZ,EAAe,KAAf,CAAb;AACD;;AACD,MAAIU,KAAK,IAAIX,CAAb,EAAgB;AACd,WAAOa,MAAM,CAAC1V,EAAD,EAAKwV,KAAL,EAAYX,CAAZ,EAAe,MAAf,CAAb;AACD;;AACD,MAAIW,KAAK,IAAIZ,CAAb,EAAgB;AACd,WAAOc,MAAM,CAAC1V,EAAD,EAAKwV,KAAL,EAAYZ,CAAZ,EAAe,QAAf,CAAb;AACD;;AACD,MAAIY,KAAK,IAAIb,CAAb,EAAgB;AACd,WAAOe,MAAM,CAAC1V,EAAD,EAAKwV,KAAL,EAAYb,CAAZ,EAAe,QAAf,CAAb;AACD;;AACD,SAAO3U,EAAE,GAAG,KAAZ;AACD;AAED;AACA;AACA;;;AAEA,SAAS0V,MAAT,CAAgB1V,EAAhB,EAAoBwV,KAApB,EAA2BF,CAA3B,EAA8B7O,IAA9B,EAAoC;AAClC,MAAIkP,QAAQ,GAAGH,KAAK,IAAIF,CAAC,GAAG,GAA5B;AACA,SAAOnV,IAAI,CAACsV,KAAL,CAAWzV,EAAE,GAAGsV,CAAhB,IAAqB,GAArB,GAA2B7O,IAA3B,IAAmCkP,QAAQ,GAAG,GAAH,GAAS,EAApD,CAAP;AACD,C;;;;;;;;;;;ACjKD;AACA;AACA;AACA;AACA;AACA;AACA;AAEAvhB,OAAO,CAACgH,MAAR,GAAiB,UAAUY,GAAV,EAAe;AAC9B,MAAIoZ,GAAG,GAAG,EAAV;;AAEA,OAAK,IAAI/Z,CAAT,IAAcW,GAAd,EAAmB;AACjB,QAAIA,GAAG,CAACuB,cAAJ,CAAmBlC,CAAnB,CAAJ,EAA2B;AACzB,UAAI+Z,GAAG,CAAChd,MAAR,EAAgBgd,GAAG,IAAI,GAAP;AAChBA,SAAG,IAAIQ,kBAAkB,CAACva,CAAD,CAAlB,GAAwB,GAAxB,GAA8Bua,kBAAkB,CAAC5Z,GAAG,CAACX,CAAD,CAAJ,CAAvD;AACD;AACF;;AAED,SAAO+Z,GAAP;AACD,CAXD;AAaA;AACA;AACA;AACA;AACA;AACA;;;AAEAhhB,OAAO,CAAC6T,MAAR,GAAiB,UAAS4N,EAAT,EAAY;AAC3B,MAAIC,GAAG,GAAG,EAAV;AACA,MAAIC,KAAK,GAAGF,EAAE,CAACzP,KAAH,CAAS,GAAT,CAAZ;;AACA,OAAK,IAAI/K,CAAC,GAAG,CAAR,EAAW2O,CAAC,GAAG+L,KAAK,CAAC3d,MAA1B,EAAkCiD,CAAC,GAAG2O,CAAtC,EAAyC3O,CAAC,EAA1C,EAA8C;AAC5C,QAAI2a,IAAI,GAAGD,KAAK,CAAC1a,CAAD,CAAL,CAAS+K,KAAT,CAAe,GAAf,CAAX;AACA0P,OAAG,CAACG,kBAAkB,CAACD,IAAI,CAAC,CAAD,CAAL,CAAnB,CAAH,GAAmCC,kBAAkB,CAACD,IAAI,CAAC,CAAD,CAAL,CAArD;AACD;;AACD,SAAOF,GAAP;AACD,CARD,C;;;;;;;;;;;AC5BA;AACA;AACA;AACA;AACA;AACA;AAEA,IAAII,EAAE,GAAG,yOAAT;AAEA,IAAIC,KAAK,GAAG,CACR,QADQ,EACE,UADF,EACc,WADd,EAC2B,UAD3B,EACuC,MADvC,EAC+C,UAD/C,EAC2D,MAD3D,EACmE,MADnE,EAC2E,UAD3E,EACuF,MADvF,EAC+F,WAD/F,EAC4G,MAD5G,EACoH,OADpH,EAC6H,QAD7H,CAAZ;;AAIAlhB,MAAM,CAACb,OAAP,GAAiB,SAASmL,QAAT,CAAkB6V,GAAlB,EAAuB;AACpC,MAAItI,GAAG,GAAGsI,GAAV;AAAA,MACIgB,CAAC,GAAGhB,GAAG,CAACjc,OAAJ,CAAY,GAAZ,CADR;AAAA,MAEIuP,CAAC,GAAG0M,GAAG,CAACjc,OAAJ,CAAY,GAAZ,CAFR;;AAIA,MAAIid,CAAC,IAAI,CAAC,CAAN,IAAW1N,CAAC,IAAI,CAAC,CAArB,EAAwB;AACpB0M,OAAG,GAAGA,GAAG,CAACxO,SAAJ,CAAc,CAAd,EAAiBwP,CAAjB,IAAsBhB,GAAG,CAACxO,SAAJ,CAAcwP,CAAd,EAAiB1N,CAAjB,EAAoBlF,OAApB,CAA4B,IAA5B,EAAkC,GAAlC,CAAtB,GAA+D4R,GAAG,CAACxO,SAAJ,CAAc8B,CAAd,EAAiB0M,GAAG,CAAChd,MAArB,CAArE;AACH;;AAED,MAAIwc,CAAC,GAAGsB,EAAE,CAACb,IAAH,CAAQD,GAAG,IAAI,EAAf,CAAR;AAAA,MACI/f,GAAG,GAAG,EADV;AAAA,MAEIgG,CAAC,GAAG,EAFR;;AAIA,SAAOA,CAAC,EAAR,EAAY;AACRhG,OAAG,CAAC8gB,KAAK,CAAC9a,CAAD,CAAN,CAAH,GAAgBuZ,CAAC,CAACvZ,CAAD,CAAD,IAAQ,EAAxB;AACH;;AAED,MAAI+a,CAAC,IAAI,CAAC,CAAN,IAAW1N,CAAC,IAAI,CAAC,CAArB,EAAwB;AACpBrT,OAAG,CAACM,MAAJ,GAAamX,GAAb;AACAzX,OAAG,CAACqK,IAAJ,GAAWrK,GAAG,CAACqK,IAAJ,CAASkH,SAAT,CAAmB,CAAnB,EAAsBvR,GAAG,CAACqK,IAAJ,CAAStH,MAAT,GAAkB,CAAxC,EAA2CoL,OAA3C,CAAmD,IAAnD,EAAyD,GAAzD,CAAX;AACAnO,OAAG,CAACghB,SAAJ,GAAgBhhB,GAAG,CAACghB,SAAJ,CAAc7S,OAAd,CAAsB,GAAtB,EAA2B,EAA3B,EAA+BA,OAA/B,CAAuC,GAAvC,EAA4C,EAA5C,EAAgDA,OAAhD,CAAwD,IAAxD,EAA8D,GAA9D,CAAhB;AACAnO,OAAG,CAACihB,OAAJ,GAAc,IAAd;AACH;;AAEDjhB,KAAG,CAACkhB,SAAJ,GAAgBA,SAAS,CAAClhB,GAAD,EAAMA,GAAG,CAAC,MAAD,CAAT,CAAzB;AACAA,KAAG,CAACa,QAAJ,GAAeA,QAAQ,CAACb,GAAD,EAAMA,GAAG,CAAC,OAAD,CAAT,CAAvB;AAEA,SAAOA,GAAP;AACH,CA5BD;;AA8BA,SAASkhB,SAAT,CAAmBva,GAAnB,EAAwBtG,IAAxB,EAA8B;AAC1B,MAAI8gB,IAAI,GAAG,UAAX;AAAA,MACI3R,KAAK,GAAGnP,IAAI,CAAC8N,OAAL,CAAagT,IAAb,EAAmB,GAAnB,EAAwBpQ,KAAxB,CAA8B,GAA9B,CADZ;;AAGA,MAAI1Q,IAAI,CAAC2Q,MAAL,CAAY,CAAZ,EAAe,CAAf,KAAqB,GAArB,IAA4B3Q,IAAI,CAAC0C,MAAL,KAAgB,CAAhD,EAAmD;AAC/CyM,SAAK,CAACvF,MAAN,CAAa,CAAb,EAAgB,CAAhB;AACH;;AACD,MAAI5J,IAAI,CAAC2Q,MAAL,CAAY3Q,IAAI,CAAC0C,MAAL,GAAc,CAA1B,EAA6B,CAA7B,KAAmC,GAAvC,EAA4C;AACxCyM,SAAK,CAACvF,MAAN,CAAauF,KAAK,CAACzM,MAAN,GAAe,CAA5B,EAA+B,CAA/B;AACH;;AAED,SAAOyM,KAAP;AACH;;AAED,SAAS3O,QAAT,CAAkBb,GAAlB,EAAuBY,KAAvB,EAA8B;AAC1B,MAAI2E,IAAI,GAAG,EAAX;AAEA3E,OAAK,CAACuN,OAAN,CAAc,2BAAd,EAA2C,UAAUiT,EAAV,EAAczT,EAAd,EAAkB0T,EAAlB,EAAsB;AAC7D,QAAI1T,EAAJ,EAAQ;AACJpI,UAAI,CAACoI,EAAD,CAAJ,GAAW0T,EAAX;AACH;AACJ,GAJD;AAMA,SAAO9b,IAAP;AACH,C;;;;;;;;;;;;ACnEY;;;;AACb1G,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA+B,YAA/B,EAA6C;AAAEC,OAAK,EAAE;AAAT,CAA7C;AACAD,OAAO,CAACuiB,iBAAR,GAA4BviB,OAAO,CAACwiB,iBAAR,GAA4B,KAAK,CAA7D;;AACA,IAAMC,WAAW,GAAGliB,mBAAO,CAAC,sEAAD,CAA3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAASiiB,iBAAT,CAA2B9b,MAA3B,EAAmC;AAC/B,MAAMgc,OAAO,GAAG,EAAhB;AACA,MAAMC,UAAU,GAAGjc,MAAM,CAACF,IAA1B;AACA,MAAMoc,IAAI,GAAGlc,MAAb;AACAkc,MAAI,CAACpc,IAAL,GAAYqc,kBAAkB,CAACF,UAAD,EAAaD,OAAb,CAA9B;AACAE,MAAI,CAACE,WAAL,GAAmBJ,OAAO,CAAC1e,MAA3B,CAL+B,CAKI;;AACnC,SAAO;AAAE0C,UAAM,EAAEkc,IAAV;AAAgBF,WAAO,EAAEA;AAAzB,GAAP;AACH;;AACD1iB,OAAO,CAACwiB,iBAAR,GAA4BA,iBAA5B;;AACA,SAASK,kBAAT,CAA4Brc,IAA5B,EAAkCkc,OAAlC,EAA2C;AACvC,MAAI,CAAClc,IAAL,EACI,OAAOA,IAAP;;AACJ,MAAIic,WAAW,CAACM,QAAZ,CAAqBvc,IAArB,CAAJ,EAAgC;AAC5B,QAAMwc,WAAW,GAAG;AAAEC,kBAAY,EAAE,IAAhB;AAAsBC,SAAG,EAAER,OAAO,CAAC1e;AAAnC,KAApB;AACA0e,WAAO,CAAC3c,IAAR,CAAaS,IAAb;AACA,WAAOwc,WAAP;AACH,GAJD,MAKK,IAAIjW,KAAK,CAACoW,OAAN,CAAc3c,IAAd,CAAJ,EAAyB;AAC1B,QAAM4c,OAAO,GAAG,IAAIrW,KAAJ,CAAUvG,IAAI,CAACxC,MAAf,CAAhB;;AACA,SAAK,IAAIiD,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGT,IAAI,CAACxC,MAAzB,EAAiCiD,CAAC,EAAlC,EAAsC;AAClCmc,aAAO,CAACnc,CAAD,CAAP,GAAa4b,kBAAkB,CAACrc,IAAI,CAACS,CAAD,CAAL,EAAUyb,OAAV,CAA/B;AACH;;AACD,WAAOU,OAAP;AACH,GANI,MAOA,IAAI,QAAO5c,IAAP,MAAgB,QAAhB,IAA4B,EAAEA,IAAI,YAAY2K,IAAlB,CAAhC,EAAyD;AAC1D,QAAMiS,QAAO,GAAG,EAAhB;;AACA,SAAK,IAAM9W,GAAX,IAAkB9F,IAAlB,EAAwB;AACpB,UAAIA,IAAI,CAAC2C,cAAL,CAAoBmD,GAApB,CAAJ,EAA8B;AAC1B8W,gBAAO,CAAC9W,GAAD,CAAP,GAAeuW,kBAAkB,CAACrc,IAAI,CAAC8F,GAAD,CAAL,EAAYoW,OAAZ,CAAjC;AACH;AACJ;;AACD,WAAOU,QAAP;AACH;;AACD,SAAO5c,IAAP;AACH;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAAS+b,iBAAT,CAA2B7b,MAA3B,EAAmCgc,OAAnC,EAA4C;AACxChc,QAAM,CAACF,IAAP,GAAc6c,kBAAkB,CAAC3c,MAAM,CAACF,IAAR,EAAckc,OAAd,CAAhC;AACAhc,QAAM,CAACoc,WAAP,GAAqB3hB,SAArB,CAFwC,CAER;;AAChC,SAAOuF,MAAP;AACH;;AACD1G,OAAO,CAACuiB,iBAAR,GAA4BA,iBAA5B;;AACA,SAASc,kBAAT,CAA4B7c,IAA5B,EAAkCkc,OAAlC,EAA2C;AACvC,MAAI,CAAClc,IAAL,EACI,OAAOA,IAAP;;AACJ,MAAIA,IAAI,IAAIA,IAAI,CAACyc,YAAjB,EAA+B;AAC3B,WAAOP,OAAO,CAAClc,IAAI,CAAC0c,GAAN,CAAd,CAD2B,CACD;AAC7B,GAFD,MAGK,IAAInW,KAAK,CAACoW,OAAN,CAAc3c,IAAd,CAAJ,EAAyB;AAC1B,SAAK,IAAIS,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGT,IAAI,CAACxC,MAAzB,EAAiCiD,CAAC,EAAlC,EAAsC;AAClCT,UAAI,CAACS,CAAD,CAAJ,GAAUoc,kBAAkB,CAAC7c,IAAI,CAACS,CAAD,CAAL,EAAUyb,OAAV,CAA5B;AACH;AACJ,GAJI,MAKA,IAAI,QAAOlc,IAAP,MAAgB,QAApB,EAA8B;AAC/B,SAAK,IAAM8F,GAAX,IAAkB9F,IAAlB,EAAwB;AACpB,UAAIA,IAAI,CAAC2C,cAAL,CAAoBmD,GAApB,CAAJ,EAA8B;AAC1B9F,YAAI,CAAC8F,GAAD,CAAJ,GAAY+W,kBAAkB,CAAC7c,IAAI,CAAC8F,GAAD,CAAL,EAAYoW,OAAZ,CAA9B;AACH;AACJ;AACJ;;AACD,SAAOlc,IAAP;AACH,C;;;;;;;;;;;;AC/EY;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACb1G,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA+B,YAA/B,EAA6C;AAAEC,OAAK,EAAE;AAAT,CAA7C;AACAD,OAAO,CAAC0D,OAAR,GAAkB1D,OAAO,CAACwD,OAAR,GAAkBxD,OAAO,CAACqJ,UAAR,GAAqBrJ,OAAO,CAACK,QAAR,GAAmB,KAAK,CAAjF;;AACA,IAAM+B,OAAO,GAAG7B,mBAAO,CAAC,oEAAD,CAAvB;;AACA,IAAM+iB,QAAQ,GAAG/iB,mBAAO,CAAC,gEAAD,CAAxB;;AACA,IAAMkiB,WAAW,GAAGliB,mBAAO,CAAC,sEAAD,CAA3B;;AACA,IAAMK,KAAK,GAAGL,mBAAO,CAAC,kDAAD,CAAP,CAAiB,kBAAjB,CAAd;AACA;AACA;AACA;AACA;AACA;;;AACAP,OAAO,CAACK,QAAR,GAAmB,CAAnB;AACA,IAAIgJ,UAAJ;;AACA,CAAC,UAAUA,UAAV,EAAsB;AACnBA,YAAU,CAACA,UAAU,CAAC,SAAD,CAAV,GAAwB,CAAzB,CAAV,GAAwC,SAAxC;AACAA,YAAU,CAACA,UAAU,CAAC,YAAD,CAAV,GAA2B,CAA5B,CAAV,GAA2C,YAA3C;AACAA,YAAU,CAACA,UAAU,CAAC,OAAD,CAAV,GAAsB,CAAvB,CAAV,GAAsC,OAAtC;AACAA,YAAU,CAACA,UAAU,CAAC,KAAD,CAAV,GAAoB,CAArB,CAAV,GAAoC,KAApC;AACAA,YAAU,CAACA,UAAU,CAAC,eAAD,CAAV,GAA8B,CAA/B,CAAV,GAA8C,eAA9C;AACAA,YAAU,CAACA,UAAU,CAAC,cAAD,CAAV,GAA6B,CAA9B,CAAV,GAA6C,cAA7C;AACAA,YAAU,CAACA,UAAU,CAAC,YAAD,CAAV,GAA2B,CAA5B,CAAV,GAA2C,YAA3C;AACH,CARD,EAQGA,UAAU,GAAGrJ,OAAO,CAACqJ,UAAR,KAAuBrJ,OAAO,CAACqJ,UAAR,GAAqB,EAA5C,CARhB;AASA;AACA;AACA;;;IACM7F,O;;;;;;;;AACF;AACJ;AACA;AACA;AACA;AACA;2BACWoE,G,EAAK;AACRhH,WAAK,CAAC,oBAAD,EAAuBgH,GAAvB,CAAL;;AACA,UAAIA,GAAG,CAACwB,IAAJ,KAAaC,UAAU,CAACC,KAAxB,IAAiC1B,GAAG,CAACwB,IAAJ,KAAaC,UAAU,CAACc,GAA7D,EAAkE;AAC9D,YAAIsY,WAAW,CAACc,SAAZ,CAAsB3b,GAAtB,CAAJ,EAAgC;AAC5BA,aAAG,CAACwB,IAAJ,GACIxB,GAAG,CAACwB,IAAJ,KAAaC,UAAU,CAACC,KAAxB,GACMD,UAAU,CAACa,YADjB,GAEMb,UAAU,CAACgB,UAHrB;AAIA,iBAAO,KAAKmZ,cAAL,CAAoB5b,GAApB,CAAP;AACH;AACJ;;AACD,aAAO,CAAC,KAAK6b,cAAL,CAAoB7b,GAApB,CAAD,CAAP;AACH;AACD;AACJ;AACA;;;;mCACmBA,G,EAAK;AAChB;AACA,UAAIoZ,GAAG,GAAG,KAAKpZ,GAAG,CAACwB,IAAnB,CAFgB,CAGhB;;AACA,UAAIxB,GAAG,CAACwB,IAAJ,KAAaC,UAAU,CAACa,YAAxB,IACAtC,GAAG,CAACwB,IAAJ,KAAaC,UAAU,CAACgB,UAD5B,EACwC;AACpC2W,WAAG,IAAIpZ,GAAG,CAACkb,WAAJ,GAAkB,GAAzB;AACH,OAPe,CAQhB;AACA;;;AACA,UAAIlb,GAAG,CAACjB,GAAJ,IAAW,QAAQiB,GAAG,CAACjB,GAA3B,EAAgC;AAC5Bqa,WAAG,IAAIpZ,GAAG,CAACjB,GAAJ,GAAU,GAAjB;AACH,OAZe,CAahB;;;AACA,UAAI,QAAQiB,GAAG,CAACpG,EAAhB,EAAoB;AAChBwf,WAAG,IAAIpZ,GAAG,CAACpG,EAAX;AACH,OAhBe,CAiBhB;;;AACA,UAAI,QAAQoG,GAAG,CAACpB,IAAhB,EAAsB;AAClBwa,WAAG,IAAI/Q,IAAI,CAACC,SAAL,CAAetI,GAAG,CAACpB,IAAnB,CAAP;AACH;;AACD5F,WAAK,CAAC,kBAAD,EAAqBgH,GAArB,EAA0BoZ,GAA1B,CAAL;AACA,aAAOA,GAAP;AACH;AACD;AACJ;AACA;AACA;AACA;;;;mCACmBpZ,G,EAAK;AAChB,UAAM8b,cAAc,GAAGJ,QAAQ,CAACd,iBAAT,CAA2B5a,GAA3B,CAAvB;AACA,UAAMgb,IAAI,GAAG,KAAKa,cAAL,CAAoBC,cAAc,CAAChd,MAAnC,CAAb;AACA,UAAMgc,OAAO,GAAGgB,cAAc,CAAChB,OAA/B;AACAA,aAAO,CAACzZ,OAAR,CAAgB2Z,IAAhB,EAJgB,CAIO;;AACvB,aAAOF,OAAP,CALgB,CAKA;AACnB;;;;;;AAEL1iB,OAAO,CAACwD,OAAR,GAAkBA,OAAlB;AACA;AACA;AACA;AACA;AACA;;IACME,O;;;;;AACF,qBAAc;AAAA;;AAAA;AAEb;AACD;AACJ;AACA;AACA;AACA;;;;;wBACQkE,G,EAAK;AACL,UAAIlB,MAAJ;;AACA,UAAI,OAAOkB,GAAP,KAAe,QAAnB,EAA6B;AACzBlB,cAAM,GAAG,KAAKid,YAAL,CAAkB/b,GAAlB,CAAT;;AACA,YAAIlB,MAAM,CAAC0C,IAAP,KAAgBC,UAAU,CAACa,YAA3B,IACAxD,MAAM,CAAC0C,IAAP,KAAgBC,UAAU,CAACgB,UAD/B,EAC2C;AACvC;AACA,eAAKuZ,aAAL,GAAqB,IAAIC,mBAAJ,CAAwBnd,MAAxB,CAArB,CAFuC,CAGvC;;AACA,cAAIA,MAAM,CAACoc,WAAP,KAAuB,CAA3B,EAA8B;AAC1B,8EAAW,SAAX,EAAsBpc,MAAtB;AACH;AACJ,SARD,MASK;AACD;AACA,4EAAW,SAAX,EAAsBA,MAAtB;AACH;AACJ,OAfD,MAgBK,IAAI+b,WAAW,CAACM,QAAZ,CAAqBnb,GAArB,KAA6BA,GAAG,CAACgX,MAArC,EAA6C;AAC9C;AACA,YAAI,CAAC,KAAKgF,aAAV,EAAyB;AACrB,gBAAM,IAAI9d,KAAJ,CAAU,kDAAV,CAAN;AACH,SAFD,MAGK;AACDY,gBAAM,GAAG,KAAKkd,aAAL,CAAmBE,cAAnB,CAAkClc,GAAlC,CAAT;;AACA,cAAIlB,MAAJ,EAAY;AACR;AACA,iBAAKkd,aAAL,GAAqB,IAArB;;AACA,8EAAW,SAAX,EAAsBld,MAAtB;AACH;AACJ;AACJ,OAbI,MAcA;AACD,cAAM,IAAIZ,KAAJ,CAAU,mBAAmB8B,GAA7B,CAAN;AACH;AACJ;AACD;AACJ;AACA;AACA;AACA;AACA;;;;iCACiBoZ,G,EAAK;AACd,UAAI/Z,CAAC,GAAG,CAAR,CADc,CAEd;;AACA,UAAMiZ,CAAC,GAAG;AACN9W,YAAI,EAAE8H,MAAM,CAAC8P,GAAG,CAACzV,MAAJ,CAAW,CAAX,CAAD;AADN,OAAV;;AAGA,UAAIlC,UAAU,CAAC6W,CAAC,CAAC9W,IAAH,CAAV,KAAuBjI,SAA3B,EAAsC;AAClC,cAAM,IAAI2E,KAAJ,CAAU,yBAAyBoa,CAAC,CAAC9W,IAArC,CAAN;AACH,OARa,CASd;;;AACA,UAAI8W,CAAC,CAAC9W,IAAF,KAAWC,UAAU,CAACa,YAAtB,IACAgW,CAAC,CAAC9W,IAAF,KAAWC,UAAU,CAACgB,UAD1B,EACsC;AAClC,YAAM0Z,KAAK,GAAG9c,CAAC,GAAG,CAAlB;;AACA,eAAO+Z,GAAG,CAACzV,MAAJ,CAAW,EAAEtE,CAAb,MAAoB,GAApB,IAA2BA,CAAC,IAAI+Z,GAAG,CAAChd,MAA3C,EAAmD,CAAG;;AACtD,YAAMggB,GAAG,GAAGhD,GAAG,CAACxO,SAAJ,CAAcuR,KAAd,EAAqB9c,CAArB,CAAZ;;AACA,YAAI+c,GAAG,IAAI9S,MAAM,CAAC8S,GAAD,CAAb,IAAsBhD,GAAG,CAACzV,MAAJ,CAAWtE,CAAX,MAAkB,GAA5C,EAAiD;AAC7C,gBAAM,IAAInB,KAAJ,CAAU,qBAAV,CAAN;AACH;;AACDoa,SAAC,CAAC4C,WAAF,GAAgB5R,MAAM,CAAC8S,GAAD,CAAtB;AACH,OAnBa,CAoBd;;;AACA,UAAI,QAAQhD,GAAG,CAACzV,MAAJ,CAAWtE,CAAC,GAAG,CAAf,CAAZ,EAA+B;AAC3B,YAAM8c,MAAK,GAAG9c,CAAC,GAAG,CAAlB;;AACA,eAAO,EAAEA,CAAT,EAAY;AACR,cAAM+H,CAAC,GAAGgS,GAAG,CAACzV,MAAJ,CAAWtE,CAAX,CAAV;AACA,cAAI,QAAQ+H,CAAZ,EACI;AACJ,cAAI/H,CAAC,KAAK+Z,GAAG,CAAChd,MAAd,EACI;AACP;;AACDkc,SAAC,CAACvZ,GAAF,GAAQqa,GAAG,CAACxO,SAAJ,CAAcuR,MAAd,EAAqB9c,CAArB,CAAR;AACH,OAVD,MAWK;AACDiZ,SAAC,CAACvZ,GAAF,GAAQ,GAAR;AACH,OAlCa,CAmCd;;;AACA,UAAMsd,IAAI,GAAGjD,GAAG,CAACzV,MAAJ,CAAWtE,CAAC,GAAG,CAAf,CAAb;;AACA,UAAI,OAAOgd,IAAP,IAAe/S,MAAM,CAAC+S,IAAD,CAAN,IAAgBA,IAAnC,EAAyC;AACrC,YAAMF,OAAK,GAAG9c,CAAC,GAAG,CAAlB;;AACA,eAAO,EAAEA,CAAT,EAAY;AACR,cAAM+H,EAAC,GAAGgS,GAAG,CAACzV,MAAJ,CAAWtE,CAAX,CAAV;;AACA,cAAI,QAAQ+H,EAAR,IAAakC,MAAM,CAAClC,EAAD,CAAN,IAAaA,EAA9B,EAAiC;AAC7B,cAAE/H,CAAF;AACA;AACH;;AACD,cAAIA,CAAC,KAAK+Z,GAAG,CAAChd,MAAd,EACI;AACP;;AACDkc,SAAC,CAAC1e,EAAF,GAAO0P,MAAM,CAAC8P,GAAG,CAACxO,SAAJ,CAAcuR,OAAd,EAAqB9c,CAAC,GAAG,CAAzB,CAAD,CAAb;AACH,OAjDa,CAkDd;;;AACA,UAAI+Z,GAAG,CAACzV,MAAJ,CAAW,EAAEtE,CAAb,CAAJ,EAAqB;AACjB,YAAMid,OAAO,GAAGC,QAAQ,CAACnD,GAAG,CAAC/O,MAAJ,CAAWhL,CAAX,CAAD,CAAxB;;AACA,YAAIvD,OAAO,CAAC0gB,cAAR,CAAuBlE,CAAC,CAAC9W,IAAzB,EAA+B8a,OAA/B,CAAJ,EAA6C;AACzChE,WAAC,CAAC1Z,IAAF,GAAS0d,OAAT;AACH,SAFD,MAGK;AACD,gBAAM,IAAIpe,KAAJ,CAAU,iBAAV,CAAN;AACH;AACJ;;AACDlF,WAAK,CAAC,kBAAD,EAAqBogB,GAArB,EAA0Bd,CAA1B,CAAL;AACA,aAAOA,CAAP;AACH;;;;AAiBD;AACJ;AACA;8BACc;AACN,UAAI,KAAK0D,aAAT,EAAwB;AACpB,aAAKA,aAAL,CAAmBS,sBAAnB;AACH;AACJ;;;mCAvBqBjb,I,EAAM8a,O,EAAS;AACjC,cAAQ9a,IAAR;AACI,aAAKC,UAAU,CAACS,OAAhB;AACI,iBAAO,QAAOoa,OAAP,MAAmB,QAA1B;;AACJ,aAAK7a,UAAU,CAACiB,UAAhB;AACI,iBAAO4Z,OAAO,KAAK/iB,SAAnB;;AACJ,aAAKkI,UAAU,CAACmB,aAAhB;AACI,iBAAO,OAAO0Z,OAAP,KAAmB,QAAnB,IAA+B,QAAOA,OAAP,MAAmB,QAAzD;;AACJ,aAAK7a,UAAU,CAACC,KAAhB;AACA,aAAKD,UAAU,CAACa,YAAhB;AACI,iBAAO6C,KAAK,CAACoW,OAAN,CAAce,OAAd,KAA0BA,OAAO,CAAClgB,MAAR,GAAiB,CAAlD;;AACJ,aAAKqF,UAAU,CAACc,GAAhB;AACA,aAAKd,UAAU,CAACgB,UAAhB;AACI,iBAAO0C,KAAK,CAACoW,OAAN,CAAce,OAAd,CAAP;AAZR;AAcH;;;;EAjIiB9hB,O;;AA2ItBpC,OAAO,CAAC0D,OAAR,GAAkBA,OAAlB;;AACA,SAASygB,QAAT,CAAkBnD,GAAlB,EAAuB;AACnB,MAAI;AACA,WAAO/Q,IAAI,CAAC6F,KAAL,CAAWkL,GAAX,CAAP;AACH,GAFD,CAGA,OAAO1M,CAAP,EAAU;AACN,WAAO,KAAP;AACH;AACJ;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;IACMuP,mB;AACF,+BAAYnd,MAAZ,EAAoB;AAAA;;AAChB,SAAKA,MAAL,GAAcA,MAAd;AACA,SAAKgc,OAAL,GAAe,EAAf;AACA,SAAK4B,SAAL,GAAiB5d,MAAjB;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;;;mCACmB6d,O,EAAS;AACpB,WAAK7B,OAAL,CAAa3c,IAAb,CAAkBwe,OAAlB;;AACA,UAAI,KAAK7B,OAAL,CAAa1e,MAAb,KAAwB,KAAKsgB,SAAL,CAAexB,WAA3C,EAAwD;AACpD;AACA,YAAMpc,MAAM,GAAG4c,QAAQ,CAACf,iBAAT,CAA2B,KAAK+B,SAAhC,EAA2C,KAAK5B,OAAhD,CAAf;AACA,aAAK2B,sBAAL;AACA,eAAO3d,MAAP;AACH;;AACD,aAAO,IAAP;AACH;AACD;AACJ;AACA;;;;6CAC6B;AACrB,WAAK4d,SAAL,GAAiB,IAAjB;AACA,WAAK5B,OAAL,GAAe,EAAf;AACH;;;;;;;;;;;;;;;;ACtRQ;;;;AACb5iB,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA+B,YAA/B,EAA6C;AAAEC,OAAK,EAAE;AAAT,CAA7C;AACAD,OAAO,CAACujB,SAAR,GAAoBvjB,OAAO,CAAC+iB,QAAR,GAAmB,KAAK,CAA5C;AACA,IAAM3E,qBAAqB,GAAG,OAAOC,WAAP,KAAuB,UAArD;;AACA,IAAMU,MAAM,GAAG,SAATA,MAAS,CAACnX,GAAD,EAAS;AACpB,SAAO,OAAOyW,WAAW,CAACU,MAAnB,KAA8B,UAA9B,GACDV,WAAW,CAACU,MAAZ,CAAmBnX,GAAnB,CADC,GAEDA,GAAG,CAACoX,MAAJ,YAAsBX,WAF5B;AAGH,CAJD;;AAKA,IAAM9L,QAAQ,GAAGzS,MAAM,CAACgM,SAAP,CAAiByG,QAAlC;AACA,IAAMuM,cAAc,GAAG,OAAOD,IAAP,KAAgB,UAAhB,IAClB,OAAOA,IAAP,KAAgB,WAAhB,IACGtM,QAAQ,CAACf,IAAT,CAAcqN,IAAd,MAAwB,0BAFhC;AAGA,IAAM2F,cAAc,GAAG,OAAOC,IAAP,KAAgB,UAAhB,IAClB,OAAOA,IAAP,KAAgB,WAAhB,IACGlS,QAAQ,CAACf,IAAT,CAAciT,IAAd,MAAwB,0BAFhC;AAGA;AACA;AACA;AACA;AACA;;AACA,SAAS1B,QAAT,CAAkBnb,GAAlB,EAAuB;AACnB,SAASwW,qBAAqB,KAAKxW,GAAG,YAAYyW,WAAf,IAA8BU,MAAM,CAACnX,GAAD,CAAzC,CAAtB,IACHkX,cAAc,IAAIlX,GAAG,YAAYiX,IAD9B,IAEH2F,cAAc,IAAI5c,GAAG,YAAY6c,IAFtC;AAGH;;AACDzkB,OAAO,CAAC+iB,QAAR,GAAmBA,QAAnB;;AACA,SAASQ,SAAT,CAAmB3b,GAAnB,EAAwB8c,MAAxB,EAAgC;AAC5B,MAAI,CAAC9c,GAAD,IAAQ,QAAOA,GAAP,MAAe,QAA3B,EAAqC;AACjC,WAAO,KAAP;AACH;;AACD,MAAImF,KAAK,CAACoW,OAAN,CAAcvb,GAAd,CAAJ,EAAwB;AACpB,SAAK,IAAIX,CAAC,GAAG,CAAR,EAAW2O,CAAC,GAAGhO,GAAG,CAAC5D,MAAxB,EAAgCiD,CAAC,GAAG2O,CAApC,EAAuC3O,CAAC,EAAxC,EAA4C;AACxC,UAAIsc,SAAS,CAAC3b,GAAG,CAACX,CAAD,CAAJ,CAAb,EAAuB;AACnB,eAAO,IAAP;AACH;AACJ;;AACD,WAAO,KAAP;AACH;;AACD,MAAI8b,QAAQ,CAACnb,GAAD,CAAZ,EAAmB;AACf,WAAO,IAAP;AACH;;AACD,MAAIA,GAAG,CAAC8c,MAAJ,IACA,OAAO9c,GAAG,CAAC8c,MAAX,KAAsB,UADtB,IAEA3gB,SAAS,CAACC,MAAV,KAAqB,CAFzB,EAE4B;AACxB,WAAOuf,SAAS,CAAC3b,GAAG,CAAC8c,MAAJ,EAAD,EAAe,IAAf,CAAhB;AACH;;AACD,OAAK,IAAMpY,GAAX,IAAkB1E,GAAlB,EAAuB;AACnB,QAAI9H,MAAM,CAACgM,SAAP,CAAiB3C,cAAjB,CAAgCqI,IAAhC,CAAqC5J,GAArC,EAA0C0E,GAA1C,KAAkDiX,SAAS,CAAC3b,GAAG,CAAC0E,GAAD,CAAJ,CAA/D,EAA2E;AACvE,aAAO,IAAP;AACH;AACJ;;AACD,SAAO,KAAP;AACH;;AACDtM,OAAO,CAACujB,SAAR,GAAoBA,SAApB,C;;;;;;;;;;;;ACtDa;;AAEb,IAAIoB,QAAQ,GAAG,mEAAmE3S,KAAnE,CAAyE,EAAzE,CAAf;AAAA,IACIhO,MAAM,GAAG,EADb;AAAA,IAEIkO,GAAG,GAAG,EAFV;AAAA,IAGI0S,IAAI,GAAG,CAHX;AAAA,IAII3d,CAAC,GAAG,CAJR;AAAA,IAKImK,IALJ;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,SAASpK,MAAT,CAAgBkc,GAAhB,EAAqB;AACnB,MAAI2B,OAAO,GAAG,EAAd;;AAEA,KAAG;AACDA,WAAO,GAAGF,QAAQ,CAACzB,GAAG,GAAGlf,MAAP,CAAR,GAAyB6gB,OAAnC;AACA3B,OAAG,GAAGnX,IAAI,CAACK,KAAL,CAAW8W,GAAG,GAAGlf,MAAjB,CAAN;AACD,GAHD,QAGSkf,GAAG,GAAG,CAHf;;AAKA,SAAO2B,OAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,SAAShR,MAAT,CAAgBmN,GAAhB,EAAqB;AACnB,MAAIrC,OAAO,GAAG,CAAd;;AAEA,OAAK1X,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG+Z,GAAG,CAAChd,MAApB,EAA4BiD,CAAC,EAA7B,EAAiC;AAC/B0X,WAAO,GAAGA,OAAO,GAAG3a,MAAV,GAAmBkO,GAAG,CAAC8O,GAAG,CAACzV,MAAJ,CAAWtE,CAAX,CAAD,CAAhC;AACD;;AAED,SAAO0X,OAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;;;AACA,SAAS7C,KAAT,GAAiB;AACf,MAAIgJ,GAAG,GAAG9d,MAAM,CAAC,CAAC,IAAImK,IAAJ,EAAF,CAAhB;AAEA,MAAI2T,GAAG,KAAK1T,IAAZ,EAAkB,OAAOwT,IAAI,GAAG,CAAP,EAAUxT,IAAI,GAAG0T,GAAxB;AAClB,SAAOA,GAAG,GAAE,GAAL,GAAU9d,MAAM,CAAC4d,IAAI,EAAL,CAAvB;AACD,C,CAED;AACA;AACA;;;AACA,OAAO3d,CAAC,GAAGjD,MAAX,EAAmBiD,CAAC,EAApB;AAAwBiL,KAAG,CAACyS,QAAQ,CAAC1d,CAAD,CAAT,CAAH,GAAmBA,CAAnB;AAAxB,C,CAEA;AACA;AACA;;;AACA6U,KAAK,CAAC9U,MAAN,GAAeA,MAAf;AACA8U,KAAK,CAACjI,MAAN,GAAeA,MAAf;AACAhT,MAAM,CAACb,OAAP,GAAiB8b,KAAjB,C", "file": "socket.io.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"io\"] = factory();\n\telse\n\t\troot[\"io\"] = factory();\n})(this, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = \"./build/index.js\");\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Socket = exports.io = exports.Manager = exports.protocol = void 0;\nconst url_1 = require(\"./url\");\nconst manager_1 = require(\"./manager\");\nconst socket_1 = require(\"./socket\");\nObject.defineProperty(exports, \"Socket\", { enumerable: true, get: function () { return socket_1.Socket; } });\nconst debug = require(\"debug\")(\"socket.io-client\");\n/**\n * Module exports.\n */\nmodule.exports = exports = lookup;\n/**\n * Managers cache.\n */\nconst cache = (exports.managers = {});\nfunction lookup(uri, opts) {\n    if (typeof uri === \"object\") {\n        opts = uri;\n        uri = undefined;\n    }\n    opts = opts || {};\n    const parsed = url_1.url(uri, opts.path);\n    const source = parsed.source;\n    const id = parsed.id;\n    const path = parsed.path;\n    const sameNamespace = cache[id] && path in cache[id][\"nsps\"];\n    const newConnection = opts.forceNew ||\n        opts[\"force new connection\"] ||\n        false === opts.multiplex ||\n        sameNamespace;\n    let io;\n    if (newConnection) {\n        debug(\"ignoring socket cache for %s\", source);\n        io = new manager_1.Manager(source, opts);\n    }\n    else {\n        if (!cache[id]) {\n            debug(\"new io instance for %s\", source);\n            cache[id] = new manager_1.Manager(source, opts);\n        }\n        io = cache[id];\n    }\n    if (parsed.query && !opts.query) {\n        opts.query = parsed.queryKey;\n    }\n    return io.socket(parsed.path, opts);\n}\nexports.io = lookup;\n/**\n * Protocol version.\n *\n * @public\n */\nvar socket_io_parser_1 = require(\"socket.io-parser\");\nObject.defineProperty(exports, \"protocol\", { enumerable: true, get: function () { return socket_io_parser_1.protocol; } });\n/**\n * `connect`.\n *\n * @param {String} uri\n * @public\n */\nexports.connect = lookup;\n/**\n * Expose constructors for standalone build.\n *\n * @public\n */\nvar manager_2 = require(\"./manager\");\nObject.defineProperty(exports, \"Manager\", { enumerable: true, get: function () { return manager_2.Manager; } });\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Manager = void 0;\nconst eio = require(\"engine.io-client\");\nconst socket_1 = require(\"./socket\");\nconst Emitter = require(\"component-emitter\");\nconst parser = require(\"socket.io-parser\");\nconst on_1 = require(\"./on\");\nconst Backoff = require(\"backo2\");\nconst debug = require(\"debug\")(\"socket.io-client:manager\");\nclass Manager extends Emitter {\n    constructor(uri, opts) {\n        super();\n        this.nsps = {};\n        this.subs = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = undefined;\n        }\n        opts = opts || {};\n        opts.path = opts.path || \"/socket.io\";\n        this.opts = opts;\n        this.reconnection(opts.reconnection !== false);\n        this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n        this.reconnectionDelay(opts.reconnectionDelay || 1000);\n        this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n        this.randomizationFactor(opts.randomizationFactor || 0.5);\n        this.backoff = new Backoff({\n            min: this.reconnectionDelay(),\n            max: this.reconnectionDelayMax(),\n            jitter: this.randomizationFactor(),\n        });\n        this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n        this._readyState = \"closed\";\n        this.uri = uri;\n        const _parser = opts.parser || parser;\n        this.encoder = new _parser.Encoder();\n        this.decoder = new _parser.Decoder();\n        this._autoConnect = opts.autoConnect !== false;\n        if (this._autoConnect)\n            this.open();\n    }\n    reconnection(v) {\n        if (!arguments.length)\n            return this._reconnection;\n        this._reconnection = !!v;\n        return this;\n    }\n    reconnectionAttempts(v) {\n        if (v === undefined)\n            return this._reconnectionAttempts;\n        this._reconnectionAttempts = v;\n        return this;\n    }\n    reconnectionDelay(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelay;\n        this._reconnectionDelay = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMin(v);\n        return this;\n    }\n    randomizationFactor(v) {\n        var _a;\n        if (v === undefined)\n            return this._randomizationFactor;\n        this._randomizationFactor = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setJitter(v);\n        return this;\n    }\n    reconnectionDelayMax(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelayMax;\n        this._reconnectionDelayMax = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMax(v);\n        return this;\n    }\n    timeout(v) {\n        if (!arguments.length)\n            return this._timeout;\n        this._timeout = v;\n        return this;\n    }\n    /**\n     * Starts trying to reconnect if reconnection is enabled and we have not\n     * started reconnecting yet\n     *\n     * @private\n     */\n    maybeReconnectOnOpen() {\n        // Only try to reconnect if it's the first time we're connecting\n        if (!this._reconnecting &&\n            this._reconnection &&\n            this.backoff.attempts === 0) {\n            // keeps reconnection from firing twice for the same reconnection loop\n            this.reconnect();\n        }\n    }\n    /**\n     * Sets the current transport `socket`.\n     *\n     * @param {Function} fn - optional, callback\n     * @return self\n     * @public\n     */\n    open(fn) {\n        debug(\"readyState %s\", this._readyState);\n        if (~this._readyState.indexOf(\"open\"))\n            return this;\n        debug(\"opening %s\", this.uri);\n        this.engine = eio(this.uri, this.opts);\n        const socket = this.engine;\n        const self = this;\n        this._readyState = \"opening\";\n        this.skipReconnect = false;\n        // emit `open`\n        const openSubDestroy = on_1.on(socket, \"open\", function () {\n            self.onopen();\n            fn && fn();\n        });\n        // emit `error`\n        const errorSub = on_1.on(socket, \"error\", (err) => {\n            debug(\"error\");\n            self.cleanup();\n            self._readyState = \"closed\";\n            super.emit(\"error\", err);\n            if (fn) {\n                fn(err);\n            }\n            else {\n                // Only do this if there is no fn to handle the error\n                self.maybeReconnectOnOpen();\n            }\n        });\n        if (false !== this._timeout) {\n            const timeout = this._timeout;\n            debug(\"connect attempt will timeout after %d\", timeout);\n            if (timeout === 0) {\n                openSubDestroy(); // prevents a race condition with the 'open' event\n            }\n            // set timer\n            const timer = setTimeout(() => {\n                debug(\"connect attempt timed out after %d\", timeout);\n                openSubDestroy();\n                socket.close();\n                socket.emit(\"error\", new Error(\"timeout\"));\n            }, timeout);\n            this.subs.push(function subDestroy() {\n                clearTimeout(timer);\n            });\n        }\n        this.subs.push(openSubDestroy);\n        this.subs.push(errorSub);\n        return this;\n    }\n    /**\n     * Alias for open()\n     *\n     * @return self\n     * @public\n     */\n    connect(fn) {\n        return this.open(fn);\n    }\n    /**\n     * Called upon transport open.\n     *\n     * @private\n     */\n    onopen() {\n        debug(\"open\");\n        // clear old subs\n        this.cleanup();\n        // mark as open\n        this._readyState = \"open\";\n        super.emit(\"open\");\n        // add new subs\n        const socket = this.engine;\n        this.subs.push(on_1.on(socket, \"ping\", this.onping.bind(this)), on_1.on(socket, \"data\", this.ondata.bind(this)), on_1.on(socket, \"error\", this.onerror.bind(this)), on_1.on(socket, \"close\", this.onclose.bind(this)), on_1.on(this.decoder, \"decoded\", this.ondecoded.bind(this)));\n    }\n    /**\n     * Called upon a ping.\n     *\n     * @private\n     */\n    onping() {\n        super.emit(\"ping\");\n    }\n    /**\n     * Called with data.\n     *\n     * @private\n     */\n    ondata(data) {\n        this.decoder.add(data);\n    }\n    /**\n     * Called when parser fully decodes a packet.\n     *\n     * @private\n     */\n    ondecoded(packet) {\n        super.emit(\"packet\", packet);\n    }\n    /**\n     * Called upon socket error.\n     *\n     * @private\n     */\n    onerror(err) {\n        debug(\"error\", err);\n        super.emit(\"error\", err);\n    }\n    /**\n     * Creates a new socket for the given `nsp`.\n     *\n     * @return {Socket}\n     * @public\n     */\n    socket(nsp, opts) {\n        let socket = this.nsps[nsp];\n        if (!socket) {\n            socket = new socket_1.Socket(this, nsp, opts);\n            this.nsps[nsp] = socket;\n        }\n        return socket;\n    }\n    /**\n     * Called upon a socket close.\n     *\n     * @param socket\n     * @private\n     */\n    _destroy(socket) {\n        const nsps = Object.keys(this.nsps);\n        for (const nsp of nsps) {\n            const socket = this.nsps[nsp];\n            if (socket.active) {\n                debug(\"socket %s is still active, skipping close\", nsp);\n                return;\n            }\n        }\n        this._close();\n    }\n    /**\n     * Writes a packet.\n     *\n     * @param packet\n     * @private\n     */\n    _packet(packet) {\n        debug(\"writing packet %j\", packet);\n        const encodedPackets = this.encoder.encode(packet);\n        for (let i = 0; i < encodedPackets.length; i++) {\n            this.engine.write(encodedPackets[i], packet.options);\n        }\n    }\n    /**\n     * Clean up transport subscriptions and packet buffer.\n     *\n     * @private\n     */\n    cleanup() {\n        debug(\"cleanup\");\n        this.subs.forEach((subDestroy) => subDestroy());\n        this.subs.length = 0;\n        this.decoder.destroy();\n    }\n    /**\n     * Close the current socket.\n     *\n     * @private\n     */\n    _close() {\n        debug(\"disconnect\");\n        this.skipReconnect = true;\n        this._reconnecting = false;\n        if (\"opening\" === this._readyState) {\n            // `onclose` will not fire because\n            // an open event never happened\n            this.cleanup();\n        }\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        if (this.engine)\n            this.engine.close();\n    }\n    /**\n     * Alias for close()\n     *\n     * @private\n     */\n    disconnect() {\n        return this._close();\n    }\n    /**\n     * Called upon engine close.\n     *\n     * @private\n     */\n    onclose(reason) {\n        debug(\"onclose\");\n        this.cleanup();\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        super.emit(\"close\", reason);\n        if (this._reconnection && !this.skipReconnect) {\n            this.reconnect();\n        }\n    }\n    /**\n     * Attempt a reconnection.\n     *\n     * @private\n     */\n    reconnect() {\n        if (this._reconnecting || this.skipReconnect)\n            return this;\n        const self = this;\n        if (this.backoff.attempts >= this._reconnectionAttempts) {\n            debug(\"reconnect failed\");\n            this.backoff.reset();\n            super.emit(\"reconnect_failed\");\n            this._reconnecting = false;\n        }\n        else {\n            const delay = this.backoff.duration();\n            debug(\"will wait %dms before reconnect attempt\", delay);\n            this._reconnecting = true;\n            const timer = setTimeout(() => {\n                if (self.skipReconnect)\n                    return;\n                debug(\"attempting reconnect\");\n                super.emit(\"reconnect_attempt\", self.backoff.attempts);\n                // check again for the case socket closed in above events\n                if (self.skipReconnect)\n                    return;\n                self.open((err) => {\n                    if (err) {\n                        debug(\"reconnect attempt error\");\n                        self._reconnecting = false;\n                        self.reconnect();\n                        super.emit(\"reconnect_error\", err);\n                    }\n                    else {\n                        debug(\"reconnect success\");\n                        self.onreconnect();\n                    }\n                });\n            }, delay);\n            this.subs.push(function subDestroy() {\n                clearTimeout(timer);\n            });\n        }\n    }\n    /**\n     * Called upon successful reconnect.\n     *\n     * @private\n     */\n    onreconnect() {\n        const attempt = this.backoff.attempts;\n        this._reconnecting = false;\n        this.backoff.reset();\n        super.emit(\"reconnect\", attempt);\n    }\n}\nexports.Manager = Manager;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.on = void 0;\nfunction on(obj, ev, fn) {\n    obj.on(ev, fn);\n    return function subDestroy() {\n        obj.off(ev, fn);\n    };\n}\nexports.on = on;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Socket = void 0;\nconst socket_io_parser_1 = require(\"socket.io-parser\");\nconst Emitter = require(\"component-emitter\");\nconst on_1 = require(\"./on\");\nconst debug = require(\"debug\")(\"socket.io-client:socket\");\n/**\n * Internal events.\n * These events can't be emitted by the user.\n */\nconst RESERVED_EVENTS = Object.freeze({\n    connect: 1,\n    connect_error: 1,\n    disconnect: 1,\n    disconnecting: 1,\n    // EventEmitter reserved events: https://nodejs.org/api/events.html#events_event_newlistener\n    newListener: 1,\n    removeListener: 1,\n});\nclass Socket extends Emitter {\n    /**\n     * `Socket` constructor.\n     *\n     * @public\n     */\n    constructor(io, nsp, opts) {\n        super();\n        this.receiveBuffer = [];\n        this.sendBuffer = [];\n        this.ids = 0;\n        this.acks = {};\n        this.flags = {};\n        this.io = io;\n        this.nsp = nsp;\n        this.ids = 0;\n        this.acks = {};\n        this.receiveBuffer = [];\n        this.sendBuffer = [];\n        this.connected = false;\n        this.disconnected = true;\n        this.flags = {};\n        if (opts && opts.auth) {\n            this.auth = opts.auth;\n        }\n        if (this.io._autoConnect)\n            this.open();\n    }\n    /**\n     * Subscribe to open, close and packet events\n     *\n     * @private\n     */\n    subEvents() {\n        if (this.subs)\n            return;\n        const io = this.io;\n        this.subs = [\n            on_1.on(io, \"open\", this.onopen.bind(this)),\n            on_1.on(io, \"packet\", this.onpacket.bind(this)),\n            on_1.on(io, \"error\", this.onerror.bind(this)),\n            on_1.on(io, \"close\", this.onclose.bind(this)),\n        ];\n    }\n    /**\n     * Whether the Socket will try to reconnect when its Manager connects or reconnects\n     */\n    get active() {\n        return !!this.subs;\n    }\n    /**\n     * \"Opens\" the socket.\n     *\n     * @public\n     */\n    connect() {\n        if (this.connected)\n            return this;\n        this.subEvents();\n        if (!this.io[\"_reconnecting\"])\n            this.io.open(); // ensure open\n        if (\"open\" === this.io._readyState)\n            this.onopen();\n        return this;\n    }\n    /**\n     * Alias for connect()\n     */\n    open() {\n        return this.connect();\n    }\n    /**\n     * Sends a `message` event.\n     *\n     * @return self\n     * @public\n     */\n    send(...args) {\n        args.unshift(\"message\");\n        this.emit.apply(this, args);\n        return this;\n    }\n    /**\n     * Override `emit`.\n     * If the event is in `events`, it's emitted normally.\n     *\n     * @param ev - event name\n     * @return self\n     * @public\n     */\n    emit(ev, ...args) {\n        if (RESERVED_EVENTS.hasOwnProperty(ev)) {\n            throw new Error('\"' + ev + '\" is a reserved event name');\n        }\n        args.unshift(ev);\n        const packet = {\n            type: socket_io_parser_1.PacketType.EVENT,\n            data: args,\n        };\n        packet.options = {};\n        packet.options.compress = this.flags.compress !== false;\n        // event ack callback\n        if (\"function\" === typeof args[args.length - 1]) {\n            debug(\"emitting packet with ack id %d\", this.ids);\n            this.acks[this.ids] = args.pop();\n            packet.id = this.ids++;\n        }\n        const isTransportWritable = this.io.engine &&\n            this.io.engine.transport &&\n            this.io.engine.transport.writable;\n        const discardPacket = this.flags.volatile && (!isTransportWritable || !this.connected);\n        if (discardPacket) {\n            debug(\"discard packet as the transport is not currently writable\");\n        }\n        else if (this.connected) {\n            this.packet(packet);\n        }\n        else {\n            this.sendBuffer.push(packet);\n        }\n        this.flags = {};\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param packet\n     * @private\n     */\n    packet(packet) {\n        packet.nsp = this.nsp;\n        this.io._packet(packet);\n    }\n    /**\n     * Called upon engine `open`.\n     *\n     * @private\n     */\n    onopen() {\n        debug(\"transport is open - connecting\");\n        if (typeof this.auth == \"function\") {\n            this.auth((data) => {\n                this.packet({ type: socket_io_parser_1.PacketType.CONNECT, data });\n            });\n        }\n        else {\n            this.packet({ type: socket_io_parser_1.PacketType.CONNECT, data: this.auth });\n        }\n    }\n    /**\n     * Called upon engine or manager `error`.\n     *\n     * @param err\n     * @private\n     */\n    onerror(err) {\n        if (!this.connected) {\n            super.emit(\"connect_error\", err);\n        }\n    }\n    /**\n     * Called upon engine `close`.\n     *\n     * @param reason\n     * @private\n     */\n    onclose(reason) {\n        debug(\"close (%s)\", reason);\n        this.connected = false;\n        this.disconnected = true;\n        delete this.id;\n        super.emit(\"disconnect\", reason);\n    }\n    /**\n     * Called with socket packet.\n     *\n     * @param packet\n     * @private\n     */\n    onpacket(packet) {\n        const sameNamespace = packet.nsp === this.nsp;\n        if (!sameNamespace)\n            return;\n        switch (packet.type) {\n            case socket_io_parser_1.PacketType.CONNECT:\n                if (packet.data && packet.data.sid) {\n                    const id = packet.data.sid;\n                    this.onconnect(id);\n                }\n                else {\n                    super.emit(\"connect_error\", new Error(\"It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)\"));\n                }\n                break;\n            case socket_io_parser_1.PacketType.EVENT:\n                this.onevent(packet);\n                break;\n            case socket_io_parser_1.PacketType.BINARY_EVENT:\n                this.onevent(packet);\n                break;\n            case socket_io_parser_1.PacketType.ACK:\n                this.onack(packet);\n                break;\n            case socket_io_parser_1.PacketType.BINARY_ACK:\n                this.onack(packet);\n                break;\n            case socket_io_parser_1.PacketType.DISCONNECT:\n                this.ondisconnect();\n                break;\n            case socket_io_parser_1.PacketType.CONNECT_ERROR:\n                const err = new Error(packet.data.message);\n                // @ts-ignore\n                err.data = packet.data.data;\n                super.emit(\"connect_error\", err);\n                break;\n        }\n    }\n    /**\n     * Called upon a server event.\n     *\n     * @param packet\n     * @private\n     */\n    onevent(packet) {\n        const args = packet.data || [];\n        debug(\"emitting event %j\", args);\n        if (null != packet.id) {\n            debug(\"attaching ack callback to event\");\n            args.push(this.ack(packet.id));\n        }\n        if (this.connected) {\n            this.emitEvent(args);\n        }\n        else {\n            this.receiveBuffer.push(Object.freeze(args));\n        }\n    }\n    emitEvent(args) {\n        if (this._anyListeners && this._anyListeners.length) {\n            const listeners = this._anyListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, args);\n            }\n        }\n        super.emit.apply(this, args);\n    }\n    /**\n     * Produces an ack callback to emit with an event.\n     *\n     * @private\n     */\n    ack(id) {\n        const self = this;\n        let sent = false;\n        return function (...args) {\n            // prevent double callbacks\n            if (sent)\n                return;\n            sent = true;\n            debug(\"sending ack %j\", args);\n            self.packet({\n                type: socket_io_parser_1.PacketType.ACK,\n                id: id,\n                data: args,\n            });\n        };\n    }\n    /**\n     * Called upon a server acknowlegement.\n     *\n     * @param packet\n     * @private\n     */\n    onack(packet) {\n        const ack = this.acks[packet.id];\n        if (\"function\" === typeof ack) {\n            debug(\"calling ack %s with %j\", packet.id, packet.data);\n            ack.apply(this, packet.data);\n            delete this.acks[packet.id];\n        }\n        else {\n            debug(\"bad ack %s\", packet.id);\n        }\n    }\n    /**\n     * Called upon server connect.\n     *\n     * @private\n     */\n    onconnect(id) {\n        debug(\"socket connected with id %s\", id);\n        this.id = id;\n        this.connected = true;\n        this.disconnected = false;\n        super.emit(\"connect\");\n        this.emitBuffered();\n    }\n    /**\n     * Emit buffered events (received and emitted).\n     *\n     * @private\n     */\n    emitBuffered() {\n        this.receiveBuffer.forEach((args) => this.emitEvent(args));\n        this.receiveBuffer = [];\n        this.sendBuffer.forEach((packet) => this.packet(packet));\n        this.sendBuffer = [];\n    }\n    /**\n     * Called upon server disconnect.\n     *\n     * @private\n     */\n    ondisconnect() {\n        debug(\"server disconnect (%s)\", this.nsp);\n        this.destroy();\n        this.onclose(\"io server disconnect\");\n    }\n    /**\n     * Called upon forced client/server side disconnections,\n     * this method ensures the manager stops tracking us and\n     * that reconnections don't get triggered for this.\n     *\n     * @private\n     */\n    destroy() {\n        if (this.subs) {\n            // clean subscriptions to avoid reconnections\n            this.subs.forEach((subDestroy) => subDestroy());\n            this.subs = undefined;\n        }\n        this.io[\"_destroy\"](this);\n    }\n    /**\n     * Disconnects the socket manually.\n     *\n     * @return self\n     * @public\n     */\n    disconnect() {\n        if (this.connected) {\n            debug(\"performing disconnect (%s)\", this.nsp);\n            this.packet({ type: socket_io_parser_1.PacketType.DISCONNECT });\n        }\n        // remove socket from pool\n        this.destroy();\n        if (this.connected) {\n            // fire events\n            this.onclose(\"io client disconnect\");\n        }\n        return this;\n    }\n    /**\n     * Alias for disconnect()\n     *\n     * @return self\n     * @public\n     */\n    close() {\n        return this.disconnect();\n    }\n    /**\n     * Sets the compress flag.\n     *\n     * @param compress - if `true`, compresses the sending data\n     * @return self\n     * @public\n     */\n    compress(compress) {\n        this.flags.compress = compress;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the event message will be dropped when this socket is not\n     * ready to send messages.\n     *\n     * @returns self\n     * @public\n     */\n    get volatile() {\n        this.flags.volatile = true;\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * @param listener\n     * @public\n     */\n    onAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * @param listener\n     * @public\n     */\n    prependAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @param listener\n     * @public\n     */\n    offAny(listener) {\n        if (!this._anyListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     *\n     * @public\n     */\n    listenersAny() {\n        return this._anyListeners || [];\n    }\n}\nexports.Socket = Socket;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.url = void 0;\nconst parseuri = require(\"parseuri\");\nconst debug = require(\"debug\")(\"socket.io-client:url\");\n/**\n * URL parser.\n *\n * @param uri - url\n * @param path - the request path of the connection\n * @param loc - An object meant to mimic window.location.\n *        Defaults to window.location.\n * @public\n */\nfunction url(uri, path = \"\", loc) {\n    let obj = uri;\n    // default to window.location\n    loc = loc || (typeof location !== \"undefined\" && location);\n    if (null == uri)\n        uri = loc.protocol + \"//\" + loc.host;\n    // relative path support\n    if (typeof uri === \"string\") {\n        if (\"/\" === uri.charAt(0)) {\n            if (\"/\" === uri.charAt(1)) {\n                uri = loc.protocol + uri;\n            }\n            else {\n                uri = loc.host + uri;\n            }\n        }\n        if (!/^(https?|wss?):\\/\\//.test(uri)) {\n            debug(\"protocol-less url %s\", uri);\n            if (\"undefined\" !== typeof loc) {\n                uri = loc.protocol + \"//\" + uri;\n            }\n            else {\n                uri = \"https://\" + uri;\n            }\n        }\n        // parse\n        debug(\"parse %s\", uri);\n        obj = parseuri(uri);\n    }\n    // make sure we treat `localhost:80` and `localhost` equally\n    if (!obj.port) {\n        if (/^(http|ws)$/.test(obj.protocol)) {\n            obj.port = \"80\";\n        }\n        else if (/^(http|ws)s$/.test(obj.protocol)) {\n            obj.port = \"443\";\n        }\n    }\n    obj.path = obj.path || \"/\";\n    const ipv6 = obj.host.indexOf(\":\") !== -1;\n    const host = ipv6 ? \"[\" + obj.host + \"]\" : obj.host;\n    // define unique id\n    obj.id = obj.protocol + \"://\" + host + \":\" + obj.port + path;\n    // define href\n    obj.href =\n        obj.protocol +\n            \"://\" +\n            host +\n            (loc && loc.port === obj.port ? \"\" : \":\" + obj.port);\n    return obj;\n}\nexports.url = url;\n", "\n/**\n * Expose `Backoff`.\n */\n\nmodule.exports = Backoff;\n\n/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */\n\nfunction Backoff(opts) {\n  opts = opts || {};\n  this.ms = opts.min || 100;\n  this.max = opts.max || 10000;\n  this.factor = opts.factor || 2;\n  this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n  this.attempts = 0;\n}\n\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */\n\nBackoff.prototype.duration = function(){\n  var ms = this.ms * Math.pow(this.factor, this.attempts++);\n  if (this.jitter) {\n    var rand =  Math.random();\n    var deviation = Math.floor(rand * this.jitter * ms);\n    ms = (Math.floor(rand * 10) & 1) == 0  ? ms - deviation : ms + deviation;\n  }\n  return Math.min(ms, this.max) | 0;\n};\n\n/**\n * Reset the number of attempts.\n *\n * @api public\n */\n\nBackoff.prototype.reset = function(){\n  this.attempts = 0;\n};\n\n/**\n * Set the minimum duration\n *\n * @api public\n */\n\nBackoff.prototype.setMin = function(min){\n  this.ms = min;\n};\n\n/**\n * Set the maximum duration\n *\n * @api public\n */\n\nBackoff.prototype.setMax = function(max){\n  this.max = max;\n};\n\n/**\n * Set the jitter\n *\n * @api public\n */\n\nBackoff.prototype.setJitter = function(jitter){\n  this.jitter = jitter;\n};\n\n", "\r\n/**\r\n * Expose `Emitter`.\r\n */\r\n\r\nif (typeof module !== 'undefined') {\r\n  module.exports = Emitter;\r\n}\r\n\r\n/**\r\n * Initialize a new `Emitter`.\r\n *\r\n * @api public\r\n */\r\n\r\nfunction Emitter(obj) {\r\n  if (obj) return mixin(obj);\r\n};\r\n\r\n/**\r\n * Mixin the emitter properties.\r\n *\r\n * @param {Object} obj\r\n * @return {Object}\r\n * @api private\r\n */\r\n\r\nfunction mixin(obj) {\r\n  for (var key in Emitter.prototype) {\r\n    obj[key] = Emitter.prototype[key];\r\n  }\r\n  return obj;\r\n}\r\n\r\n/**\r\n * Listen on the given `event` with `fn`.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.on =\r\nEmitter.prototype.addEventListener = function(event, fn){\r\n  this._callbacks = this._callbacks || {};\r\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\r\n    .push(fn);\r\n  return this;\r\n};\r\n\r\n/**\r\n * Adds an `event` listener that will be invoked a single\r\n * time then automatically removed.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.once = function(event, fn){\r\n  function on() {\r\n    this.off(event, on);\r\n    fn.apply(this, arguments);\r\n  }\r\n\r\n  on.fn = fn;\r\n  this.on(event, on);\r\n  return this;\r\n};\r\n\r\n/**\r\n * Remove the given callback for `event` or all\r\n * registered callbacks.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.off =\r\nEmitter.prototype.removeListener =\r\nEmitter.prototype.removeAllListeners =\r\nEmitter.prototype.removeEventListener = function(event, fn){\r\n  this._callbacks = this._callbacks || {};\r\n\r\n  // all\r\n  if (0 == arguments.length) {\r\n    this._callbacks = {};\r\n    return this;\r\n  }\r\n\r\n  // specific event\r\n  var callbacks = this._callbacks['$' + event];\r\n  if (!callbacks) return this;\r\n\r\n  // remove all handlers\r\n  if (1 == arguments.length) {\r\n    delete this._callbacks['$' + event];\r\n    return this;\r\n  }\r\n\r\n  // remove specific handler\r\n  var cb;\r\n  for (var i = 0; i < callbacks.length; i++) {\r\n    cb = callbacks[i];\r\n    if (cb === fn || cb.fn === fn) {\r\n      callbacks.splice(i, 1);\r\n      break;\r\n    }\r\n  }\r\n\r\n  // Remove event specific arrays for event types that no\r\n  // one is subscribed for to avoid memory leak.\r\n  if (callbacks.length === 0) {\r\n    delete this._callbacks['$' + event];\r\n  }\r\n\r\n  return this;\r\n};\r\n\r\n/**\r\n * Emit `event` with the given args.\r\n *\r\n * @param {String} event\r\n * @param {Mixed} ...\r\n * @return {Emitter}\r\n */\r\n\r\nEmitter.prototype.emit = function(event){\r\n  this._callbacks = this._callbacks || {};\r\n\r\n  var args = new Array(arguments.length - 1)\r\n    , callbacks = this._callbacks['$' + event];\r\n\r\n  for (var i = 1; i < arguments.length; i++) {\r\n    args[i - 1] = arguments[i];\r\n  }\r\n\r\n  if (callbacks) {\r\n    callbacks = callbacks.slice(0);\r\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\r\n      callbacks[i].apply(this, args);\r\n    }\r\n  }\r\n\r\n  return this;\r\n};\r\n\r\n/**\r\n * Return array of callbacks for `event`.\r\n *\r\n * @param {String} event\r\n * @return {Array}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.listeners = function(event){\r\n  this._callbacks = this._callbacks || {};\r\n  return this._callbacks['$' + event] || [];\r\n};\r\n\r\n/**\r\n * Check if this emitter has `event` handlers.\r\n *\r\n * @param {String} event\r\n * @return {Boolean}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.hasListeners = function(event){\r\n  return !! this.listeners(event).length;\r\n};\r\n", "/* eslint-env browser */\n\n/**\n * This is the web browser implementation of `debug()`.\n */\n\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.storage = localstorage();\nexports.destroy = (() => {\n\tlet warned = false;\n\n\treturn () => {\n\t\tif (!warned) {\n\t\t\twarned = true;\n\t\t\tconsole.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');\n\t\t}\n\t};\n})();\n\n/**\n * Colors.\n */\n\nexports.colors = [\n\t'#0000CC',\n\t'#0000FF',\n\t'#0033CC',\n\t'#0033FF',\n\t'#0066CC',\n\t'#0066FF',\n\t'#0099CC',\n\t'#0099FF',\n\t'#00CC00',\n\t'#00CC33',\n\t'#00CC66',\n\t'#00CC99',\n\t'#00CCCC',\n\t'#00CCFF',\n\t'#3300CC',\n\t'#3300FF',\n\t'#3333CC',\n\t'#3333FF',\n\t'#3366CC',\n\t'#3366FF',\n\t'#3399CC',\n\t'#3399FF',\n\t'#33CC00',\n\t'#33CC33',\n\t'#33CC66',\n\t'#33CC99',\n\t'#33CCCC',\n\t'#33CCFF',\n\t'#6600CC',\n\t'#6600FF',\n\t'#6633CC',\n\t'#6633FF',\n\t'#66CC00',\n\t'#66CC33',\n\t'#9900CC',\n\t'#9900FF',\n\t'#9933CC',\n\t'#9933FF',\n\t'#99CC00',\n\t'#99CC33',\n\t'#CC0000',\n\t'#CC0033',\n\t'#CC0066',\n\t'#CC0099',\n\t'#CC00CC',\n\t'#CC00FF',\n\t'#CC3300',\n\t'#CC3333',\n\t'#CC3366',\n\t'#CC3399',\n\t'#CC33CC',\n\t'#CC33FF',\n\t'#CC6600',\n\t'#CC6633',\n\t'#CC9900',\n\t'#CC9933',\n\t'#CCCC00',\n\t'#CCCC33',\n\t'#FF0000',\n\t'#FF0033',\n\t'#FF0066',\n\t'#FF0099',\n\t'#FF00CC',\n\t'#FF00FF',\n\t'#FF3300',\n\t'#FF3333',\n\t'#FF3366',\n\t'#FF3399',\n\t'#FF33CC',\n\t'#FF33FF',\n\t'#FF6600',\n\t'#FF6633',\n\t'#FF9900',\n\t'#FF9933',\n\t'#FFCC00',\n\t'#FFCC33'\n];\n\n/**\n * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n * and the Firebug extension (any Firefox version) are known\n * to support \"%c\" CSS customizations.\n *\n * TODO: add a `localStorage` variable to explicitly enable/disable colors\n */\n\n// eslint-disable-next-line complexity\nfunction useColors() {\n\t// NB: In an Electron preload script, document will be defined but not fully\n\t// initialized. Since we know we're in Chrome, we'll just detect this case\n\t// explicitly\n\tif (typeof window !== 'undefined' && window.process && (window.process.type === 'renderer' || window.process.__nwjs)) {\n\t\treturn true;\n\t}\n\n\t// Internet Explorer and Edge do not support colors.\n\tif (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/)) {\n\t\treturn false;\n\t}\n\n\t// Is webkit? http://stackoverflow.com/a/16459606/376773\n\t// document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n\treturn (typeof document !== 'undefined' && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance) ||\n\t\t// Is firebug? http://stackoverflow.com/a/398120/376773\n\t\t(typeof window !== 'undefined' && window.console && (window.console.firebug || (window.console.exception && window.console.table))) ||\n\t\t// Is firefox >= v31?\n\t\t// https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n\t\t(typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/) && parseInt(RegExp.$1, 10) >= 31) ||\n\t\t// Double check webkit in userAgent just in case we are in a worker\n\t\t(typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/));\n}\n\n/**\n * Colorize log arguments if enabled.\n *\n * @api public\n */\n\nfunction formatArgs(args) {\n\targs[0] = (this.useColors ? '%c' : '') +\n\t\tthis.namespace +\n\t\t(this.useColors ? ' %c' : ' ') +\n\t\targs[0] +\n\t\t(this.useColors ? '%c ' : ' ') +\n\t\t'+' + module.exports.humanize(this.diff);\n\n\tif (!this.useColors) {\n\t\treturn;\n\t}\n\n\tconst c = 'color: ' + this.color;\n\targs.splice(1, 0, c, 'color: inherit');\n\n\t// The final \"%c\" is somewhat tricky, because there could be other\n\t// arguments passed either before or after the %c, so we need to\n\t// figure out the correct index to insert the CSS into\n\tlet index = 0;\n\tlet lastC = 0;\n\targs[0].replace(/%[a-zA-Z%]/g, match => {\n\t\tif (match === '%%') {\n\t\t\treturn;\n\t\t}\n\t\tindex++;\n\t\tif (match === '%c') {\n\t\t\t// We only are interested in the *last* %c\n\t\t\t// (the user may have provided their own)\n\t\t\tlastC = index;\n\t\t}\n\t});\n\n\targs.splice(lastC, 0, c);\n}\n\n/**\n * Invokes `console.debug()` when available.\n * No-op when `console.debug` is not a \"function\".\n * If `console.debug` is not available, falls back\n * to `console.log`.\n *\n * @api public\n */\nexports.log = console.debug || console.log || (() => {});\n\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */\nfunction save(namespaces) {\n\ttry {\n\t\tif (namespaces) {\n\t\t\texports.storage.setItem('debug', namespaces);\n\t\t} else {\n\t\t\texports.storage.removeItem('debug');\n\t\t}\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n}\n\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */\nfunction load() {\n\tlet r;\n\ttry {\n\t\tr = exports.storage.getItem('debug');\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n\n\t// If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n\tif (!r && typeof process !== 'undefined' && 'env' in process) {\n\t\tr = process.env.DEBUG;\n\t}\n\n\treturn r;\n}\n\n/**\n * Localstorage attempts to return the localstorage.\n *\n * This is necessary because safari throws\n * when a user disables cookies/localstorage\n * and you attempt to access it.\n *\n * @return {LocalStorage}\n * @api private\n */\n\nfunction localstorage() {\n\ttry {\n\t\t// TVMLKit (Apple TV JS Runtime) does not have a window object, just localStorage in the global context\n\t\t// The Browser also has localStorage in the global context.\n\t\treturn localStorage;\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n}\n\nmodule.exports = require('./common')(exports);\n\nconst {formatters} = module.exports;\n\n/**\n * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n */\n\nformatters.j = function (v) {\n\ttry {\n\t\treturn JSON.stringify(v);\n\t} catch (error) {\n\t\treturn '[UnexpectedJSONParseError]: ' + error.message;\n\t}\n};\n", "\n/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n */\n\nfunction setup(env) {\n\tcreateDebug.debug = createDebug;\n\tcreateDebug.default = createDebug;\n\tcreateDebug.coerce = coerce;\n\tcreateDebug.disable = disable;\n\tcreateDebug.enable = enable;\n\tcreateDebug.enabled = enabled;\n\tcreateDebug.humanize = require('ms');\n\tcreateDebug.destroy = destroy;\n\n\tObject.keys(env).forEach(key => {\n\t\tcreateDebug[key] = env[key];\n\t});\n\n\t/**\n\t* The currently active debug mode names, and names to skip.\n\t*/\n\n\tcreateDebug.names = [];\n\tcreateDebug.skips = [];\n\n\t/**\n\t* Map of special \"%n\" handling functions, for the debug \"format\" argument.\n\t*\n\t* Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n\t*/\n\tcreateDebug.formatters = {};\n\n\t/**\n\t* Selects a color for a debug namespace\n\t* @param {String} namespace The namespace string for the for the debug instance to be colored\n\t* @return {Number|String} An ANSI color code for the given namespace\n\t* @api private\n\t*/\n\tfunction selectColor(namespace) {\n\t\tlet hash = 0;\n\n\t\tfor (let i = 0; i < namespace.length; i++) {\n\t\t\thash = ((hash << 5) - hash) + namespace.charCodeAt(i);\n\t\t\thash |= 0; // Convert to 32bit integer\n\t\t}\n\n\t\treturn createDebug.colors[Math.abs(hash) % createDebug.colors.length];\n\t}\n\tcreateDebug.selectColor = selectColor;\n\n\t/**\n\t* Create a debugger with the given `namespace`.\n\t*\n\t* @param {String} namespace\n\t* @return {Function}\n\t* @api public\n\t*/\n\tfunction createDebug(namespace) {\n\t\tlet prevTime;\n\t\tlet enableOverride = null;\n\n\t\tfunction debug(...args) {\n\t\t\t// Disabled?\n\t\t\tif (!debug.enabled) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst self = debug;\n\n\t\t\t// Set `diff` timestamp\n\t\t\tconst curr = Number(new Date());\n\t\t\tconst ms = curr - (prevTime || curr);\n\t\t\tself.diff = ms;\n\t\t\tself.prev = prevTime;\n\t\t\tself.curr = curr;\n\t\t\tprevTime = curr;\n\n\t\t\targs[0] = createDebug.coerce(args[0]);\n\n\t\t\tif (typeof args[0] !== 'string') {\n\t\t\t\t// Anything else let's inspect with %O\n\t\t\t\targs.unshift('%O');\n\t\t\t}\n\n\t\t\t// Apply any `formatters` transformations\n\t\t\tlet index = 0;\n\t\t\targs[0] = args[0].replace(/%([a-zA-Z%])/g, (match, format) => {\n\t\t\t\t// If we encounter an escaped % then don't increase the array index\n\t\t\t\tif (match === '%%') {\n\t\t\t\t\treturn '%';\n\t\t\t\t}\n\t\t\t\tindex++;\n\t\t\t\tconst formatter = createDebug.formatters[format];\n\t\t\t\tif (typeof formatter === 'function') {\n\t\t\t\t\tconst val = args[index];\n\t\t\t\t\tmatch = formatter.call(self, val);\n\n\t\t\t\t\t// Now we need to remove `args[index]` since it's inlined in the `format`\n\t\t\t\t\targs.splice(index, 1);\n\t\t\t\t\tindex--;\n\t\t\t\t}\n\t\t\t\treturn match;\n\t\t\t});\n\n\t\t\t// Apply env-specific formatting (colors, etc.)\n\t\t\tcreateDebug.formatArgs.call(self, args);\n\n\t\t\tconst logFn = self.log || createDebug.log;\n\t\t\tlogFn.apply(self, args);\n\t\t}\n\n\t\tdebug.namespace = namespace;\n\t\tdebug.useColors = createDebug.useColors();\n\t\tdebug.color = createDebug.selectColor(namespace);\n\t\tdebug.extend = extend;\n\t\tdebug.destroy = createDebug.destroy; // XXX Temporary. Will be removed in the next major release.\n\n\t\tObject.defineProperty(debug, 'enabled', {\n\t\t\tenumerable: true,\n\t\t\tconfigurable: false,\n\t\t\tget: () => enableOverride === null ? createDebug.enabled(namespace) : enableOverride,\n\t\t\tset: v => {\n\t\t\t\tenableOverride = v;\n\t\t\t}\n\t\t});\n\n\t\t// Env-specific initialization logic for debug instances\n\t\tif (typeof createDebug.init === 'function') {\n\t\t\tcreateDebug.init(debug);\n\t\t}\n\n\t\treturn debug;\n\t}\n\n\tfunction extend(namespace, delimiter) {\n\t\tconst newDebug = createDebug(this.namespace + (typeof delimiter === 'undefined' ? ':' : delimiter) + namespace);\n\t\tnewDebug.log = this.log;\n\t\treturn newDebug;\n\t}\n\n\t/**\n\t* Enables a debug mode by namespaces. This can include modes\n\t* separated by a colon and wildcards.\n\t*\n\t* @param {String} namespaces\n\t* @api public\n\t*/\n\tfunction enable(namespaces) {\n\t\tcreateDebug.save(namespaces);\n\n\t\tcreateDebug.names = [];\n\t\tcreateDebug.skips = [];\n\n\t\tlet i;\n\t\tconst split = (typeof namespaces === 'string' ? namespaces : '').split(/[\\s,]+/);\n\t\tconst len = split.length;\n\n\t\tfor (i = 0; i < len; i++) {\n\t\t\tif (!split[i]) {\n\t\t\t\t// ignore empty strings\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tnamespaces = split[i].replace(/\\*/g, '.*?');\n\n\t\t\tif (namespaces[0] === '-') {\n\t\t\t\tcreateDebug.skips.push(new RegExp('^' + namespaces.substr(1) + '$'));\n\t\t\t} else {\n\t\t\t\tcreateDebug.names.push(new RegExp('^' + namespaces + '$'));\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t* Disable debug output.\n\t*\n\t* @return {String} namespaces\n\t* @api public\n\t*/\n\tfunction disable() {\n\t\tconst namespaces = [\n\t\t\t...createDebug.names.map(toNamespace),\n\t\t\t...createDebug.skips.map(toNamespace).map(namespace => '-' + namespace)\n\t\t].join(',');\n\t\tcreateDebug.enable('');\n\t\treturn namespaces;\n\t}\n\n\t/**\n\t* Returns true if the given mode name is enabled, false otherwise.\n\t*\n\t* @param {String} name\n\t* @return {Boolean}\n\t* @api public\n\t*/\n\tfunction enabled(name) {\n\t\tif (name[name.length - 1] === '*') {\n\t\t\treturn true;\n\t\t}\n\n\t\tlet i;\n\t\tlet len;\n\n\t\tfor (i = 0, len = createDebug.skips.length; i < len; i++) {\n\t\t\tif (createDebug.skips[i].test(name)) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\tfor (i = 0, len = createDebug.names.length; i < len; i++) {\n\t\t\tif (createDebug.names[i].test(name)) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t}\n\n\t/**\n\t* Convert regexp to namespace\n\t*\n\t* @param {RegExp} regxep\n\t* @return {String} namespace\n\t* @api private\n\t*/\n\tfunction toNamespace(regexp) {\n\t\treturn regexp.toString()\n\t\t\t.substring(2, regexp.toString().length - 2)\n\t\t\t.replace(/\\.\\*\\?$/, '*');\n\t}\n\n\t/**\n\t* Coerce `val`.\n\t*\n\t* @param {Mixed} val\n\t* @return {Mixed}\n\t* @api private\n\t*/\n\tfunction coerce(val) {\n\t\tif (val instanceof Error) {\n\t\t\treturn val.stack || val.message;\n\t\t}\n\t\treturn val;\n\t}\n\n\t/**\n\t* XXX DO NOT USE. This is a temporary stub function.\n\t* XXX It WILL be removed in the next major release.\n\t*/\n\tfunction destroy() {\n\t\tconsole.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');\n\t}\n\n\tcreateDebug.enable(createDebug.load());\n\n\treturn createDebug;\n}\n\nmodule.exports = setup;\n", "module.exports = (() => {\n  if (typeof self !== \"undefined\") {\n    return self;\n  } else if (typeof window !== \"undefined\") {\n    return window;\n  } else {\n    return Function(\"return this\")();\n  }\n})();\n", "const Socket = require(\"./socket\");\n\nmodule.exports = (uri, opts) => new Socket(uri, opts);\n\n/**\n * Expose deps for legacy compatibility\n * and standalone browser access.\n */\n\nmodule.exports.Socket = Socket;\nmodule.exports.protocol = Socket.protocol; // this is an int\nmodule.exports.Transport = require(\"./transport\");\nmodule.exports.transports = require(\"./transports/index\");\nmodule.exports.parser = require(\"engine.io-parser\");\n", "const transports = require(\"./transports/index\");\nconst Emitter = require(\"component-emitter\");\nconst debug = require(\"debug\")(\"engine.io-client:socket\");\nconst parser = require(\"engine.io-parser\");\nconst parseuri = require(\"parseuri\");\nconst parseqs = require(\"parseqs\");\n\nclass Socket extends Emitter {\n  /**\n   * Socket constructor.\n   *\n   * @param {String|Object} uri or options\n   * @param {Object} options\n   * @api public\n   */\n  constructor(uri, opts = {}) {\n    super();\n\n    if (uri && \"object\" === typeof uri) {\n      opts = uri;\n      uri = null;\n    }\n\n    if (uri) {\n      uri = parseuri(uri);\n      opts.hostname = uri.host;\n      opts.secure = uri.protocol === \"https\" || uri.protocol === \"wss\";\n      opts.port = uri.port;\n      if (uri.query) opts.query = uri.query;\n    } else if (opts.host) {\n      opts.hostname = parseuri(opts.host).host;\n    }\n\n    this.secure =\n      null != opts.secure\n        ? opts.secure\n        : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n\n    if (opts.hostname && !opts.port) {\n      // if no port is specified manually, use the protocol default\n      opts.port = this.secure ? \"443\" : \"80\";\n    }\n\n    this.hostname =\n      opts.hostname ||\n      (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n    this.port =\n      opts.port ||\n      (typeof location !== \"undefined\" && location.port\n        ? location.port\n        : this.secure\n        ? 443\n        : 80);\n\n    this.transports = opts.transports || [\"polling\", \"websocket\"];\n    this.readyState = \"\";\n    this.writeBuffer = [];\n    this.prevBufferLen = 0;\n\n    this.opts = Object.assign(\n      {\n        path: \"/engine.io\",\n        agent: false,\n        withCredentials: false,\n        upgrade: true,\n        jsonp: true,\n        timestampParam: \"t\",\n        rememberUpgrade: false,\n        rejectUnauthorized: true,\n        perMessageDeflate: {\n          threshold: 1024\n        },\n        transportOptions: {}\n      },\n      opts\n    );\n\n    this.opts.path = this.opts.path.replace(/\\/$/, \"\") + \"/\";\n\n    if (typeof this.opts.query === \"string\") {\n      this.opts.query = parseqs.decode(this.opts.query);\n    }\n\n    // set on handshake\n    this.id = null;\n    this.upgrades = null;\n    this.pingInterval = null;\n    this.pingTimeout = null;\n\n    // set on heartbeat\n    this.pingTimeoutTimer = null;\n\n    if (typeof addEventListener === \"function\") {\n      addEventListener(\n        \"beforeunload\",\n        () => {\n          if (this.transport) {\n            // silently close the transport\n            this.transport.removeAllListeners();\n            this.transport.close();\n          }\n        },\n        false\n      );\n    }\n\n    this.open();\n  }\n\n  /**\n   * Creates transport of the given type.\n   *\n   * @param {String} transport name\n   * @return {Transport}\n   * @api private\n   */\n  createTransport(name) {\n    debug('creating transport \"%s\"', name);\n    const query = clone(this.opts.query);\n\n    // append engine.io protocol identifier\n    query.EIO = parser.protocol;\n\n    // transport name\n    query.transport = name;\n\n    // session id if we already have one\n    if (this.id) query.sid = this.id;\n\n    const opts = Object.assign(\n      {},\n      this.opts.transportOptions[name],\n      this.opts,\n      {\n        query,\n        socket: this,\n        hostname: this.hostname,\n        secure: this.secure,\n        port: this.port\n      }\n    );\n\n    debug(\"options: %j\", opts);\n\n    return new transports[name](opts);\n  }\n\n  /**\n   * Initializes transport to use and starts probe.\n   *\n   * @api private\n   */\n  open() {\n    let transport;\n    if (\n      this.opts.rememberUpgrade &&\n      Socket.priorWebsocketSuccess &&\n      this.transports.indexOf(\"websocket\") !== -1\n    ) {\n      transport = \"websocket\";\n    } else if (0 === this.transports.length) {\n      // Emit error on next tick so it can be listened to\n      const self = this;\n      setTimeout(function() {\n        self.emit(\"error\", \"No transports available\");\n      }, 0);\n      return;\n    } else {\n      transport = this.transports[0];\n    }\n    this.readyState = \"opening\";\n\n    // Retry with the next transport if the transport is disabled (jsonp: false)\n    try {\n      transport = this.createTransport(transport);\n    } catch (e) {\n      debug(\"error while creating transport: %s\", e);\n      this.transports.shift();\n      this.open();\n      return;\n    }\n\n    transport.open();\n    this.setTransport(transport);\n  }\n\n  /**\n   * Sets the current transport. Disables the existing one (if any).\n   *\n   * @api private\n   */\n  setTransport(transport) {\n    debug(\"setting transport %s\", transport.name);\n    const self = this;\n\n    if (this.transport) {\n      debug(\"clearing existing transport %s\", this.transport.name);\n      this.transport.removeAllListeners();\n    }\n\n    // set up transport\n    this.transport = transport;\n\n    // set up transport listeners\n    transport\n      .on(\"drain\", function() {\n        self.onDrain();\n      })\n      .on(\"packet\", function(packet) {\n        self.onPacket(packet);\n      })\n      .on(\"error\", function(e) {\n        self.onError(e);\n      })\n      .on(\"close\", function() {\n        self.onClose(\"transport close\");\n      });\n  }\n\n  /**\n   * Probes a transport.\n   *\n   * @param {String} transport name\n   * @api private\n   */\n  probe(name) {\n    debug('probing transport \"%s\"', name);\n    let transport = this.createTransport(name, { probe: 1 });\n    let failed = false;\n    const self = this;\n\n    Socket.priorWebsocketSuccess = false;\n\n    function onTransportOpen() {\n      if (self.onlyBinaryUpgrades) {\n        const upgradeLosesBinary =\n          !this.supportsBinary && self.transport.supportsBinary;\n        failed = failed || upgradeLosesBinary;\n      }\n      if (failed) return;\n\n      debug('probe transport \"%s\" opened', name);\n      transport.send([{ type: \"ping\", data: \"probe\" }]);\n      transport.once(\"packet\", function(msg) {\n        if (failed) return;\n        if (\"pong\" === msg.type && \"probe\" === msg.data) {\n          debug('probe transport \"%s\" pong', name);\n          self.upgrading = true;\n          self.emit(\"upgrading\", transport);\n          if (!transport) return;\n          Socket.priorWebsocketSuccess = \"websocket\" === transport.name;\n\n          debug('pausing current transport \"%s\"', self.transport.name);\n          self.transport.pause(function() {\n            if (failed) return;\n            if (\"closed\" === self.readyState) return;\n            debug(\"changing transport and sending upgrade packet\");\n\n            cleanup();\n\n            self.setTransport(transport);\n            transport.send([{ type: \"upgrade\" }]);\n            self.emit(\"upgrade\", transport);\n            transport = null;\n            self.upgrading = false;\n            self.flush();\n          });\n        } else {\n          debug('probe transport \"%s\" failed', name);\n          const err = new Error(\"probe error\");\n          err.transport = transport.name;\n          self.emit(\"upgradeError\", err);\n        }\n      });\n    }\n\n    function freezeTransport() {\n      if (failed) return;\n\n      // Any callback called by transport should be ignored since now\n      failed = true;\n\n      cleanup();\n\n      transport.close();\n      transport = null;\n    }\n\n    // Handle any error that happens while probing\n    function onerror(err) {\n      const error = new Error(\"probe error: \" + err);\n      error.transport = transport.name;\n\n      freezeTransport();\n\n      debug('probe transport \"%s\" failed because of error: %s', name, err);\n\n      self.emit(\"upgradeError\", error);\n    }\n\n    function onTransportClose() {\n      onerror(\"transport closed\");\n    }\n\n    // When the socket is closed while we're probing\n    function onclose() {\n      onerror(\"socket closed\");\n    }\n\n    // When the socket is upgraded while we're probing\n    function onupgrade(to) {\n      if (transport && to.name !== transport.name) {\n        debug('\"%s\" works - aborting \"%s\"', to.name, transport.name);\n        freezeTransport();\n      }\n    }\n\n    // Remove all listeners on the transport and on self\n    function cleanup() {\n      transport.removeListener(\"open\", onTransportOpen);\n      transport.removeListener(\"error\", onerror);\n      transport.removeListener(\"close\", onTransportClose);\n      self.removeListener(\"close\", onclose);\n      self.removeListener(\"upgrading\", onupgrade);\n    }\n\n    transport.once(\"open\", onTransportOpen);\n    transport.once(\"error\", onerror);\n    transport.once(\"close\", onTransportClose);\n\n    this.once(\"close\", onclose);\n    this.once(\"upgrading\", onupgrade);\n\n    transport.open();\n  }\n\n  /**\n   * Called when connection is deemed open.\n   *\n   * @api public\n   */\n  onOpen() {\n    debug(\"socket open\");\n    this.readyState = \"open\";\n    Socket.priorWebsocketSuccess = \"websocket\" === this.transport.name;\n    this.emit(\"open\");\n    this.flush();\n\n    // we check for `readyState` in case an `open`\n    // listener already closed the socket\n    if (\n      \"open\" === this.readyState &&\n      this.opts.upgrade &&\n      this.transport.pause\n    ) {\n      debug(\"starting upgrade probes\");\n      let i = 0;\n      const l = this.upgrades.length;\n      for (; i < l; i++) {\n        this.probe(this.upgrades[i]);\n      }\n    }\n  }\n\n  /**\n   * Handles a packet.\n   *\n   * @api private\n   */\n  onPacket(packet) {\n    if (\n      \"opening\" === this.readyState ||\n      \"open\" === this.readyState ||\n      \"closing\" === this.readyState\n    ) {\n      debug('socket receive: type \"%s\", data \"%s\"', packet.type, packet.data);\n\n      this.emit(\"packet\", packet);\n\n      // Socket is live - any packet counts\n      this.emit(\"heartbeat\");\n\n      switch (packet.type) {\n        case \"open\":\n          this.onHandshake(JSON.parse(packet.data));\n          break;\n\n        case \"ping\":\n          this.resetPingTimeout();\n          this.sendPacket(\"pong\");\n          this.emit(\"pong\");\n          break;\n\n        case \"error\":\n          const err = new Error(\"server error\");\n          err.code = packet.data;\n          this.onError(err);\n          break;\n\n        case \"message\":\n          this.emit(\"data\", packet.data);\n          this.emit(\"message\", packet.data);\n          break;\n      }\n    } else {\n      debug('packet received with socket readyState \"%s\"', this.readyState);\n    }\n  }\n\n  /**\n   * Called upon handshake completion.\n   *\n   * @param {Object} handshake obj\n   * @api private\n   */\n  onHandshake(data) {\n    this.emit(\"handshake\", data);\n    this.id = data.sid;\n    this.transport.query.sid = data.sid;\n    this.upgrades = this.filterUpgrades(data.upgrades);\n    this.pingInterval = data.pingInterval;\n    this.pingTimeout = data.pingTimeout;\n    this.onOpen();\n    // In case open handler closes socket\n    if (\"closed\" === this.readyState) return;\n    this.resetPingTimeout();\n  }\n\n  /**\n   * Sets and resets ping timeout timer based on server pings.\n   *\n   * @api private\n   */\n  resetPingTimeout() {\n    clearTimeout(this.pingTimeoutTimer);\n    this.pingTimeoutTimer = setTimeout(() => {\n      this.onClose(\"ping timeout\");\n    }, this.pingInterval + this.pingTimeout);\n  }\n\n  /**\n   * Called on `drain` event\n   *\n   * @api private\n   */\n  onDrain() {\n    this.writeBuffer.splice(0, this.prevBufferLen);\n\n    // setting prevBufferLen = 0 is very important\n    // for example, when upgrading, upgrade packet is sent over,\n    // and a nonzero prevBufferLen could cause problems on `drain`\n    this.prevBufferLen = 0;\n\n    if (0 === this.writeBuffer.length) {\n      this.emit(\"drain\");\n    } else {\n      this.flush();\n    }\n  }\n\n  /**\n   * Flush write buffers.\n   *\n   * @api private\n   */\n  flush() {\n    if (\n      \"closed\" !== this.readyState &&\n      this.transport.writable &&\n      !this.upgrading &&\n      this.writeBuffer.length\n    ) {\n      debug(\"flushing %d packets in socket\", this.writeBuffer.length);\n      this.transport.send(this.writeBuffer);\n      // keep track of current length of writeBuffer\n      // splice writeBuffer and callbackBuffer on `drain`\n      this.prevBufferLen = this.writeBuffer.length;\n      this.emit(\"flush\");\n    }\n  }\n\n  /**\n   * Sends a message.\n   *\n   * @param {String} message.\n   * @param {Function} callback function.\n   * @param {Object} options.\n   * @return {Socket} for chaining.\n   * @api public\n   */\n  write(msg, options, fn) {\n    this.sendPacket(\"message\", msg, options, fn);\n    return this;\n  }\n\n  send(msg, options, fn) {\n    this.sendPacket(\"message\", msg, options, fn);\n    return this;\n  }\n\n  /**\n   * Sends a packet.\n   *\n   * @param {String} packet type.\n   * @param {String} data.\n   * @param {Object} options.\n   * @param {Function} callback function.\n   * @api private\n   */\n  sendPacket(type, data, options, fn) {\n    if (\"function\" === typeof data) {\n      fn = data;\n      data = undefined;\n    }\n\n    if (\"function\" === typeof options) {\n      fn = options;\n      options = null;\n    }\n\n    if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n      return;\n    }\n\n    options = options || {};\n    options.compress = false !== options.compress;\n\n    const packet = {\n      type: type,\n      data: data,\n      options: options\n    };\n    this.emit(\"packetCreate\", packet);\n    this.writeBuffer.push(packet);\n    if (fn) this.once(\"flush\", fn);\n    this.flush();\n  }\n\n  /**\n   * Closes the connection.\n   *\n   * @api private\n   */\n  close() {\n    const self = this;\n\n    if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n      this.readyState = \"closing\";\n\n      if (this.writeBuffer.length) {\n        this.once(\"drain\", function() {\n          if (this.upgrading) {\n            waitForUpgrade();\n          } else {\n            close();\n          }\n        });\n      } else if (this.upgrading) {\n        waitForUpgrade();\n      } else {\n        close();\n      }\n    }\n\n    function close() {\n      self.onClose(\"forced close\");\n      debug(\"socket closing - telling transport to close\");\n      self.transport.close();\n    }\n\n    function cleanupAndClose() {\n      self.removeListener(\"upgrade\", cleanupAndClose);\n      self.removeListener(\"upgradeError\", cleanupAndClose);\n      close();\n    }\n\n    function waitForUpgrade() {\n      // wait for upgrade to finish since we can't send packets while pausing a transport\n      self.once(\"upgrade\", cleanupAndClose);\n      self.once(\"upgradeError\", cleanupAndClose);\n    }\n\n    return this;\n  }\n\n  /**\n   * Called upon transport error\n   *\n   * @api private\n   */\n  onError(err) {\n    debug(\"socket error %j\", err);\n    Socket.priorWebsocketSuccess = false;\n    this.emit(\"error\", err);\n    this.onClose(\"transport error\", err);\n  }\n\n  /**\n   * Called upon transport close.\n   *\n   * @api private\n   */\n  onClose(reason, desc) {\n    if (\n      \"opening\" === this.readyState ||\n      \"open\" === this.readyState ||\n      \"closing\" === this.readyState\n    ) {\n      debug('socket close with reason: \"%s\"', reason);\n      const self = this;\n\n      // clear timers\n      clearTimeout(this.pingIntervalTimer);\n      clearTimeout(this.pingTimeoutTimer);\n\n      // stop event from firing again for transport\n      this.transport.removeAllListeners(\"close\");\n\n      // ensure transport won't stay open\n      this.transport.close();\n\n      // ignore further transport communication\n      this.transport.removeAllListeners();\n\n      // set ready state\n      this.readyState = \"closed\";\n\n      // clear session id\n      this.id = null;\n\n      // emit close event\n      this.emit(\"close\", reason, desc);\n\n      // clean buffers after, so users can still\n      // grab the buffers on `close` event\n      self.writeBuffer = [];\n      self.prevBufferLen = 0;\n    }\n  }\n\n  /**\n   * Filters upgrades, returning only those matching client transports.\n   *\n   * @param {Array} server upgrades\n   * @api private\n   *\n   */\n  filterUpgrades(upgrades) {\n    const filteredUpgrades = [];\n    let i = 0;\n    const j = upgrades.length;\n    for (; i < j; i++) {\n      if (~this.transports.indexOf(upgrades[i]))\n        filteredUpgrades.push(upgrades[i]);\n    }\n    return filteredUpgrades;\n  }\n}\n\nSocket.priorWebsocketSuccess = false;\n\n/**\n * Protocol version.\n *\n * @api public\n */\n\nSocket.protocol = parser.protocol; // this is an int\n\nfunction clone(obj) {\n  const o = {};\n  for (let i in obj) {\n    if (obj.hasOwnProperty(i)) {\n      o[i] = obj[i];\n    }\n  }\n  return o;\n}\n\nmodule.exports = Socket;\n", "const parser = require(\"engine.io-parser\");\nconst Emitter = require(\"component-emitter\");\n\nclass Transport extends Emitter {\n  /**\n   * Transport abstract constructor.\n   *\n   * @param {Object} options.\n   * @api private\n   */\n  constructor(opts) {\n    super();\n\n    this.opts = opts;\n    this.query = opts.query;\n    this.readyState = \"\";\n    this.socket = opts.socket;\n  }\n\n  /**\n   * Emits an error.\n   *\n   * @param {String} str\n   * @return {Transport} for chaining\n   * @api public\n   */\n  onError(msg, desc) {\n    const err = new Error(msg);\n    err.type = \"TransportError\";\n    err.description = desc;\n    this.emit(\"error\", err);\n    return this;\n  }\n\n  /**\n   * Opens the transport.\n   *\n   * @api public\n   */\n  open() {\n    if (\"closed\" === this.readyState || \"\" === this.readyState) {\n      this.readyState = \"opening\";\n      this.doOpen();\n    }\n\n    return this;\n  }\n\n  /**\n   * Closes the transport.\n   *\n   * @api private\n   */\n  close() {\n    if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n      this.doClose();\n      this.onClose();\n    }\n\n    return this;\n  }\n\n  /**\n   * Sends multiple packets.\n   *\n   * @param {Array} packets\n   * @api private\n   */\n  send(packets) {\n    if (\"open\" === this.readyState) {\n      this.write(packets);\n    } else {\n      throw new Error(\"Transport not open\");\n    }\n  }\n\n  /**\n   * Called upon open\n   *\n   * @api private\n   */\n  onOpen() {\n    this.readyState = \"open\";\n    this.writable = true;\n    this.emit(\"open\");\n  }\n\n  /**\n   * Called with data.\n   *\n   * @param {String} data\n   * @api private\n   */\n  onData(data) {\n    const packet = parser.decodePacket(data, this.socket.binaryType);\n    this.onPacket(packet);\n  }\n\n  /**\n   * Called with a decoded packet.\n   */\n  onPacket(packet) {\n    this.emit(\"packet\", packet);\n  }\n\n  /**\n   * Called upon close.\n   *\n   * @api private\n   */\n  onClose() {\n    this.readyState = \"closed\";\n    this.emit(\"close\");\n  }\n}\n\nmodule.exports = Transport;\n", "const XMLHttpRequest = require(\"xmlhttprequest-ssl\");\nconst XHR = require(\"./polling-xhr\");\nconst JSONP = require(\"./polling-jsonp\");\nconst websocket = require(\"./websocket\");\n\nexports.polling = polling;\nexports.websocket = websocket;\n\n/**\n * Polling transport polymorphic constructor.\n * Decides on xhr vs jsonp based on feature detection.\n *\n * @api private\n */\n\nfunction polling(opts) {\n  let xhr;\n  let xd = false;\n  let xs = false;\n  const jsonp = false !== opts.jsonp;\n\n  if (typeof location !== \"undefined\") {\n    const isSSL = \"https:\" === location.protocol;\n    let port = location.port;\n\n    // some user agents have empty `location.port`\n    if (!port) {\n      port = isSSL ? 443 : 80;\n    }\n\n    xd = opts.hostname !== location.hostname || port !== opts.port;\n    xs = opts.secure !== isSSL;\n  }\n\n  opts.xdomain = xd;\n  opts.xscheme = xs;\n  xhr = new XMLHttpRequest(opts);\n\n  if (\"open\" in xhr && !opts.forceJSONP) {\n    return new XHR(opts);\n  } else {\n    if (!jsonp) throw new Error(\"JSONP disabled\");\n    return new JSONP(opts);\n  }\n}\n", "const Polling = require(\"./polling\");\nconst globalThis = require(\"../globalThis\");\n\nconst rNewline = /\\n/g;\nconst rEscapedNewline = /\\\\n/g;\n\n/**\n * Global JSONP callbacks.\n */\n\nlet callbacks;\n\nclass JSONPPolling extends Polling {\n  /**\n   * JSONP Polling constructor.\n   *\n   * @param {Object} opts.\n   * @api public\n   */\n  constructor(opts) {\n    super(opts);\n\n    this.query = this.query || {};\n\n    // define global callbacks array if not present\n    // we do this here (lazily) to avoid unneeded global pollution\n    if (!callbacks) {\n      // we need to consider multiple engines in the same page\n      callbacks = globalThis.___eio = globalThis.___eio || [];\n    }\n\n    // callback identifier\n    this.index = callbacks.length;\n\n    // add callback to jsonp global\n    const self = this;\n    callbacks.push(function(msg) {\n      self.onData(msg);\n    });\n\n    // append to query string\n    this.query.j = this.index;\n  }\n\n  /**\n   * JSONP only supports binary as base64 encoded strings\n   */\n  get supportsBinary() {\n    return false;\n  }\n\n  /**\n   * Closes the socket.\n   *\n   * @api private\n   */\n  doClose() {\n    if (this.script) {\n      // prevent spurious errors from being emitted when the window is unloaded\n      this.script.onerror = () => {};\n      this.script.parentNode.removeChild(this.script);\n      this.script = null;\n    }\n\n    if (this.form) {\n      this.form.parentNode.removeChild(this.form);\n      this.form = null;\n      this.iframe = null;\n    }\n\n    super.doClose();\n  }\n\n  /**\n   * Starts a poll cycle.\n   *\n   * @api private\n   */\n  doPoll() {\n    const self = this;\n    const script = document.createElement(\"script\");\n\n    if (this.script) {\n      this.script.parentNode.removeChild(this.script);\n      this.script = null;\n    }\n\n    script.async = true;\n    script.src = this.uri();\n    script.onerror = function(e) {\n      self.onError(\"jsonp poll error\", e);\n    };\n\n    const insertAt = document.getElementsByTagName(\"script\")[0];\n    if (insertAt) {\n      insertAt.parentNode.insertBefore(script, insertAt);\n    } else {\n      (document.head || document.body).appendChild(script);\n    }\n    this.script = script;\n\n    const isUAgecko =\n      \"undefined\" !== typeof navigator && /gecko/i.test(navigator.userAgent);\n\n    if (isUAgecko) {\n      setTimeout(function() {\n        const iframe = document.createElement(\"iframe\");\n        document.body.appendChild(iframe);\n        document.body.removeChild(iframe);\n      }, 100);\n    }\n  }\n\n  /**\n   * Writes with a hidden iframe.\n   *\n   * @param {String} data to send\n   * @param {Function} called upon flush.\n   * @api private\n   */\n  doWrite(data, fn) {\n    const self = this;\n    let iframe;\n\n    if (!this.form) {\n      const form = document.createElement(\"form\");\n      const area = document.createElement(\"textarea\");\n      const id = (this.iframeId = \"eio_iframe_\" + this.index);\n\n      form.className = \"socketio\";\n      form.style.position = \"absolute\";\n      form.style.top = \"-1000px\";\n      form.style.left = \"-1000px\";\n      form.target = id;\n      form.method = \"POST\";\n      form.setAttribute(\"accept-charset\", \"utf-8\");\n      area.name = \"d\";\n      form.appendChild(area);\n      document.body.appendChild(form);\n\n      this.form = form;\n      this.area = area;\n    }\n\n    this.form.action = this.uri();\n\n    function complete() {\n      initIframe();\n      fn();\n    }\n\n    function initIframe() {\n      if (self.iframe) {\n        try {\n          self.form.removeChild(self.iframe);\n        } catch (e) {\n          self.onError(\"jsonp polling iframe removal error\", e);\n        }\n      }\n\n      try {\n        // ie6 dynamic iframes with target=\"\" support (thanks Chris Lambacher)\n        const html = '<iframe src=\"javascript:0\" name=\"' + self.iframeId + '\">';\n        iframe = document.createElement(html);\n      } catch (e) {\n        iframe = document.createElement(\"iframe\");\n        iframe.name = self.iframeId;\n        iframe.src = \"javascript:0\";\n      }\n\n      iframe.id = self.iframeId;\n\n      self.form.appendChild(iframe);\n      self.iframe = iframe;\n    }\n\n    initIframe();\n\n    // escape \\n to prevent it from being converted into \\r\\n by some UAs\n    // double escaping is required for escaped new lines because unescaping of new lines can be done safely on server-side\n    data = data.replace(rEscapedNewline, \"\\\\\\n\");\n    this.area.value = data.replace(rNewline, \"\\\\n\");\n\n    try {\n      this.form.submit();\n    } catch (e) {}\n\n    if (this.iframe.attachEvent) {\n      this.iframe.onreadystatechange = function() {\n        if (self.iframe.readyState === \"complete\") {\n          complete();\n        }\n      };\n    } else {\n      this.iframe.onload = complete;\n    }\n  }\n}\n\nmodule.exports = JSONPPolling;\n", "/* global attachEvent */\n\nconst XMLHttpRequest = require(\"xmlhttprequest-ssl\");\nconst Polling = require(\"./polling\");\nconst Emitter = require(\"component-emitter\");\nconst { pick } = require(\"../util\");\nconst globalThis = require(\"../globalThis\");\n\nconst debug = require(\"debug\")(\"engine.io-client:polling-xhr\");\n\n/**\n * Empty function\n */\n\nfunction empty() {}\n\nconst hasXHR2 = (function() {\n  const xhr = new XMLHttpRequest({ xdomain: false });\n  return null != xhr.responseType;\n})();\n\nclass XHR extends Polling {\n  /**\n   * XHR Polling constructor.\n   *\n   * @param {Object} opts\n   * @api public\n   */\n  constructor(opts) {\n    super(opts);\n\n    if (typeof location !== \"undefined\") {\n      const isSSL = \"https:\" === location.protocol;\n      let port = location.port;\n\n      // some user agents have empty `location.port`\n      if (!port) {\n        port = isSSL ? 443 : 80;\n      }\n\n      this.xd =\n        (typeof location !== \"undefined\" &&\n          opts.hostname !== location.hostname) ||\n        port !== opts.port;\n      this.xs = opts.secure !== isSSL;\n    }\n    /**\n     * XHR supports binary\n     */\n    const forceBase64 = opts && opts.forceBase64;\n    this.supportsBinary = hasXHR2 && !forceBase64;\n  }\n\n  /**\n   * Creates a request.\n   *\n   * @param {String} method\n   * @api private\n   */\n  request(opts = {}) {\n    Object.assign(opts, { xd: this.xd, xs: this.xs }, this.opts);\n    return new Request(this.uri(), opts);\n  }\n\n  /**\n   * Sends data.\n   *\n   * @param {String} data to send.\n   * @param {Function} called upon flush.\n   * @api private\n   */\n  doWrite(data, fn) {\n    const req = this.request({\n      method: \"POST\",\n      data: data\n    });\n    const self = this;\n    req.on(\"success\", fn);\n    req.on(\"error\", function(err) {\n      self.onError(\"xhr post error\", err);\n    });\n  }\n\n  /**\n   * Starts a poll cycle.\n   *\n   * @api private\n   */\n  doPoll() {\n    debug(\"xhr poll\");\n    const req = this.request();\n    const self = this;\n    req.on(\"data\", function(data) {\n      self.onData(data);\n    });\n    req.on(\"error\", function(err) {\n      self.onError(\"xhr poll error\", err);\n    });\n    this.pollXhr = req;\n  }\n}\n\nclass Request extends Emitter {\n  /**\n   * Request constructor\n   *\n   * @param {Object} options\n   * @api public\n   */\n  constructor(uri, opts) {\n    super();\n    this.opts = opts;\n\n    this.method = opts.method || \"GET\";\n    this.uri = uri;\n    this.async = false !== opts.async;\n    this.data = undefined !== opts.data ? opts.data : null;\n\n    this.create();\n  }\n\n  /**\n   * Creates the XHR object and sends the request.\n   *\n   * @api private\n   */\n  create() {\n    const opts = pick(\n      this.opts,\n      \"agent\",\n      \"enablesXDR\",\n      \"pfx\",\n      \"key\",\n      \"passphrase\",\n      \"cert\",\n      \"ca\",\n      \"ciphers\",\n      \"rejectUnauthorized\"\n    );\n    opts.xdomain = !!this.opts.xd;\n    opts.xscheme = !!this.opts.xs;\n\n    const xhr = (this.xhr = new XMLHttpRequest(opts));\n    const self = this;\n\n    try {\n      debug(\"xhr open %s: %s\", this.method, this.uri);\n      xhr.open(this.method, this.uri, this.async);\n      try {\n        if (this.opts.extraHeaders) {\n          xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n          for (let i in this.opts.extraHeaders) {\n            if (this.opts.extraHeaders.hasOwnProperty(i)) {\n              xhr.setRequestHeader(i, this.opts.extraHeaders[i]);\n            }\n          }\n        }\n      } catch (e) {}\n\n      if (\"POST\" === this.method) {\n        try {\n          xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n        } catch (e) {}\n      }\n\n      try {\n        xhr.setRequestHeader(\"Accept\", \"*/*\");\n      } catch (e) {}\n\n      // ie6 check\n      if (\"withCredentials\" in xhr) {\n        xhr.withCredentials = this.opts.withCredentials;\n      }\n\n      if (this.opts.requestTimeout) {\n        xhr.timeout = this.opts.requestTimeout;\n      }\n\n      if (this.hasXDR()) {\n        xhr.onload = function() {\n          self.onLoad();\n        };\n        xhr.onerror = function() {\n          self.onError(xhr.responseText);\n        };\n      } else {\n        xhr.onreadystatechange = function() {\n          if (4 !== xhr.readyState) return;\n          if (200 === xhr.status || 1223 === xhr.status) {\n            self.onLoad();\n          } else {\n            // make sure the `error` event handler that's user-set\n            // does not throw in the same tick and gets caught here\n            setTimeout(function() {\n              self.onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n            }, 0);\n          }\n        };\n      }\n\n      debug(\"xhr data %s\", this.data);\n      xhr.send(this.data);\n    } catch (e) {\n      // Need to defer since .create() is called directly from the constructor\n      // and thus the 'error' event can only be only bound *after* this exception\n      // occurs.  Therefore, also, we cannot throw here at all.\n      setTimeout(function() {\n        self.onError(e);\n      }, 0);\n      return;\n    }\n\n    if (typeof document !== \"undefined\") {\n      this.index = Request.requestsCount++;\n      Request.requests[this.index] = this;\n    }\n  }\n\n  /**\n   * Called upon successful response.\n   *\n   * @api private\n   */\n  onSuccess() {\n    this.emit(\"success\");\n    this.cleanup();\n  }\n\n  /**\n   * Called if we have data.\n   *\n   * @api private\n   */\n  onData(data) {\n    this.emit(\"data\", data);\n    this.onSuccess();\n  }\n\n  /**\n   * Called upon error.\n   *\n   * @api private\n   */\n  onError(err) {\n    this.emit(\"error\", err);\n    this.cleanup(true);\n  }\n\n  /**\n   * Cleans up house.\n   *\n   * @api private\n   */\n  cleanup(fromError) {\n    if (\"undefined\" === typeof this.xhr || null === this.xhr) {\n      return;\n    }\n    // xmlhttprequest\n    if (this.hasXDR()) {\n      this.xhr.onload = this.xhr.onerror = empty;\n    } else {\n      this.xhr.onreadystatechange = empty;\n    }\n\n    if (fromError) {\n      try {\n        this.xhr.abort();\n      } catch (e) {}\n    }\n\n    if (typeof document !== \"undefined\") {\n      delete Request.requests[this.index];\n    }\n\n    this.xhr = null;\n  }\n\n  /**\n   * Called upon load.\n   *\n   * @api private\n   */\n  onLoad() {\n    const data = this.xhr.responseText;\n    if (data !== null) {\n      this.onData(data);\n    }\n  }\n\n  /**\n   * Check if it has XDomainRequest.\n   *\n   * @api private\n   */\n  hasXDR() {\n    return typeof XDomainRequest !== \"undefined\" && !this.xs && this.enablesXDR;\n  }\n\n  /**\n   * Aborts the request.\n   *\n   * @api public\n   */\n  abort() {\n    this.cleanup();\n  }\n}\n\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\n\nRequest.requestsCount = 0;\nRequest.requests = {};\n\nif (typeof document !== \"undefined\") {\n  if (typeof attachEvent === \"function\") {\n    attachEvent(\"onunload\", unloadHandler);\n  } else if (typeof addEventListener === \"function\") {\n    const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n    addEventListener(terminationEvent, unloadHandler, false);\n  }\n}\n\nfunction unloadHandler() {\n  for (let i in Request.requests) {\n    if (Request.requests.hasOwnProperty(i)) {\n      Request.requests[i].abort();\n    }\n  }\n}\n\nmodule.exports = XHR;\nmodule.exports.Request = Request;\n", "const Transport = require(\"../transport\");\nconst parseqs = require(\"parseqs\");\nconst parser = require(\"engine.io-parser\");\nconst yeast = require(\"yeast\");\n\nconst debug = require(\"debug\")(\"engine.io-client:polling\");\n\nclass Polling extends Transport {\n  /**\n   * Transport name.\n   */\n  get name() {\n    return \"polling\";\n  }\n\n  /**\n   * Opens the socket (triggers polling). We write a PING message to determine\n   * when the transport is open.\n   *\n   * @api private\n   */\n  doOpen() {\n    this.poll();\n  }\n\n  /**\n   * Pauses polling.\n   *\n   * @param {Function} callback upon buffers are flushed and transport is paused\n   * @api private\n   */\n  pause(onPause) {\n    const self = this;\n\n    this.readyState = \"pausing\";\n\n    function pause() {\n      debug(\"paused\");\n      self.readyState = \"paused\";\n      onPause();\n    }\n\n    if (this.polling || !this.writable) {\n      let total = 0;\n\n      if (this.polling) {\n        debug(\"we are currently polling - waiting to pause\");\n        total++;\n        this.once(\"pollComplete\", function() {\n          debug(\"pre-pause polling complete\");\n          --total || pause();\n        });\n      }\n\n      if (!this.writable) {\n        debug(\"we are currently writing - waiting to pause\");\n        total++;\n        this.once(\"drain\", function() {\n          debug(\"pre-pause writing complete\");\n          --total || pause();\n        });\n      }\n    } else {\n      pause();\n    }\n  }\n\n  /**\n   * Starts polling cycle.\n   *\n   * @api public\n   */\n  poll() {\n    debug(\"polling\");\n    this.polling = true;\n    this.doPoll();\n    this.emit(\"poll\");\n  }\n\n  /**\n   * Overloads onData to detect payloads.\n   *\n   * @api private\n   */\n  onData(data) {\n    const self = this;\n    debug(\"polling got data %s\", data);\n    const callback = function(packet, index, total) {\n      // if its the first message we consider the transport open\n      if (\"opening\" === self.readyState && packet.type === \"open\") {\n        self.onOpen();\n      }\n\n      // if its a close packet, we close the ongoing requests\n      if (\"close\" === packet.type) {\n        self.onClose();\n        return false;\n      }\n\n      // otherwise bypass onData and handle the message\n      self.onPacket(packet);\n    };\n\n    // decode payload\n    parser.decodePayload(data, this.socket.binaryType).forEach(callback);\n\n    // if an event did not trigger closing\n    if (\"closed\" !== this.readyState) {\n      // if we got data we're not polling\n      this.polling = false;\n      this.emit(\"pollComplete\");\n\n      if (\"open\" === this.readyState) {\n        this.poll();\n      } else {\n        debug('ignoring poll - transport state \"%s\"', this.readyState);\n      }\n    }\n  }\n\n  /**\n   * For polling, send a close packet.\n   *\n   * @api private\n   */\n  doClose() {\n    const self = this;\n\n    function close() {\n      debug(\"writing close packet\");\n      self.write([{ type: \"close\" }]);\n    }\n\n    if (\"open\" === this.readyState) {\n      debug(\"transport open - closing\");\n      close();\n    } else {\n      // in case we're trying to close while\n      // handshaking is in progress (GH-164)\n      debug(\"transport not open - deferring close\");\n      this.once(\"open\", close);\n    }\n  }\n\n  /**\n   * Writes a packets payload.\n   *\n   * @param {Array} data packets\n   * @param {Function} drain callback\n   * @api private\n   */\n  write(packets) {\n    this.writable = false;\n\n    parser.encodePayload(packets, data => {\n      this.doWrite(data, () => {\n        this.writable = true;\n        this.emit(\"drain\");\n      });\n    });\n  }\n\n  /**\n   * Generates uri for connection.\n   *\n   * @api private\n   */\n  uri() {\n    let query = this.query || {};\n    const schema = this.opts.secure ? \"https\" : \"http\";\n    let port = \"\";\n\n    // cache busting is forced\n    if (false !== this.opts.timestampRequests) {\n      query[this.opts.timestampParam] = yeast();\n    }\n\n    if (!this.supportsBinary && !query.sid) {\n      query.b64 = 1;\n    }\n\n    query = parseqs.encode(query);\n\n    // avoid port if default for schema\n    if (\n      this.opts.port &&\n      ((\"https\" === schema && Number(this.opts.port) !== 443) ||\n        (\"http\" === schema && Number(this.opts.port) !== 80))\n    ) {\n      port = \":\" + this.opts.port;\n    }\n\n    // prepend ? to query\n    if (query.length) {\n      query = \"?\" + query;\n    }\n\n    const ipv6 = this.opts.hostname.indexOf(\":\") !== -1;\n    return (\n      schema +\n      \"://\" +\n      (ipv6 ? \"[\" + this.opts.hostname + \"]\" : this.opts.hostname) +\n      port +\n      this.opts.path +\n      query\n    );\n  }\n}\n\nmodule.exports = Polling;\n", "const globalThis = require(\"../globalThis\");\n\nmodule.exports = {\n  WebSocket: globalThis.WebSocket || globalThis.MozWebSocket,\n  usingBrowserWebSocket: true,\n  defaultBinaryType: \"arraybuffer\"\n};\n", "const Transport = require(\"../transport\");\nconst parser = require(\"engine.io-parser\");\nconst parseqs = require(\"parseqs\");\nconst yeast = require(\"yeast\");\nconst { pick } = require(\"../util\");\nconst {\n  WebSocket,\n  usingBrowserWebSocket,\n  defaultBinaryType\n} = require(\"./websocket-constructor\");\n\nconst debug = require(\"debug\")(\"engine.io-client:websocket\");\n\n// detect ReactNative environment\nconst isReactNative =\n  typeof navigator !== \"undefined\" &&\n  typeof navigator.product === \"string\" &&\n  navigator.product.toLowerCase() === \"reactnative\";\n\nclass WS extends Transport {\n  /**\n   * WebSocket transport constructor.\n   *\n   * @api {Object} connection options\n   * @api public\n   */\n  constructor(opts) {\n    super(opts);\n\n    this.supportsBinary = !opts.forceBase64;\n  }\n\n  /**\n   * Transport name.\n   *\n   * @api public\n   */\n  get name() {\n    return \"websocket\";\n  }\n\n  /**\n   * Opens socket.\n   *\n   * @api private\n   */\n  doOpen() {\n    if (!this.check()) {\n      // let probe timeout\n      return;\n    }\n\n    const uri = this.uri();\n    const protocols = this.opts.protocols;\n\n    // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n    const opts = isReactNative\n      ? {}\n      : pick(\n          this.opts,\n          \"agent\",\n          \"perMessageDeflate\",\n          \"pfx\",\n          \"key\",\n          \"passphrase\",\n          \"cert\",\n          \"ca\",\n          \"ciphers\",\n          \"rejectUnauthorized\",\n          \"localAddress\",\n          \"protocolVersion\",\n          \"origin\",\n          \"maxPayload\",\n          \"family\",\n          \"checkServerIdentity\"\n        );\n\n    if (this.opts.extraHeaders) {\n      opts.headers = this.opts.extraHeaders;\n    }\n\n    try {\n      this.ws =\n        usingBrowserWebSocket && !isReactNative\n          ? protocols\n            ? new WebSocket(uri, protocols)\n            : new WebSocket(uri)\n          : new WebSocket(uri, protocols, opts);\n    } catch (err) {\n      return this.emit(\"error\", err);\n    }\n\n    this.ws.binaryType = this.socket.binaryType || defaultBinaryType;\n\n    this.addEventListeners();\n  }\n\n  /**\n   * Adds event listeners to the socket\n   *\n   * @api private\n   */\n  addEventListeners() {\n    const self = this;\n\n    this.ws.onopen = function() {\n      self.onOpen();\n    };\n    this.ws.onclose = function() {\n      self.onClose();\n    };\n    this.ws.onmessage = function(ev) {\n      self.onData(ev.data);\n    };\n    this.ws.onerror = function(e) {\n      self.onError(\"websocket error\", e);\n    };\n  }\n\n  /**\n   * Writes data to socket.\n   *\n   * @param {Array} array of packets.\n   * @api private\n   */\n  write(packets) {\n    const self = this;\n    this.writable = false;\n\n    // encodePacket efficient as it uses WS framing\n    // no need for encodePayload\n    let total = packets.length;\n    let i = 0;\n    const l = total;\n    for (; i < l; i++) {\n      (function(packet) {\n        parser.encodePacket(packet, self.supportsBinary, function(data) {\n          // always create a new object (GH-437)\n          const opts = {};\n          if (!usingBrowserWebSocket) {\n            if (packet.options) {\n              opts.compress = packet.options.compress;\n            }\n\n            if (self.opts.perMessageDeflate) {\n              const len =\n                \"string\" === typeof data\n                  ? Buffer.byteLength(data)\n                  : data.length;\n              if (len < self.opts.perMessageDeflate.threshold) {\n                opts.compress = false;\n              }\n            }\n          }\n\n          // Sometimes the websocket has already been closed but the browser didn't\n          // have a chance of informing us about it yet, in that case send will\n          // throw an error\n          try {\n            if (usingBrowserWebSocket) {\n              // TypeError is thrown when passing the second argument on Safari\n              self.ws.send(data);\n            } else {\n              self.ws.send(data, opts);\n            }\n          } catch (e) {\n            debug(\"websocket closed before onclose event\");\n          }\n\n          --total || done();\n        });\n      })(packets[i]);\n    }\n\n    function done() {\n      self.emit(\"flush\");\n\n      // fake drain\n      // defer to next tick to allow Socket to clear writeBuffer\n      setTimeout(function() {\n        self.writable = true;\n        self.emit(\"drain\");\n      }, 0);\n    }\n  }\n\n  /**\n   * Called upon close\n   *\n   * @api private\n   */\n  onClose() {\n    Transport.prototype.onClose.call(this);\n  }\n\n  /**\n   * Closes socket.\n   *\n   * @api private\n   */\n  doClose() {\n    if (typeof this.ws !== \"undefined\") {\n      this.ws.close();\n      this.ws = null;\n    }\n  }\n\n  /**\n   * Generates uri for connection.\n   *\n   * @api private\n   */\n  uri() {\n    let query = this.query || {};\n    const schema = this.opts.secure ? \"wss\" : \"ws\";\n    let port = \"\";\n\n    // avoid port if default for schema\n    if (\n      this.opts.port &&\n      ((\"wss\" === schema && Number(this.opts.port) !== 443) ||\n        (\"ws\" === schema && Number(this.opts.port) !== 80))\n    ) {\n      port = \":\" + this.opts.port;\n    }\n\n    // append timestamp to URI\n    if (this.opts.timestampRequests) {\n      query[this.opts.timestampParam] = yeast();\n    }\n\n    // communicate binary support capabilities\n    if (!this.supportsBinary) {\n      query.b64 = 1;\n    }\n\n    query = parseqs.encode(query);\n\n    // prepend ? to query\n    if (query.length) {\n      query = \"?\" + query;\n    }\n\n    const ipv6 = this.opts.hostname.indexOf(\":\") !== -1;\n    return (\n      schema +\n      \"://\" +\n      (ipv6 ? \"[\" + this.opts.hostname + \"]\" : this.opts.hostname) +\n      port +\n      this.opts.path +\n      query\n    );\n  }\n\n  /**\n   * Feature detection for WebSocket.\n   *\n   * @return {Boolean} whether this transport is available.\n   * @api public\n   */\n  check() {\n    return (\n      !!WebSocket &&\n      !(\"__initialize\" in WebSocket && this.name === WS.prototype.name)\n    );\n  }\n}\n\nmodule.exports = WS;\n", "module.exports.pick = (obj, ...attr) => {\n  return attr.reduce((acc, k) => {\n    if (obj.hasOwnProperty(k)) {\n      acc[k] = obj[k];\n    }\n    return acc;\n  }, {});\n};\n", "// browser shim for xmlhttprequest module\n\nconst hasCORS = require(\"has-cors\");\nconst globalThis = require(\"./globalThis\");\n\nmodule.exports = function(opts) {\n  const xdomain = opts.xdomain;\n\n  // scheme must be same when usign XDomainRequest\n  // http://blogs.msdn.com/b/ieinternals/archive/2010/05/13/xdomainrequest-restrictions-limitations-and-workarounds.aspx\n  const xscheme = opts.xscheme;\n\n  // XDomainRequest has a flow of not sending cookie, therefore it should be disabled as a default.\n  // https://github.com/Automattic/engine.io-client/pull/217\n  const enablesXDR = opts.enablesXDR;\n\n  // XMLHttpRequest can be disabled on IE\n  try {\n    if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n      return new XMLHttpRequest();\n    }\n  } catch (e) {}\n\n  // Use XDomainRequest for IE8 if enablesXDR is true\n  // because loading bar keeps flashing when using jsonp-polling\n  // https://github.com/yujiosaka/socke.io-ie8-loading-example\n  try {\n    if (\"undefined\" !== typeof XDomainRequest && !xscheme && enablesXDR) {\n      return new XDomainRequest();\n    }\n  } catch (e) {}\n\n  if (!xdomain) {\n    try {\n      return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\n        \"Microsoft.XMLHTTP\"\n      );\n    } catch (e) {}\n  }\n};\n", "const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\n\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach(key => {\n  PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\n\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\n\nmodule.exports = {\n  PACKET_TYPES,\n  PACKET_TYPES_REVERSE,\n  ERROR_PACKET\n};\n", "const { PACKET_TYPES_REVERSE, ERROR_PACKET } = require(\"./commons\");\n\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n\nlet base64decoder;\nif (withNativeArrayBuffer) {\n  base64decoder = require(\"base64-arraybuffer\");\n}\n\nconst decodePacket = (encodedPacket, binaryType) => {\n  if (typeof encodedPacket !== \"string\") {\n    return {\n      type: \"message\",\n      data: mapBinary(encodedPacket, binaryType)\n    };\n  }\n  const type = encodedPacket.charAt(0);\n  if (type === \"b\") {\n    return {\n      type: \"message\",\n      data: decodeBase64Packet(encodedPacket.substring(1), binaryType)\n    };\n  }\n  const packetType = PACKET_TYPES_REVERSE[type];\n  if (!packetType) {\n    return ERROR_PACKET;\n  }\n  return encodedPacket.length > 1\n    ? {\n        type: PACKET_TYPES_REVERSE[type],\n        data: encodedPacket.substring(1)\n      }\n    : {\n        type: PACKET_TYPES_REVERSE[type]\n      };\n};\n\nconst decodeBase64Packet = (data, binaryType) => {\n  if (base64decoder) {\n    const decoded = base64decoder.decode(data);\n    return mapBinary(decoded, binaryType);\n  } else {\n    return { base64: true, data }; // fallback for old browsers\n  }\n};\n\nconst mapBinary = (data, binaryType) => {\n  switch (binaryType) {\n    case \"blob\":\n      return data instanceof ArrayBuffer ? new Blob([data]) : data;\n    case \"arraybuffer\":\n    default:\n      return data; // assuming the data is already an ArrayBuffer\n  }\n};\n\nmodule.exports = decodePacket;\n", "const { PACKET_TYPES } = require(\"./commons\");\n\nconst withNativeBlob =\n  typeof Blob === \"function\" ||\n  (typeof Blob !== \"undefined\" &&\n    Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = obj => {\n  return typeof ArrayBuffer.isView === \"function\"\n    ? ArrayBuffer.isView(obj)\n    : obj && obj.buffer instanceof ArrayBuffer;\n};\n\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n  if (withNativeBlob && data instanceof Blob) {\n    if (supportsBinary) {\n      return callback(data);\n    } else {\n      return encodeBlobAsBase64(data, callback);\n    }\n  } else if (\n    withNativeArrayBuffer &&\n    (data instanceof ArrayBuffer || isView(data))\n  ) {\n    if (supportsBinary) {\n      return callback(data instanceof ArrayBuffer ? data : data.buffer);\n    } else {\n      return encodeBlobAsBase64(new Blob([data]), callback);\n    }\n  }\n  // plain string\n  return callback(PACKET_TYPES[type] + (data || \"\"));\n};\n\nconst encodeBlobAsBase64 = (data, callback) => {\n  const fileReader = new FileReader();\n  fileReader.onload = function() {\n    const content = fileReader.result.split(\",\")[1];\n    callback(\"b\" + content);\n  };\n  return fileReader.readAsDataURL(data);\n};\n\nmodule.exports = encodePacket;\n", "const encodePacket = require(\"./encodePacket\");\nconst decodePacket = require(\"./decodePacket\");\n\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\n\nconst encodePayload = (packets, callback) => {\n  // some packets may be added to the array while encoding, so the initial length must be saved\n  const length = packets.length;\n  const encodedPackets = new Array(length);\n  let count = 0;\n\n  packets.forEach((packet, i) => {\n    // force base64 encoding for binary packets\n    encodePacket(packet, false, encodedPacket => {\n      encodedPackets[i] = encodedPacket;\n      if (++count === length) {\n        callback(encodedPackets.join(SEPARATOR));\n      }\n    });\n  });\n};\n\nconst decodePayload = (encodedPayload, binaryType) => {\n  const encodedPackets = encodedPayload.split(SEPARATOR);\n  const packets = [];\n  for (let i = 0; i < encodedPackets.length; i++) {\n    const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n    packets.push(decodedPacket);\n    if (decodedPacket.type === \"error\") {\n      break;\n    }\n  }\n  return packets;\n};\n\nmodule.exports = {\n  protocol: 4,\n  encodePacket,\n  encodePayload,\n  decodePacket,\n  decodePayload\n};\n", "/*\n * base64-arraybuffer\n * https://github.com/niklasvh/base64-arraybuffer\n *\n * Copyright (c) 2012 <PERSON><PERSON>\n * Licensed under the MIT license.\n */\n(function(chars){\n  \"use strict\";\n\n  exports.encode = function(arraybuffer) {\n    var bytes = new Uint8Array(arraybuffer),\n    i, len = bytes.length, base64 = \"\";\n\n    for (i = 0; i < len; i+=3) {\n      base64 += chars[bytes[i] >> 2];\n      base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n      base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n      base64 += chars[bytes[i + 2] & 63];\n    }\n\n    if ((len % 3) === 2) {\n      base64 = base64.substring(0, base64.length - 1) + \"=\";\n    } else if (len % 3 === 1) {\n      base64 = base64.substring(0, base64.length - 2) + \"==\";\n    }\n\n    return base64;\n  };\n\n  exports.decode =  function(base64) {\n    var bufferLength = base64.length * 0.75,\n    len = base64.length, i, p = 0,\n    encoded1, encoded2, encoded3, encoded4;\n\n    if (base64[base64.length - 1] === \"=\") {\n      bufferLength--;\n      if (base64[base64.length - 2] === \"=\") {\n        bufferLength--;\n      }\n    }\n\n    var arraybuffer = new ArrayBuffer(bufferLength),\n    bytes = new Uint8Array(arraybuffer);\n\n    for (i = 0; i < len; i+=4) {\n      encoded1 = chars.indexOf(base64[i]);\n      encoded2 = chars.indexOf(base64[i+1]);\n      encoded3 = chars.indexOf(base64[i+2]);\n      encoded4 = chars.indexOf(base64[i+3]);\n\n      bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n      bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n      bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n\n    return arraybuffer;\n  };\n})(\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\");\n", "\n/**\n * Module exports.\n *\n * Logic borrowed from Modernizr:\n *\n *   - https://github.com/Modernizr/Modernizr/blob/master/feature-detects/cors.js\n */\n\ntry {\n  module.exports = typeof XMLHttpRequest !== 'undefined' &&\n    'withCredentials' in new XMLHttpRequest();\n} catch (err) {\n  // if XMLHttp support is disabled in IE then it will throw\n  // when trying to create\n  module.exports = false;\n}\n", "/**\n * Helpers.\n */\n\nvar s = 1000;\nvar m = s * 60;\nvar h = m * 60;\nvar d = h * 24;\nvar w = d * 7;\nvar y = d * 365.25;\n\n/**\n * Parse or format the given `val`.\n *\n * Options:\n *\n *  - `long` verbose formatting [false]\n *\n * @param {String|Number} val\n * @param {Object} [options]\n * @throws {Error} throw an error if val is not a non-empty string or a number\n * @return {String|Number}\n * @api public\n */\n\nmodule.exports = function(val, options) {\n  options = options || {};\n  var type = typeof val;\n  if (type === 'string' && val.length > 0) {\n    return parse(val);\n  } else if (type === 'number' && isFinite(val)) {\n    return options.long ? fmtLong(val) : fmtShort(val);\n  }\n  throw new Error(\n    'val is not a non-empty string or a valid number. val=' +\n      JSON.stringify(val)\n  );\n};\n\n/**\n * Parse the given `str` and return milliseconds.\n *\n * @param {String} str\n * @return {Number}\n * @api private\n */\n\nfunction parse(str) {\n  str = String(str);\n  if (str.length > 100) {\n    return;\n  }\n  var match = /^(-?(?:\\d+)?\\.?\\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(\n    str\n  );\n  if (!match) {\n    return;\n  }\n  var n = parseFloat(match[1]);\n  var type = (match[2] || 'ms').toLowerCase();\n  switch (type) {\n    case 'years':\n    case 'year':\n    case 'yrs':\n    case 'yr':\n    case 'y':\n      return n * y;\n    case 'weeks':\n    case 'week':\n    case 'w':\n      return n * w;\n    case 'days':\n    case 'day':\n    case 'd':\n      return n * d;\n    case 'hours':\n    case 'hour':\n    case 'hrs':\n    case 'hr':\n    case 'h':\n      return n * h;\n    case 'minutes':\n    case 'minute':\n    case 'mins':\n    case 'min':\n    case 'm':\n      return n * m;\n    case 'seconds':\n    case 'second':\n    case 'secs':\n    case 'sec':\n    case 's':\n      return n * s;\n    case 'milliseconds':\n    case 'millisecond':\n    case 'msecs':\n    case 'msec':\n    case 'ms':\n      return n;\n    default:\n      return undefined;\n  }\n}\n\n/**\n * Short format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtShort(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return Math.round(ms / d) + 'd';\n  }\n  if (msAbs >= h) {\n    return Math.round(ms / h) + 'h';\n  }\n  if (msAbs >= m) {\n    return Math.round(ms / m) + 'm';\n  }\n  if (msAbs >= s) {\n    return Math.round(ms / s) + 's';\n  }\n  return ms + 'ms';\n}\n\n/**\n * Long format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtLong(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return plural(ms, msAbs, d, 'day');\n  }\n  if (msAbs >= h) {\n    return plural(ms, msAbs, h, 'hour');\n  }\n  if (msAbs >= m) {\n    return plural(ms, msAbs, m, 'minute');\n  }\n  if (msAbs >= s) {\n    return plural(ms, msAbs, s, 'second');\n  }\n  return ms + ' ms';\n}\n\n/**\n * Pluralization helper.\n */\n\nfunction plural(ms, msAbs, n, name) {\n  var isPlural = msAbs >= n * 1.5;\n  return Math.round(ms / n) + ' ' + name + (isPlural ? 's' : '');\n}\n", "/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\n\nexports.encode = function (obj) {\n  var str = '';\n\n  for (var i in obj) {\n    if (obj.hasOwnProperty(i)) {\n      if (str.length) str += '&';\n      str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n    }\n  }\n\n  return str;\n};\n\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\n\nexports.decode = function(qs){\n  var qry = {};\n  var pairs = qs.split('&');\n  for (var i = 0, l = pairs.length; i < l; i++) {\n    var pair = pairs[i].split('=');\n    qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n  }\n  return qry;\n};\n", "/**\n * Parses an URI\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\n\nvar re = /^(?:(?![^:@]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\n\nvar parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\n\nmodule.exports = function parseuri(str) {\n    var src = str,\n        b = str.indexOf('['),\n        e = str.indexOf(']');\n\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n\n    var m = re.exec(str || ''),\n        uri = {},\n        i = 14;\n\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n\n    return uri;\n};\n\nfunction pathNames(obj, path) {\n    var regx = /\\/{2,9}/g,\n        names = path.replace(regx, \"/\").split(\"/\");\n\n    if (path.substr(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.substr(path.length - 1, 1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n\n    return names;\n}\n\nfunction queryKey(uri, query) {\n    var data = {};\n\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n\n    return data;\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.reconstructPacket = exports.deconstructPacket = void 0;\nconst is_binary_1 = require(\"./is-binary\");\n/**\n * Replaces every Buffer | ArrayBuffer | Blob | File in packet with a numbered placeholder.\n *\n * @param {Object} packet - socket.io event packet\n * @return {Object} with deconstructed packet and list of buffers\n * @public\n */\nfunction deconstructPacket(packet) {\n    const buffers = [];\n    const packetData = packet.data;\n    const pack = packet;\n    pack.data = _deconstructPacket(packetData, buffers);\n    pack.attachments = buffers.length; // number of binary 'attachments'\n    return { packet: pack, buffers: buffers };\n}\nexports.deconstructPacket = deconstructPacket;\nfunction _deconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (is_binary_1.isBinary(data)) {\n        const placeholder = { _placeholder: true, num: buffers.length };\n        buffers.push(data);\n        return placeholder;\n    }\n    else if (Array.isArray(data)) {\n        const newData = new Array(data.length);\n        for (let i = 0; i < data.length; i++) {\n            newData[i] = _deconstructPacket(data[i], buffers);\n        }\n        return newData;\n    }\n    else if (typeof data === \"object\" && !(data instanceof Date)) {\n        const newData = {};\n        for (const key in data) {\n            if (data.hasOwnProperty(key)) {\n                newData[key] = _deconstructPacket(data[key], buffers);\n            }\n        }\n        return newData;\n    }\n    return data;\n}\n/**\n * Reconstructs a binary packet from its placeholder packet and buffers\n *\n * @param {Object} packet - event packet with placeholders\n * @param {Array} buffers - binary buffers to put in placeholder positions\n * @return {Object} reconstructed packet\n * @public\n */\nfunction reconstructPacket(packet, buffers) {\n    packet.data = _reconstructPacket(packet.data, buffers);\n    packet.attachments = undefined; // no longer useful\n    return packet;\n}\nexports.reconstructPacket = reconstructPacket;\nfunction _reconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (data && data._placeholder) {\n        return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n    }\n    else if (Array.isArray(data)) {\n        for (let i = 0; i < data.length; i++) {\n            data[i] = _reconstructPacket(data[i], buffers);\n        }\n    }\n    else if (typeof data === \"object\") {\n        for (const key in data) {\n            if (data.hasOwnProperty(key)) {\n                data[key] = _reconstructPacket(data[key], buffers);\n            }\n        }\n    }\n    return data;\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Decoder = exports.Encoder = exports.PacketType = exports.protocol = void 0;\nconst Emitter = require(\"component-emitter\");\nconst binary_1 = require(\"./binary\");\nconst is_binary_1 = require(\"./is-binary\");\nconst debug = require(\"debug\")(\"socket.io-parser\");\n/**\n * Protocol version.\n *\n * @public\n */\nexports.protocol = 5;\nvar PacketType;\n(function (PacketType) {\n    PacketType[PacketType[\"CONNECT\"] = 0] = \"CONNECT\";\n    PacketType[PacketType[\"DISCONNECT\"] = 1] = \"DISCONNECT\";\n    PacketType[PacketType[\"EVENT\"] = 2] = \"EVENT\";\n    PacketType[PacketType[\"ACK\"] = 3] = \"ACK\";\n    PacketType[PacketType[\"CONNECT_ERROR\"] = 4] = \"CONNECT_ERROR\";\n    PacketType[PacketType[\"BINARY_EVENT\"] = 5] = \"BINARY_EVENT\";\n    PacketType[PacketType[\"BINARY_ACK\"] = 6] = \"BINARY_ACK\";\n})(PacketType = exports.PacketType || (exports.PacketType = {}));\n/**\n * A socket.io Encoder instance\n */\nclass Encoder {\n    /**\n     * Encode a packet as a single string if non-binary, or as a\n     * buffer sequence, depending on packet type.\n     *\n     * @param {Object} obj - packet object\n     */\n    encode(obj) {\n        debug(\"encoding packet %j\", obj);\n        if (obj.type === PacketType.EVENT || obj.type === PacketType.ACK) {\n            if (is_binary_1.hasBinary(obj)) {\n                obj.type =\n                    obj.type === PacketType.EVENT\n                        ? PacketType.BINARY_EVENT\n                        : PacketType.BINARY_ACK;\n                return this.encodeAsBinary(obj);\n            }\n        }\n        return [this.encodeAsString(obj)];\n    }\n    /**\n     * Encode packet as string.\n     */\n    encodeAsString(obj) {\n        // first is type\n        let str = \"\" + obj.type;\n        // attachments if we have them\n        if (obj.type === PacketType.BINARY_EVENT ||\n            obj.type === PacketType.BINARY_ACK) {\n            str += obj.attachments + \"-\";\n        }\n        // if we have a namespace other than `/`\n        // we append it followed by a comma `,`\n        if (obj.nsp && \"/\" !== obj.nsp) {\n            str += obj.nsp + \",\";\n        }\n        // immediately followed by the id\n        if (null != obj.id) {\n            str += obj.id;\n        }\n        // json data\n        if (null != obj.data) {\n            str += JSON.stringify(obj.data);\n        }\n        debug(\"encoded %j as %s\", obj, str);\n        return str;\n    }\n    /**\n     * Encode packet as 'buffer sequence' by removing blobs, and\n     * deconstructing packet into object with placeholders and\n     * a list of buffers.\n     */\n    encodeAsBinary(obj) {\n        const deconstruction = binary_1.deconstructPacket(obj);\n        const pack = this.encodeAsString(deconstruction.packet);\n        const buffers = deconstruction.buffers;\n        buffers.unshift(pack); // add packet info to beginning of data list\n        return buffers; // write all the buffers\n    }\n}\nexports.Encoder = Encoder;\n/**\n * A socket.io Decoder instance\n *\n * @return {Object} decoder\n */\nclass Decoder extends Emitter {\n    constructor() {\n        super();\n    }\n    /**\n     * Decodes an encoded packet string into packet JSON.\n     *\n     * @param {String} obj - encoded packet\n     */\n    add(obj) {\n        let packet;\n        if (typeof obj === \"string\") {\n            packet = this.decodeString(obj);\n            if (packet.type === PacketType.BINARY_EVENT ||\n                packet.type === PacketType.BINARY_ACK) {\n                // binary packet's json\n                this.reconstructor = new BinaryReconstructor(packet);\n                // no attachments, labeled binary but no binary data to follow\n                if (packet.attachments === 0) {\n                    super.emit(\"decoded\", packet);\n                }\n            }\n            else {\n                // non-binary full packet\n                super.emit(\"decoded\", packet);\n            }\n        }\n        else if (is_binary_1.isBinary(obj) || obj.base64) {\n            // raw binary data\n            if (!this.reconstructor) {\n                throw new Error(\"got binary data when not reconstructing a packet\");\n            }\n            else {\n                packet = this.reconstructor.takeBinaryData(obj);\n                if (packet) {\n                    // received final buffer\n                    this.reconstructor = null;\n                    super.emit(\"decoded\", packet);\n                }\n            }\n        }\n        else {\n            throw new Error(\"Unknown type: \" + obj);\n        }\n    }\n    /**\n     * Decode a packet String (JSON data)\n     *\n     * @param {String} str\n     * @return {Object} packet\n     */\n    decodeString(str) {\n        let i = 0;\n        // look up type\n        const p = {\n            type: Number(str.charAt(0)),\n        };\n        if (PacketType[p.type] === undefined) {\n            throw new Error(\"unknown packet type \" + p.type);\n        }\n        // look up attachments if type binary\n        if (p.type === PacketType.BINARY_EVENT ||\n            p.type === PacketType.BINARY_ACK) {\n            const start = i + 1;\n            while (str.charAt(++i) !== \"-\" && i != str.length) { }\n            const buf = str.substring(start, i);\n            if (buf != Number(buf) || str.charAt(i) !== \"-\") {\n                throw new Error(\"Illegal attachments\");\n            }\n            p.attachments = Number(buf);\n        }\n        // look up namespace (if any)\n        if (\"/\" === str.charAt(i + 1)) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (\",\" === c)\n                    break;\n                if (i === str.length)\n                    break;\n            }\n            p.nsp = str.substring(start, i);\n        }\n        else {\n            p.nsp = \"/\";\n        }\n        // look up id\n        const next = str.charAt(i + 1);\n        if (\"\" !== next && Number(next) == next) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (null == c || Number(c) != c) {\n                    --i;\n                    break;\n                }\n                if (i === str.length)\n                    break;\n            }\n            p.id = Number(str.substring(start, i + 1));\n        }\n        // look up json data\n        if (str.charAt(++i)) {\n            const payload = tryParse(str.substr(i));\n            if (Decoder.isPayloadValid(p.type, payload)) {\n                p.data = payload;\n            }\n            else {\n                throw new Error(\"invalid payload\");\n            }\n        }\n        debug(\"decoded %s as %j\", str, p);\n        return p;\n    }\n    static isPayloadValid(type, payload) {\n        switch (type) {\n            case PacketType.CONNECT:\n                return typeof payload === \"object\";\n            case PacketType.DISCONNECT:\n                return payload === undefined;\n            case PacketType.CONNECT_ERROR:\n                return typeof payload === \"string\" || typeof payload === \"object\";\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                return Array.isArray(payload) && payload.length > 0;\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                return Array.isArray(payload);\n        }\n    }\n    /**\n     * Deallocates a parser's resources\n     */\n    destroy() {\n        if (this.reconstructor) {\n            this.reconstructor.finishedReconstruction();\n        }\n    }\n}\nexports.Decoder = Decoder;\nfunction tryParse(str) {\n    try {\n        return JSON.parse(str);\n    }\n    catch (e) {\n        return false;\n    }\n}\n/**\n * A manager of a binary event's 'buffer sequence'. Should\n * be constructed whenever a packet of type BINARY_EVENT is\n * decoded.\n *\n * @param {Object} packet\n * @return {BinaryReconstructor} initialized reconstructor\n */\nclass BinaryReconstructor {\n    constructor(packet) {\n        this.packet = packet;\n        this.buffers = [];\n        this.reconPack = packet;\n    }\n    /**\n     * Method to be called when binary data received from connection\n     * after a BINARY_EVENT packet.\n     *\n     * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n     * @return {null | Object} returns null if more binary data is expected or\n     *   a reconstructed packet object if all buffers have been received.\n     */\n    takeBinaryData(binData) {\n        this.buffers.push(binData);\n        if (this.buffers.length === this.reconPack.attachments) {\n            // done with buffer list\n            const packet = binary_1.reconstructPacket(this.reconPack, this.buffers);\n            this.finishedReconstruction();\n            return packet;\n        }\n        return null;\n    }\n    /**\n     * Cleans up binary packet reconstruction variables.\n     */\n    finishedReconstruction() {\n        this.reconPack = null;\n        this.buffers = [];\n    }\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.hasBinary = exports.isBinary = void 0;\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj.buffer instanceof ArrayBuffer;\n};\nconst toString = Object.prototype.toString;\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeFile = typeof File === \"function\" ||\n    (typeof File !== \"undefined\" &&\n        toString.call(File) === \"[object FileConstructor]\");\n/**\n * Returns true if obj is a Buffer, an ArrayBuffer, a Blob or a File.\n *\n * @private\n */\nfunction isBinary(obj) {\n    return ((withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj))) ||\n        (withNativeBlob && obj instanceof Blob) ||\n        (withNativeFile && obj instanceof File));\n}\nexports.isBinary = isBinary;\nfunction hasBinary(obj, toJSON) {\n    if (!obj || typeof obj !== \"object\") {\n        return false;\n    }\n    if (Array.isArray(obj)) {\n        for (let i = 0, l = obj.length; i < l; i++) {\n            if (hasBinary(obj[i])) {\n                return true;\n            }\n        }\n        return false;\n    }\n    if (isBinary(obj)) {\n        return true;\n    }\n    if (obj.toJSON &&\n        typeof obj.toJSON === \"function\" &&\n        arguments.length === 1) {\n        return hasBinary(obj.toJSON(), true);\n    }\n    for (const key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n            return true;\n        }\n    }\n    return false;\n}\nexports.hasBinary = hasBinary;\n", "'use strict';\n\nvar alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split('')\n  , length = 64\n  , map = {}\n  , seed = 0\n  , i = 0\n  , prev;\n\n/**\n * Return a string representing the specified number.\n *\n * @param {Number} num The number to convert.\n * @returns {String} The string representation of the number.\n * @api public\n */\nfunction encode(num) {\n  var encoded = '';\n\n  do {\n    encoded = alphabet[num % length] + encoded;\n    num = Math.floor(num / length);\n  } while (num > 0);\n\n  return encoded;\n}\n\n/**\n * Return the integer value specified by the given string.\n *\n * @param {String} str The string to convert.\n * @returns {Number} The integer value represented by the string.\n * @api public\n */\nfunction decode(str) {\n  var decoded = 0;\n\n  for (i = 0; i < str.length; i++) {\n    decoded = decoded * length + map[str.charAt(i)];\n  }\n\n  return decoded;\n}\n\n/**\n * Yeast: A tiny growing id generator.\n *\n * @returns {String} A unique id.\n * @api public\n */\nfunction yeast() {\n  var now = encode(+new Date());\n\n  if (now !== prev) return seed = 0, prev = now;\n  return now +'.'+ encode(seed++);\n}\n\n//\n// Map each character to its index.\n//\nfor (; i < length; i++) map[alphabet[i]] = i;\n\n//\n// Expose the `yeast`, `encode` and `decode` functions.\n//\nyeast.encode = encode;\nyeast.decode = decode;\nmodule.exports = yeast;\n"], "sourceRoot": ""}