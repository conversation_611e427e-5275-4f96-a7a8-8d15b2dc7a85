{"version": 3, "sources": ["webpack://io/webpack/universalModuleDefinition", "webpack://io/webpack/bootstrap", "webpack://io/./node_modules/component-emitter/index.js", "webpack://io/./node_modules/engine.io-parser/lib/index.js", "webpack://io/./node_modules/engine.io-client/lib/globalThis.browser.js", "webpack://io/./node_modules/engine.io-client/lib/transport.js", "webpack://io/./node_modules/parseqs/index.js", "webpack://io/./node_modules/socket.io-msgpack-parser/index.js", "webpack://io/./node_modules/parseuri/index.js", "webpack://io/./build/manager.js", "webpack://io/./node_modules/engine.io-client/lib/transports/index.js", "webpack://io/./node_modules/engine.io-client/lib/xmlhttprequest.js", "webpack://io/./node_modules/engine.io-client/lib/transports/polling.js", "webpack://io/./node_modules/engine.io-parser/lib/commons.js", "webpack://io/./node_modules/yeast/index.js", "webpack://io/./node_modules/engine.io-client/lib/util.js", "webpack://io/./build/socket.js", "webpack://io/./build/on.js", "webpack://io/./build/index.js", "webpack://io/./build/url.js", "webpack://io/./node_modules/engine.io-client/lib/index.js", "webpack://io/./node_modules/engine.io-client/lib/socket.js", "webpack://io/./node_modules/has-cors/index.js", "webpack://io/./node_modules/engine.io-client/lib/transports/polling-xhr.js", "webpack://io/./node_modules/engine.io-parser/lib/encodePacket.browser.js", "webpack://io/./node_modules/engine.io-parser/lib/decodePacket.browser.js", "webpack://io/./node_modules/engine.io-parser/node_modules/base64-arraybuffer/lib/base64-arraybuffer.js", "webpack://io/./node_modules/engine.io-client/lib/transports/polling-jsonp.js", "webpack://io/./node_modules/engine.io-client/lib/transports/websocket.js", "webpack://io/./node_modules/engine.io-client/lib/transports/websocket-constructor.browser.js", "webpack://io/./node_modules/notepack.io/lib/index.js", "webpack://io/./node_modules/notepack.io/browser/encode.js", "webpack://io/./node_modules/notepack.io/browser/decode.js", "webpack://io/./node_modules/backo2/index.js"], "names": ["root", "factory", "exports", "module", "define", "amd", "this", "installedModules", "__webpack_require__", "moduleId", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "Emitter", "obj", "mixin", "on", "addEventListener", "event", "fn", "_callbacks", "push", "once", "off", "apply", "arguments", "removeListener", "removeAllListeners", "removeEventListener", "length", "cb", "callbacks", "splice", "emit", "args", "Array", "len", "slice", "listeners", "hasListeners", "encodePacket", "require", "decodePacket", "SEPARATOR", "String", "fromCharCode", "protocol", "encodePayload", "packets", "callback", "encodedPackets", "count", "for<PERSON>ach", "packet", "encodedPacket", "join", "decodePayload", "encodedPayload", "binaryType", "split", "decodedPacket", "type", "self", "window", "Function", "parser", "Transport", "opts", "query", "readyState", "socket", "msg", "desc", "err", "Error", "description", "doOpen", "doClose", "onClose", "write", "writable", "data", "onPacket", "encode", "str", "encodeURIComponent", "decode", "qs", "qry", "pairs", "pair", "decodeURIComponent", "msgpack", "PacketType", "CONNECT", "DISCONNECT", "EVENT", "ACK", "CONNECT_ERROR", "isInteger", "Number", "isFinite", "Math", "floor", "isString", "isObject", "toString", "Encoder", "Decoder", "add", "decoded", "checkPacket", "nsp", "undefined", "isArray", "isDataValid", "id", "destroy", "re", "parts", "src", "b", "indexOf", "e", "substring", "replace", "exec", "uri", "source", "host", "authority", "ipv6uri", "pathNames", "path", "names", "substr", "query<PERSON><PERSON>", "$0", "$1", "$2", "Manager", "eio", "socket_1", "on_1", "Backoff", "nsps", "subs", "reconnection", "reconnectionAttempts", "Infinity", "reconnectionDelay", "reconnectionDelayMax", "randomizationFactor", "backoff", "min", "max", "jitter", "timeout", "_readyState", "_parser", "encoder", "decoder", "_autoConnect", "autoConnect", "open", "v", "_reconnection", "_reconnectionAttempts", "_a", "_reconnectionDelay", "setMin", "_randomizationFactor", "setJitter", "_reconnectionDelayMax", "setMax", "_timeout", "_reconnecting", "attempts", "reconnect", "engine", "skipReconnect", "openSubDestroy", "onopen", "errorSub", "cleanup", "maybeReconnectOnOpen", "timer", "setTimeout", "close", "clearTimeout", "onping", "ondata", "onerror", "onclose", "ondecoded", "Socket", "keys", "active", "_close", "options", "subDestroy", "reset", "reason", "delay", "duration", "onreconnect", "attempt", "XMLHttpRequest", "XHR", "JSONP", "websocket", "polling", "xd", "xs", "jsonp", "location", "isSSL", "port", "hostname", "secure", "xdomain", "xscheme", "forceJSONP", "hasCORS", "globalThis", "enablesXDR", "XDomainRequest", "concat", "parseqs", "yeast", "Polling", "poll", "onPause", "pause", "total", "doPoll", "index", "onOpen", "doWrite", "schema", "timestampRequests", "timestampParam", "supportsBinary", "sid", "b64", "PACKET_TYPES", "PACKET_TYPES_REVERSE", "ERROR_PACKET", "prev", "alphabet", "map", "seed", "num", "encoded", "now", "Date", "char<PERSON>t", "pick", "attr", "reduce", "acc", "k", "socket_io_parser_1", "RESERVED_EVENTS", "freeze", "connect", "connect_error", "disconnect", "disconnecting", "newListener", "io", "<PERSON><PERSON><PERSON><PERSON>", "send<PERSON><PERSON><PERSON>", "ids", "acks", "flags", "connected", "disconnected", "auth", "onpacket", "subEvents", "unshift", "ev", "compress", "pop", "isTransportWritable", "transport", "discardPacket", "_packet", "onconnect", "BINARY_EVENT", "onevent", "BINARY_ACK", "onack", "ondisconnect", "message", "ack", "emitEvent", "_anyListeners", "sent", "emitBuffered", "listener", "url_1", "manager_1", "lookup", "cache", "managers", "parsed", "url", "sameNamespace", "forceNew", "multiplex", "manager_2", "parseuri", "loc", "test", "ipv6", "href", "transports", "writeBuffer", "prevBufferLen", "agent", "withCredentials", "upgrade", "rememberUpgrade", "rejectUnauthorized", "perMessageDeflate", "threshold", "transportOptions", "upgrades", "pingInterval", "pingTimeout", "pingTimeoutTimer", "clone", "EIO", "priorWebsocketSuccess", "createTransport", "shift", "setTransport", "onDrain", "onError", "probe", "failed", "onTransportOpen", "onlyBinaryUpgrades", "upgradeLosesBinary", "send", "upgrading", "flush", "freezeTransport", "error", "onTransportClose", "onupgrade", "to", "onHandshake", "JSON", "parse", "resetPingTimeout", "sendPacket", "code", "filterUpgrades", "cleanupAndClose", "waitForUpgrade", "pingIntervalTimer", "filteredUpgrades", "j", "empty", "hasXHR2", "responseType", "forceBase64", "Request", "req", "request", "method", "onData", "pollXhr", "async", "xhr", "extraHeaders", "setDisableHeaderCheck", "setRequestHeader", "requestTimeout", "hasXDR", "onload", "onLoad", "responseText", "onreadystatechange", "status", "document", "requestsCount", "requests", "onSuccess", "fromError", "abort", "attachEvent", "unload<PERSON><PERSON><PERSON>", "withNativeBlob", "Blob", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "encodeBlobAsBase64", "fileReader", "FileReader", "content", "result", "readAsDataURL", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "base64decoder", "decodeBase64Packet", "mapBinary", "base64", "chars", "arraybuffer", "bytes", "Uint8Array", "encoded1", "encoded2", "encoded3", "encoded4", "bufferLength", "rNewline", "rEscapedNewline", "JSONPPolling", "___eio", "script", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "form", "iframe", "createElement", "insertAt", "getElementsByTagName", "insertBefore", "head", "body", "append<PERSON><PERSON><PERSON>", "navigator", "userAgent", "area", "iframeId", "className", "style", "position", "top", "left", "target", "setAttribute", "complete", "initIframe", "html", "action", "submit", "WebSocket", "usingBrowserWebSocket", "defaultBinaryType", "isReactNative", "product", "toLowerCase", "WS", "check", "protocols", "headers", "ws", "addEventListeners", "onmessage", "<PERSON><PERSON><PERSON>", "byteLength", "MozWebSocket", "utf8Write", "view", "offset", "charCodeAt", "setUint8", "defers", "size", "_encode", "hi", "lo", "utf8Length", "_str", "_length", "_offset", "pow", "_float", "time", "getTime", "_bin", "toJSON", "allKeys", "buf", "DataView", "deferIndex", "defer<PERSON><PERSON>ten", "nextOffset", "defer", "deferL<PERSON>th", "bin", "setFloat64", "_buffer", "_view", "byteOffset", "_array", "_parse", "_map", "string", "chr", "end", "byte", "getUint8", "utf8Read", "prefix", "getUint16", "getUint32", "getInt8", "getFloat32", "getFloat64", "getInt16", "getInt32", "ms", "factor", "rand", "random", "deviation"], "mappings": ";;;;;CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAY,GAAID,IAEhBD,EAAS,GAAIC,IARf,CASGK,MAAM,WACT,O,YCTE,IAAIC,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUP,QAGnC,IAAIC,EAASI,EAAiBE,GAAY,CACzCC,EAAGD,EACHE,GAAG,EACHT,QAAS,IAUV,OANAU,EAAQH,GAAUI,KAAKV,EAAOD,QAASC,EAAQA,EAAOD,QAASM,GAG/DL,EAAOQ,GAAI,EAGJR,EAAOD,QA0Df,OArDAM,EAAoBM,EAAIF,EAGxBJ,EAAoBO,EAAIR,EAGxBC,EAAoBQ,EAAI,SAASd,EAASe,EAAMC,GAC3CV,EAAoBW,EAAEjB,EAASe,IAClCG,OAAOC,eAAenB,EAASe,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhEV,EAAoBgB,EAAI,SAAStB,GACX,oBAAXuB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAenB,EAASuB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,KAQvDnB,EAAoBoB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQnB,EAAoBmB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,iBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFAxB,EAAoBgB,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOnB,EAAoBQ,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRvB,EAAoB2B,EAAI,SAAShC,GAChC,IAAIe,EAASf,GAAUA,EAAO2B,WAC7B,WAAwB,OAAO3B,EAAgB,SAC/C,WAA8B,OAAOA,GAEtC,OADAK,EAAoBQ,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRV,EAAoBW,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,IAGzG7B,EAAoBgC,EAAI,GAIjBhC,EAAoBA,EAAoBiC,EAAI,I,kBCnErD,SAASC,EAAQC,GACf,GAAIA,EAAK,OAWX,SAAeA,GACb,IAAK,IAAIV,KAAOS,EAAQJ,UACtBK,EAAIV,GAAOS,EAAQJ,UAAUL,GAE/B,OAAOU,EAfSC,CAAMD,GAVtBxC,EAAOD,QAAUwC,EAqCnBA,EAAQJ,UAAUO,GAClBH,EAAQJ,UAAUQ,iBAAmB,SAASC,EAAOC,GAInD,OAHA1C,KAAK2C,WAAa3C,KAAK2C,YAAc,IACpC3C,KAAK2C,WAAW,IAAMF,GAASzC,KAAK2C,WAAW,IAAMF,IAAU,IAC7DG,KAAKF,GACD1C,MAaToC,EAAQJ,UAAUa,KAAO,SAASJ,EAAOC,GACvC,SAASH,IACPvC,KAAK8C,IAAIL,EAAOF,GAChBG,EAAGK,MAAM/C,KAAMgD,WAKjB,OAFAT,EAAGG,GAAKA,EACR1C,KAAKuC,GAAGE,EAAOF,GACRvC,MAaToC,EAAQJ,UAAUc,IAClBV,EAAQJ,UAAUiB,eAClBb,EAAQJ,UAAUkB,mBAClBd,EAAQJ,UAAUmB,oBAAsB,SAASV,EAAOC,GAItD,GAHA1C,KAAK2C,WAAa3C,KAAK2C,YAAc,GAGjC,GAAKK,UAAUI,OAEjB,OADApD,KAAK2C,WAAa,GACX3C,KAIT,IAUIqD,EAVAC,EAAYtD,KAAK2C,WAAW,IAAMF,GACtC,IAAKa,EAAW,OAAOtD,KAGvB,GAAI,GAAKgD,UAAUI,OAEjB,cADOpD,KAAK2C,WAAW,IAAMF,GACtBzC,KAKT,IAAK,IAAII,EAAI,EAAGA,EAAIkD,EAAUF,OAAQhD,IAEpC,IADAiD,EAAKC,EAAUlD,MACJsC,GAAMW,EAAGX,KAAOA,EAAI,CAC7BY,EAAUC,OAAOnD,EAAG,GACpB,MAUJ,OAJyB,IAArBkD,EAAUF,eACLpD,KAAK2C,WAAW,IAAMF,GAGxBzC,MAWToC,EAAQJ,UAAUwB,KAAO,SAASf,GAChCzC,KAAK2C,WAAa3C,KAAK2C,YAAc,GAKrC,IAHA,IAAIc,EAAO,IAAIC,MAAMV,UAAUI,OAAS,GACpCE,EAAYtD,KAAK2C,WAAW,IAAMF,GAE7BrC,EAAI,EAAGA,EAAI4C,UAAUI,OAAQhD,IACpCqD,EAAKrD,EAAI,GAAK4C,UAAU5C,GAG1B,GAAIkD,EAEG,CAAIlD,EAAI,EAAb,IAAK,IAAWuD,GADhBL,EAAYA,EAAUM,MAAM,IACIR,OAAQhD,EAAIuD,IAAOvD,EACjDkD,EAAUlD,GAAG2C,MAAM/C,KAAMyD,GAI7B,OAAOzD,MAWToC,EAAQJ,UAAU6B,UAAY,SAASpB,GAErC,OADAzC,KAAK2C,WAAa3C,KAAK2C,YAAc,GAC9B3C,KAAK2C,WAAW,IAAMF,IAAU,IAWzCL,EAAQJ,UAAU8B,aAAe,SAASrB,GACxC,QAAUzC,KAAK6D,UAAUpB,GAAOW,S,gBC7KlC,IAAMW,EAAeC,EAAQ,IACvBC,EAAeD,EAAQ,IAEvBE,EAAYC,OAAOC,aAAa,IAgCtCvE,EAAOD,QAAU,CACfyE,SAAU,EACVN,eACAO,cAjCoB,SAACC,EAASC,GAE9B,IAAMpB,EAASmB,EAAQnB,OACjBqB,EAAiB,IAAIf,MAAMN,GAC7BsB,EAAQ,EAEZH,EAAQI,SAAQ,SAACC,EAAQxE,GAEvB2D,EAAaa,GAAQ,GAAO,SAAAC,GAC1BJ,EAAerE,GAAKyE,IACdH,IAAUtB,GACdoB,EAASC,EAAeK,KAAKZ,WAuBnCD,eACAc,cAlBoB,SAACC,EAAgBC,GAGrC,IAFA,IAAMR,EAAiBO,EAAeE,MAAMhB,GACtCK,EAAU,GACPnE,EAAI,EAAGA,EAAIqE,EAAerB,OAAQhD,IAAK,CAC9C,IAAM+E,EAAgBlB,EAAaQ,EAAerE,GAAI6E,GAEtD,GADAV,EAAQ3B,KAAKuC,GACc,UAAvBA,EAAcC,KAChB,MAGJ,OAAOb,K,cChCT1E,EAAOD,QACe,oBAATyF,KACFA,KACoB,oBAAXC,OACTA,OAEAC,SAAS,cAATA,I,ytCCNX,IAAMC,EAASxB,EAAQ,GAGjByB,E,sQAOJ,WAAYC,GAAM,a,4FAAA,UAChB,gBAEKA,KAAOA,EACZ,EAAKC,MAAQD,EAAKC,MAClB,EAAKC,WAAa,GAClB,EAAKC,OAASH,EAAKG,OANH,E,6CAgBVC,EAAKC,GACX,IAAMC,EAAM,IAAIC,MAAMH,GAItB,OAHAE,EAAIZ,KAAO,iBACXY,EAAIE,YAAcH,EAClB/F,KAAKwD,KAAK,QAASwC,GACZhG,O,6BAcP,MALI,WAAaA,KAAK4F,YAAc,KAAO5F,KAAK4F,aAC9C5F,KAAK4F,WAAa,UAClB5F,KAAKmG,UAGAnG,O,8BAcP,MALI,YAAcA,KAAK4F,YAAc,SAAW5F,KAAK4F,aACnD5F,KAAKoG,UACLpG,KAAKqG,WAGArG,O,2BASJuE,GACH,GAAI,SAAWvE,KAAK4F,WAGlB,MAAM,IAAIK,MAAM,sBAFhBjG,KAAKsG,MAAM/B,K,+BAYbvE,KAAK4F,WAAa,OAClB5F,KAAKuG,UAAW,EAChBvG,KAAKwD,KAAK,U,6BASLgD,GACL,IAAM5B,EAASY,EAAOvB,aAAauC,EAAMxG,KAAK6F,OAAOZ,YACrDjF,KAAKyG,SAAS7B,K,+BAMPA,GACP5E,KAAKwD,KAAK,SAAUoB,K,gCASpB5E,KAAK4F,WAAa,SAClB5F,KAAKwD,KAAK,c,8BA/GEQ,EAAQ,IAmHxBnE,EAAOD,QAAU6F,G,cC5GjB7F,EAAQ8G,OAAS,SAAUrE,GACzB,IAAIsE,EAAM,GAEV,IAAK,IAAIvG,KAAKiC,EACRA,EAAIJ,eAAe7B,KACjBuG,EAAIvD,SAAQuD,GAAO,KACvBA,GAAOC,mBAAmBxG,GAAK,IAAMwG,mBAAmBvE,EAAIjC,KAIhE,OAAOuG,GAUT/G,EAAQiH,OAAS,SAASC,GAGxB,IAFA,IAAIC,EAAM,GACNC,EAAQF,EAAG5B,MAAM,KACZ9E,EAAI,EAAGC,EAAI2G,EAAM5D,OAAQhD,EAAIC,EAAGD,IAAK,CAC5C,IAAI6G,EAAOD,EAAM5G,GAAG8E,MAAM,KAC1B6B,EAAIG,mBAAmBD,EAAK,KAAOC,mBAAmBD,EAAK,IAE7D,OAAOF,I,gBCnCT,IAAII,EAAUnD,EAAQ,IAClB5B,EAAU4B,EAAQ,GAEtBpE,EAAQyE,SAAW,EAMnB,IAAI+C,EAAcxH,EAAQwH,WAAa,CACrCC,QAAS,EACTC,WAAY,EACZC,MAAO,EACPC,IAAK,EACLC,cAAe,GAGbC,EACFC,OAAOD,WACP,SAAUrG,GACR,MACmB,iBAAVA,GACPuG,SAASvG,IACTwG,KAAKC,MAAMzG,KAAWA,GAIxB0G,EAAW,SAAU1G,GACvB,MAAwB,iBAAVA,GAGZ2G,EAAW,SAAU3G,GACvB,MAAiD,oBAA1CP,OAAOkB,UAAUiG,SAAS1H,KAAKc,IAGxC,SAAS6G,KAMT,SAASC,KAJTD,EAAQlG,UAAU0E,OAAS,SAAU9B,GACnC,MAAO,CAACuC,EAAQT,OAAO9B,KAKzBxC,EAAQ+F,EAAQnG,WAEhBmG,EAAQnG,UAAUoG,IAAM,SAAU/F,GAChC,IAAIgG,EAAUlB,EAAQN,OAAOxE,GAC7BrC,KAAKsI,YAAYD,GACjBrI,KAAKwD,KAAK,UAAW6E,IAgBvBF,EAAQnG,UAAUsG,YAAc,SAAUD,GAKxC,KAHEX,EAAUW,EAAQjD,OAClBiD,EAAQjD,MAAQgC,EAAWC,SAC3BgB,EAAQjD,MAAQgC,EAAWK,eAE3B,MAAM,IAAIxB,MAAM,uBAGlB,IAAK8B,EAASM,EAAQE,KACpB,MAAM,IAAItC,MAAM,qBAGlB,IA1BF,SAAqBoC,GACnB,OAAQA,EAAQjD,MACd,KAAKgC,EAAWC,QACd,YAAwBmB,IAAjBH,EAAQ7B,MAAsBwB,EAASK,EAAQ7B,MACxD,KAAKY,EAAWE,WACd,YAAwBkB,IAAjBH,EAAQ7B,KACjB,KAAKY,EAAWK,cACd,OAAOM,EAASM,EAAQ7B,OAASwB,EAASK,EAAQ7B,MACpD,QACE,OAAO9C,MAAM+E,QAAQJ,EAAQ7B,OAiB5BkC,CAAYL,GACf,MAAM,IAAIpC,MAAM,mBAIlB,UADgCuC,IAAfH,EAAQM,IAAoBjB,EAAUW,EAAQM,KAE7D,MAAM,IAAI1C,MAAM,sBAIpBkC,EAAQnG,UAAU4G,QAAU,aAE5BhJ,EAAQsI,QAAUA,EAClBtI,EAAQuI,QAAUA,G,cCnFlB,IAAIU,EAAK,0OAELC,EAAQ,CACR,SAAU,WAAY,YAAa,WAAY,OAAQ,WAAY,OAAQ,OAAQ,WAAY,OAAQ,YAAa,OAAQ,QAAS,UAGzIjJ,EAAOD,QAAU,SAAkB+G,GAC/B,IAAIoC,EAAMpC,EACNqC,EAAIrC,EAAIsC,QAAQ,KAChBC,EAAIvC,EAAIsC,QAAQ,MAEV,GAAND,IAAiB,GAANE,IACXvC,EAAMA,EAAIwC,UAAU,EAAGH,GAAKrC,EAAIwC,UAAUH,EAAGE,GAAGE,QAAQ,KAAM,KAAOzC,EAAIwC,UAAUD,EAAGvC,EAAIvD,SAO9F,IAJA,IAmCmBuC,EACfa,EApCAhG,EAAIqI,EAAGQ,KAAK1C,GAAO,IACnB2C,EAAM,GACNlJ,EAAI,GAEDA,KACHkJ,EAAIR,EAAM1I,IAAMI,EAAEJ,IAAM,GAa5B,OAVU,GAAN4I,IAAiB,GAANE,IACXI,EAAIC,OAASR,EACbO,EAAIE,KAAOF,EAAIE,KAAKL,UAAU,EAAGG,EAAIE,KAAKpG,OAAS,GAAGgG,QAAQ,KAAM,KACpEE,EAAIG,UAAYH,EAAIG,UAAUL,QAAQ,IAAK,IAAIA,QAAQ,IAAK,IAAIA,QAAQ,KAAM,KAC9EE,EAAII,SAAU,GAGlBJ,EAAIK,UAMR,SAAmBtH,EAAKuH,GACpB,IACIC,EAAQD,EAAKR,QADN,WACoB,KAAKlE,MAAM,KAEjB,KAArB0E,EAAKE,OAAO,EAAG,IAA6B,IAAhBF,EAAKxG,QACjCyG,EAAMtG,OAAO,EAAG,GAEmB,KAAnCqG,EAAKE,OAAOF,EAAKxG,OAAS,EAAG,IAC7ByG,EAAMtG,OAAOsG,EAAMzG,OAAS,EAAG,GAGnC,OAAOyG,EAjBSF,CAAUL,EAAKA,EAAG,MAClCA,EAAIS,UAmBepE,EAnBU2D,EAAG,MAoB5B9C,EAAO,GAEXb,EAAMyD,QAAQ,6BAA6B,SAAUY,EAAIC,EAAIC,GACrDD,IACAzD,EAAKyD,GAAMC,MAIZ1D,GA1BA8C,I,6gDCvCXxI,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,IACtDzB,EAAQuK,aAAU,EAClB,IAAMC,EAAMpG,EAAQ,IACdqG,EAAWrG,EAAQ,IACnB5B,EAAU4B,EAAQ,GAClBwB,EAASxB,EAAQ,GACjBsG,EAAOtG,EAAQ,IACfuG,EAAUvG,EAAQ,IAGlBmG,E,sQACF,WAAYb,EAAK5D,GAAM,O,4FAAA,UACnB,gBACK8E,KAAO,GACZ,EAAKC,KAAO,GACRnB,GAAO,WAAa,EAAOA,KAC3B5D,EAAO4D,EACPA,OAAMd,IAEV9C,EAAOA,GAAQ,IACVkE,KAAOlE,EAAKkE,MAAQ,aACzB,EAAKlE,KAAOA,EACZ,EAAKgF,cAAmC,IAAtBhF,EAAKgF,cACvB,EAAKC,qBAAqBjF,EAAKiF,sBAAwBC,KACvD,EAAKC,kBAAkBnF,EAAKmF,mBAAqB,KACjD,EAAKC,qBAAqBpF,EAAKoF,sBAAwB,KACvD,EAAKC,oBAAoBrF,EAAKqF,qBAAuB,IACrD,EAAKC,QAAU,IAAIT,EAAQ,CACvBU,IAAK,EAAKJ,oBACVK,IAAK,EAAKJ,uBACVK,OAAQ,EAAKJ,wBAEjB,EAAKK,QAAQ,MAAQ1F,EAAK0F,QAAU,IAAQ1F,EAAK0F,SACjD,EAAKC,YAAc,SACnB,EAAK/B,IAAMA,EACX,IAAMgC,EAAU5F,EAAKF,QAAUA,EAxBZ,OAyBnB,EAAK+F,QAAU,IAAID,EAAQpD,QAC3B,EAAKsD,QAAU,IAAIF,EAAQnD,QAC3B,EAAKsD,cAAoC,IAArB/F,EAAKgG,YACrB,EAAKD,cACL,EAAKE,OA7BU,E,kDA+BVC,GACT,OAAK5I,UAAUI,QAEfpD,KAAK6L,gBAAkBD,EAChB5L,MAFIA,KAAK6L,gB,2CAICD,GACjB,YAAUpD,IAANoD,EACO5L,KAAK8L,uBAChB9L,KAAK8L,sBAAwBF,EACtB5L,Q,wCAEO4L,GACd,IAAIG,EACJ,YAAUvD,IAANoD,EACO5L,KAAKgM,oBAChBhM,KAAKgM,mBAAqBJ,EACF,QAAvBG,EAAK/L,KAAKgL,eAA4B,IAAPe,GAAyBA,EAAGE,OAAOL,GAC5D5L,Q,0CAES4L,GAChB,IAAIG,EACJ,YAAUvD,IAANoD,EACO5L,KAAKkM,sBAChBlM,KAAKkM,qBAAuBN,EACJ,QAAvBG,EAAK/L,KAAKgL,eAA4B,IAAPe,GAAyBA,EAAGI,UAAUP,GAC/D5L,Q,2CAEU4L,GACjB,IAAIG,EACJ,YAAUvD,IAANoD,EACO5L,KAAKoM,uBAChBpM,KAAKoM,sBAAwBR,EACL,QAAvBG,EAAK/L,KAAKgL,eAA4B,IAAPe,GAAyBA,EAAGM,OAAOT,GAC5D5L,Q,8BAEH4L,GACJ,OAAK5I,UAAUI,QAEfpD,KAAKsM,SAAWV,EACT5L,MAFIA,KAAKsM,W,8CAYXtM,KAAKuM,eACNvM,KAAK6L,eACqB,IAA1B7L,KAAKgL,QAAQwB,UAEbxM,KAAKyM,c,2BAUR/J,GAAI,WAGL,IAAK1C,KAAKqL,YAAYpC,QAAQ,QAC1B,OAAOjJ,KAGXA,KAAK0M,OAAStC,EAAIpK,KAAKsJ,IAAKtJ,KAAK0F,MACjC,IAAMG,EAAS7F,KAAK0M,OACdrH,EAAOrF,KACbA,KAAKqL,YAAc,UACnBrL,KAAK2M,eAAgB,EAErB,IAAMC,EAAiBtC,EAAK/H,GAAGsD,EAAQ,QAAQ,WAC3CR,EAAKwH,SACLnK,GAAMA,OAGJoK,EAAWxC,EAAK/H,GAAGsD,EAAQ,SAAS,SAACG,GAGvCX,EAAK0H,UACL1H,EAAKgG,YAAc,SACnB,kCAAW,QAASrF,GAChBtD,EACAA,EAAGsD,GAIHX,EAAK2H,0BAGb,IAAI,IAAUhN,KAAKsM,SAAU,CACzB,IAAMlB,EAAUpL,KAAKsM,SAGL,IAAZlB,GACAwB,IAGJ,IAAMK,EAAQC,YAAW,WAGrBN,IACA/G,EAAOsH,QACPtH,EAAOrC,KAAK,QAAS,IAAIyC,MAAM,cAChCmF,GACHpL,KAAKyK,KAAK7H,MAAK,WACXwK,aAAaH,MAKrB,OAFAjN,KAAKyK,KAAK7H,KAAKgK,GACf5M,KAAKyK,KAAK7H,KAAKkK,GACR9M,O,8BAQH0C,GACJ,OAAO1C,KAAK2L,KAAKjJ,K,+BAWjB1C,KAAK+M,UAEL/M,KAAKqL,YAAc,OACnB,wCAAW,QAEX,IAAMxF,EAAS7F,KAAK0M,OACpB1M,KAAKyK,KAAK7H,KAAK0H,EAAK/H,GAAGsD,EAAQ,OAAQ7F,KAAKqN,OAAOzL,KAAK5B,OAAQsK,EAAK/H,GAAGsD,EAAQ,OAAQ7F,KAAKsN,OAAO1L,KAAK5B,OAAQsK,EAAK/H,GAAGsD,EAAQ,QAAS7F,KAAKuN,QAAQ3L,KAAK5B,OAAQsK,EAAK/H,GAAGsD,EAAQ,QAAS7F,KAAKwN,QAAQ5L,KAAK5B,OAAQsK,EAAK/H,GAAGvC,KAAKwL,QAAS,UAAWxL,KAAKyN,UAAU7L,KAAK5B,U,+BAQ5Q,wCAAW,U,6BAORwG,GACHxG,KAAKwL,QAAQpD,IAAI5B,K,gCAOX5B,GACN,wCAAW,SAAUA,K,8BAOjBoB,GAGJ,wCAAW,QAASA,K,6BAQjBuC,EAAK7C,GACR,IAAIG,EAAS7F,KAAKwK,KAAKjC,GAKvB,OAJK1C,IACDA,EAAS,IAAIwE,EAASqD,OAAO1N,KAAMuI,EAAK7C,GACxC1F,KAAKwK,KAAKjC,GAAO1C,GAEdA,I,+BAQFA,GAEL,IADA,IACA,MADa/E,OAAO6M,KAAK3N,KAAKwK,MAC9B,eAAwB,CAAnB,IAAMjC,EAAG,KAEV,GADevI,KAAKwK,KAAKjC,GACdqF,OAGP,OAGR5N,KAAK6N,W,8BAQDjJ,GAIJ,IADA,IAAMH,EAAiBzE,KAAKuL,QAAQ7E,OAAO9B,GAClCxE,EAAI,EAAGA,EAAIqE,EAAerB,OAAQhD,IACvCJ,KAAK0M,OAAOpG,MAAM7B,EAAerE,GAAIwE,EAAOkJ,W,gCAWhD9N,KAAKyK,KAAK9F,SAAQ,SAACoJ,GAAD,OAAgBA,OAClC/N,KAAKyK,KAAKrH,OAAS,EACnBpD,KAAKwL,QAAQ5C,Y,+BAUb5I,KAAK2M,eAAgB,EACrB3M,KAAKuM,eAAgB,EACjB,YAAcvM,KAAKqL,aAGnBrL,KAAK+M,UAET/M,KAAKgL,QAAQgD,QACbhO,KAAKqL,YAAc,SACfrL,KAAK0M,QACL1M,KAAK0M,OAAOS,U,mCAQhB,OAAOnN,KAAK6N,W,8BAORI,GAGJjO,KAAK+M,UACL/M,KAAKgL,QAAQgD,QACbhO,KAAKqL,YAAc,SACnB,wCAAW,QAAS4C,GAChBjO,KAAK6L,gBAAkB7L,KAAK2M,eAC5B3M,KAAKyM,c,kCAQD,WACR,GAAIzM,KAAKuM,eAAiBvM,KAAK2M,cAC3B,OAAO3M,KACX,IAAMqF,EAAOrF,KACb,GAAIA,KAAKgL,QAAQwB,UAAYxM,KAAK8L,sBAG9B9L,KAAKgL,QAAQgD,QACb,wCAAW,oBACXhO,KAAKuM,eAAgB,MAEpB,CACD,IAAM2B,EAAQlO,KAAKgL,QAAQmD,WAG3BnO,KAAKuM,eAAgB,EACrB,IAAMU,EAAQC,YAAW,WACjB7H,EAAKsH,gBAIT,kCAAW,oBAAqBtH,EAAK2F,QAAQwB,UAEzCnH,EAAKsH,eAETtH,EAAKsG,MAAK,SAAC3F,GACHA,GAGAX,EAAKkH,eAAgB,EACrBlH,EAAKoH,YACL,kCAAW,kBAAmBzG,IAK9BX,EAAK+I,oBAGdF,GACHlO,KAAKyK,KAAK7H,MAAK,WACXwK,aAAaH,S,oCAUrB,IAAMoB,EAAUrO,KAAKgL,QAAQwB,SAC7BxM,KAAKuM,eAAgB,EACrBvM,KAAKgL,QAAQgD,QACb,wCAAW,YAAaK,Q,8BApXVjM,GAuXtBxC,EAAQuK,QAAUA,G,gBClYlB,IAAMmE,EAAiBtK,EAAQ,GACzBuK,EAAMvK,EAAQ,IACdwK,EAAQxK,EAAQ,IAChByK,EAAYzK,EAAQ,IAE1BpE,EAAQ8O,QAUR,SAAiBhJ,GACf,IACIiJ,GAAK,EACLC,GAAK,EACHC,GAAQ,IAAUnJ,EAAKmJ,MAE7B,GAAwB,oBAAbC,SAA0B,CACnC,IAAMC,EAAQ,WAAaD,SAASzK,SAChC2K,EAAOF,SAASE,KAGfA,IACHA,EAAOD,EAAQ,IAAM,IAGvBJ,EAAKjJ,EAAKuJ,WAAaH,SAASG,UAAYD,IAAStJ,EAAKsJ,KAC1DJ,EAAKlJ,EAAKwJ,SAAWH,EAOvB,GAJArJ,EAAKyJ,QAAUR,EACfjJ,EAAK0J,QAAUR,EAGX,SAFE,IAAIN,EAAe5I,KAEHA,EAAK2J,WACzB,OAAO,IAAId,EAAI7I,GAEf,IAAKmJ,EAAO,MAAM,IAAI5I,MAAM,kBAC5B,OAAO,IAAIuI,EAAM9I,IApCrB9F,EAAQ6O,UAAYA,G,gBCJpB,IAAMa,EAAUtL,EAAQ,IAClBuL,EAAavL,EAAQ,GAE3BnE,EAAOD,QAAU,SAAS8F,GACxB,IAAMyJ,EAAUzJ,EAAKyJ,QAIfC,EAAU1J,EAAK0J,QAIfI,EAAa9J,EAAK8J,WAGxB,IACE,GAAI,oBAAuBlB,kBAAoBa,GAAWG,GACxD,OAAO,IAAIhB,eAEb,MAAOpF,IAKT,IACE,GAAI,oBAAuBuG,iBAAmBL,GAAWI,EACvD,OAAO,IAAIC,eAEb,MAAOvG,IAET,IAAKiG,EACH,IACE,OAAO,IAAII,EAAW,CAAC,UAAUG,OAAO,UAAU5K,KAAK,OACrD,qBAEF,MAAOoE,O,uzCCrCb,IAAMzD,EAAYzB,EAAQ,GACpB2L,EAAU3L,EAAQ,GAClBwB,EAASxB,EAAQ,GACjB4L,EAAQ5L,EAAQ,IAKhB6L,E,0WAeF7P,KAAK8P,S,4BASDC,GACJ,IAAM1K,EAAOrF,KAIb,SAASgQ,IAGP3K,EAAKO,WAAa,SAClBmK,IAGF,GATA/P,KAAK4F,WAAa,UASd5F,KAAK0O,UAAY1O,KAAKuG,SAAU,CAClC,IAAI0J,EAAQ,EAERjQ,KAAK0O,UAGPuB,IACAjQ,KAAK6C,KAAK,gBAAgB,aAGtBoN,GAASD,QAIVhQ,KAAKuG,WAGR0J,IACAjQ,KAAK6C,KAAK,SAAS,aAGfoN,GAASD,aAIfA,M,6BAYFhQ,KAAK0O,SAAU,EACf1O,KAAKkQ,SACLlQ,KAAKwD,KAAK,U,6BAQLgD,GACL,IAAMnB,EAAOrF,KAoBbwF,EAAOT,cAAcyB,EAAMxG,KAAK6F,OAAOZ,YAAYN,SAjBlC,SAASC,EAAQuL,EAAOF,GAOvC,GALI,YAAc5K,EAAKO,YAA8B,SAAhBhB,EAAOQ,MAC1CC,EAAK+K,SAIH,UAAYxL,EAAOQ,KAErB,OADAC,EAAKgB,WACE,EAIThB,EAAKoB,SAAS7B,MAOZ,WAAa5E,KAAK4F,aAEpB5F,KAAK0O,SAAU,EACf1O,KAAKwD,KAAK,gBAEN,SAAWxD,KAAK4F,YAClB5F,KAAK8P,U,gCAcT,IAAMzK,EAAOrF,KAEb,SAASmN,IAGP9H,EAAKiB,MAAM,CAAC,CAAElB,KAAM,WAGlB,SAAWpF,KAAK4F,WAGlBuH,IAMAnN,KAAK6C,KAAK,OAAQsK,K,4BAWhB5I,GAAS,WACbvE,KAAKuG,UAAW,EAEhBf,EAAOlB,cAAcC,GAAS,SAAAiC,GAC5B,EAAK6J,QAAQ7J,GAAM,WACjB,EAAKD,UAAW,EAChB,EAAK/C,KAAK,iB,4BAWd,IAAImC,EAAQ3F,KAAK2F,OAAS,GACpB2K,EAAStQ,KAAK0F,KAAKwJ,OAAS,QAAU,OACxCF,EAAO,GA4BX,OAzBI,IAAUhP,KAAK0F,KAAK6K,oBACtB5K,EAAM3F,KAAK0F,KAAK8K,gBAAkBZ,KAG/B5P,KAAKyQ,gBAAmB9K,EAAM+K,MACjC/K,EAAMgL,IAAM,GAGdhL,EAAQgK,EAAQjJ,OAAOf,GAIrB3F,KAAK0F,KAAKsJ,OACR,UAAYsB,GAAqC,MAA3B3I,OAAO3H,KAAK0F,KAAKsJ,OACtC,SAAWsB,GAAqC,KAA3B3I,OAAO3H,KAAK0F,KAAKsJ,SAEzCA,EAAO,IAAMhP,KAAK0F,KAAKsJ,MAIrBrJ,EAAMvC,SACRuC,EAAQ,IAAMA,GAKd2K,EACA,QAHgD,IAArCtQ,KAAK0F,KAAKuJ,SAAShG,QAAQ,KAI9B,IAAMjJ,KAAK0F,KAAKuJ,SAAW,IAAMjP,KAAK0F,KAAKuJ,UACnDD,EACAhP,KAAK0F,KAAKkE,KACVjE,I,2BA3MF,MAAO,e,8BALWF,GAqNtB5F,EAAOD,QAAUiQ,G,cC7NjB,IAAMe,EAAe9P,OAAOY,OAAO,MACnCkP,EAAY,KAAW,IACvBA,EAAY,MAAY,IACxBA,EAAY,KAAW,IACvBA,EAAY,KAAW,IACvBA,EAAY,QAAc,IAC1BA,EAAY,QAAc,IAC1BA,EAAY,KAAW,IAEvB,IAAMC,EAAuB/P,OAAOY,OAAO,MAC3CZ,OAAO6M,KAAKiD,GAAcjM,SAAQ,SAAAhD,GAChCkP,EAAqBD,EAAajP,IAAQA,KAK5C9B,EAAOD,QAAU,CACfgR,eACAC,uBACAC,aALmB,CAAE1L,KAAM,QAASoB,KAAM,kB,6BCZ5C,IAKIuK,EALAC,EAAW,mEAAmE9L,MAAM,IAEpF+L,EAAM,GACNC,EAAO,EACP9Q,EAAI,EAUR,SAASsG,EAAOyK,GACd,IAAIC,EAAU,GAEd,GACEA,EAAUJ,EAASG,EAjBV,IAiB0BC,EACnCD,EAAMtJ,KAAKC,MAAMqJ,EAlBR,UAmBFA,EAAM,GAEf,OAAOC,EA0BT,SAASxB,IACP,IAAIyB,EAAM3K,GAAQ,IAAI4K,MAEtB,OAAID,IAAQN,GAAaG,EAAO,EAAGH,EAAOM,GACnCA,EAAK,IAAK3K,EAAOwK,KAM1B,KAAO9Q,EAzDM,GAyDMA,IAAK6Q,EAAID,EAAS5Q,IAAMA,EAK3CwP,EAAMlJ,OAASA,EACfkJ,EAAM/I,OAhCN,SAAgBF,GACd,IAAI0B,EAAU,EAEd,IAAKjI,EAAI,EAAGA,EAAIuG,EAAIvD,OAAQhD,IAC1BiI,EAnCS,GAmCCA,EAAmB4I,EAAItK,EAAI4K,OAAOnR,IAG9C,OAAOiI,GA0BTxI,EAAOD,QAAUgQ,G,cCnEjB/P,EAAOD,QAAQ4R,KAAO,SAACnP,GAAiB,2BAAToP,EAAS,iCAATA,EAAS,kBACtC,OAAOA,EAAKC,QAAO,SAACC,EAAKC,GAIvB,OAHIvP,EAAIJ,eAAe2P,KACrBD,EAAIC,GAAKvP,EAAIuP,IAERD,IACN,M,8hFCLL7Q,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,IACtDzB,EAAQ8N,YAAS,EACjB,IAAMmE,EAAqB7N,EAAQ,GAC7B5B,EAAU4B,EAAQ,GAClBsG,EAAOtG,EAAQ,IAOf8N,EAAkBhR,OAAOiR,OAAO,CAClCC,QAAS,EACTC,cAAe,EACfC,WAAY,EACZC,cAAe,EAEfC,YAAa,EACbnP,eAAgB,IAEdyK,E,sQAMF,WAAY2E,EAAI9J,EAAK7C,GAAM,a,4FAAA,UACvB,gBACK4M,cAAgB,GACrB,EAAKC,WAAa,GAClB,EAAKC,IAAM,EACX,EAAKC,KAAO,GACZ,EAAKC,MAAQ,GACb,EAAKL,GAAKA,EACV,EAAK9J,IAAMA,EACX,EAAKiK,IAAM,EACX,EAAKC,KAAO,GACZ,EAAKH,cAAgB,GACrB,EAAKC,WAAa,GAClB,EAAKI,WAAY,EACjB,EAAKC,cAAe,EACpB,EAAKF,MAAQ,GACThN,GAAQA,EAAKmN,OACb,EAAKA,KAAOnN,EAAKmN,MAEjB,EAAKR,GAAG5G,cACR,EAAKE,OApBc,E,iDA4BvB,IAAI3L,KAAKyK,KAAT,CAEA,IAAM4H,EAAKrS,KAAKqS,GAChBrS,KAAKyK,KAAO,CACRH,EAAK/H,GAAG8P,EAAI,OAAQrS,KAAK6M,OAAOjL,KAAK5B,OACrCsK,EAAK/H,GAAG8P,EAAI,SAAUrS,KAAK8S,SAASlR,KAAK5B,OACzCsK,EAAK/H,GAAG8P,EAAI,QAASrS,KAAKuN,QAAQ3L,KAAK5B,OACvCsK,EAAK/H,GAAG8P,EAAI,QAASrS,KAAKwN,QAAQ5L,KAAK5B,W,gCAe3C,OAAIA,KAAK2S,YAET3S,KAAK+S,YACA/S,KAAKqS,GAAL,eACDrS,KAAKqS,GAAG1G,OACR,SAAW3L,KAAKqS,GAAGhH,aACnBrL,KAAK6M,UALE7M,O,6BAYX,OAAOA,KAAKgS,Y,6BAQF,2BAANvO,EAAM,yBAANA,EAAM,gBAGV,OAFAA,EAAKuP,QAAQ,WACbhT,KAAKwD,KAAKT,MAAM/C,KAAMyD,GACfzD,O,2BAUNiT,GACD,GAAInB,EAAgB7P,eAAegR,GAC/B,MAAM,IAAIhN,MAAM,IAAMgN,EAAK,8BAFjB,2BAANxP,EAAM,iCAANA,EAAM,kBAIdA,EAAKuP,QAAQC,GACb,IAAMrO,EAAS,CACXQ,KAAMyM,EAAmBzK,WAAWG,MACpCf,KAAM/C,EAEVmB,QAAiB,IACjBA,EAAOkJ,QAAQoF,UAAmC,IAAxBlT,KAAK0S,MAAMQ,SAEjC,mBAAsBzP,EAAKA,EAAKL,OAAS,KAGzCpD,KAAKyS,KAAKzS,KAAKwS,KAAO/O,EAAK0P,MAC3BvO,EAAO+D,GAAK3I,KAAKwS,OAErB,IAAMY,EAAsBpT,KAAKqS,GAAG3F,QAChC1M,KAAKqS,GAAG3F,OAAO2G,WACfrT,KAAKqS,GAAG3F,OAAO2G,UAAU9M,SACvB+M,EAAgBtT,KAAK0S,MAAL,YAAyBU,IAAwBpT,KAAK2S,WAY5E,OAXIW,IAIKtT,KAAK2S,UACV3S,KAAK4E,OAAOA,GAGZ5E,KAAKuS,WAAW3P,KAAKgC,IAEzB5E,KAAK0S,MAAQ,GACN1S,O,6BAQJ4E,GACHA,EAAO2D,IAAMvI,KAAKuI,IAClBvI,KAAKqS,GAAGkB,QAAQ3O,K,+BAOX,WAGmB,mBAAb5E,KAAK6S,KACZ7S,KAAK6S,MAAK,SAACrM,GACP,EAAK5B,OAAO,CAAEQ,KAAMyM,EAAmBzK,WAAWC,QAASb,YAI/DxG,KAAK4E,OAAO,CAAEQ,KAAMyM,EAAmBzK,WAAWC,QAASb,KAAMxG,KAAK6S,S,8BAStE7M,GACChG,KAAK2S,WACN,wCAAW,gBAAiB3M,K,8BAS5BiI,GAGJjO,KAAK2S,WAAY,EACjB3S,KAAK4S,cAAe,SACb5S,KAAK2I,GACZ,wCAAW,aAAcsF,K,+BAQpBrJ,GAEL,GADsBA,EAAO2D,MAAQvI,KAAKuI,IAG1C,OAAQ3D,EAAOQ,MACX,KAAKyM,EAAmBzK,WAAWC,QAC/B,GAAIzC,EAAO4B,MAAQ5B,EAAO4B,KAAKkK,IAAK,CAChC,IAAM/H,EAAK/D,EAAO4B,KAAKkK,IACvB1Q,KAAKwT,UAAU7K,QAGf,wCAAW,gBAAiB,IAAI1C,MAAM,8LAE1C,MACJ,KAAK4L,EAAmBzK,WAAWG,MAGnC,KAAKsK,EAAmBzK,WAAWqM,aAC/BzT,KAAK0T,QAAQ9O,GACb,MACJ,KAAKiN,EAAmBzK,WAAWI,IAGnC,KAAKqK,EAAmBzK,WAAWuM,WAC/B3T,KAAK4T,MAAMhP,GACX,MACJ,KAAKiN,EAAmBzK,WAAWE,WAC/BtH,KAAK6T,eACL,MACJ,KAAKhC,EAAmBzK,WAAWK,cAC/B,IAAMzB,EAAM,IAAIC,MAAMrB,EAAO4B,KAAKsN,SAElC9N,EAAIQ,KAAO5B,EAAO4B,KAAKA,KACvB,wCAAW,gBAAiBR,M,8BAUhCpB,GACJ,IAAMnB,EAAOmB,EAAO4B,MAAQ,GAGxB,MAAQ5B,EAAO+D,IAGflF,EAAKb,KAAK5C,KAAK+T,IAAInP,EAAO+D,KAE1B3I,KAAK2S,UACL3S,KAAKgU,UAAUvQ,GAGfzD,KAAKsS,cAAc1P,KAAK9B,OAAOiR,OAAOtO,M,gCAGpCA,GACN,GAAIzD,KAAKiU,eAAiBjU,KAAKiU,cAAc7Q,OAAQ,CACjD,IADiD,MAC/BpD,KAAKiU,cAAcrQ,SADY,IAEjD,2BAAkC,QACrBb,MAAM/C,KAAMyD,GAHwB,+BAMrD,8BAAWV,MAAM/C,KAAMyD,K,0BAOvBkF,GACA,IAAMtD,EAAOrF,KACTkU,GAAO,EACX,OAAO,WAEH,IAAIA,EAAJ,CAEAA,GAAO,EAJe,2BAANzQ,EAAM,yBAANA,EAAM,gBAOtB4B,EAAKT,OAAO,CACRQ,KAAMyM,EAAmBzK,WAAWI,IACpCmB,GAAIA,EACJnC,KAAM/C,Q,4BAUZmB,GACF,IAAMmP,EAAM/T,KAAKyS,KAAK7N,EAAO+D,IACzB,mBAAsBoL,IAGtBA,EAAIhR,MAAM/C,KAAM4E,EAAO4B,aAChBxG,KAAKyS,KAAK7N,EAAO+D,O,gCAYtBA,GAGN3I,KAAK2I,GAAKA,EACV3I,KAAK2S,WAAY,EACjB3S,KAAK4S,cAAe,EACpB,wCAAW,WACX5S,KAAKmU,iB,qCAOM,WACXnU,KAAKsS,cAAc3N,SAAQ,SAAClB,GAAD,OAAU,EAAKuQ,UAAUvQ,MACpDzD,KAAKsS,cAAgB,GACrBtS,KAAKuS,WAAW5N,SAAQ,SAACC,GAAD,OAAY,EAAKA,OAAOA,MAChD5E,KAAKuS,WAAa,K,qCAUlBvS,KAAK4I,UACL5I,KAAKwN,QAAQ,0B,gCAUTxN,KAAKyK,OAELzK,KAAKyK,KAAK9F,SAAQ,SAACoJ,GAAD,OAAgBA,OAClC/N,KAAKyK,UAAOjC,GAEhBxI,KAAKqS,GAAL,SAAoBrS,Q,mCAoBpB,OAXIA,KAAK2S,WAGL3S,KAAK4E,OAAO,CAAEQ,KAAMyM,EAAmBzK,WAAWE,aAGtDtH,KAAK4I,UACD5I,KAAK2S,WAEL3S,KAAKwN,QAAQ,wBAEVxN,O,8BASP,OAAOA,KAAKkS,e,+BASPgB,GAEL,OADAlT,KAAK0S,MAAMQ,SAAWA,EACflT,O,4BAoBLoU,GAGF,OAFApU,KAAKiU,cAAgBjU,KAAKiU,eAAiB,GAC3CjU,KAAKiU,cAAcrR,KAAKwR,GACjBpU,O,iCASAoU,GAGP,OAFApU,KAAKiU,cAAgBjU,KAAKiU,eAAiB,GAC3CjU,KAAKiU,cAAcjB,QAAQoB,GACpBpU,O,6BAQJoU,GACH,IAAKpU,KAAKiU,cACN,OAAOjU,KAEX,GAAIoU,GAEA,IADA,IAAMvQ,EAAY7D,KAAKiU,cACd7T,EAAI,EAAGA,EAAIyD,EAAUT,OAAQhD,IAClC,GAAIgU,IAAavQ,EAAUzD,GAEvB,OADAyD,EAAUN,OAAOnD,EAAG,GACbJ,UAKfA,KAAKiU,cAAgB,GAEzB,OAAOjU,O,qCASP,OAAOA,KAAKiU,eAAiB,K,6BAjZ7B,QAASjU,KAAKyK,O,+BAwVd,OADAzK,KAAK0S,MAAL,UAAsB,EACf1S,U,8BAxYMoC,GAocrBxC,EAAQ8N,OAASA,G,6BCxdjB5M,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,IACtDzB,EAAQ2C,QAAK,EAOb3C,EAAQ2C,GANR,SAAYF,EAAK4Q,EAAIvQ,GAEjB,OADAL,EAAIE,GAAG0Q,EAAIvQ,GACJ,WACHL,EAAIS,IAAImQ,EAAIvQ,M,kQCLpB5B,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,IACtDzB,EAAQ8N,OAAS9N,EAAQyS,GAAKzS,EAAQuK,QAAUvK,EAAQyE,cAAW,EACnE,IAAMgQ,EAAQrQ,EAAQ,IAChBsQ,EAAYtQ,EAAQ,GACpBqG,EAAWrG,EAAQ,IACzBlD,OAAOC,eAAenB,EAAS,SAAU,CAAEoB,YAAY,EAAMC,IAAK,WAAc,OAAOoJ,EAASqD,UAMhG7N,EAAOD,QAAUA,EAAU2U,EAI3B,IAAMC,EAAS5U,EAAQ6U,SAAW,GAClC,SAASF,EAAOjL,EAAK5D,GACE,WAAf,EAAO4D,KACP5D,EAAO4D,EACPA,OAAMd,GAEV9C,EAAOA,GAAQ,GACf,IASI2M,EATEqC,EAASL,EAAMM,IAAIrL,EAAK5D,EAAKkE,MAC7BL,EAASmL,EAAOnL,OAChBZ,EAAK+L,EAAO/L,GACZiB,EAAO8K,EAAO9K,KACdgL,EAAgBJ,EAAM7L,IAAOiB,KAAQ4K,EAAM7L,GAAN,KAsB3C,OArBsBjD,EAAKmP,UACvBnP,EAAK,0BACL,IAAUA,EAAKoP,WACfF,EAKAvC,EAAK,IAAIiC,EAAUnK,QAAQZ,EAAQ7D,IAG9B8O,EAAM7L,KAGP6L,EAAM7L,GAAM,IAAI2L,EAAUnK,QAAQZ,EAAQ7D,IAE9C2M,EAAKmC,EAAM7L,IAEX+L,EAAO/O,QAAUD,EAAKC,QACtBD,EAAKC,MAAQ+O,EAAO3K,UAEjBsI,EAAGxM,OAAO6O,EAAO9K,KAAMlE,GAElC9F,EAAQyS,GAAKkC,EAMb,IAAI1C,EAAqB7N,EAAQ,GACjClD,OAAOC,eAAenB,EAAS,WAAY,CAAEoB,YAAY,EAAMC,IAAK,WAAc,OAAO4Q,EAAmBxN,YAO5GzE,EAAQoS,QAAUuC,EAMlB,IAAIQ,EAAY/Q,EAAQ,GACxBlD,OAAOC,eAAenB,EAAS,UAAW,CAAEoB,YAAY,EAAMC,IAAK,WAAc,OAAO8T,EAAU5K,Y,6BCvElGrJ,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,IACtDzB,EAAQ+U,SAAM,EACd,IAAMK,EAAWhR,EAAQ,GAiEzBpE,EAAQ+U,IArDR,SAAarL,GAAqB,IAAhBM,EAAgB,uDAAT,GAAIqL,EAAK,uCAC1B5S,EAAMiH,EAEV2L,EAAMA,GAA4B,oBAAbnG,UAA4BA,SAC7C,MAAQxF,IACRA,EAAM2L,EAAI5Q,SAAW,KAAO4Q,EAAIzL,MAEjB,iBAARF,IACH,MAAQA,EAAIiI,OAAO,KAEfjI,EADA,MAAQA,EAAIiI,OAAO,GACb0D,EAAI5Q,SAAWiF,EAGf2L,EAAIzL,KAAOF,GAGpB,sBAAsB4L,KAAK5L,KAIxBA,OADA,IAAuB2L,EACjBA,EAAI5Q,SAAW,KAAOiF,EAGtB,WAAaA,GAM3BjH,EAAM2S,EAAS1L,IAGdjH,EAAI2M,OACD,cAAckG,KAAK7S,EAAIgC,UACvBhC,EAAI2M,KAAO,KAEN,eAAekG,KAAK7S,EAAIgC,YAC7BhC,EAAI2M,KAAO,QAGnB3M,EAAIuH,KAAOvH,EAAIuH,MAAQ,IACvB,IAAMuL,GAAkC,IAA3B9S,EAAImH,KAAKP,QAAQ,KACxBO,EAAO2L,EAAO,IAAM9S,EAAImH,KAAO,IAAMnH,EAAImH,KAS/C,OAPAnH,EAAIsG,GAAKtG,EAAIgC,SAAW,MAAQmF,EAAO,IAAMnH,EAAI2M,KAAOpF,EAExDvH,EAAI+S,KACA/S,EAAIgC,SACA,MACAmF,GACCyL,GAAOA,EAAIjG,OAAS3M,EAAI2M,KAAO,GAAK,IAAM3M,EAAI2M,MAChD3M,I,gBClEX,IAAMqL,EAAS1J,EAAQ,IAEvBnE,EAAOD,QAAU,SAAC0J,EAAK5D,GAAN,OAAe,IAAIgI,EAAOpE,EAAK5D,IAOhD7F,EAAOD,QAAQ8N,OAASA,EACxB7N,EAAOD,QAAQyE,SAAWqJ,EAAOrJ,SACjCxE,EAAOD,QAAQ6F,UAAYzB,EAAQ,GACnCnE,EAAOD,QAAQyV,WAAarR,EAAQ,GACpCnE,EAAOD,QAAQ4F,OAASxB,EAAQ,I,sgDCbhC,IAAMqR,EAAarR,EAAQ,GACrB5B,EAAU4B,EAAQ,GAGlBwB,EAASxB,EAAQ,GACjBgR,EAAWhR,EAAQ,GACnB2L,EAAU3L,EAAQ,GAElB0J,E,sQAQJ,WAAYpE,GAAgB,MAAX5D,EAAW,uDAAJ,GAAI,iBAC1B,eAEI4D,GAAO,WAAa,EAAOA,KAC7B5D,EAAO4D,EACPA,EAAM,MAGJA,GACFA,EAAM0L,EAAS1L,GACf5D,EAAKuJ,SAAW3F,EAAIE,KACpB9D,EAAKwJ,OAA0B,UAAjB5F,EAAIjF,UAAyC,QAAjBiF,EAAIjF,SAC9CqB,EAAKsJ,KAAO1F,EAAI0F,KACZ1F,EAAI3D,QAAOD,EAAKC,MAAQ2D,EAAI3D,QACvBD,EAAK8D,OACd9D,EAAKuJ,SAAW+F,EAAStP,EAAK8D,MAAMA,MAGtC,EAAK0F,OACH,MAAQxJ,EAAKwJ,OACTxJ,EAAKwJ,OACe,oBAAbJ,UAA4B,WAAaA,SAASzK,SAE3DqB,EAAKuJ,WAAavJ,EAAKsJ,OAEzBtJ,EAAKsJ,KAAO,EAAKE,OAAS,MAAQ,MAGpC,EAAKD,SACHvJ,EAAKuJ,WACgB,oBAAbH,SAA2BA,SAASG,SAAW,aACzD,EAAKD,KACHtJ,EAAKsJ,OACgB,oBAAbF,UAA4BA,SAASE,KACzCF,SAASE,KACT,EAAKE,OACL,IACA,IAEN,EAAKmG,WAAa3P,EAAK2P,YAAc,CAAC,UAAW,aACjD,EAAKzP,WAAa,GAClB,EAAK0P,YAAc,GACnB,EAAKC,cAAgB,EAErB,EAAK7P,KAAO,EACV,CACEkE,KAAM,aACN4L,OAAO,EACPC,iBAAiB,EACjBC,SAAS,EACT7G,OAAO,EACP2B,eAAgB,IAChBmF,iBAAiB,EACjBC,oBAAoB,EACpBC,kBAAmB,CACjBC,UAAW,MAEbC,iBAAkB,IAEpBrQ,GAGF,EAAKA,KAAKkE,KAAO,EAAKlE,KAAKkE,KAAKR,QAAQ,MAAO,IAAM,IAEtB,iBAApB,EAAK1D,KAAKC,QACnB,EAAKD,KAAKC,MAAQgK,EAAQ9I,OAAO,EAAKnB,KAAKC,QAI7C,EAAKgD,GAAK,KACV,EAAKqN,SAAW,KAChB,EAAKC,aAAe,KACpB,EAAKC,YAAc,KAGnB,EAAKC,iBAAmB,KAEQ,mBAArB3T,kBACTA,iBACE,gBACA,WACM,EAAK6Q,YAEP,EAAKA,UAAUnQ,qBACf,EAAKmQ,UAAUlG,YAGnB,GAIJ,EAAKxB,OA3FqB,E,qDAqGZhL,GAGd,IAAMgF,EA2jBV,SAAetD,GACb,IAAMxB,EAAI,GACV,IAAK,IAAIT,KAAKiC,EACRA,EAAIJ,eAAe7B,KACrBS,EAAET,GAAKiC,EAAIjC,IAGf,OAAOS,EAlkBSuV,CAAMpW,KAAK0F,KAAKC,OAG9BA,EAAM0Q,IAAM7Q,EAAOnB,SAGnBsB,EAAM0N,UAAY1S,EAGdX,KAAK2I,KAAIhD,EAAM+K,IAAM1Q,KAAK2I,IAE9B,IAAMjD,EAAO,EACX,GACA1F,KAAK0F,KAAKqQ,iBAAiBpV,GAC3BX,KAAK0F,KACL,CACEC,QACAE,OAAQ7F,KACRiP,SAAUjP,KAAKiP,SACfC,OAAQlP,KAAKkP,OACbF,KAAMhP,KAAKgP,OAOf,OAAO,IAAIqG,EAAW1U,GAAM+E,K,6BAS5B,IAAI2N,EACJ,GACErT,KAAK0F,KAAKiQ,iBACVjI,EAAO4I,wBACmC,IAA1CtW,KAAKqV,WAAWpM,QAAQ,aAExBoK,EAAY,gBACP,IAAI,IAAMrT,KAAKqV,WAAWjS,OAAQ,CAEvC,IAAMiC,EAAOrF,KAIb,YAHAkN,YAAW,WACT7H,EAAK7B,KAAK,QAAS,6BAClB,GAGH6P,EAAYrT,KAAKqV,WAAW,GAE9BrV,KAAK4F,WAAa,UAGlB,IACEyN,EAAYrT,KAAKuW,gBAAgBlD,GACjC,MAAOnK,GAKP,OAFAlJ,KAAKqV,WAAWmB,aAChBxW,KAAK2L,OAIP0H,EAAU1H,OACV3L,KAAKyW,aAAapD,K,mCAQPA,GAGX,IAAMhO,EAAOrF,KAETA,KAAKqT,WAGPrT,KAAKqT,UAAUnQ,qBAIjBlD,KAAKqT,UAAYA,EAGjBA,EACG9Q,GAAG,SAAS,WACX8C,EAAKqR,aAENnU,GAAG,UAAU,SAASqC,GACrBS,EAAKoB,SAAS7B,MAEfrC,GAAG,SAAS,SAAS2G,GACpB7D,EAAKsR,QAAQzN,MAEd3G,GAAG,SAAS,WACX8C,EAAKgB,QAAQ,wB,4BAUb1F,GAGJ,IAAI0S,EAAYrT,KAAKuW,gBAAgB5V,EAAM,CAAEiW,MAAO,IAChDC,GAAS,EACPxR,EAAOrF,KAIb,SAAS8W,IACP,GAAIzR,EAAK0R,mBAAoB,CAC3B,IAAMC,GACHhX,KAAKyQ,gBAAkBpL,EAAKgO,UAAU5C,eACzCoG,EAASA,GAAUG,EAEjBH,IAIJxD,EAAU4D,KAAK,CAAC,CAAE7R,KAAM,OAAQoB,KAAM,WACtC6M,EAAUxQ,KAAK,UAAU,SAASiD,GAChC,IAAI+Q,EACJ,GAAI,SAAW/Q,EAAIV,MAAQ,UAAYU,EAAIU,KAAM,CAK/C,GAFAnB,EAAK6R,WAAY,EACjB7R,EAAK7B,KAAK,YAAa6P,IAClBA,EAAW,OAChB3F,EAAO4I,sBAAwB,cAAgBjD,EAAU1S,KAIzD0E,EAAKgO,UAAUrD,OAAM,WACf6G,GACA,WAAaxR,EAAKO,aAItBmH,IAEA1H,EAAKoR,aAAapD,GAClBA,EAAU4D,KAAK,CAAC,CAAE7R,KAAM,aACxBC,EAAK7B,KAAK,UAAW6P,GACrBA,EAAY,KACZhO,EAAK6R,WAAY,EACjB7R,EAAK8R,gBAEF,CAGL,IAAMnR,EAAM,IAAIC,MAAM,eACtBD,EAAIqN,UAAYA,EAAU1S,KAC1B0E,EAAK7B,KAAK,eAAgBwC,QAKhC,SAASoR,IACHP,IAGJA,GAAS,EAET9J,IAEAsG,EAAUlG,QACVkG,EAAY,MAId,SAAS9F,EAAQvH,GACf,IAAMqR,EAAQ,IAAIpR,MAAM,gBAAkBD,GAC1CqR,EAAMhE,UAAYA,EAAU1S,KAE5ByW,IAKA/R,EAAK7B,KAAK,eAAgB6T,GAG5B,SAASC,IACP/J,EAAQ,oBAIV,SAASC,IACPD,EAAQ,iBAIV,SAASgK,EAAUC,GACbnE,GAAamE,EAAG7W,OAAS0S,EAAU1S,MAGrCyW,IAKJ,SAASrK,IACPsG,EAAUpQ,eAAe,OAAQ6T,GACjCzD,EAAUpQ,eAAe,QAASsK,GAClC8F,EAAUpQ,eAAe,QAASqU,GAClCjS,EAAKpC,eAAe,QAASuK,GAC7BnI,EAAKpC,eAAe,YAAasU,GAnGnC7J,EAAO4I,uBAAwB,EAsG/BjD,EAAUxQ,KAAK,OAAQiU,GACvBzD,EAAUxQ,KAAK,QAAS0K,GACxB8F,EAAUxQ,KAAK,QAASyU,GAExBtX,KAAK6C,KAAK,QAAS2K,GACnBxN,KAAK6C,KAAK,YAAa0U,GAEvBlE,EAAU1H,S,+BAkBV,GAPA3L,KAAK4F,WAAa,OAClB8H,EAAO4I,sBAAwB,cAAgBtW,KAAKqT,UAAU1S,KAC9DX,KAAKwD,KAAK,QACVxD,KAAKmX,QAKH,SAAWnX,KAAK4F,YAChB5F,KAAK0F,KAAKgQ,SACV1V,KAAKqT,UAAUrD,MAMf,IAFA,IAAI5P,EAAI,EACFC,EAAIL,KAAKgW,SAAS5S,OACjBhD,EAAIC,EAAGD,IACZJ,KAAK4W,MAAM5W,KAAKgW,SAAS5V,M,+BAUtBwE,GACP,GACE,YAAc5E,KAAK4F,YACnB,SAAW5F,KAAK4F,YAChB,YAAc5F,KAAK4F,WAUnB,OALA5F,KAAKwD,KAAK,SAAUoB,GAGpB5E,KAAKwD,KAAK,aAEFoB,EAAOQ,MACb,IAAK,OACHpF,KAAKyX,YAAYC,KAAKC,MAAM/S,EAAO4B,OACnC,MAEF,IAAK,OACHxG,KAAK4X,mBACL5X,KAAK6X,WAAW,QAChB7X,KAAKwD,KAAK,QACV,MAEF,IAAK,QACH,IAAMwC,EAAM,IAAIC,MAAM,gBACtBD,EAAI8R,KAAOlT,EAAO4B,KAClBxG,KAAK2W,QAAQ3Q,GACb,MAEF,IAAK,UACHhG,KAAKwD,KAAK,OAAQoB,EAAO4B,MACzBxG,KAAKwD,KAAK,UAAWoB,EAAO4B,S,kCAexBA,GACVxG,KAAKwD,KAAK,YAAagD,GACvBxG,KAAK2I,GAAKnC,EAAKkK,IACf1Q,KAAKqT,UAAU1N,MAAM+K,IAAMlK,EAAKkK,IAChC1Q,KAAKgW,SAAWhW,KAAK+X,eAAevR,EAAKwP,UACzChW,KAAKiW,aAAezP,EAAKyP,aACzBjW,KAAKkW,YAAc1P,EAAK0P,YACxBlW,KAAKoQ,SAED,WAAapQ,KAAK4F,YACtB5F,KAAK4X,qB,yCAQY,WACjBxK,aAAapN,KAAKmW,kBAClBnW,KAAKmW,iBAAmBjJ,YAAW,WACjC,EAAK7G,QAAQ,kBACZrG,KAAKiW,aAAejW,KAAKkW,e,gCAS5BlW,KAAKsV,YAAY/R,OAAO,EAAGvD,KAAKuV,eAKhCvV,KAAKuV,cAAgB,EAEjB,IAAMvV,KAAKsV,YAAYlS,OACzBpD,KAAKwD,KAAK,SAEVxD,KAAKmX,U,8BAWL,WAAanX,KAAK4F,YAClB5F,KAAKqT,UAAU9M,WACdvG,KAAKkX,WACNlX,KAAKsV,YAAYlS,SAIjBpD,KAAKqT,UAAU4D,KAAKjX,KAAKsV,aAGzBtV,KAAKuV,cAAgBvV,KAAKsV,YAAYlS,OACtCpD,KAAKwD,KAAK,Y,4BAaRsC,EAAKgI,EAASpL,GAElB,OADA1C,KAAK6X,WAAW,UAAW/R,EAAKgI,EAASpL,GAClC1C,O,2BAGJ8F,EAAKgI,EAASpL,GAEjB,OADA1C,KAAK6X,WAAW,UAAW/R,EAAKgI,EAASpL,GAClC1C,O,iCAYEoF,EAAMoB,EAAMsH,EAASpL,GAW9B,GAVI,mBAAsB8D,IACxB9D,EAAK8D,EACLA,OAAOgC,GAGL,mBAAsBsF,IACxBpL,EAAKoL,EACLA,EAAU,MAGR,YAAc9N,KAAK4F,YAAc,WAAa5F,KAAK4F,WAAvD,EAIAkI,EAAUA,GAAW,IACboF,UAAW,IAAUpF,EAAQoF,SAErC,IAAMtO,EAAS,CACbQ,KAAMA,EACNoB,KAAMA,EACNsH,QAASA,GAEX9N,KAAKwD,KAAK,eAAgBoB,GAC1B5E,KAAKsV,YAAY1S,KAAKgC,GAClBlC,GAAI1C,KAAK6C,KAAK,QAASH,GAC3B1C,KAAKmX,W,8BASL,IAAM9R,EAAOrF,KAoBb,SAASmN,IACP9H,EAAKgB,QAAQ,gBAGbhB,EAAKgO,UAAUlG,QAGjB,SAAS6K,IACP3S,EAAKpC,eAAe,UAAW+U,GAC/B3S,EAAKpC,eAAe,eAAgB+U,GACpC7K,IAGF,SAAS8K,IAEP5S,EAAKxC,KAAK,UAAWmV,GACrB3S,EAAKxC,KAAK,eAAgBmV,GAG5B,MArCI,YAAchY,KAAK4F,YAAc,SAAW5F,KAAK4F,aACnD5F,KAAK4F,WAAa,UAEd5F,KAAKsV,YAAYlS,OACnBpD,KAAK6C,KAAK,SAAS,WACb7C,KAAKkX,UACPe,IAEA9K,OAGKnN,KAAKkX,UACde,IAEA9K,KAuBGnN,O,8BAQDgG,GAGN0H,EAAO4I,uBAAwB,EAC/BtW,KAAKwD,KAAK,QAASwC,GACnBhG,KAAKqG,QAAQ,kBAAmBL,K,8BAQ1BiI,EAAQlI,GAEZ,YAAc/F,KAAK4F,YACnB,SAAW5F,KAAK4F,YAChB,YAAc5F,KAAK4F,aAOnBwH,aAAapN,KAAKkY,mBAClB9K,aAAapN,KAAKmW,kBAGlBnW,KAAKqT,UAAUnQ,mBAAmB,SAGlClD,KAAKqT,UAAUlG,QAGfnN,KAAKqT,UAAUnQ,qBAGflD,KAAK4F,WAAa,SAGlB5F,KAAK2I,GAAK,KAGV3I,KAAKwD,KAAK,QAASyK,EAAQlI,GAtBd/F,KA0BRsV,YAAc,GA1BNtV,KA2BRuV,cAAgB,K,qCAWVS,GAIb,IAHA,IAAMmC,EAAmB,GACrB/X,EAAI,EACFgY,EAAIpC,EAAS5S,OACZhD,EAAIgY,EAAGhY,KACPJ,KAAKqV,WAAWpM,QAAQ+M,EAAS5V,KACpC+X,EAAiBvV,KAAKoT,EAAS5V,IAEnC,OAAO+X,O,8BA7pBU/V,GAiqBrBsL,EAAO4I,uBAAwB,EAQ/B5I,EAAOrJ,SAAWmB,EAAOnB,SAYzBxE,EAAOD,QAAU8N,G,cCprBjB,IACE7N,EAAOD,QAAoC,oBAAnB0O,gBACtB,oBAAqB,IAAIA,eAC3B,MAAOtI,GAGPnG,EAAOD,SAAU,I,myDCbnB,IAAM0O,EAAiBtK,EAAQ,GACzB6L,EAAU7L,EAAQ,IAClB5B,EAAU4B,EAAQ,GAChBwN,EAASxN,EAAQ,IAAjBwN,KACFjC,EAAavL,EAAQ,GAS3B,SAASqU,KAET,IAAMC,EAEG,MADK,IAAIhK,EAAe,CAAEa,SAAS,IACvBoJ,aAGfhK,E,8BAOJ,WAAY7I,GAAM,MAGhB,GAHgB,UAChB,cAAMA,GAEkB,oBAAboJ,SAA0B,CACnC,IAAMC,EAAQ,WAAaD,SAASzK,SAChC2K,EAAOF,SAASE,KAGfA,IACHA,EAAOD,EAAQ,IAAM,IAGvB,EAAKJ,GACkB,oBAAbG,UACNpJ,EAAKuJ,WAAaH,SAASG,UAC7BD,IAAStJ,EAAKsJ,KAChB,EAAKJ,GAAKlJ,EAAKwJ,SAAWH,EAK5B,IAAMyJ,EAAc9S,GAAQA,EAAK8S,YArBjB,OAsBhB,EAAK/H,eAAiB6H,IAAYE,EAtBlB,E,4CA+BC,IAAX9S,EAAW,uDAAJ,GAEb,OADA,EAAcA,EAAM,CAAEiJ,GAAI3O,KAAK2O,GAAIC,GAAI5O,KAAK4O,IAAM5O,KAAK0F,MAChD,IAAI+S,EAAQzY,KAAKsJ,MAAO5D,K,8BAUzBc,EAAM9D,GACZ,IAAMgW,EAAM1Y,KAAK2Y,QAAQ,CACvBC,OAAQ,OACRpS,KAAMA,IAEFnB,EAAOrF,KACb0Y,EAAInW,GAAG,UAAWG,GAClBgW,EAAInW,GAAG,SAAS,SAASyD,GACvBX,EAAKsR,QAAQ,iBAAkB3Q,Q,+BAYjC,IAAM0S,EAAM1Y,KAAK2Y,UACXtT,EAAOrF,KACb0Y,EAAInW,GAAG,QAAQ,SAASiE,GACtBnB,EAAKwT,OAAOrS,MAEdkS,EAAInW,GAAG,SAAS,SAASyD,GACvBX,EAAKsR,QAAQ,iBAAkB3Q,MAEjChG,KAAK8Y,QAAUJ,M,GA9ED7I,GAkFZ4I,E,8BAOJ,WAAYnP,EAAK5D,GAAM,wBACrB,gBACKA,KAAOA,EAEZ,EAAKkT,OAASlT,EAAKkT,QAAU,MAC7B,EAAKtP,IAAMA,EACX,EAAKyP,OAAQ,IAAUrT,EAAKqT,MAC5B,EAAKvS,UAAOgC,IAAc9C,EAAKc,KAAOd,EAAKc,KAAO,KAElD,EAAK9E,SATgB,E,2CAkBrB,IAAMgE,EAAO8L,EACXxR,KAAK0F,KACL,QACA,aACA,MACA,MACA,aACA,OACA,KACA,UACA,sBAEFA,EAAKyJ,UAAYnP,KAAK0F,KAAKiJ,GAC3BjJ,EAAK0J,UAAYpP,KAAK0F,KAAKkJ,GAE3B,IAAMoK,EAAOhZ,KAAKgZ,IAAM,IAAI1K,EAAe5I,GACrCL,EAAOrF,KAEb,IAGEgZ,EAAIrN,KAAK3L,KAAK4Y,OAAQ5Y,KAAKsJ,IAAKtJ,KAAK+Y,OACrC,IACE,GAAI/Y,KAAK0F,KAAKuT,aAEZ,IAAK,IAAI7Y,KADT4Y,EAAIE,uBAAyBF,EAAIE,uBAAsB,GACzClZ,KAAK0F,KAAKuT,aAClBjZ,KAAK0F,KAAKuT,aAAahX,eAAe7B,IACxC4Y,EAAIG,iBAAiB/Y,EAAGJ,KAAK0F,KAAKuT,aAAa7Y,IAIrD,MAAO8I,IAET,GAAI,SAAWlJ,KAAK4Y,OAClB,IACEI,EAAIG,iBAAiB,eAAgB,4BACrC,MAAOjQ,IAGX,IACE8P,EAAIG,iBAAiB,SAAU,OAC/B,MAAOjQ,IAGL,oBAAqB8P,IACvBA,EAAIvD,gBAAkBzV,KAAK0F,KAAK+P,iBAG9BzV,KAAK0F,KAAK0T,iBACZJ,EAAI5N,QAAUpL,KAAK0F,KAAK0T,gBAGtBpZ,KAAKqZ,UACPL,EAAIM,OAAS,WACXjU,EAAKkU,UAEPP,EAAIzL,QAAU,WACZlI,EAAKsR,QAAQqC,EAAIQ,gBAGnBR,EAAIS,mBAAqB,WACnB,IAAMT,EAAIpT,aACV,MAAQoT,EAAIU,QAAU,OAASV,EAAIU,OACrCrU,EAAKkU,SAILrM,YAAW,WACT7H,EAAKsR,QAA8B,iBAAfqC,EAAIU,OAAsBV,EAAIU,OAAS,KAC1D,KAOTV,EAAI/B,KAAKjX,KAAKwG,MACd,MAAO0C,GAOP,YAHAgE,YAAW,WACT7H,EAAKsR,QAAQzN,KACZ,GAImB,oBAAbyQ,WACT3Z,KAAKmQ,MAAQsI,EAAQmB,gBACrBnB,EAAQoB,SAAS7Z,KAAKmQ,OAASnQ,Q,kCAUjCA,KAAKwD,KAAK,WACVxD,KAAK+M,Y,6BAQAvG,GACLxG,KAAKwD,KAAK,OAAQgD,GAClBxG,KAAK8Z,c,8BAQC9T,GACNhG,KAAKwD,KAAK,QAASwC,GACnBhG,KAAK+M,SAAQ,K,8BAQPgN,GACN,QAAI,IAAuB/Z,KAAKgZ,KAAO,OAAShZ,KAAKgZ,IAArD,CAUA,GANIhZ,KAAKqZ,SACPrZ,KAAKgZ,IAAIM,OAAStZ,KAAKgZ,IAAIzL,QAAU8K,EAErCrY,KAAKgZ,IAAIS,mBAAqBpB,EAG5B0B,EACF,IACE/Z,KAAKgZ,IAAIgB,QACT,MAAO9Q,IAGa,oBAAbyQ,iBACFlB,EAAQoB,SAAS7Z,KAAKmQ,OAG/BnQ,KAAKgZ,IAAM,Q,+BASX,IAAMxS,EAAOxG,KAAKgZ,IAAIQ,aACT,OAAThT,GACFxG,KAAK6Y,OAAOrS,K,+BAUd,MAAiC,oBAAnBiJ,iBAAmCzP,KAAK4O,IAAM5O,KAAKwP,a,8BASjExP,KAAK+M,c,GA5Ma3K,GAyNtB,GAHAqW,EAAQmB,cAAgB,EACxBnB,EAAQoB,SAAW,GAEK,oBAAbF,SACT,GAA2B,mBAAhBM,YACTA,YAAY,WAAYC,QACnB,GAAgC,mBAArB1X,iBAAiC,CAEjDA,iBADyB,eAAgB+M,EAAa,WAAa,SAChC2K,GAAe,GAItD,SAASA,IACP,IAAK,IAAI9Z,KAAKqY,EAAQoB,SAChBpB,EAAQoB,SAAS5X,eAAe7B,IAClCqY,EAAQoB,SAASzZ,GAAG4Z,QAK1Bna,EAAOD,QAAU2O,EACjB1O,EAAOD,QAAQ6Y,QAAUA,G,oBCnVjB7H,EAAiB5M,EAAQ,IAAzB4M,aAEFuJ,EACY,mBAATC,MACU,oBAATA,MACmC,6BAAzCtZ,OAAOkB,UAAUiG,SAAS1H,KAAK6Z,MAC7BC,EAA+C,mBAAhBC,YA8B/BC,EAAqB,SAAC/T,EAAMhC,GAChC,IAAMgW,EAAa,IAAIC,WAKvB,OAJAD,EAAWlB,OAAS,WAClB,IAAMoB,EAAUF,EAAWG,OAAOzV,MAAM,KAAK,GAC7CV,EAAS,IAAMkW,IAEVF,EAAWI,cAAcpU,IAGlC3G,EAAOD,QA9Bc,SAAC,EAAgB6Q,EAAgBjM,GAAa,IANpDnC,EAMS+C,EAA2C,EAA3CA,KAAMoB,EAAqC,EAArCA,KAC5B,OAAI2T,GAAkB3T,aAAgB4T,KAChC3J,EACKjM,EAASgC,GAET+T,EAAmB/T,EAAMhC,GAGlC6V,IACC7T,aAAgB8T,cAfNjY,EAe4BmE,EAdJ,mBAAvB8T,YAAYO,OACtBP,YAAYO,OAAOxY,GACnBA,GAAOA,EAAIyY,kBAAkBR,cAc3B7J,EACKjM,EAASgC,aAAgB8T,YAAc9T,EAAOA,EAAKsU,QAEnDP,EAAmB,IAAIH,KAAK,CAAC5T,IAAQhC,GAIzCA,EAASoM,EAAaxL,IAASoB,GAAQ,O,oBC7B5CuU,E,EAJ2C/W,EAAQ,IAA/C6M,E,EAAAA,qBAAsBC,E,EAAAA,aAEuB,mBAAhBwJ,cAInCS,EAAgB/W,EAAQ,KAG1B,IA4BMgX,EAAqB,SAACxU,EAAMvB,GAChC,GAAI8V,EAAe,CACjB,IAAM1S,EAAU0S,EAAclU,OAAOL,GACrC,OAAOyU,EAAU5S,EAASpD,GAE1B,MAAO,CAAEiW,QAAQ,EAAM1U,SAIrByU,EAAY,SAACzU,EAAMvB,GACvB,OAAQA,GACN,IAAK,OACH,OAAOuB,aAAgB8T,YAAc,IAAIF,KAAK,CAAC5T,IAASA,EAC1D,IAAK,cACL,QACE,OAAOA,IAIb3G,EAAOD,QA/Cc,SAACiF,EAAeI,GACnC,GAA6B,iBAAlBJ,EACT,MAAO,CACLO,KAAM,UACNoB,KAAMyU,EAAUpW,EAAeI,IAGnC,IAAMG,EAAOP,EAAc0M,OAAO,GAClC,MAAa,MAATnM,EACK,CACLA,KAAM,UACNoB,KAAMwU,EAAmBnW,EAAcsE,UAAU,GAAIlE,IAGtC4L,EAAqBzL,GAIjCP,EAAczB,OAAS,EAC1B,CACEgC,KAAMyL,EAAqBzL,GAC3BoB,KAAM3B,EAAcsE,UAAU,IAEhC,CACE/D,KAAMyL,EAAqBzL,IARxB0L,I,eClBX,SAAUqK,GACR,aAEAvb,EAAQ8G,OAAS,SAAS0U,GACxB,IACAhb,EADIib,EAAQ,IAAIC,WAAWF,GACxBzX,EAAM0X,EAAMjY,OAAQ8X,EAAS,GAEhC,IAAK9a,EAAI,EAAGA,EAAIuD,EAAKvD,GAAG,EACtB8a,GAAUC,EAAME,EAAMjb,IAAM,GAC5B8a,GAAUC,GAAmB,EAAXE,EAAMjb,KAAW,EAAMib,EAAMjb,EAAI,IAAM,GACzD8a,GAAUC,GAAuB,GAAfE,EAAMjb,EAAI,KAAY,EAAMib,EAAMjb,EAAI,IAAM,GAC9D8a,GAAUC,EAAqB,GAAfE,EAAMjb,EAAI,IAS5B,OANKuD,EAAM,GAAO,EAChBuX,EAASA,EAAO/R,UAAU,EAAG+R,EAAO9X,OAAS,GAAK,IACzCO,EAAM,GAAM,IACrBuX,EAASA,EAAO/R,UAAU,EAAG+R,EAAO9X,OAAS,GAAK,MAG7C8X,GAGTtb,EAAQiH,OAAU,SAASqU,GACzB,IACqB9a,EACrBmb,EAAUC,EAAUC,EAAUC,EAF1BC,EAA+B,IAAhBT,EAAO9X,OAC1BO,EAAMuX,EAAO9X,OAAWlB,EAAI,EAGM,MAA9BgZ,EAAOA,EAAO9X,OAAS,KACzBuY,IACkC,MAA9BT,EAAOA,EAAO9X,OAAS,IACzBuY,KAIJ,IAAIP,EAAc,IAAId,YAAYqB,GAClCN,EAAQ,IAAIC,WAAWF,GAEvB,IAAKhb,EAAI,EAAGA,EAAIuD,EAAKvD,GAAG,EACtBmb,EAAWJ,EAAMlS,QAAQiS,EAAO9a,IAChCob,EAAWL,EAAMlS,QAAQiS,EAAO9a,EAAE,IAClCqb,EAAWN,EAAMlS,QAAQiS,EAAO9a,EAAE,IAClCsb,EAAWP,EAAMlS,QAAQiS,EAAO9a,EAAE,IAElCib,EAAMnZ,KAAQqZ,GAAY,EAAMC,GAAY,EAC5CH,EAAMnZ,MAAoB,GAAXsZ,IAAkB,EAAMC,GAAY,EACnDJ,EAAMnZ,MAAoB,EAAXuZ,IAAiB,EAAiB,GAAXC,EAGxC,OAAON,GAjDX,CAmDG,qE,mgDC1DH,IAUI9X,EAVEuM,EAAU7L,EAAQ,IAClBuL,EAAavL,EAAQ,GAErB4X,EAAW,MACXC,EAAkB,OAQlBC,E,sQAOJ,WAAYpW,GAAM,O,4FAAA,UAChB,cAAMA,IAEDC,MAAQ,EAAKA,OAAS,GAItBrC,IAEHA,EAAYiM,EAAWwM,OAASxM,EAAWwM,QAAU,IAIvD,EAAK5L,MAAQ7M,EAAUF,OAGvB,IAAMiC,EAAO,EAAH,GAhBM,OAiBhB/B,EAAUV,MAAK,SAASkD,GACtBT,EAAKwT,OAAO/S,MAId,EAAKH,MAAMyS,EAAI,EAAKjI,MAtBJ,E,+CAsCZnQ,KAAKgc,SAEPhc,KAAKgc,OAAOzO,QAAU,aACtBvN,KAAKgc,OAAOC,WAAWC,YAAYlc,KAAKgc,QACxChc,KAAKgc,OAAS,MAGZhc,KAAKmc,OACPnc,KAAKmc,KAAKF,WAAWC,YAAYlc,KAAKmc,MACtCnc,KAAKmc,KAAO,KACZnc,KAAKoc,OAAS,MAGhB,8C,+BASA,IAAM/W,EAAOrF,KACPgc,EAASrC,SAAS0C,cAAc,UAElCrc,KAAKgc,SACPhc,KAAKgc,OAAOC,WAAWC,YAAYlc,KAAKgc,QACxChc,KAAKgc,OAAS,MAGhBA,EAAOjD,OAAQ,EACfiD,EAAOjT,IAAM/I,KAAKsJ,MAClB0S,EAAOzO,QAAU,SAASrE,GACxB7D,EAAKsR,QAAQ,mBAAoBzN,IAGnC,IAAMoT,EAAW3C,SAAS4C,qBAAqB,UAAU,GACrDD,EACFA,EAASL,WAAWO,aAAaR,EAAQM,IAExC3C,SAAS8C,MAAQ9C,SAAS+C,MAAMC,YAAYX,GAE/Chc,KAAKgc,OAASA,EAGZ,oBAAuBY,WAAa,SAAS1H,KAAK0H,UAAUC,YAG5D3P,YAAW,WACT,IAAMkP,EAASzC,SAAS0C,cAAc,UACtC1C,SAAS+C,KAAKC,YAAYP,GAC1BzC,SAAS+C,KAAKR,YAAYE,KACzB,O,8BAWC5V,EAAM9D,GACZ,IACI0Z,EADE/W,EAAOrF,KAGb,IAAKA,KAAKmc,KAAM,CACd,IAAMA,EAAOxC,SAAS0C,cAAc,QAC9BS,EAAOnD,SAAS0C,cAAc,YAC9B1T,EAAM3I,KAAK+c,SAAW,cAAgB/c,KAAKmQ,MAEjDgM,EAAKa,UAAY,WACjBb,EAAKc,MAAMC,SAAW,WACtBf,EAAKc,MAAME,IAAM,UACjBhB,EAAKc,MAAMG,KAAO,UAClBjB,EAAKkB,OAAS1U,EACdwT,EAAKvD,OAAS,OACduD,EAAKmB,aAAa,iBAAkB,SACpCR,EAAKnc,KAAO,IACZwb,EAAKQ,YAAYG,GACjBnD,SAAS+C,KAAKC,YAAYR,GAE1Bnc,KAAKmc,KAAOA,EACZnc,KAAK8c,KAAOA,EAKd,SAASS,IACPC,IACA9a,IAGF,SAAS8a,IACP,GAAInY,EAAK+W,OACP,IACE/W,EAAK8W,KAAKD,YAAY7W,EAAK+W,QAC3B,MAAOlT,GACP7D,EAAKsR,QAAQ,qCAAsCzN,GAIvD,IAEE,IAAMuU,EAAO,oCAAsCpY,EAAK0X,SAAW,KACnEX,EAASzC,SAAS0C,cAAcoB,GAChC,MAAOvU,IACPkT,EAASzC,SAAS0C,cAAc,WACzB1b,KAAO0E,EAAK0X,SACnBX,EAAOrT,IAAM,eAGfqT,EAAOzT,GAAKtD,EAAK0X,SAEjB1X,EAAK8W,KAAKQ,YAAYP,GACtB/W,EAAK+W,OAASA,EA7BhBpc,KAAKmc,KAAKuB,OAAS1d,KAAKsJ,MAgCxBkU,IAIAhX,EAAOA,EAAK4C,QAAQyS,EAAiB,QACrC7b,KAAK8c,KAAKzb,MAAQmF,EAAK4C,QAAQwS,EAAU,OAEzC,IACE5b,KAAKmc,KAAKwB,SACV,MAAOzU,IAELlJ,KAAKoc,OAAOnC,YACdja,KAAKoc,OAAO3C,mBAAqB,WACA,aAA3BpU,EAAK+W,OAAOxW,YACd2X,KAIJvd,KAAKoc,OAAO9C,OAASiE,I,qCAlJvB,OAAO,O,8BApCgB1N,GA2L3BhQ,EAAOD,QAAUkc,G,ytCCvMjB,IAAMrW,EAAYzB,EAAQ,GACpBwB,EAASxB,EAAQ,GACjB2L,EAAU3L,EAAQ,GAClB4L,EAAQ5L,EAAQ,IACdwN,EAASxN,EAAQ,IAAjBwN,K,EAKJxN,EAAQ,IAHV4Z,E,EAAAA,UACAC,E,EAAAA,sBACAC,E,EAAAA,kBAOIC,EACiB,oBAAdnB,WACsB,iBAAtBA,UAAUoB,SACmB,gBAApCpB,UAAUoB,QAAQC,cAEdC,E,sQAOJ,WAAYxY,GAAM,a,4FAAA,UAChB,cAAMA,IAED+K,gBAAkB/K,EAAK8S,YAHZ,E,8CAqBhB,GAAKxY,KAAKme,QAAV,CAKA,IAAM7U,EAAMtJ,KAAKsJ,MACX8U,EAAYpe,KAAK0F,KAAK0Y,UAGtB1Y,EAAOqY,EACT,GACAvM,EACExR,KAAK0F,KACL,QACA,oBACA,MACA,MACA,aACA,OACA,KACA,UACA,qBACA,eACA,kBACA,SACA,aACA,SACA,uBAGF1F,KAAK0F,KAAKuT,eACZvT,EAAK2Y,QAAUre,KAAK0F,KAAKuT,cAG3B,IACEjZ,KAAKse,GACHT,IAA0BE,EACtBK,EACE,IAAIR,EAAUtU,EAAK8U,GACnB,IAAIR,EAAUtU,GAChB,IAAIsU,EAAUtU,EAAK8U,EAAW1Y,GACpC,MAAOM,GACP,OAAOhG,KAAKwD,KAAK,QAASwC,GAG5BhG,KAAKse,GAAGrZ,WAAajF,KAAK6F,OAAOZ,YAAc6Y,EAE/C9d,KAAKue,uB,0CASL,IAAMlZ,EAAOrF,KAEbA,KAAKse,GAAGzR,OAAS,WACfxH,EAAK+K,UAEPpQ,KAAKse,GAAG9Q,QAAU,WAChBnI,EAAKgB,WAEPrG,KAAKse,GAAGE,UAAY,SAASvL,GAC3B5N,EAAKwT,OAAO5F,EAAGzM,OAEjBxG,KAAKse,GAAG/Q,QAAU,SAASrE,GACzB7D,EAAKsR,QAAQ,kBAAmBzN,M,4BAU9B3E,GACJ,IAAMc,EAAOrF,KACbA,KAAKuG,UAAW,EAOhB,IAHA,IAAI0J,EAAQ1L,EAAQnB,OAChBhD,EAAI,EACFC,EAAI4P,EACH7P,EAAIC,EAAGD,KACZ,SAAUwE,GACRY,EAAOzB,aAAaa,EAAQS,EAAKoL,gBAAgB,SAASjK,GAExD,IAAMd,EAAO,GACRmY,IACCjZ,EAAOkJ,UACTpI,EAAKwN,SAAWtO,EAAOkJ,QAAQoF,UAG7B7N,EAAKK,KAAKmQ,oBAEV,iBAAoBrP,EAChBiY,OAAOC,WAAWlY,GAClBA,EAAKpD,QACDiC,EAAKK,KAAKmQ,kBAAkBC,YACpCpQ,EAAKwN,UAAW,IAQtB,IACM2K,EAEFxY,EAAKiZ,GAAGrH,KAAKzQ,GAEbnB,EAAKiZ,GAAGrH,KAAKzQ,EAAMd,GAErB,MAAOwD,MAKP+G,IAMN5K,EAAK7B,KAAK,SAIV0J,YAAW,WACT7H,EAAKkB,UAAW,EAChBlB,EAAK7B,KAAK,WACT,OAhDH,CAqCGe,EAAQnE,M,gCAqBbqF,EAAUzD,UAAUqE,QAAQ9F,KAAKP,Q,qCASV,IAAZA,KAAKse,KACdte,KAAKse,GAAGnR,QACRnN,KAAKse,GAAK,Q,4BAUZ,IAAI3Y,EAAQ3F,KAAK2F,OAAS,GACpB2K,EAAStQ,KAAK0F,KAAKwJ,OAAS,MAAQ,KACtCF,EAAO,GA6BX,OAzBEhP,KAAK0F,KAAKsJ,OACR,QAAUsB,GAAqC,MAA3B3I,OAAO3H,KAAK0F,KAAKsJ,OACpC,OAASsB,GAAqC,KAA3B3I,OAAO3H,KAAK0F,KAAKsJ,SAEvCA,EAAO,IAAMhP,KAAK0F,KAAKsJ,MAIrBhP,KAAK0F,KAAK6K,oBACZ5K,EAAM3F,KAAK0F,KAAK8K,gBAAkBZ,KAI/B5P,KAAKyQ,iBACR9K,EAAMgL,IAAM,IAGdhL,EAAQgK,EAAQjJ,OAAOf,IAGbvC,SACRuC,EAAQ,IAAMA,GAKd2K,EACA,QAHgD,IAArCtQ,KAAK0F,KAAKuJ,SAAShG,QAAQ,KAI9B,IAAMjJ,KAAK0F,KAAKuJ,SAAW,IAAMjP,KAAK0F,KAAKuJ,UACnDD,EACAhP,KAAK0F,KAAKkE,KACVjE,I,8BAWF,SACIiY,GACA,iBAAkBA,GAAa5d,KAAKW,OAASud,EAAGlc,UAAUrB,Q,2BAlO9D,MAAO,iB,8BAnBM8E,GA0PjB5F,EAAOD,QAAUse,G,gBC9QjB,IAAM3O,EAAavL,EAAQ,GAE3BnE,EAAOD,QAAU,CACfge,UAAWrO,EAAWqO,WAAarO,EAAWoP,aAC9Cd,uBAAuB,EACvBC,kBAAmB,gB,gBCLrBle,EAAQ8G,OAAS1C,EAAQ,IACzBpE,EAAQiH,OAAS7C,EAAQ,K,kQCCzB,SAAS4a,EAAUC,EAAMC,EAAQnY,GAE/B,IADA,IAAIlG,EAAI,EACCL,EAAI,EAAGC,EAAIsG,EAAIvD,OAAQhD,EAAIC,EAAGD,KACrCK,EAAIkG,EAAIoY,WAAW3e,IACX,IACNye,EAAKG,SAASF,IAAUre,GAEjBA,EAAI,MACXoe,EAAKG,SAASF,IAAU,IAAQre,GAAK,GACrCoe,EAAKG,SAASF,IAAU,IAAY,GAAJre,IAEzBA,EAAI,OAAUA,GAAK,OAC1Boe,EAAKG,SAASF,IAAU,IAAQre,GAAK,IACrCoe,EAAKG,SAASF,IAAU,IAAQre,GAAK,EAAK,IAC1Coe,EAAKG,SAASF,IAAU,IAAY,GAAJre,KAGhCL,IACAK,EAAI,QAAiB,KAAJA,IAAc,GAA2B,KAApBkG,EAAIoY,WAAW3e,IACrDye,EAAKG,SAASF,IAAU,IAAQre,GAAK,IACrCoe,EAAKG,SAASF,IAAU,IAAQre,GAAK,GAAM,IAC3Coe,EAAKG,SAASF,IAAU,IAAQre,GAAK,EAAK,IAC1Coe,EAAKG,SAASF,IAAU,IAAY,GAAJre,IAwRtCZ,EAAOD,QAxCP,SAAgByB,GACd,IAAIga,EAAQ,GACR4D,EAAS,GACTC,EAzNN,SAASC,EAAQ9D,EAAO4D,EAAQ5d,GAC9B,IAAI+D,EAAO,EAAO/D,GAAOjB,EAAI,EAAGC,EAAI,EAAG+e,EAAK,EAAGC,EAAK,EAAGjc,EAAS,EAAG8b,EAAO,EAE1E,GAAa,WAAT9Z,EAAmB,CAIrB,IAHAhC,EAzBJ,SAAoBuD,GAElB,IADA,IAAIlG,EAAI,EAAG2C,EAAS,EACXhD,EAAI,EAAGC,EAAIsG,EAAIvD,OAAQhD,EAAIC,EAAGD,KACrCK,EAAIkG,EAAIoY,WAAW3e,IACX,IACNgD,GAAU,EAEH3C,EAAI,KACX2C,GAAU,EAEH3C,EAAI,OAAUA,GAAK,MAC1B2C,GAAU,GAGVhD,IACAgD,GAAU,GAGd,OAAOA,EAOIkc,CAAWje,IAGP,GACXga,EAAMzY,KAAc,IAATQ,GACX8b,EAAO,OAGJ,GAAI9b,EAAS,IAChBiY,EAAMzY,KAAK,IAAMQ,GACjB8b,EAAO,OAGJ,GAAI9b,EAAS,MAChBiY,EAAMzY,KAAK,IAAMQ,GAAU,EAAGA,GAC9B8b,EAAO,MAGJ,MAAI9b,EAAS,YAIhB,MAAM,IAAI6C,MAAM,mBAHhBoV,EAAMzY,KAAK,IAAMQ,GAAU,GAAIA,GAAU,GAAIA,GAAU,EAAGA,GAC1D8b,EAAO,EAKT,OADAD,EAAOrc,KAAK,CAAE2c,KAAMle,EAAOme,QAASpc,EAAQqc,QAASpE,EAAMjY,SACpD8b,EAAO9b,EAEhB,GAAa,WAATgC,EAIF,OAAIyC,KAAKC,MAAMzG,KAAWA,GAAUuG,SAASvG,GAMzCA,GAAS,EAEPA,EAAQ,KACVga,EAAMzY,KAAKvB,GACJ,GAGLA,EAAQ,KACVga,EAAMzY,KAAK,IAAMvB,GACV,GAGLA,EAAQ,OACVga,EAAMzY,KAAK,IAAMvB,GAAS,EAAGA,GACtB,GAGLA,EAAQ,YACVga,EAAMzY,KAAK,IAAMvB,GAAS,GAAIA,GAAS,GAAIA,GAAS,EAAGA,GAChD,IAGT+d,EAAM/d,EAAQwG,KAAK6X,IAAI,EAAG,KAAQ,EAClCL,EAAKhe,IAAU,EACfga,EAAMzY,KAAK,IAAMwc,GAAM,GAAIA,GAAM,GAAIA,GAAM,EAAGA,EAAIC,GAAM,GAAIA,GAAM,GAAIA,GAAM,EAAGA,GACxE,GAGHhe,IAAU,IACZga,EAAMzY,KAAKvB,GACJ,GAGLA,IAAU,KACZga,EAAMzY,KAAK,IAAMvB,GACV,GAGLA,IAAU,OACZga,EAAMzY,KAAK,IAAMvB,GAAS,EAAGA,GACtB,GAGLA,IAAU,YACZga,EAAMzY,KAAK,IAAMvB,GAAS,GAAIA,GAAS,GAAIA,GAAS,EAAGA,GAChD,IAGT+d,EAAKvX,KAAKC,MAAMzG,EAAQwG,KAAK6X,IAAI,EAAG,KACpCL,EAAKhe,IAAU,EACfga,EAAMzY,KAAK,IAAMwc,GAAM,GAAIA,GAAM,GAAIA,GAAM,EAAGA,EAAIC,GAAM,GAAIA,GAAM,GAAIA,GAAM,EAAGA,GACxE,IAxDPhE,EAAMzY,KAAK,KACXqc,EAAOrc,KAAK,CAAE+c,OAAQte,EAAOme,QAAS,EAAGC,QAASpE,EAAMjY,SACjD,GAyDX,GAAa,WAATgC,EAAmB,CAErB,GAAc,OAAV/D,EAEF,OADAga,EAAMzY,KAAK,KACJ,EAGT,GAAIc,MAAM+E,QAAQpH,GAAQ,CAIxB,IAHA+B,EAAS/B,EAAM+B,QAGF,GACXiY,EAAMzY,KAAc,IAATQ,GACX8b,EAAO,OAGJ,GAAI9b,EAAS,MAChBiY,EAAMzY,KAAK,IAAMQ,GAAU,EAAGA,GAC9B8b,EAAO,MAGJ,MAAI9b,EAAS,YAIhB,MAAM,IAAI6C,MAAM,mBAHhBoV,EAAMzY,KAAK,IAAMQ,GAAU,GAAIA,GAAU,GAAIA,GAAU,EAAGA,GAC1D8b,EAAO,EAIT,IAAK9e,EAAI,EAAGA,EAAIgD,EAAQhD,IACtB8e,GAAQC,EAAQ9D,EAAO4D,EAAQ5d,EAAMjB,IAEvC,OAAO8e,EAIT,GAAI7d,aAAiBiQ,KAAM,CACzB,IAAIsO,EAAOve,EAAMwe,UAIjB,OAHAT,EAAKvX,KAAKC,MAAM8X,EAAO/X,KAAK6X,IAAI,EAAG,KACnCL,EAAKO,IAAS,EACdvE,EAAMzY,KAAK,IAAM,EAAGwc,GAAM,GAAIA,GAAM,GAAIA,GAAM,EAAGA,EAAIC,GAAM,GAAIA,GAAM,GAAIA,GAAM,EAAGA,GAC3E,GAGT,GAAIhe,aAAiBiZ,YAAa,CAIhC,IAHAlX,EAAS/B,EAAMqd,YAGF,IACXrD,EAAMzY,KAAK,IAAMQ,GACjB8b,EAAO,OAGT,GAAI9b,EAAS,MACXiY,EAAMzY,KAAK,IAAMQ,GAAU,EAAGA,GAC9B8b,EAAO,MAGT,MAAI9b,EAAS,YAIX,MAAM,IAAI6C,MAAM,oBAHhBoV,EAAMzY,KAAK,IAAMQ,GAAU,GAAIA,GAAU,GAAIA,GAAU,EAAGA,GAC1D8b,EAAO,EAKT,OADAD,EAAOrc,KAAK,CAAEkd,KAAMze,EAAOme,QAASpc,EAAQqc,QAASpE,EAAMjY,SACpD8b,EAAO9b,EAGhB,GAA4B,mBAAjB/B,EAAM0e,OACf,OAAOZ,EAAQ9D,EAAO4D,EAAQ5d,EAAM0e,UAGtC,IAAIpS,EAAO,GAAIhM,EAAM,GAEjBqe,EAAUlf,OAAO6M,KAAKtM,GAC1B,IAAKjB,EAAI,EAAGC,EAAI2f,EAAQ5c,OAAQhD,EAAIC,EAAGD,IAEX,mBAAfiB,EADXM,EAAMqe,EAAQ5f,KAEZuN,EAAK/K,KAAKjB,GAMd,IAHAyB,EAASuK,EAAKvK,QAGD,GACXiY,EAAMzY,KAAc,IAATQ,GACX8b,EAAO,OAGJ,GAAI9b,EAAS,MAChBiY,EAAMzY,KAAK,IAAMQ,GAAU,EAAGA,GAC9B8b,EAAO,MAGJ,MAAI9b,EAAS,YAIhB,MAAM,IAAI6C,MAAM,oBAHhBoV,EAAMzY,KAAK,IAAMQ,GAAU,GAAIA,GAAU,GAAIA,GAAU,EAAGA,GAC1D8b,EAAO,EAKT,IAAK9e,EAAI,EAAGA,EAAIgD,EAAQhD,IAEtB8e,GAAQC,EAAQ9D,EAAO4D,EADvBtd,EAAMgM,EAAKvN,IAEX8e,GAAQC,EAAQ9D,EAAO4D,EAAQ5d,EAAMM,IAEvC,OAAOud,EAGT,GAAa,YAAT9Z,EAEF,OADAiW,EAAMzY,KAAKvB,EAAQ,IAAO,KACnB,EAGT,GAAa,cAAT+D,EAEF,OADAiW,EAAMzY,KAAK,IAAM,EAAG,GACb,EAET,MAAM,IAAIqD,MAAM,oBAMLkZ,CAAQ9D,EAAO4D,EAAQ5d,GAC9B4e,EAAM,IAAI3F,YAAY4E,GACtBL,EAAO,IAAIqB,SAASD,GAEpBE,EAAa,EACbC,EAAe,EACfC,GAAc,EACdpB,EAAO7b,OAAS,IAClBid,EAAapB,EAAO,GAAGQ,SAIzB,IADA,IAAIa,EAAOC,EAAc,EAAGzB,EAAS,EAC5B1e,EAAI,EAAGC,EAAIgb,EAAMjY,OAAQhD,EAAIC,EAAGD,IAEvC,GADAye,EAAKG,SAASoB,EAAehgB,EAAGib,EAAMjb,IAClCA,EAAI,IAAMigB,EAAd,CAIA,GAFAE,GADAD,EAAQrB,EAAOkB,IACKX,QACpBV,EAASsB,EAAeC,EACpBC,EAAMR,KAER,IADA,IAAIU,EAAM,IAAIlF,WAAWgF,EAAMR,MACtB1H,EAAI,EAAGA,EAAImI,EAAanI,IAC/ByG,EAAKG,SAASF,EAAS1G,EAAGoI,EAAIpI,SAEvBkI,EAAMf,KACfX,EAAUC,EAAMC,EAAQwB,EAAMf,WACJ/W,IAAjB8X,EAAMX,QACfd,EAAK4B,WAAW3B,EAAQwB,EAAMX,QAGhCS,GAAgBG,EACZtB,IAFJkB,KAGEE,EAAapB,EAAOkB,GAAYV,SAGpC,OAAOQ,I,6BC3ST,SAAS9X,EAAQ2S,GAEf,GADA9a,KAAKyf,QAAU,EACX3E,aAAkBR,YACpBta,KAAK0gB,QAAU5F,EACf9a,KAAK2gB,MAAQ,IAAIT,SAASlgB,KAAK0gB,aAC1B,KAAIpG,YAAYO,OAAOC,GAI5B,MAAM,IAAI7U,MAAM,oBAHhBjG,KAAK0gB,QAAU5F,EAAOA,OACtB9a,KAAK2gB,MAAQ,IAAIT,SAASlgB,KAAK0gB,QAAS5F,EAAO8F,WAAY9F,EAAO4D,aA+CtEvW,EAAQnG,UAAU6e,OAAS,SAAUzd,GAEnC,IADA,IAAI/B,EAAQ,IAAIqC,MAAMN,GACbhD,EAAI,EAAGA,EAAIgD,EAAQhD,IAC1BiB,EAAMjB,GAAKJ,KAAK8gB,SAElB,OAAOzf,GAGT8G,EAAQnG,UAAU+e,KAAO,SAAU3d,GAEjC,IADA,IAAc/B,EAAQ,GACbjB,EAAI,EAAGA,EAAIgD,EAAQhD,IAE1BiB,EADMrB,KAAK8gB,UACE9gB,KAAK8gB,SAEpB,OAAOzf,GAGT8G,EAAQnG,UAAUud,KAAO,SAAUnc,GACjC,IAAI/B,EA3DN,SAAkBwd,EAAMC,EAAQ1b,GAE9B,IADA,IAAI4d,EAAS,GAAIC,EAAM,EACd7gB,EAAI0e,EAAQoC,EAAMpC,EAAS1b,EAAQhD,EAAI8gB,EAAK9gB,IAAK,CACxD,IAAI+gB,EAAOtC,EAAKuC,SAAShhB,GACzB,GAAsB,IAAV,IAAP+gB,GAIL,GAAsB,MAAV,IAAPA,GAOL,GAAsB,MAAV,IAAPA,GAAL,CAQA,GAAsB,MAAV,IAAPA,GAaL,MAAM,IAAIlb,MAAM,gBAAkBkb,EAAKlZ,SAAS,MAZ9CgZ,GAAe,EAAPE,IAAgB,IACC,GAArBtC,EAAKuC,WAAWhhB,KAAc,IACT,GAArBye,EAAKuC,WAAWhhB,KAAc,GACT,GAArBye,EAAKuC,WAAWhhB,KAAc,IACvB,OACT6gB,GAAO,MACPD,GAAU7c,OAAOC,aAA4B,OAAd6c,IAAQ,IAA8B,OAAT,KAANA,KAEtDD,GAAU7c,OAAOC,aAAa6c,QAhBhCD,GAAU7c,OAAOC,cACN,GAAP+c,IAAgB,IACK,GAArBtC,EAAKuC,WAAWhhB,KAAc,GACT,GAArBye,EAAKuC,WAAWhhB,KAAc,QAVlC4gB,GAAU7c,OAAOC,cACN,GAAP+c,IAAgB,EACI,GAArBtC,EAAKuC,WAAWhhB,SANnB4gB,GAAU7c,OAAOC,aAAa+c,GAiClC,OAAOH,EAqBKK,CAASrhB,KAAK2gB,MAAO3gB,KAAKyf,QAASrc,GAE/C,OADApD,KAAKyf,SAAWrc,EACT/B,GAGT8G,EAAQnG,UAAU8d,KAAO,SAAU1c,GACjC,IAAI/B,EAAQrB,KAAK0gB,QAAQ9c,MAAM5D,KAAKyf,QAASzf,KAAKyf,QAAUrc,GAE5D,OADApD,KAAKyf,SAAWrc,EACT/B,GAGT8G,EAAQnG,UAAU8e,OAAS,WACzB,IACIzf,EADAigB,EAASthB,KAAK2gB,MAAMS,SAASphB,KAAKyf,WAC3Brc,EAAS,EAAGgC,EAAO,EAAGga,EAAK,EAAGC,EAAK,EAE9C,GAAIiC,EAAS,IAEX,OAAIA,EAAS,IACJA,EAGLA,EAAS,IACJthB,KAAK+gB,KAAc,GAATO,GAGfA,EAAS,IACJthB,KAAK6gB,OAAgB,GAATS,GAGdthB,KAAKuf,KAAc,GAAT+B,GAInB,GAAIA,EAAS,IACX,OAA8B,GAAtB,IAAOA,EAAS,GAG1B,OAAQA,GAEN,KAAK,IACH,OAAO,KAET,KAAK,IACH,OAAO,EAET,KAAK,IACH,OAAO,EAGT,KAAK,IAGH,OAFAle,EAASpD,KAAK2gB,MAAMS,SAASphB,KAAKyf,SAClCzf,KAAKyf,SAAW,EACTzf,KAAK8f,KAAK1c,GACnB,KAAK,IAGH,OAFAA,EAASpD,KAAK2gB,MAAMY,UAAUvhB,KAAKyf,SACnCzf,KAAKyf,SAAW,EACTzf,KAAK8f,KAAK1c,GACnB,KAAK,IAGH,OAFAA,EAASpD,KAAK2gB,MAAMa,UAAUxhB,KAAKyf,SACnCzf,KAAKyf,SAAW,EACTzf,KAAK8f,KAAK1c,GAGnB,KAAK,IAIH,OAHAA,EAASpD,KAAK2gB,MAAMS,SAASphB,KAAKyf,SAClCra,EAAOpF,KAAK2gB,MAAMc,QAAQzhB,KAAKyf,QAAU,GACzCzf,KAAKyf,SAAW,EACT,CAACra,EAAMpF,KAAK8f,KAAK1c,IAC1B,KAAK,IAIH,OAHAA,EAASpD,KAAK2gB,MAAMY,UAAUvhB,KAAKyf,SACnCra,EAAOpF,KAAK2gB,MAAMc,QAAQzhB,KAAKyf,QAAU,GACzCzf,KAAKyf,SAAW,EACT,CAACra,EAAMpF,KAAK8f,KAAK1c,IAC1B,KAAK,IAIH,OAHAA,EAASpD,KAAK2gB,MAAMa,UAAUxhB,KAAKyf,SACnCra,EAAOpF,KAAK2gB,MAAMc,QAAQzhB,KAAKyf,QAAU,GACzCzf,KAAKyf,SAAW,EACT,CAACra,EAAMpF,KAAK8f,KAAK1c,IAG1B,KAAK,IAGH,OAFA/B,EAAQrB,KAAK2gB,MAAMe,WAAW1hB,KAAKyf,SACnCzf,KAAKyf,SAAW,EACTpe,EACT,KAAK,IAGH,OAFAA,EAAQrB,KAAK2gB,MAAMgB,WAAW3hB,KAAKyf,SACnCzf,KAAKyf,SAAW,EACTpe,EAGT,KAAK,IAGH,OAFAA,EAAQrB,KAAK2gB,MAAMS,SAASphB,KAAKyf,SACjCzf,KAAKyf,SAAW,EACTpe,EACT,KAAK,IAGH,OAFAA,EAAQrB,KAAK2gB,MAAMY,UAAUvhB,KAAKyf,SAClCzf,KAAKyf,SAAW,EACTpe,EACT,KAAK,IAGH,OAFAA,EAAQrB,KAAK2gB,MAAMa,UAAUxhB,KAAKyf,SAClCzf,KAAKyf,SAAW,EACTpe,EACT,KAAK,IAIH,OAHA+d,EAAKpf,KAAK2gB,MAAMa,UAAUxhB,KAAKyf,SAAW5X,KAAK6X,IAAI,EAAG,IACtDL,EAAKrf,KAAK2gB,MAAMa,UAAUxhB,KAAKyf,QAAU,GACzCzf,KAAKyf,SAAW,EACTL,EAAKC,EAGd,KAAK,IAGH,OAFAhe,EAAQrB,KAAK2gB,MAAMc,QAAQzhB,KAAKyf,SAChCzf,KAAKyf,SAAW,EACTpe,EACT,KAAK,IAGH,OAFAA,EAAQrB,KAAK2gB,MAAMiB,SAAS5hB,KAAKyf,SACjCzf,KAAKyf,SAAW,EACTpe,EACT,KAAK,IAGH,OAFAA,EAAQrB,KAAK2gB,MAAMkB,SAAS7hB,KAAKyf,SACjCzf,KAAKyf,SAAW,EACTpe,EACT,KAAK,IAIH,OAHA+d,EAAKpf,KAAK2gB,MAAMkB,SAAS7hB,KAAKyf,SAAW5X,KAAK6X,IAAI,EAAG,IACrDL,EAAKrf,KAAK2gB,MAAMa,UAAUxhB,KAAKyf,QAAU,GACzCzf,KAAKyf,SAAW,EACTL,EAAKC,EAGd,KAAK,IAGH,OAFAja,EAAOpF,KAAK2gB,MAAMc,QAAQzhB,KAAKyf,SAC/Bzf,KAAKyf,SAAW,EACH,IAATra,OACFpF,KAAKyf,SAAW,GAGX,CAACra,EAAMpF,KAAK8f,KAAK,IAC1B,KAAK,IAGH,OAFA1a,EAAOpF,KAAK2gB,MAAMc,QAAQzhB,KAAKyf,SAC/Bzf,KAAKyf,SAAW,EACT,CAACra,EAAMpF,KAAK8f,KAAK,IAC1B,KAAK,IAGH,OAFA1a,EAAOpF,KAAK2gB,MAAMc,QAAQzhB,KAAKyf,SAC/Bzf,KAAKyf,SAAW,EACT,CAACra,EAAMpF,KAAK8f,KAAK,IAC1B,KAAK,IAGH,OAFA1a,EAAOpF,KAAK2gB,MAAMc,QAAQzhB,KAAKyf,SAC/Bzf,KAAKyf,SAAW,EACH,IAATra,GACFga,EAAKpf,KAAK2gB,MAAMkB,SAAS7hB,KAAKyf,SAAW5X,KAAK6X,IAAI,EAAG,IACrDL,EAAKrf,KAAK2gB,MAAMa,UAAUxhB,KAAKyf,QAAU,GACzCzf,KAAKyf,SAAW,EACT,IAAInO,KAAK8N,EAAKC,IAEhB,CAACja,EAAMpF,KAAK8f,KAAK,IAC1B,KAAK,IAGH,OAFA1a,EAAOpF,KAAK2gB,MAAMc,QAAQzhB,KAAKyf,SAC/Bzf,KAAKyf,SAAW,EACT,CAACra,EAAMpF,KAAK8f,KAAK,KAG1B,KAAK,IAGH,OAFA1c,EAASpD,KAAK2gB,MAAMS,SAASphB,KAAKyf,SAClCzf,KAAKyf,SAAW,EACTzf,KAAKuf,KAAKnc,GACnB,KAAK,IAGH,OAFAA,EAASpD,KAAK2gB,MAAMY,UAAUvhB,KAAKyf,SACnCzf,KAAKyf,SAAW,EACTzf,KAAKuf,KAAKnc,GACnB,KAAK,IAGH,OAFAA,EAASpD,KAAK2gB,MAAMa,UAAUxhB,KAAKyf,SACnCzf,KAAKyf,SAAW,EACTzf,KAAKuf,KAAKnc,GAGnB,KAAK,IAGH,OAFAA,EAASpD,KAAK2gB,MAAMY,UAAUvhB,KAAKyf,SACnCzf,KAAKyf,SAAW,EACTzf,KAAK6gB,OAAOzd,GACrB,KAAK,IAGH,OAFAA,EAASpD,KAAK2gB,MAAMa,UAAUxhB,KAAKyf,SACnCzf,KAAKyf,SAAW,EACTzf,KAAK6gB,OAAOzd,GAGrB,KAAK,IAGH,OAFAA,EAASpD,KAAK2gB,MAAMY,UAAUvhB,KAAKyf,SACnCzf,KAAKyf,SAAW,EACTzf,KAAK+gB,KAAK3d,GACnB,KAAK,IAGH,OAFAA,EAASpD,KAAK2gB,MAAMa,UAAUxhB,KAAKyf,SACnCzf,KAAKyf,SAAW,EACTzf,KAAK+gB,KAAK3d,GAGrB,MAAM,IAAI6C,MAAM,oBAYlBpG,EAAOD,QATP,SAAgBkb,GACd,IAAItP,EAAU,IAAIrD,EAAQ2S,GACtBzZ,EAAQmK,EAAQsV,SACpB,GAAItV,EAAQiU,UAAY3E,EAAO4D,WAC7B,MAAM,IAAIzY,MAAO6U,EAAO4D,WAAalT,EAAQiU,QAAW,mBAE1D,OAAOpe,I,cClQT,SAASkJ,EAAQ7E,GACfA,EAAOA,GAAQ,GACf1F,KAAK8hB,GAAKpc,EAAKuF,KAAO,IACtBjL,KAAKkL,IAAMxF,EAAKwF,KAAO,IACvBlL,KAAK+hB,OAASrc,EAAKqc,QAAU,EAC7B/hB,KAAKmL,OAASzF,EAAKyF,OAAS,GAAKzF,EAAKyF,QAAU,EAAIzF,EAAKyF,OAAS,EAClEnL,KAAKwM,SAAW,EApBlB3M,EAAOD,QAAU2K,EA8BjBA,EAAQvI,UAAUmM,SAAW,WAC3B,IAAI2T,EAAK9hB,KAAK8hB,GAAKja,KAAK6X,IAAI1f,KAAK+hB,OAAQ/hB,KAAKwM,YAC9C,GAAIxM,KAAKmL,OAAQ,CACf,IAAI6W,EAAQna,KAAKoa,SACbC,EAAYra,KAAKC,MAAMka,EAAOhiB,KAAKmL,OAAS2W,GAChDA,EAAoC,IAAN,EAAxBja,KAAKC,MAAa,GAAPka,IAAwBF,EAAKI,EAAYJ,EAAKI,EAEjE,OAAgC,EAAzBra,KAAKoD,IAAI6W,EAAI9hB,KAAKkL,MAS3BX,EAAQvI,UAAUgM,MAAQ,WACxBhO,KAAKwM,SAAW,GASlBjC,EAAQvI,UAAUiK,OAAS,SAAShB,GAClCjL,KAAK8hB,GAAK7W,GASZV,EAAQvI,UAAUqK,OAAS,SAASnB,GAClClL,KAAKkL,IAAMA,GASbX,EAAQvI,UAAUmK,UAAY,SAAShB,GACrCnL,KAAKmL,OAASA", "file": "socket.io.msgpack.min.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"io\"] = factory();\n\telse\n\t\troot[\"io\"] = factory();\n})(this, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 16);\n", "\r\n/**\r\n * Expose `Emitter`.\r\n */\r\n\r\nif (typeof module !== 'undefined') {\r\n  module.exports = Emitter;\r\n}\r\n\r\n/**\r\n * Initialize a new `Emitter`.\r\n *\r\n * @api public\r\n */\r\n\r\nfunction Emitter(obj) {\r\n  if (obj) return mixin(obj);\r\n};\r\n\r\n/**\r\n * Mixin the emitter properties.\r\n *\r\n * @param {Object} obj\r\n * @return {Object}\r\n * @api private\r\n */\r\n\r\nfunction mixin(obj) {\r\n  for (var key in Emitter.prototype) {\r\n    obj[key] = Emitter.prototype[key];\r\n  }\r\n  return obj;\r\n}\r\n\r\n/**\r\n * Listen on the given `event` with `fn`.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.on =\r\nEmitter.prototype.addEventListener = function(event, fn){\r\n  this._callbacks = this._callbacks || {};\r\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\r\n    .push(fn);\r\n  return this;\r\n};\r\n\r\n/**\r\n * Adds an `event` listener that will be invoked a single\r\n * time then automatically removed.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.once = function(event, fn){\r\n  function on() {\r\n    this.off(event, on);\r\n    fn.apply(this, arguments);\r\n  }\r\n\r\n  on.fn = fn;\r\n  this.on(event, on);\r\n  return this;\r\n};\r\n\r\n/**\r\n * Remove the given callback for `event` or all\r\n * registered callbacks.\r\n *\r\n * @param {String} event\r\n * @param {Function} fn\r\n * @return {Emitter}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.off =\r\nEmitter.prototype.removeListener =\r\nEmitter.prototype.removeAllListeners =\r\nEmitter.prototype.removeEventListener = function(event, fn){\r\n  this._callbacks = this._callbacks || {};\r\n\r\n  // all\r\n  if (0 == arguments.length) {\r\n    this._callbacks = {};\r\n    return this;\r\n  }\r\n\r\n  // specific event\r\n  var callbacks = this._callbacks['$' + event];\r\n  if (!callbacks) return this;\r\n\r\n  // remove all handlers\r\n  if (1 == arguments.length) {\r\n    delete this._callbacks['$' + event];\r\n    return this;\r\n  }\r\n\r\n  // remove specific handler\r\n  var cb;\r\n  for (var i = 0; i < callbacks.length; i++) {\r\n    cb = callbacks[i];\r\n    if (cb === fn || cb.fn === fn) {\r\n      callbacks.splice(i, 1);\r\n      break;\r\n    }\r\n  }\r\n\r\n  // Remove event specific arrays for event types that no\r\n  // one is subscribed for to avoid memory leak.\r\n  if (callbacks.length === 0) {\r\n    delete this._callbacks['$' + event];\r\n  }\r\n\r\n  return this;\r\n};\r\n\r\n/**\r\n * Emit `event` with the given args.\r\n *\r\n * @param {String} event\r\n * @param {Mixed} ...\r\n * @return {Emitter}\r\n */\r\n\r\nEmitter.prototype.emit = function(event){\r\n  this._callbacks = this._callbacks || {};\r\n\r\n  var args = new Array(arguments.length - 1)\r\n    , callbacks = this._callbacks['$' + event];\r\n\r\n  for (var i = 1; i < arguments.length; i++) {\r\n    args[i - 1] = arguments[i];\r\n  }\r\n\r\n  if (callbacks) {\r\n    callbacks = callbacks.slice(0);\r\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\r\n      callbacks[i].apply(this, args);\r\n    }\r\n  }\r\n\r\n  return this;\r\n};\r\n\r\n/**\r\n * Return array of callbacks for `event`.\r\n *\r\n * @param {String} event\r\n * @return {Array}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.listeners = function(event){\r\n  this._callbacks = this._callbacks || {};\r\n  return this._callbacks['$' + event] || [];\r\n};\r\n\r\n/**\r\n * Check if this emitter has `event` handlers.\r\n *\r\n * @param {String} event\r\n * @return {Boolean}\r\n * @api public\r\n */\r\n\r\nEmitter.prototype.hasListeners = function(event){\r\n  return !! this.listeners(event).length;\r\n};\r\n", "const encodePacket = require(\"./encodePacket\");\nconst decodePacket = require(\"./decodePacket\");\n\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\n\nconst encodePayload = (packets, callback) => {\n  // some packets may be added to the array while encoding, so the initial length must be saved\n  const length = packets.length;\n  const encodedPackets = new Array(length);\n  let count = 0;\n\n  packets.forEach((packet, i) => {\n    // force base64 encoding for binary packets\n    encodePacket(packet, false, encodedPacket => {\n      encodedPackets[i] = encodedPacket;\n      if (++count === length) {\n        callback(encodedPackets.join(SEPARATOR));\n      }\n    });\n  });\n};\n\nconst decodePayload = (encodedPayload, binaryType) => {\n  const encodedPackets = encodedPayload.split(SEPARATOR);\n  const packets = [];\n  for (let i = 0; i < encodedPackets.length; i++) {\n    const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n    packets.push(decodedPacket);\n    if (decodedPacket.type === \"error\") {\n      break;\n    }\n  }\n  return packets;\n};\n\nmodule.exports = {\n  protocol: 4,\n  encodePacket,\n  encodePayload,\n  decodePacket,\n  decodePayload\n};\n", "module.exports = (() => {\n  if (typeof self !== \"undefined\") {\n    return self;\n  } else if (typeof window !== \"undefined\") {\n    return window;\n  } else {\n    return Function(\"return this\")();\n  }\n})();\n", "const parser = require(\"engine.io-parser\");\nconst Emitter = require(\"component-emitter\");\n\nclass Transport extends Emitter {\n  /**\n   * Transport abstract constructor.\n   *\n   * @param {Object} options.\n   * @api private\n   */\n  constructor(opts) {\n    super();\n\n    this.opts = opts;\n    this.query = opts.query;\n    this.readyState = \"\";\n    this.socket = opts.socket;\n  }\n\n  /**\n   * Emits an error.\n   *\n   * @param {String} str\n   * @return {Transport} for chaining\n   * @api public\n   */\n  onError(msg, desc) {\n    const err = new Error(msg);\n    err.type = \"TransportError\";\n    err.description = desc;\n    this.emit(\"error\", err);\n    return this;\n  }\n\n  /**\n   * Opens the transport.\n   *\n   * @api public\n   */\n  open() {\n    if (\"closed\" === this.readyState || \"\" === this.readyState) {\n      this.readyState = \"opening\";\n      this.doOpen();\n    }\n\n    return this;\n  }\n\n  /**\n   * Closes the transport.\n   *\n   * @api private\n   */\n  close() {\n    if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n      this.doClose();\n      this.onClose();\n    }\n\n    return this;\n  }\n\n  /**\n   * Sends multiple packets.\n   *\n   * @param {Array} packets\n   * @api private\n   */\n  send(packets) {\n    if (\"open\" === this.readyState) {\n      this.write(packets);\n    } else {\n      throw new Error(\"Transport not open\");\n    }\n  }\n\n  /**\n   * Called upon open\n   *\n   * @api private\n   */\n  onOpen() {\n    this.readyState = \"open\";\n    this.writable = true;\n    this.emit(\"open\");\n  }\n\n  /**\n   * Called with data.\n   *\n   * @param {String} data\n   * @api private\n   */\n  onData(data) {\n    const packet = parser.decodePacket(data, this.socket.binaryType);\n    this.onPacket(packet);\n  }\n\n  /**\n   * Called with a decoded packet.\n   */\n  onPacket(packet) {\n    this.emit(\"packet\", packet);\n  }\n\n  /**\n   * Called upon close.\n   *\n   * @api private\n   */\n  onClose() {\n    this.readyState = \"closed\";\n    this.emit(\"close\");\n  }\n}\n\nmodule.exports = Transport;\n", "/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\n\nexports.encode = function (obj) {\n  var str = '';\n\n  for (var i in obj) {\n    if (obj.hasOwnProperty(i)) {\n      if (str.length) str += '&';\n      str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n    }\n  }\n\n  return str;\n};\n\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\n\nexports.decode = function(qs){\n  var qry = {};\n  var pairs = qs.split('&');\n  for (var i = 0, l = pairs.length; i < l; i++) {\n    var pair = pairs[i].split('=');\n    qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n  }\n  return qry;\n};\n", "var msgpack = require(\"notepack.io\");\nvar Emitter = require(\"component-emitter\");\n\nexports.protocol = 5;\n\n/**\n * Packet types (see https://github.com/socketio/socket.io-protocol)\n */\n\nvar PacketType = (exports.PacketType = {\n  CONNECT: 0,\n  DISCONNECT: 1,\n  EVENT: 2,\n  ACK: 3,\n  CONNECT_ERROR: 4,\n});\n\nvar isInteger =\n  Number.isInteger ||\n  function (value) {\n    return (\n      typeof value === \"number\" &&\n      isFinite(value) &&\n      Math.floor(value) === value\n    );\n  };\n\nvar isString = function (value) {\n  return typeof value === \"string\";\n};\n\nvar isObject = function (value) {\n  return Object.prototype.toString.call(value) === \"[object Object]\";\n};\n\nfunction Encoder() {}\n\nEncoder.prototype.encode = function (packet) {\n  return [msgpack.encode(packet)];\n};\n\nfunction Decoder() {}\n\nEmitter(Decoder.prototype);\n\nDecoder.prototype.add = function (obj) {\n  var decoded = msgpack.decode(obj);\n  this.checkPacket(decoded);\n  this.emit(\"decoded\", decoded);\n};\n\nfunction isDataValid(decoded) {\n  switch (decoded.type) {\n    case PacketType.CONNECT:\n      return decoded.data === undefined || isObject(decoded.data);\n    case PacketType.DISCONNECT:\n      return decoded.data === undefined;\n    case PacketType.CONNECT_ERROR:\n      return isString(decoded.data) || isObject(decoded.data);\n    default:\n      return Array.isArray(decoded.data);\n  }\n}\n\nDecoder.prototype.checkPacket = function (decoded) {\n  var isTypeValid =\n    isInteger(decoded.type) &&\n    decoded.type >= PacketType.CONNECT &&\n    decoded.type <= PacketType.CONNECT_ERROR;\n  if (!isTypeValid) {\n    throw new Error(\"invalid packet type\");\n  }\n\n  if (!isString(decoded.nsp)) {\n    throw new Error(\"invalid namespace\");\n  }\n\n  if (!isDataValid(decoded)) {\n    throw new Error(\"invalid payload\");\n  }\n\n  var isAckValid = decoded.id === undefined || isInteger(decoded.id);\n  if (!isAckValid) {\n    throw new Error(\"invalid packet id\");\n  }\n};\n\nDecoder.prototype.destroy = function () {};\n\nexports.Encoder = Encoder;\nexports.Decoder = Decoder;\n", "/**\n * Parses an URI\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\n\nvar re = /^(?:(?![^:@]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\n\nvar parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\n\nmodule.exports = function parseuri(str) {\n    var src = str,\n        b = str.indexOf('['),\n        e = str.indexOf(']');\n\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n\n    var m = re.exec(str || ''),\n        uri = {},\n        i = 14;\n\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n\n    return uri;\n};\n\nfunction pathNames(obj, path) {\n    var regx = /\\/{2,9}/g,\n        names = path.replace(regx, \"/\").split(\"/\");\n\n    if (path.substr(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.substr(path.length - 1, 1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n\n    return names;\n}\n\nfunction queryKey(uri, query) {\n    var data = {};\n\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n\n    return data;\n}\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Manager = void 0;\nconst eio = require(\"engine.io-client\");\nconst socket_1 = require(\"./socket\");\nconst Emitter = require(\"component-emitter\");\nconst parser = require(\"socket.io-parser\");\nconst on_1 = require(\"./on\");\nconst Backoff = require(\"backo2\");\n\n\nclass Manager extends Emitter {\n    constructor(uri, opts) {\n        super();\n        this.nsps = {};\n        this.subs = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = undefined;\n        }\n        opts = opts || {};\n        opts.path = opts.path || \"/socket.io\";\n        this.opts = opts;\n        this.reconnection(opts.reconnection !== false);\n        this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n        this.reconnectionDelay(opts.reconnectionDelay || 1000);\n        this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n        this.randomizationFactor(opts.randomizationFactor || 0.5);\n        this.backoff = new Backoff({\n            min: this.reconnectionDelay(),\n            max: this.reconnectionDelayMax(),\n            jitter: this.randomizationFactor(),\n        });\n        this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n        this._readyState = \"closed\";\n        this.uri = uri;\n        const _parser = opts.parser || parser;\n        this.encoder = new _parser.Encoder();\n        this.decoder = new _parser.Decoder();\n        this._autoConnect = opts.autoConnect !== false;\n        if (this._autoConnect)\n            this.open();\n    }\n    reconnection(v) {\n        if (!arguments.length)\n            return this._reconnection;\n        this._reconnection = !!v;\n        return this;\n    }\n    reconnectionAttempts(v) {\n        if (v === undefined)\n            return this._reconnectionAttempts;\n        this._reconnectionAttempts = v;\n        return this;\n    }\n    reconnectionDelay(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelay;\n        this._reconnectionDelay = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMin(v);\n        return this;\n    }\n    randomizationFactor(v) {\n        var _a;\n        if (v === undefined)\n            return this._randomizationFactor;\n        this._randomizationFactor = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setJitter(v);\n        return this;\n    }\n    reconnectionDelayMax(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelayMax;\n        this._reconnectionDelayMax = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMax(v);\n        return this;\n    }\n    timeout(v) {\n        if (!arguments.length)\n            return this._timeout;\n        this._timeout = v;\n        return this;\n    }\n    /**\n     * Starts trying to reconnect if reconnection is enabled and we have not\n     * started reconnecting yet\n     *\n     * @private\n     */\n    maybeReconnectOnOpen() {\n        // Only try to reconnect if it's the first time we're connecting\n        if (!this._reconnecting &&\n            this._reconnection &&\n            this.backoff.attempts === 0) {\n            // keeps reconnection from firing twice for the same reconnection loop\n            this.reconnect();\n        }\n    }\n    /**\n     * Sets the current transport `socket`.\n     *\n     * @param {Function} fn - optional, callback\n     * @return self\n     * @public\n     */\n    open(fn) {\n\n\n        if (~this._readyState.indexOf(\"open\"))\n            return this;\n\n\n        this.engine = eio(this.uri, this.opts);\n        const socket = this.engine;\n        const self = this;\n        this._readyState = \"opening\";\n        this.skipReconnect = false;\n        // emit `open`\n        const openSubDestroy = on_1.on(socket, \"open\", function () {\n            self.onopen();\n            fn && fn();\n        });\n        // emit `error`\n        const errorSub = on_1.on(socket, \"error\", (err) => {\n\n\n            self.cleanup();\n            self._readyState = \"closed\";\n            super.emit(\"error\", err);\n            if (fn) {\n                fn(err);\n            }\n            else {\n                // Only do this if there is no fn to handle the error\n                self.maybeReconnectOnOpen();\n            }\n        });\n        if (false !== this._timeout) {\n            const timeout = this._timeout;\n\n\n            if (timeout === 0) {\n                openSubDestroy(); // prevents a race condition with the 'open' event\n            }\n            // set timer\n            const timer = setTimeout(() => {\n\n\n                openSubDestroy();\n                socket.close();\n                socket.emit(\"error\", new Error(\"timeout\"));\n            }, timeout);\n            this.subs.push(function subDestroy() {\n                clearTimeout(timer);\n            });\n        }\n        this.subs.push(openSubDestroy);\n        this.subs.push(errorSub);\n        return this;\n    }\n    /**\n     * Alias for open()\n     *\n     * @return self\n     * @public\n     */\n    connect(fn) {\n        return this.open(fn);\n    }\n    /**\n     * Called upon transport open.\n     *\n     * @private\n     */\n    onopen() {\n\n\n        // clear old subs\n        this.cleanup();\n        // mark as open\n        this._readyState = \"open\";\n        super.emit(\"open\");\n        // add new subs\n        const socket = this.engine;\n        this.subs.push(on_1.on(socket, \"ping\", this.onping.bind(this)), on_1.on(socket, \"data\", this.ondata.bind(this)), on_1.on(socket, \"error\", this.onerror.bind(this)), on_1.on(socket, \"close\", this.onclose.bind(this)), on_1.on(this.decoder, \"decoded\", this.ondecoded.bind(this)));\n    }\n    /**\n     * Called upon a ping.\n     *\n     * @private\n     */\n    onping() {\n        super.emit(\"ping\");\n    }\n    /**\n     * Called with data.\n     *\n     * @private\n     */\n    ondata(data) {\n        this.decoder.add(data);\n    }\n    /**\n     * Called when parser fully decodes a packet.\n     *\n     * @private\n     */\n    ondecoded(packet) {\n        super.emit(\"packet\", packet);\n    }\n    /**\n     * Called upon socket error.\n     *\n     * @private\n     */\n    onerror(err) {\n\n\n        super.emit(\"error\", err);\n    }\n    /**\n     * Creates a new socket for the given `nsp`.\n     *\n     * @return {Socket}\n     * @public\n     */\n    socket(nsp, opts) {\n        let socket = this.nsps[nsp];\n        if (!socket) {\n            socket = new socket_1.Socket(this, nsp, opts);\n            this.nsps[nsp] = socket;\n        }\n        return socket;\n    }\n    /**\n     * Called upon a socket close.\n     *\n     * @param socket\n     * @private\n     */\n    _destroy(socket) {\n        const nsps = Object.keys(this.nsps);\n        for (const nsp of nsps) {\n            const socket = this.nsps[nsp];\n            if (socket.active) {\n\n\n                return;\n            }\n        }\n        this._close();\n    }\n    /**\n     * Writes a packet.\n     *\n     * @param packet\n     * @private\n     */\n    _packet(packet) {\n\n\n        const encodedPackets = this.encoder.encode(packet);\n        for (let i = 0; i < encodedPackets.length; i++) {\n            this.engine.write(encodedPackets[i], packet.options);\n        }\n    }\n    /**\n     * Clean up transport subscriptions and packet buffer.\n     *\n     * @private\n     */\n    cleanup() {\n\n\n        this.subs.forEach((subDestroy) => subDestroy());\n        this.subs.length = 0;\n        this.decoder.destroy();\n    }\n    /**\n     * Close the current socket.\n     *\n     * @private\n     */\n    _close() {\n\n\n        this.skipReconnect = true;\n        this._reconnecting = false;\n        if (\"opening\" === this._readyState) {\n            // `onclose` will not fire because\n            // an open event never happened\n            this.cleanup();\n        }\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        if (this.engine)\n            this.engine.close();\n    }\n    /**\n     * Alias for close()\n     *\n     * @private\n     */\n    disconnect() {\n        return this._close();\n    }\n    /**\n     * Called upon engine close.\n     *\n     * @private\n     */\n    onclose(reason) {\n\n\n        this.cleanup();\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        super.emit(\"close\", reason);\n        if (this._reconnection && !this.skipReconnect) {\n            this.reconnect();\n        }\n    }\n    /**\n     * Attempt a reconnection.\n     *\n     * @private\n     */\n    reconnect() {\n        if (this._reconnecting || this.skipReconnect)\n            return this;\n        const self = this;\n        if (this.backoff.attempts >= this._reconnectionAttempts) {\n\n\n            this.backoff.reset();\n            super.emit(\"reconnect_failed\");\n            this._reconnecting = false;\n        }\n        else {\n            const delay = this.backoff.duration();\n\n\n            this._reconnecting = true;\n            const timer = setTimeout(() => {\n                if (self.skipReconnect)\n                    return;\n\n\n                super.emit(\"reconnect_attempt\", self.backoff.attempts);\n                // check again for the case socket closed in above events\n                if (self.skipReconnect)\n                    return;\n                self.open((err) => {\n                    if (err) {\n\n\n                        self._reconnecting = false;\n                        self.reconnect();\n                        super.emit(\"reconnect_error\", err);\n                    }\n                    else {\n\n\n                        self.onreconnect();\n                    }\n                });\n            }, delay);\n            this.subs.push(function subDestroy() {\n                clearTimeout(timer);\n            });\n        }\n    }\n    /**\n     * Called upon successful reconnect.\n     *\n     * @private\n     */\n    onreconnect() {\n        const attempt = this.backoff.attempts;\n        this._reconnecting = false;\n        this.backoff.reset();\n        super.emit(\"reconnect\", attempt);\n    }\n}\nexports.Manager = Manager;\n", "const XMLHttpRequest = require(\"xmlhttprequest-ssl\");\nconst XHR = require(\"./polling-xhr\");\nconst JSONP = require(\"./polling-jsonp\");\nconst websocket = require(\"./websocket\");\n\nexports.polling = polling;\nexports.websocket = websocket;\n\n/**\n * Polling transport polymorphic constructor.\n * Decides on xhr vs jsonp based on feature detection.\n *\n * @api private\n */\n\nfunction polling(opts) {\n  let xhr;\n  let xd = false;\n  let xs = false;\n  const jsonp = false !== opts.jsonp;\n\n  if (typeof location !== \"undefined\") {\n    const isSSL = \"https:\" === location.protocol;\n    let port = location.port;\n\n    // some user agents have empty `location.port`\n    if (!port) {\n      port = isSSL ? 443 : 80;\n    }\n\n    xd = opts.hostname !== location.hostname || port !== opts.port;\n    xs = opts.secure !== isSSL;\n  }\n\n  opts.xdomain = xd;\n  opts.xscheme = xs;\n  xhr = new XMLHttpRequest(opts);\n\n  if (\"open\" in xhr && !opts.forceJSONP) {\n    return new XHR(opts);\n  } else {\n    if (!jsonp) throw new Error(\"JSONP disabled\");\n    return new JSONP(opts);\n  }\n}\n", "// browser shim for xmlhttprequest module\n\nconst hasCORS = require(\"has-cors\");\nconst globalThis = require(\"./globalThis\");\n\nmodule.exports = function(opts) {\n  const xdomain = opts.xdomain;\n\n  // scheme must be same when usign XDomainRequest\n  // http://blogs.msdn.com/b/ieinternals/archive/2010/05/13/xdomainrequest-restrictions-limitations-and-workarounds.aspx\n  const xscheme = opts.xscheme;\n\n  // XDomainRequest has a flow of not sending cookie, therefore it should be disabled as a default.\n  // https://github.com/Automattic/engine.io-client/pull/217\n  const enablesXDR = opts.enablesXDR;\n\n  // XMLHttpRequest can be disabled on IE\n  try {\n    if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n      return new XMLHttpRequest();\n    }\n  } catch (e) {}\n\n  // Use XDomainRequest for IE8 if enablesXDR is true\n  // because loading bar keeps flashing when using jsonp-polling\n  // https://github.com/yujiosaka/socke.io-ie8-loading-example\n  try {\n    if (\"undefined\" !== typeof XDomainRequest && !xscheme && enablesXDR) {\n      return new XDomainRequest();\n    }\n  } catch (e) {}\n\n  if (!xdomain) {\n    try {\n      return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\n        \"Microsoft.XMLHTTP\"\n      );\n    } catch (e) {}\n  }\n};\n", "const Transport = require(\"../transport\");\nconst parseqs = require(\"parseqs\");\nconst parser = require(\"engine.io-parser\");\nconst yeast = require(\"yeast\");\n\n\n\n\nclass Polling extends Transport {\n  /**\n   * Transport name.\n   */\n  get name() {\n    return \"polling\";\n  }\n\n  /**\n   * Opens the socket (triggers polling). We write a PING message to determine\n   * when the transport is open.\n   *\n   * @api private\n   */\n  doOpen() {\n    this.poll();\n  }\n\n  /**\n   * Pauses polling.\n   *\n   * @param {Function} callback upon buffers are flushed and transport is paused\n   * @api private\n   */\n  pause(onPause) {\n    const self = this;\n\n    this.readyState = \"pausing\";\n\n    function pause() {\n\n\n      self.readyState = \"paused\";\n      onPause();\n    }\n\n    if (this.polling || !this.writable) {\n      let total = 0;\n\n      if (this.polling) {\n\n\n        total++;\n        this.once(\"pollComplete\", function() {\n\n\n          --total || pause();\n        });\n      }\n\n      if (!this.writable) {\n\n\n        total++;\n        this.once(\"drain\", function() {\n\n\n          --total || pause();\n        });\n      }\n    } else {\n      pause();\n    }\n  }\n\n  /**\n   * Starts polling cycle.\n   *\n   * @api public\n   */\n  poll() {\n\n\n    this.polling = true;\n    this.doPoll();\n    this.emit(\"poll\");\n  }\n\n  /**\n   * Overloads onData to detect payloads.\n   *\n   * @api private\n   */\n  onData(data) {\n    const self = this;\n\n\n    const callback = function(packet, index, total) {\n      // if its the first message we consider the transport open\n      if (\"opening\" === self.readyState && packet.type === \"open\") {\n        self.onOpen();\n      }\n\n      // if its a close packet, we close the ongoing requests\n      if (\"close\" === packet.type) {\n        self.onClose();\n        return false;\n      }\n\n      // otherwise bypass onData and handle the message\n      self.onPacket(packet);\n    };\n\n    // decode payload\n    parser.decodePayload(data, this.socket.binaryType).forEach(callback);\n\n    // if an event did not trigger closing\n    if (\"closed\" !== this.readyState) {\n      // if we got data we're not polling\n      this.polling = false;\n      this.emit(\"pollComplete\");\n\n      if (\"open\" === this.readyState) {\n        this.poll();\n      } else {\n\n\n      }\n    }\n  }\n\n  /**\n   * For polling, send a close packet.\n   *\n   * @api private\n   */\n  doClose() {\n    const self = this;\n\n    function close() {\n\n\n      self.write([{ type: \"close\" }]);\n    }\n\n    if (\"open\" === this.readyState) {\n\n\n      close();\n    } else {\n      // in case we're trying to close while\n      // handshaking is in progress (GH-164)\n\n\n      this.once(\"open\", close);\n    }\n  }\n\n  /**\n   * Writes a packets payload.\n   *\n   * @param {Array} data packets\n   * @param {Function} drain callback\n   * @api private\n   */\n  write(packets) {\n    this.writable = false;\n\n    parser.encodePayload(packets, data => {\n      this.doWrite(data, () => {\n        this.writable = true;\n        this.emit(\"drain\");\n      });\n    });\n  }\n\n  /**\n   * Generates uri for connection.\n   *\n   * @api private\n   */\n  uri() {\n    let query = this.query || {};\n    const schema = this.opts.secure ? \"https\" : \"http\";\n    let port = \"\";\n\n    // cache busting is forced\n    if (false !== this.opts.timestampRequests) {\n      query[this.opts.timestampParam] = yeast();\n    }\n\n    if (!this.supportsBinary && !query.sid) {\n      query.b64 = 1;\n    }\n\n    query = parseqs.encode(query);\n\n    // avoid port if default for schema\n    if (\n      this.opts.port &&\n      ((\"https\" === schema && Number(this.opts.port) !== 443) ||\n        (\"http\" === schema && Number(this.opts.port) !== 80))\n    ) {\n      port = \":\" + this.opts.port;\n    }\n\n    // prepend ? to query\n    if (query.length) {\n      query = \"?\" + query;\n    }\n\n    const ipv6 = this.opts.hostname.indexOf(\":\") !== -1;\n    return (\n      schema +\n      \"://\" +\n      (ipv6 ? \"[\" + this.opts.hostname + \"]\" : this.opts.hostname) +\n      port +\n      this.opts.path +\n      query\n    );\n  }\n}\n\nmodule.exports = Polling;\n", "const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\n\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach(key => {\n  PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\n\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\n\nmodule.exports = {\n  PACKET_TYPES,\n  PACKET_TYPES_REVERSE,\n  ERROR_PACKET\n};\n", "'use strict';\n\nvar alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split('')\n  , length = 64\n  , map = {}\n  , seed = 0\n  , i = 0\n  , prev;\n\n/**\n * Return a string representing the specified number.\n *\n * @param {Number} num The number to convert.\n * @returns {String} The string representation of the number.\n * @api public\n */\nfunction encode(num) {\n  var encoded = '';\n\n  do {\n    encoded = alphabet[num % length] + encoded;\n    num = Math.floor(num / length);\n  } while (num > 0);\n\n  return encoded;\n}\n\n/**\n * Return the integer value specified by the given string.\n *\n * @param {String} str The string to convert.\n * @returns {Number} The integer value represented by the string.\n * @api public\n */\nfunction decode(str) {\n  var decoded = 0;\n\n  for (i = 0; i < str.length; i++) {\n    decoded = decoded * length + map[str.charAt(i)];\n  }\n\n  return decoded;\n}\n\n/**\n * Yeast: A tiny growing id generator.\n *\n * @returns {String} A unique id.\n * @api public\n */\nfunction yeast() {\n  var now = encode(+new Date());\n\n  if (now !== prev) return seed = 0, prev = now;\n  return now +'.'+ encode(seed++);\n}\n\n//\n// Map each character to its index.\n//\nfor (; i < length; i++) map[alphabet[i]] = i;\n\n//\n// Expose the `yeast`, `encode` and `decode` functions.\n//\nyeast.encode = encode;\nyeast.decode = decode;\nmodule.exports = yeast;\n", "module.exports.pick = (obj, ...attr) => {\n  return attr.reduce((acc, k) => {\n    if (obj.hasOwnProperty(k)) {\n      acc[k] = obj[k];\n    }\n    return acc;\n  }, {});\n};\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Socket = void 0;\nconst socket_io_parser_1 = require(\"socket.io-parser\");\nconst Emitter = require(\"component-emitter\");\nconst on_1 = require(\"./on\");\n\n\n/**\n * Internal events.\n * These events can't be emitted by the user.\n */\nconst RESERVED_EVENTS = Object.freeze({\n    connect: 1,\n    connect_error: 1,\n    disconnect: 1,\n    disconnecting: 1,\n    // EventEmitter reserved events: https://nodejs.org/api/events.html#events_event_newlistener\n    newListener: 1,\n    removeListener: 1,\n});\nclass Socket extends Emitter {\n    /**\n     * `Socket` constructor.\n     *\n     * @public\n     */\n    constructor(io, nsp, opts) {\n        super();\n        this.receiveBuffer = [];\n        this.sendBuffer = [];\n        this.ids = 0;\n        this.acks = {};\n        this.flags = {};\n        this.io = io;\n        this.nsp = nsp;\n        this.ids = 0;\n        this.acks = {};\n        this.receiveBuffer = [];\n        this.sendBuffer = [];\n        this.connected = false;\n        this.disconnected = true;\n        this.flags = {};\n        if (opts && opts.auth) {\n            this.auth = opts.auth;\n        }\n        if (this.io._autoConnect)\n            this.open();\n    }\n    /**\n     * Subscribe to open, close and packet events\n     *\n     * @private\n     */\n    subEvents() {\n        if (this.subs)\n            return;\n        const io = this.io;\n        this.subs = [\n            on_1.on(io, \"open\", this.onopen.bind(this)),\n            on_1.on(io, \"packet\", this.onpacket.bind(this)),\n            on_1.on(io, \"error\", this.onerror.bind(this)),\n            on_1.on(io, \"close\", this.onclose.bind(this)),\n        ];\n    }\n    /**\n     * Whether the Socket will try to reconnect when its Manager connects or reconnects\n     */\n    get active() {\n        return !!this.subs;\n    }\n    /**\n     * \"Opens\" the socket.\n     *\n     * @public\n     */\n    connect() {\n        if (this.connected)\n            return this;\n        this.subEvents();\n        if (!this.io[\"_reconnecting\"])\n            this.io.open(); // ensure open\n        if (\"open\" === this.io._readyState)\n            this.onopen();\n        return this;\n    }\n    /**\n     * Alias for connect()\n     */\n    open() {\n        return this.connect();\n    }\n    /**\n     * Sends a `message` event.\n     *\n     * @return self\n     * @public\n     */\n    send(...args) {\n        args.unshift(\"message\");\n        this.emit.apply(this, args);\n        return this;\n    }\n    /**\n     * Override `emit`.\n     * If the event is in `events`, it's emitted normally.\n     *\n     * @param ev - event name\n     * @return self\n     * @public\n     */\n    emit(ev, ...args) {\n        if (RESERVED_EVENTS.hasOwnProperty(ev)) {\n            throw new Error('\"' + ev + '\" is a reserved event name');\n        }\n        args.unshift(ev);\n        const packet = {\n            type: socket_io_parser_1.PacketType.EVENT,\n            data: args,\n        };\n        packet.options = {};\n        packet.options.compress = this.flags.compress !== false;\n        // event ack callback\n        if (\"function\" === typeof args[args.length - 1]) {\n\n\n            this.acks[this.ids] = args.pop();\n            packet.id = this.ids++;\n        }\n        const isTransportWritable = this.io.engine &&\n            this.io.engine.transport &&\n            this.io.engine.transport.writable;\n        const discardPacket = this.flags.volatile && (!isTransportWritable || !this.connected);\n        if (discardPacket) {\n\n\n        }\n        else if (this.connected) {\n            this.packet(packet);\n        }\n        else {\n            this.sendBuffer.push(packet);\n        }\n        this.flags = {};\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param packet\n     * @private\n     */\n    packet(packet) {\n        packet.nsp = this.nsp;\n        this.io._packet(packet);\n    }\n    /**\n     * Called upon engine `open`.\n     *\n     * @private\n     */\n    onopen() {\n\n\n        if (typeof this.auth == \"function\") {\n            this.auth((data) => {\n                this.packet({ type: socket_io_parser_1.PacketType.CONNECT, data });\n            });\n        }\n        else {\n            this.packet({ type: socket_io_parser_1.PacketType.CONNECT, data: this.auth });\n        }\n    }\n    /**\n     * Called upon engine or manager `error`.\n     *\n     * @param err\n     * @private\n     */\n    onerror(err) {\n        if (!this.connected) {\n            super.emit(\"connect_error\", err);\n        }\n    }\n    /**\n     * Called upon engine `close`.\n     *\n     * @param reason\n     * @private\n     */\n    onclose(reason) {\n\n\n        this.connected = false;\n        this.disconnected = true;\n        delete this.id;\n        super.emit(\"disconnect\", reason);\n    }\n    /**\n     * Called with socket packet.\n     *\n     * @param packet\n     * @private\n     */\n    onpacket(packet) {\n        const sameNamespace = packet.nsp === this.nsp;\n        if (!sameNamespace)\n            return;\n        switch (packet.type) {\n            case socket_io_parser_1.PacketType.CONNECT:\n                if (packet.data && packet.data.sid) {\n                    const id = packet.data.sid;\n                    this.onconnect(id);\n                }\n                else {\n                    super.emit(\"connect_error\", new Error(\"It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)\"));\n                }\n                break;\n            case socket_io_parser_1.PacketType.EVENT:\n                this.onevent(packet);\n                break;\n            case socket_io_parser_1.PacketType.BINARY_EVENT:\n                this.onevent(packet);\n                break;\n            case socket_io_parser_1.PacketType.ACK:\n                this.onack(packet);\n                break;\n            case socket_io_parser_1.PacketType.BINARY_ACK:\n                this.onack(packet);\n                break;\n            case socket_io_parser_1.PacketType.DISCONNECT:\n                this.ondisconnect();\n                break;\n            case socket_io_parser_1.PacketType.CONNECT_ERROR:\n                const err = new Error(packet.data.message);\n                // @ts-ignore\n                err.data = packet.data.data;\n                super.emit(\"connect_error\", err);\n                break;\n        }\n    }\n    /**\n     * Called upon a server event.\n     *\n     * @param packet\n     * @private\n     */\n    onevent(packet) {\n        const args = packet.data || [];\n\n\n        if (null != packet.id) {\n\n\n            args.push(this.ack(packet.id));\n        }\n        if (this.connected) {\n            this.emitEvent(args);\n        }\n        else {\n            this.receiveBuffer.push(Object.freeze(args));\n        }\n    }\n    emitEvent(args) {\n        if (this._anyListeners && this._anyListeners.length) {\n            const listeners = this._anyListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, args);\n            }\n        }\n        super.emit.apply(this, args);\n    }\n    /**\n     * Produces an ack callback to emit with an event.\n     *\n     * @private\n     */\n    ack(id) {\n        const self = this;\n        let sent = false;\n        return function (...args) {\n            // prevent double callbacks\n            if (sent)\n                return;\n            sent = true;\n\n\n            self.packet({\n                type: socket_io_parser_1.PacketType.ACK,\n                id: id,\n                data: args,\n            });\n        };\n    }\n    /**\n     * Called upon a server acknowlegement.\n     *\n     * @param packet\n     * @private\n     */\n    onack(packet) {\n        const ack = this.acks[packet.id];\n        if (\"function\" === typeof ack) {\n\n\n            ack.apply(this, packet.data);\n            delete this.acks[packet.id];\n        }\n        else {\n\n\n        }\n    }\n    /**\n     * Called upon server connect.\n     *\n     * @private\n     */\n    onconnect(id) {\n\n\n        this.id = id;\n        this.connected = true;\n        this.disconnected = false;\n        super.emit(\"connect\");\n        this.emitBuffered();\n    }\n    /**\n     * Emit buffered events (received and emitted).\n     *\n     * @private\n     */\n    emitBuffered() {\n        this.receiveBuffer.forEach((args) => this.emitEvent(args));\n        this.receiveBuffer = [];\n        this.sendBuffer.forEach((packet) => this.packet(packet));\n        this.sendBuffer = [];\n    }\n    /**\n     * Called upon server disconnect.\n     *\n     * @private\n     */\n    ondisconnect() {\n\n\n        this.destroy();\n        this.onclose(\"io server disconnect\");\n    }\n    /**\n     * Called upon forced client/server side disconnections,\n     * this method ensures the manager stops tracking us and\n     * that reconnections don't get triggered for this.\n     *\n     * @private\n     */\n    destroy() {\n        if (this.subs) {\n            // clean subscriptions to avoid reconnections\n            this.subs.forEach((subDestroy) => subDestroy());\n            this.subs = undefined;\n        }\n        this.io[\"_destroy\"](this);\n    }\n    /**\n     * Disconnects the socket manually.\n     *\n     * @return self\n     * @public\n     */\n    disconnect() {\n        if (this.connected) {\n\n\n            this.packet({ type: socket_io_parser_1.PacketType.DISCONNECT });\n        }\n        // remove socket from pool\n        this.destroy();\n        if (this.connected) {\n            // fire events\n            this.onclose(\"io client disconnect\");\n        }\n        return this;\n    }\n    /**\n     * Alias for disconnect()\n     *\n     * @return self\n     * @public\n     */\n    close() {\n        return this.disconnect();\n    }\n    /**\n     * Sets the compress flag.\n     *\n     * @param compress - if `true`, compresses the sending data\n     * @return self\n     * @public\n     */\n    compress(compress) {\n        this.flags.compress = compress;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the event message will be dropped when this socket is not\n     * ready to send messages.\n     *\n     * @returns self\n     * @public\n     */\n    get volatile() {\n        this.flags.volatile = true;\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * @param listener\n     * @public\n     */\n    onAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * @param listener\n     * @public\n     */\n    prependAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @param listener\n     * @public\n     */\n    offAny(listener) {\n        if (!this._anyListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     *\n     * @public\n     */\n    listenersAny() {\n        return this._anyListeners || [];\n    }\n}\nexports.Socket = Socket;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.on = void 0;\nfunction on(obj, ev, fn) {\n    obj.on(ev, fn);\n    return function subDestroy() {\n        obj.off(ev, fn);\n    };\n}\nexports.on = on;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Socket = exports.io = exports.Manager = exports.protocol = void 0;\nconst url_1 = require(\"./url\");\nconst manager_1 = require(\"./manager\");\nconst socket_1 = require(\"./socket\");\nObject.defineProperty(exports, \"Socket\", { enumerable: true, get: function () { return socket_1.Socket; } });\n\n\n/**\n * Module exports.\n */\nmodule.exports = exports = lookup;\n/**\n * Managers cache.\n */\nconst cache = (exports.managers = {});\nfunction lookup(uri, opts) {\n    if (typeof uri === \"object\") {\n        opts = uri;\n        uri = undefined;\n    }\n    opts = opts || {};\n    const parsed = url_1.url(uri, opts.path);\n    const source = parsed.source;\n    const id = parsed.id;\n    const path = parsed.path;\n    const sameNamespace = cache[id] && path in cache[id][\"nsps\"];\n    const newConnection = opts.forceNew ||\n        opts[\"force new connection\"] ||\n        false === opts.multiplex ||\n        sameNamespace;\n    let io;\n    if (newConnection) {\n\n\n        io = new manager_1.Manager(source, opts);\n    }\n    else {\n        if (!cache[id]) {\n\n\n            cache[id] = new manager_1.Manager(source, opts);\n        }\n        io = cache[id];\n    }\n    if (parsed.query && !opts.query) {\n        opts.query = parsed.queryKey;\n    }\n    return io.socket(parsed.path, opts);\n}\nexports.io = lookup;\n/**\n * Protocol version.\n *\n * @public\n */\nvar socket_io_parser_1 = require(\"socket.io-parser\");\nObject.defineProperty(exports, \"protocol\", { enumerable: true, get: function () { return socket_io_parser_1.protocol; } });\n/**\n * `connect`.\n *\n * @param {String} uri\n * @public\n */\nexports.connect = lookup;\n/**\n * Expose constructors for standalone build.\n *\n * @public\n */\nvar manager_2 = require(\"./manager\");\nObject.defineProperty(exports, \"Manager\", { enumerable: true, get: function () { return manager_2.Manager; } });\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.url = void 0;\nconst parseuri = require(\"parseuri\");\n\n\n/**\n * URL parser.\n *\n * @param uri - url\n * @param path - the request path of the connection\n * @param loc - An object meant to mimic window.location.\n *        Defaults to window.location.\n * @public\n */\nfunction url(uri, path = \"\", loc) {\n    let obj = uri;\n    // default to window.location\n    loc = loc || (typeof location !== \"undefined\" && location);\n    if (null == uri)\n        uri = loc.protocol + \"//\" + loc.host;\n    // relative path support\n    if (typeof uri === \"string\") {\n        if (\"/\" === uri.charAt(0)) {\n            if (\"/\" === uri.charAt(1)) {\n                uri = loc.protocol + uri;\n            }\n            else {\n                uri = loc.host + uri;\n            }\n        }\n        if (!/^(https?|wss?):\\/\\//.test(uri)) {\n\n\n            if (\"undefined\" !== typeof loc) {\n                uri = loc.protocol + \"//\" + uri;\n            }\n            else {\n                uri = \"https://\" + uri;\n            }\n        }\n        // parse\n\n\n        obj = parseuri(uri);\n    }\n    // make sure we treat `localhost:80` and `localhost` equally\n    if (!obj.port) {\n        if (/^(http|ws)$/.test(obj.protocol)) {\n            obj.port = \"80\";\n        }\n        else if (/^(http|ws)s$/.test(obj.protocol)) {\n            obj.port = \"443\";\n        }\n    }\n    obj.path = obj.path || \"/\";\n    const ipv6 = obj.host.indexOf(\":\") !== -1;\n    const host = ipv6 ? \"[\" + obj.host + \"]\" : obj.host;\n    // define unique id\n    obj.id = obj.protocol + \"://\" + host + \":\" + obj.port + path;\n    // define href\n    obj.href =\n        obj.protocol +\n            \"://\" +\n            host +\n            (loc && loc.port === obj.port ? \"\" : \":\" + obj.port);\n    return obj;\n}\nexports.url = url;\n", "const Socket = require(\"./socket\");\n\nmodule.exports = (uri, opts) => new Socket(uri, opts);\n\n/**\n * Expose deps for legacy compatibility\n * and standalone browser access.\n */\n\nmodule.exports.Socket = Socket;\nmodule.exports.protocol = Socket.protocol; // this is an int\nmodule.exports.Transport = require(\"./transport\");\nmodule.exports.transports = require(\"./transports/index\");\nmodule.exports.parser = require(\"engine.io-parser\");\n", "const transports = require(\"./transports/index\");\nconst Emitter = require(\"component-emitter\");\n\n\nconst parser = require(\"engine.io-parser\");\nconst parseuri = require(\"parseuri\");\nconst parseqs = require(\"parseqs\");\n\nclass Socket extends Emitter {\n  /**\n   * Socket constructor.\n   *\n   * @param {String|Object} uri or options\n   * @param {Object} options\n   * @api public\n   */\n  constructor(uri, opts = {}) {\n    super();\n\n    if (uri && \"object\" === typeof uri) {\n      opts = uri;\n      uri = null;\n    }\n\n    if (uri) {\n      uri = parseuri(uri);\n      opts.hostname = uri.host;\n      opts.secure = uri.protocol === \"https\" || uri.protocol === \"wss\";\n      opts.port = uri.port;\n      if (uri.query) opts.query = uri.query;\n    } else if (opts.host) {\n      opts.hostname = parseuri(opts.host).host;\n    }\n\n    this.secure =\n      null != opts.secure\n        ? opts.secure\n        : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n\n    if (opts.hostname && !opts.port) {\n      // if no port is specified manually, use the protocol default\n      opts.port = this.secure ? \"443\" : \"80\";\n    }\n\n    this.hostname =\n      opts.hostname ||\n      (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n    this.port =\n      opts.port ||\n      (typeof location !== \"undefined\" && location.port\n        ? location.port\n        : this.secure\n        ? 443\n        : 80);\n\n    this.transports = opts.transports || [\"polling\", \"websocket\"];\n    this.readyState = \"\";\n    this.writeBuffer = [];\n    this.prevBufferLen = 0;\n\n    this.opts = Object.assign(\n      {\n        path: \"/engine.io\",\n        agent: false,\n        withCredentials: false,\n        upgrade: true,\n        jsonp: true,\n        timestampParam: \"t\",\n        rememberUpgrade: false,\n        rejectUnauthorized: true,\n        perMessageDeflate: {\n          threshold: 1024\n        },\n        transportOptions: {}\n      },\n      opts\n    );\n\n    this.opts.path = this.opts.path.replace(/\\/$/, \"\") + \"/\";\n\n    if (typeof this.opts.query === \"string\") {\n      this.opts.query = parseqs.decode(this.opts.query);\n    }\n\n    // set on handshake\n    this.id = null;\n    this.upgrades = null;\n    this.pingInterval = null;\n    this.pingTimeout = null;\n\n    // set on heartbeat\n    this.pingTimeoutTimer = null;\n\n    if (typeof addEventListener === \"function\") {\n      addEventListener(\n        \"beforeunload\",\n        () => {\n          if (this.transport) {\n            // silently close the transport\n            this.transport.removeAllListeners();\n            this.transport.close();\n          }\n        },\n        false\n      );\n    }\n\n    this.open();\n  }\n\n  /**\n   * Creates transport of the given type.\n   *\n   * @param {String} transport name\n   * @return {Transport}\n   * @api private\n   */\n  createTransport(name) {\n\n\n    const query = clone(this.opts.query);\n\n    // append engine.io protocol identifier\n    query.EIO = parser.protocol;\n\n    // transport name\n    query.transport = name;\n\n    // session id if we already have one\n    if (this.id) query.sid = this.id;\n\n    const opts = Object.assign(\n      {},\n      this.opts.transportOptions[name],\n      this.opts,\n      {\n        query,\n        socket: this,\n        hostname: this.hostname,\n        secure: this.secure,\n        port: this.port\n      }\n    );\n\n\n\n\n    return new transports[name](opts);\n  }\n\n  /**\n   * Initializes transport to use and starts probe.\n   *\n   * @api private\n   */\n  open() {\n    let transport;\n    if (\n      this.opts.rememberUpgrade &&\n      Socket.priorWebsocketSuccess &&\n      this.transports.indexOf(\"websocket\") !== -1\n    ) {\n      transport = \"websocket\";\n    } else if (0 === this.transports.length) {\n      // Emit error on next tick so it can be listened to\n      const self = this;\n      setTimeout(function() {\n        self.emit(\"error\", \"No transports available\");\n      }, 0);\n      return;\n    } else {\n      transport = this.transports[0];\n    }\n    this.readyState = \"opening\";\n\n    // Retry with the next transport if the transport is disabled (jsonp: false)\n    try {\n      transport = this.createTransport(transport);\n    } catch (e) {\n\n\n      this.transports.shift();\n      this.open();\n      return;\n    }\n\n    transport.open();\n    this.setTransport(transport);\n  }\n\n  /**\n   * Sets the current transport. Disables the existing one (if any).\n   *\n   * @api private\n   */\n  setTransport(transport) {\n\n\n    const self = this;\n\n    if (this.transport) {\n\n\n      this.transport.removeAllListeners();\n    }\n\n    // set up transport\n    this.transport = transport;\n\n    // set up transport listeners\n    transport\n      .on(\"drain\", function() {\n        self.onDrain();\n      })\n      .on(\"packet\", function(packet) {\n        self.onPacket(packet);\n      })\n      .on(\"error\", function(e) {\n        self.onError(e);\n      })\n      .on(\"close\", function() {\n        self.onClose(\"transport close\");\n      });\n  }\n\n  /**\n   * Probes a transport.\n   *\n   * @param {String} transport name\n   * @api private\n   */\n  probe(name) {\n\n\n    let transport = this.createTransport(name, { probe: 1 });\n    let failed = false;\n    const self = this;\n\n    Socket.priorWebsocketSuccess = false;\n\n    function onTransportOpen() {\n      if (self.onlyBinaryUpgrades) {\n        const upgradeLosesBinary =\n          !this.supportsBinary && self.transport.supportsBinary;\n        failed = failed || upgradeLosesBinary;\n      }\n      if (failed) return;\n\n\n\n      transport.send([{ type: \"ping\", data: \"probe\" }]);\n      transport.once(\"packet\", function(msg) {\n        if (failed) return;\n        if (\"pong\" === msg.type && \"probe\" === msg.data) {\n\n\n          self.upgrading = true;\n          self.emit(\"upgrading\", transport);\n          if (!transport) return;\n          Socket.priorWebsocketSuccess = \"websocket\" === transport.name;\n\n\n\n          self.transport.pause(function() {\n            if (failed) return;\n            if (\"closed\" === self.readyState) return;\n\n\n\n            cleanup();\n\n            self.setTransport(transport);\n            transport.send([{ type: \"upgrade\" }]);\n            self.emit(\"upgrade\", transport);\n            transport = null;\n            self.upgrading = false;\n            self.flush();\n          });\n        } else {\n\n\n          const err = new Error(\"probe error\");\n          err.transport = transport.name;\n          self.emit(\"upgradeError\", err);\n        }\n      });\n    }\n\n    function freezeTransport() {\n      if (failed) return;\n\n      // Any callback called by transport should be ignored since now\n      failed = true;\n\n      cleanup();\n\n      transport.close();\n      transport = null;\n    }\n\n    // Handle any error that happens while probing\n    function onerror(err) {\n      const error = new Error(\"probe error: \" + err);\n      error.transport = transport.name;\n\n      freezeTransport();\n\n\n\n\n      self.emit(\"upgradeError\", error);\n    }\n\n    function onTransportClose() {\n      onerror(\"transport closed\");\n    }\n\n    // When the socket is closed while we're probing\n    function onclose() {\n      onerror(\"socket closed\");\n    }\n\n    // When the socket is upgraded while we're probing\n    function onupgrade(to) {\n      if (transport && to.name !== transport.name) {\n\n\n        freezeTransport();\n      }\n    }\n\n    // Remove all listeners on the transport and on self\n    function cleanup() {\n      transport.removeListener(\"open\", onTransportOpen);\n      transport.removeListener(\"error\", onerror);\n      transport.removeListener(\"close\", onTransportClose);\n      self.removeListener(\"close\", onclose);\n      self.removeListener(\"upgrading\", onupgrade);\n    }\n\n    transport.once(\"open\", onTransportOpen);\n    transport.once(\"error\", onerror);\n    transport.once(\"close\", onTransportClose);\n\n    this.once(\"close\", onclose);\n    this.once(\"upgrading\", onupgrade);\n\n    transport.open();\n  }\n\n  /**\n   * Called when connection is deemed open.\n   *\n   * @api public\n   */\n  onOpen() {\n\n\n    this.readyState = \"open\";\n    Socket.priorWebsocketSuccess = \"websocket\" === this.transport.name;\n    this.emit(\"open\");\n    this.flush();\n\n    // we check for `readyState` in case an `open`\n    // listener already closed the socket\n    if (\n      \"open\" === this.readyState &&\n      this.opts.upgrade &&\n      this.transport.pause\n    ) {\n\n\n      let i = 0;\n      const l = this.upgrades.length;\n      for (; i < l; i++) {\n        this.probe(this.upgrades[i]);\n      }\n    }\n  }\n\n  /**\n   * Handles a packet.\n   *\n   * @api private\n   */\n  onPacket(packet) {\n    if (\n      \"opening\" === this.readyState ||\n      \"open\" === this.readyState ||\n      \"closing\" === this.readyState\n    ) {\n\n\n\n      this.emit(\"packet\", packet);\n\n      // Socket is live - any packet counts\n      this.emit(\"heartbeat\");\n\n      switch (packet.type) {\n        case \"open\":\n          this.onHandshake(JSON.parse(packet.data));\n          break;\n\n        case \"ping\":\n          this.resetPingTimeout();\n          this.sendPacket(\"pong\");\n          this.emit(\"pong\");\n          break;\n\n        case \"error\":\n          const err = new Error(\"server error\");\n          err.code = packet.data;\n          this.onError(err);\n          break;\n\n        case \"message\":\n          this.emit(\"data\", packet.data);\n          this.emit(\"message\", packet.data);\n          break;\n      }\n    } else {\n\n\n    }\n  }\n\n  /**\n   * Called upon handshake completion.\n   *\n   * @param {Object} handshake obj\n   * @api private\n   */\n  onHandshake(data) {\n    this.emit(\"handshake\", data);\n    this.id = data.sid;\n    this.transport.query.sid = data.sid;\n    this.upgrades = this.filterUpgrades(data.upgrades);\n    this.pingInterval = data.pingInterval;\n    this.pingTimeout = data.pingTimeout;\n    this.onOpen();\n    // In case open handler closes socket\n    if (\"closed\" === this.readyState) return;\n    this.resetPingTimeout();\n  }\n\n  /**\n   * Sets and resets ping timeout timer based on server pings.\n   *\n   * @api private\n   */\n  resetPingTimeout() {\n    clearTimeout(this.pingTimeoutTimer);\n    this.pingTimeoutTimer = setTimeout(() => {\n      this.onClose(\"ping timeout\");\n    }, this.pingInterval + this.pingTimeout);\n  }\n\n  /**\n   * Called on `drain` event\n   *\n   * @api private\n   */\n  onDrain() {\n    this.writeBuffer.splice(0, this.prevBufferLen);\n\n    // setting prevBufferLen = 0 is very important\n    // for example, when upgrading, upgrade packet is sent over,\n    // and a nonzero prevBufferLen could cause problems on `drain`\n    this.prevBufferLen = 0;\n\n    if (0 === this.writeBuffer.length) {\n      this.emit(\"drain\");\n    } else {\n      this.flush();\n    }\n  }\n\n  /**\n   * Flush write buffers.\n   *\n   * @api private\n   */\n  flush() {\n    if (\n      \"closed\" !== this.readyState &&\n      this.transport.writable &&\n      !this.upgrading &&\n      this.writeBuffer.length\n    ) {\n\n\n      this.transport.send(this.writeBuffer);\n      // keep track of current length of writeBuffer\n      // splice writeBuffer and callbackBuffer on `drain`\n      this.prevBufferLen = this.writeBuffer.length;\n      this.emit(\"flush\");\n    }\n  }\n\n  /**\n   * Sends a message.\n   *\n   * @param {String} message.\n   * @param {Function} callback function.\n   * @param {Object} options.\n   * @return {Socket} for chaining.\n   * @api public\n   */\n  write(msg, options, fn) {\n    this.sendPacket(\"message\", msg, options, fn);\n    return this;\n  }\n\n  send(msg, options, fn) {\n    this.sendPacket(\"message\", msg, options, fn);\n    return this;\n  }\n\n  /**\n   * Sends a packet.\n   *\n   * @param {String} packet type.\n   * @param {String} data.\n   * @param {Object} options.\n   * @param {Function} callback function.\n   * @api private\n   */\n  sendPacket(type, data, options, fn) {\n    if (\"function\" === typeof data) {\n      fn = data;\n      data = undefined;\n    }\n\n    if (\"function\" === typeof options) {\n      fn = options;\n      options = null;\n    }\n\n    if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n      return;\n    }\n\n    options = options || {};\n    options.compress = false !== options.compress;\n\n    const packet = {\n      type: type,\n      data: data,\n      options: options\n    };\n    this.emit(\"packetCreate\", packet);\n    this.writeBuffer.push(packet);\n    if (fn) this.once(\"flush\", fn);\n    this.flush();\n  }\n\n  /**\n   * Closes the connection.\n   *\n   * @api private\n   */\n  close() {\n    const self = this;\n\n    if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n      this.readyState = \"closing\";\n\n      if (this.writeBuffer.length) {\n        this.once(\"drain\", function() {\n          if (this.upgrading) {\n            waitForUpgrade();\n          } else {\n            close();\n          }\n        });\n      } else if (this.upgrading) {\n        waitForUpgrade();\n      } else {\n        close();\n      }\n    }\n\n    function close() {\n      self.onClose(\"forced close\");\n\n\n      self.transport.close();\n    }\n\n    function cleanupAndClose() {\n      self.removeListener(\"upgrade\", cleanupAndClose);\n      self.removeListener(\"upgradeError\", cleanupAndClose);\n      close();\n    }\n\n    function waitForUpgrade() {\n      // wait for upgrade to finish since we can't send packets while pausing a transport\n      self.once(\"upgrade\", cleanupAndClose);\n      self.once(\"upgradeError\", cleanupAndClose);\n    }\n\n    return this;\n  }\n\n  /**\n   * Called upon transport error\n   *\n   * @api private\n   */\n  onError(err) {\n\n\n    Socket.priorWebsocketSuccess = false;\n    this.emit(\"error\", err);\n    this.onClose(\"transport error\", err);\n  }\n\n  /**\n   * Called upon transport close.\n   *\n   * @api private\n   */\n  onClose(reason, desc) {\n    if (\n      \"opening\" === this.readyState ||\n      \"open\" === this.readyState ||\n      \"closing\" === this.readyState\n    ) {\n\n\n      const self = this;\n\n      // clear timers\n      clearTimeout(this.pingIntervalTimer);\n      clearTimeout(this.pingTimeoutTimer);\n\n      // stop event from firing again for transport\n      this.transport.removeAllListeners(\"close\");\n\n      // ensure transport won't stay open\n      this.transport.close();\n\n      // ignore further transport communication\n      this.transport.removeAllListeners();\n\n      // set ready state\n      this.readyState = \"closed\";\n\n      // clear session id\n      this.id = null;\n\n      // emit close event\n      this.emit(\"close\", reason, desc);\n\n      // clean buffers after, so users can still\n      // grab the buffers on `close` event\n      self.writeBuffer = [];\n      self.prevBufferLen = 0;\n    }\n  }\n\n  /**\n   * Filters upgrades, returning only those matching client transports.\n   *\n   * @param {Array} server upgrades\n   * @api private\n   *\n   */\n  filterUpgrades(upgrades) {\n    const filteredUpgrades = [];\n    let i = 0;\n    const j = upgrades.length;\n    for (; i < j; i++) {\n      if (~this.transports.indexOf(upgrades[i]))\n        filteredUpgrades.push(upgrades[i]);\n    }\n    return filteredUpgrades;\n  }\n}\n\nSocket.priorWebsocketSuccess = false;\n\n/**\n * Protocol version.\n *\n * @api public\n */\n\nSocket.protocol = parser.protocol; // this is an int\n\nfunction clone(obj) {\n  const o = {};\n  for (let i in obj) {\n    if (obj.hasOwnProperty(i)) {\n      o[i] = obj[i];\n    }\n  }\n  return o;\n}\n\nmodule.exports = Socket;\n", "\n/**\n * Module exports.\n *\n * Logic borrowed from Modernizr:\n *\n *   - https://github.com/Modernizr/Modernizr/blob/master/feature-detects/cors.js\n */\n\ntry {\n  module.exports = typeof XMLHttpRequest !== 'undefined' &&\n    'withCredentials' in new XMLHttpRequest();\n} catch (err) {\n  // if XMLHttp support is disabled in IE then it will throw\n  // when trying to create\n  module.exports = false;\n}\n", "/* global attachEvent */\n\nconst XMLHttpRequest = require(\"xmlhttprequest-ssl\");\nconst Polling = require(\"./polling\");\nconst Emitter = require(\"component-emitter\");\nconst { pick } = require(\"../util\");\nconst globalThis = require(\"../globalThis\");\n\n\n\n\n/**\n * Empty function\n */\n\nfunction empty() {}\n\nconst hasXHR2 = (function() {\n  const xhr = new XMLHttpRequest({ xdomain: false });\n  return null != xhr.responseType;\n})();\n\nclass XHR extends Polling {\n  /**\n   * XHR Polling constructor.\n   *\n   * @param {Object} opts\n   * @api public\n   */\n  constructor(opts) {\n    super(opts);\n\n    if (typeof location !== \"undefined\") {\n      const isSSL = \"https:\" === location.protocol;\n      let port = location.port;\n\n      // some user agents have empty `location.port`\n      if (!port) {\n        port = isSSL ? 443 : 80;\n      }\n\n      this.xd =\n        (typeof location !== \"undefined\" &&\n          opts.hostname !== location.hostname) ||\n        port !== opts.port;\n      this.xs = opts.secure !== isSSL;\n    }\n    /**\n     * XHR supports binary\n     */\n    const forceBase64 = opts && opts.forceBase64;\n    this.supportsBinary = hasXHR2 && !forceBase64;\n  }\n\n  /**\n   * Creates a request.\n   *\n   * @param {String} method\n   * @api private\n   */\n  request(opts = {}) {\n    Object.assign(opts, { xd: this.xd, xs: this.xs }, this.opts);\n    return new Request(this.uri(), opts);\n  }\n\n  /**\n   * Sends data.\n   *\n   * @param {String} data to send.\n   * @param {Function} called upon flush.\n   * @api private\n   */\n  doWrite(data, fn) {\n    const req = this.request({\n      method: \"POST\",\n      data: data\n    });\n    const self = this;\n    req.on(\"success\", fn);\n    req.on(\"error\", function(err) {\n      self.onError(\"xhr post error\", err);\n    });\n  }\n\n  /**\n   * Starts a poll cycle.\n   *\n   * @api private\n   */\n  doPoll() {\n\n\n    const req = this.request();\n    const self = this;\n    req.on(\"data\", function(data) {\n      self.onData(data);\n    });\n    req.on(\"error\", function(err) {\n      self.onError(\"xhr poll error\", err);\n    });\n    this.pollXhr = req;\n  }\n}\n\nclass Request extends Emitter {\n  /**\n   * Request constructor\n   *\n   * @param {Object} options\n   * @api public\n   */\n  constructor(uri, opts) {\n    super();\n    this.opts = opts;\n\n    this.method = opts.method || \"GET\";\n    this.uri = uri;\n    this.async = false !== opts.async;\n    this.data = undefined !== opts.data ? opts.data : null;\n\n    this.create();\n  }\n\n  /**\n   * Creates the XHR object and sends the request.\n   *\n   * @api private\n   */\n  create() {\n    const opts = pick(\n      this.opts,\n      \"agent\",\n      \"enablesXDR\",\n      \"pfx\",\n      \"key\",\n      \"passphrase\",\n      \"cert\",\n      \"ca\",\n      \"ciphers\",\n      \"rejectUnauthorized\"\n    );\n    opts.xdomain = !!this.opts.xd;\n    opts.xscheme = !!this.opts.xs;\n\n    const xhr = (this.xhr = new XMLHttpRequest(opts));\n    const self = this;\n\n    try {\n\n\n      xhr.open(this.method, this.uri, this.async);\n      try {\n        if (this.opts.extraHeaders) {\n          xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n          for (let i in this.opts.extraHeaders) {\n            if (this.opts.extraHeaders.hasOwnProperty(i)) {\n              xhr.setRequestHeader(i, this.opts.extraHeaders[i]);\n            }\n          }\n        }\n      } catch (e) {}\n\n      if (\"POST\" === this.method) {\n        try {\n          xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n        } catch (e) {}\n      }\n\n      try {\n        xhr.setRequestHeader(\"Accept\", \"*/*\");\n      } catch (e) {}\n\n      // ie6 check\n      if (\"withCredentials\" in xhr) {\n        xhr.withCredentials = this.opts.withCredentials;\n      }\n\n      if (this.opts.requestTimeout) {\n        xhr.timeout = this.opts.requestTimeout;\n      }\n\n      if (this.hasXDR()) {\n        xhr.onload = function() {\n          self.onLoad();\n        };\n        xhr.onerror = function() {\n          self.onError(xhr.responseText);\n        };\n      } else {\n        xhr.onreadystatechange = function() {\n          if (4 !== xhr.readyState) return;\n          if (200 === xhr.status || 1223 === xhr.status) {\n            self.onLoad();\n          } else {\n            // make sure the `error` event handler that's user-set\n            // does not throw in the same tick and gets caught here\n            setTimeout(function() {\n              self.onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n            }, 0);\n          }\n        };\n      }\n\n\n\n      xhr.send(this.data);\n    } catch (e) {\n      // Need to defer since .create() is called directly from the constructor\n      // and thus the 'error' event can only be only bound *after* this exception\n      // occurs.  Therefore, also, we cannot throw here at all.\n      setTimeout(function() {\n        self.onError(e);\n      }, 0);\n      return;\n    }\n\n    if (typeof document !== \"undefined\") {\n      this.index = Request.requestsCount++;\n      Request.requests[this.index] = this;\n    }\n  }\n\n  /**\n   * Called upon successful response.\n   *\n   * @api private\n   */\n  onSuccess() {\n    this.emit(\"success\");\n    this.cleanup();\n  }\n\n  /**\n   * Called if we have data.\n   *\n   * @api private\n   */\n  onData(data) {\n    this.emit(\"data\", data);\n    this.onSuccess();\n  }\n\n  /**\n   * Called upon error.\n   *\n   * @api private\n   */\n  onError(err) {\n    this.emit(\"error\", err);\n    this.cleanup(true);\n  }\n\n  /**\n   * Cleans up house.\n   *\n   * @api private\n   */\n  cleanup(fromError) {\n    if (\"undefined\" === typeof this.xhr || null === this.xhr) {\n      return;\n    }\n    // xmlhttprequest\n    if (this.hasXDR()) {\n      this.xhr.onload = this.xhr.onerror = empty;\n    } else {\n      this.xhr.onreadystatechange = empty;\n    }\n\n    if (fromError) {\n      try {\n        this.xhr.abort();\n      } catch (e) {}\n    }\n\n    if (typeof document !== \"undefined\") {\n      delete Request.requests[this.index];\n    }\n\n    this.xhr = null;\n  }\n\n  /**\n   * Called upon load.\n   *\n   * @api private\n   */\n  onLoad() {\n    const data = this.xhr.responseText;\n    if (data !== null) {\n      this.onData(data);\n    }\n  }\n\n  /**\n   * Check if it has XDomainRequest.\n   *\n   * @api private\n   */\n  hasXDR() {\n    return typeof XDomainRequest !== \"undefined\" && !this.xs && this.enablesXDR;\n  }\n\n  /**\n   * Aborts the request.\n   *\n   * @api public\n   */\n  abort() {\n    this.cleanup();\n  }\n}\n\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\n\nRequest.requestsCount = 0;\nRequest.requests = {};\n\nif (typeof document !== \"undefined\") {\n  if (typeof attachEvent === \"function\") {\n    attachEvent(\"onunload\", unloadHandler);\n  } else if (typeof addEventListener === \"function\") {\n    const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n    addEventListener(terminationEvent, unloadHandler, false);\n  }\n}\n\nfunction unloadHandler() {\n  for (let i in Request.requests) {\n    if (Request.requests.hasOwnProperty(i)) {\n      Request.requests[i].abort();\n    }\n  }\n}\n\nmodule.exports = XHR;\nmodule.exports.Request = Request;\n", "const { PACKET_TYPES } = require(\"./commons\");\n\nconst withNativeBlob =\n  typeof Blob === \"function\" ||\n  (typeof Blob !== \"undefined\" &&\n    Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = obj => {\n  return typeof ArrayBuffer.isView === \"function\"\n    ? ArrayBuffer.isView(obj)\n    : obj && obj.buffer instanceof ArrayBuffer;\n};\n\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n  if (withNativeBlob && data instanceof Blob) {\n    if (supportsBinary) {\n      return callback(data);\n    } else {\n      return encodeBlobAsBase64(data, callback);\n    }\n  } else if (\n    withNativeArrayBuffer &&\n    (data instanceof ArrayBuffer || isView(data))\n  ) {\n    if (supportsBinary) {\n      return callback(data instanceof ArrayBuffer ? data : data.buffer);\n    } else {\n      return encodeBlobAsBase64(new Blob([data]), callback);\n    }\n  }\n  // plain string\n  return callback(PACKET_TYPES[type] + (data || \"\"));\n};\n\nconst encodeBlobAsBase64 = (data, callback) => {\n  const fileReader = new FileReader();\n  fileReader.onload = function() {\n    const content = fileReader.result.split(\",\")[1];\n    callback(\"b\" + content);\n  };\n  return fileReader.readAsDataURL(data);\n};\n\nmodule.exports = encodePacket;\n", "const { PACKET_TYPES_REVERSE, ERROR_PACKET } = require(\"./commons\");\n\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n\nlet base64decoder;\nif (withNativeArrayBuffer) {\n  base64decoder = require(\"base64-arraybuffer\");\n}\n\nconst decodePacket = (encodedPacket, binaryType) => {\n  if (typeof encodedPacket !== \"string\") {\n    return {\n      type: \"message\",\n      data: mapBinary(encodedPacket, binaryType)\n    };\n  }\n  const type = encodedPacket.charAt(0);\n  if (type === \"b\") {\n    return {\n      type: \"message\",\n      data: decodeBase64Packet(encodedPacket.substring(1), binaryType)\n    };\n  }\n  const packetType = PACKET_TYPES_REVERSE[type];\n  if (!packetType) {\n    return ERROR_PACKET;\n  }\n  return encodedPacket.length > 1\n    ? {\n        type: PACKET_TYPES_REVERSE[type],\n        data: encodedPacket.substring(1)\n      }\n    : {\n        type: PACKET_TYPES_REVERSE[type]\n      };\n};\n\nconst decodeBase64Packet = (data, binaryType) => {\n  if (base64decoder) {\n    const decoded = base64decoder.decode(data);\n    return mapBinary(decoded, binaryType);\n  } else {\n    return { base64: true, data }; // fallback for old browsers\n  }\n};\n\nconst mapBinary = (data, binaryType) => {\n  switch (binaryType) {\n    case \"blob\":\n      return data instanceof ArrayBuffer ? new Blob([data]) : data;\n    case \"arraybuffer\":\n    default:\n      return data; // assuming the data is already an ArrayBuffer\n  }\n};\n\nmodule.exports = decodePacket;\n", "/*\n * base64-arraybuffer\n * https://github.com/niklasvh/base64-arraybuffer\n *\n * Copyright (c) 2012 <PERSON><PERSON>\n * Licensed under the MIT license.\n */\n(function(chars){\n  \"use strict\";\n\n  exports.encode = function(arraybuffer) {\n    var bytes = new Uint8Array(arraybuffer),\n    i, len = bytes.length, base64 = \"\";\n\n    for (i = 0; i < len; i+=3) {\n      base64 += chars[bytes[i] >> 2];\n      base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n      base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n      base64 += chars[bytes[i + 2] & 63];\n    }\n\n    if ((len % 3) === 2) {\n      base64 = base64.substring(0, base64.length - 1) + \"=\";\n    } else if (len % 3 === 1) {\n      base64 = base64.substring(0, base64.length - 2) + \"==\";\n    }\n\n    return base64;\n  };\n\n  exports.decode =  function(base64) {\n    var bufferLength = base64.length * 0.75,\n    len = base64.length, i, p = 0,\n    encoded1, encoded2, encoded3, encoded4;\n\n    if (base64[base64.length - 1] === \"=\") {\n      bufferLength--;\n      if (base64[base64.length - 2] === \"=\") {\n        bufferLength--;\n      }\n    }\n\n    var arraybuffer = new ArrayBuffer(bufferLength),\n    bytes = new Uint8Array(arraybuffer);\n\n    for (i = 0; i < len; i+=4) {\n      encoded1 = chars.indexOf(base64[i]);\n      encoded2 = chars.indexOf(base64[i+1]);\n      encoded3 = chars.indexOf(base64[i+2]);\n      encoded4 = chars.indexOf(base64[i+3]);\n\n      bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n      bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n      bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n\n    return arraybuffer;\n  };\n})(\"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\");\n", "const Polling = require(\"./polling\");\nconst globalThis = require(\"../globalThis\");\n\nconst rNewline = /\\n/g;\nconst rEscapedNewline = /\\\\n/g;\n\n/**\n * Global JSONP callbacks.\n */\n\nlet callbacks;\n\nclass JSONPPolling extends Polling {\n  /**\n   * JSONP Polling constructor.\n   *\n   * @param {Object} opts.\n   * @api public\n   */\n  constructor(opts) {\n    super(opts);\n\n    this.query = this.query || {};\n\n    // define global callbacks array if not present\n    // we do this here (lazily) to avoid unneeded global pollution\n    if (!callbacks) {\n      // we need to consider multiple engines in the same page\n      callbacks = globalThis.___eio = globalThis.___eio || [];\n    }\n\n    // callback identifier\n    this.index = callbacks.length;\n\n    // add callback to jsonp global\n    const self = this;\n    callbacks.push(function(msg) {\n      self.onData(msg);\n    });\n\n    // append to query string\n    this.query.j = this.index;\n  }\n\n  /**\n   * JSONP only supports binary as base64 encoded strings\n   */\n  get supportsBinary() {\n    return false;\n  }\n\n  /**\n   * Closes the socket.\n   *\n   * @api private\n   */\n  doClose() {\n    if (this.script) {\n      // prevent spurious errors from being emitted when the window is unloaded\n      this.script.onerror = () => {};\n      this.script.parentNode.removeChild(this.script);\n      this.script = null;\n    }\n\n    if (this.form) {\n      this.form.parentNode.removeChild(this.form);\n      this.form = null;\n      this.iframe = null;\n    }\n\n    super.doClose();\n  }\n\n  /**\n   * Starts a poll cycle.\n   *\n   * @api private\n   */\n  doPoll() {\n    const self = this;\n    const script = document.createElement(\"script\");\n\n    if (this.script) {\n      this.script.parentNode.removeChild(this.script);\n      this.script = null;\n    }\n\n    script.async = true;\n    script.src = this.uri();\n    script.onerror = function(e) {\n      self.onError(\"jsonp poll error\", e);\n    };\n\n    const insertAt = document.getElementsByTagName(\"script\")[0];\n    if (insertAt) {\n      insertAt.parentNode.insertBefore(script, insertAt);\n    } else {\n      (document.head || document.body).appendChild(script);\n    }\n    this.script = script;\n\n    const isUAgecko =\n      \"undefined\" !== typeof navigator && /gecko/i.test(navigator.userAgent);\n\n    if (isUAgecko) {\n      setTimeout(function() {\n        const iframe = document.createElement(\"iframe\");\n        document.body.appendChild(iframe);\n        document.body.removeChild(iframe);\n      }, 100);\n    }\n  }\n\n  /**\n   * Writes with a hidden iframe.\n   *\n   * @param {String} data to send\n   * @param {Function} called upon flush.\n   * @api private\n   */\n  doWrite(data, fn) {\n    const self = this;\n    let iframe;\n\n    if (!this.form) {\n      const form = document.createElement(\"form\");\n      const area = document.createElement(\"textarea\");\n      const id = (this.iframeId = \"eio_iframe_\" + this.index);\n\n      form.className = \"socketio\";\n      form.style.position = \"absolute\";\n      form.style.top = \"-1000px\";\n      form.style.left = \"-1000px\";\n      form.target = id;\n      form.method = \"POST\";\n      form.setAttribute(\"accept-charset\", \"utf-8\");\n      area.name = \"d\";\n      form.appendChild(area);\n      document.body.appendChild(form);\n\n      this.form = form;\n      this.area = area;\n    }\n\n    this.form.action = this.uri();\n\n    function complete() {\n      initIframe();\n      fn();\n    }\n\n    function initIframe() {\n      if (self.iframe) {\n        try {\n          self.form.removeChild(self.iframe);\n        } catch (e) {\n          self.onError(\"jsonp polling iframe removal error\", e);\n        }\n      }\n\n      try {\n        // ie6 dynamic iframes with target=\"\" support (thanks Chris Lambacher)\n        const html = '<iframe src=\"javascript:0\" name=\"' + self.iframeId + '\">';\n        iframe = document.createElement(html);\n      } catch (e) {\n        iframe = document.createElement(\"iframe\");\n        iframe.name = self.iframeId;\n        iframe.src = \"javascript:0\";\n      }\n\n      iframe.id = self.iframeId;\n\n      self.form.appendChild(iframe);\n      self.iframe = iframe;\n    }\n\n    initIframe();\n\n    // escape \\n to prevent it from being converted into \\r\\n by some UAs\n    // double escaping is required for escaped new lines because unescaping of new lines can be done safely on server-side\n    data = data.replace(rEscapedNewline, \"\\\\\\n\");\n    this.area.value = data.replace(rNewline, \"\\\\n\");\n\n    try {\n      this.form.submit();\n    } catch (e) {}\n\n    if (this.iframe.attachEvent) {\n      this.iframe.onreadystatechange = function() {\n        if (self.iframe.readyState === \"complete\") {\n          complete();\n        }\n      };\n    } else {\n      this.iframe.onload = complete;\n    }\n  }\n}\n\nmodule.exports = JSONPPolling;\n", "const Transport = require(\"../transport\");\nconst parser = require(\"engine.io-parser\");\nconst parseqs = require(\"parseqs\");\nconst yeast = require(\"yeast\");\nconst { pick } = require(\"../util\");\nconst {\n  WebSocket,\n  usingBrowserWebSocket,\n  defaultBinaryType\n} = require(\"./websocket-constructor\");\n\n\n\n\n// detect ReactNative environment\nconst isReactNative =\n  typeof navigator !== \"undefined\" &&\n  typeof navigator.product === \"string\" &&\n  navigator.product.toLowerCase() === \"reactnative\";\n\nclass WS extends Transport {\n  /**\n   * WebSocket transport constructor.\n   *\n   * @api {Object} connection options\n   * @api public\n   */\n  constructor(opts) {\n    super(opts);\n\n    this.supportsBinary = !opts.forceBase64;\n  }\n\n  /**\n   * Transport name.\n   *\n   * @api public\n   */\n  get name() {\n    return \"websocket\";\n  }\n\n  /**\n   * Opens socket.\n   *\n   * @api private\n   */\n  doOpen() {\n    if (!this.check()) {\n      // let probe timeout\n      return;\n    }\n\n    const uri = this.uri();\n    const protocols = this.opts.protocols;\n\n    // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n    const opts = isReactNative\n      ? {}\n      : pick(\n          this.opts,\n          \"agent\",\n          \"perMessageDeflate\",\n          \"pfx\",\n          \"key\",\n          \"passphrase\",\n          \"cert\",\n          \"ca\",\n          \"ciphers\",\n          \"rejectUnauthorized\",\n          \"localAddress\",\n          \"protocolVersion\",\n          \"origin\",\n          \"maxPayload\",\n          \"family\",\n          \"checkServerIdentity\"\n        );\n\n    if (this.opts.extraHeaders) {\n      opts.headers = this.opts.extraHeaders;\n    }\n\n    try {\n      this.ws =\n        usingBrowserWebSocket && !isReactNative\n          ? protocols\n            ? new WebSocket(uri, protocols)\n            : new WebSocket(uri)\n          : new WebSocket(uri, protocols, opts);\n    } catch (err) {\n      return this.emit(\"error\", err);\n    }\n\n    this.ws.binaryType = this.socket.binaryType || defaultBinaryType;\n\n    this.addEventListeners();\n  }\n\n  /**\n   * Adds event listeners to the socket\n   *\n   * @api private\n   */\n  addEventListeners() {\n    const self = this;\n\n    this.ws.onopen = function() {\n      self.onOpen();\n    };\n    this.ws.onclose = function() {\n      self.onClose();\n    };\n    this.ws.onmessage = function(ev) {\n      self.onData(ev.data);\n    };\n    this.ws.onerror = function(e) {\n      self.onError(\"websocket error\", e);\n    };\n  }\n\n  /**\n   * Writes data to socket.\n   *\n   * @param {Array} array of packets.\n   * @api private\n   */\n  write(packets) {\n    const self = this;\n    this.writable = false;\n\n    // encodePacket efficient as it uses WS framing\n    // no need for encodePayload\n    let total = packets.length;\n    let i = 0;\n    const l = total;\n    for (; i < l; i++) {\n      (function(packet) {\n        parser.encodePacket(packet, self.supportsBinary, function(data) {\n          // always create a new object (GH-437)\n          const opts = {};\n          if (!usingBrowserWebSocket) {\n            if (packet.options) {\n              opts.compress = packet.options.compress;\n            }\n\n            if (self.opts.perMessageDeflate) {\n              const len =\n                \"string\" === typeof data\n                  ? Buffer.byteLength(data)\n                  : data.length;\n              if (len < self.opts.perMessageDeflate.threshold) {\n                opts.compress = false;\n              }\n            }\n          }\n\n          // Sometimes the websocket has already been closed but the browser didn't\n          // have a chance of informing us about it yet, in that case send will\n          // throw an error\n          try {\n            if (usingBrowserWebSocket) {\n              // TypeError is thrown when passing the second argument on Safari\n              self.ws.send(data);\n            } else {\n              self.ws.send(data, opts);\n            }\n          } catch (e) {\n\n\n          }\n\n          --total || done();\n        });\n      })(packets[i]);\n    }\n\n    function done() {\n      self.emit(\"flush\");\n\n      // fake drain\n      // defer to next tick to allow Socket to clear writeBuffer\n      setTimeout(function() {\n        self.writable = true;\n        self.emit(\"drain\");\n      }, 0);\n    }\n  }\n\n  /**\n   * Called upon close\n   *\n   * @api private\n   */\n  onClose() {\n    Transport.prototype.onClose.call(this);\n  }\n\n  /**\n   * Closes socket.\n   *\n   * @api private\n   */\n  doClose() {\n    if (typeof this.ws !== \"undefined\") {\n      this.ws.close();\n      this.ws = null;\n    }\n  }\n\n  /**\n   * Generates uri for connection.\n   *\n   * @api private\n   */\n  uri() {\n    let query = this.query || {};\n    const schema = this.opts.secure ? \"wss\" : \"ws\";\n    let port = \"\";\n\n    // avoid port if default for schema\n    if (\n      this.opts.port &&\n      ((\"wss\" === schema && Number(this.opts.port) !== 443) ||\n        (\"ws\" === schema && Number(this.opts.port) !== 80))\n    ) {\n      port = \":\" + this.opts.port;\n    }\n\n    // append timestamp to URI\n    if (this.opts.timestampRequests) {\n      query[this.opts.timestampParam] = yeast();\n    }\n\n    // communicate binary support capabilities\n    if (!this.supportsBinary) {\n      query.b64 = 1;\n    }\n\n    query = parseqs.encode(query);\n\n    // prepend ? to query\n    if (query.length) {\n      query = \"?\" + query;\n    }\n\n    const ipv6 = this.opts.hostname.indexOf(\":\") !== -1;\n    return (\n      schema +\n      \"://\" +\n      (ipv6 ? \"[\" + this.opts.hostname + \"]\" : this.opts.hostname) +\n      port +\n      this.opts.path +\n      query\n    );\n  }\n\n  /**\n   * Feature detection for WebSocket.\n   *\n   * @return {Boolean} whether this transport is available.\n   * @api public\n   */\n  check() {\n    return (\n      !!WebSocket &&\n      !(\"__initialize\" in WebSocket && this.name === WS.prototype.name)\n    );\n  }\n}\n\nmodule.exports = WS;\n", "const globalThis = require(\"../globalThis\");\n\nmodule.exports = {\n  WebSocket: globalThis.WebSocket || globalThis.MozWebSocket,\n  usingBrowserWebSocket: true,\n  defaultBinaryType: \"arraybuffer\"\n};\n", "exports.encode = require('./encode');\nexports.decode = require('./decode');\n", "'use strict';\n\nfunction utf8Write(view, offset, str) {\n  var c = 0;\n  for (var i = 0, l = str.length; i < l; i++) {\n    c = str.charCodeAt(i);\n    if (c < 0x80) {\n      view.setUint8(offset++, c);\n    }\n    else if (c < 0x800) {\n      view.setUint8(offset++, 0xc0 | (c >> 6));\n      view.setUint8(offset++, 0x80 | (c & 0x3f));\n    }\n    else if (c < 0xd800 || c >= 0xe000) {\n      view.setUint8(offset++, 0xe0 | (c >> 12));\n      view.setUint8(offset++, 0x80 | (c >> 6) & 0x3f);\n      view.setUint8(offset++, 0x80 | (c & 0x3f));\n    }\n    else {\n      i++;\n      c = 0x10000 + (((c & 0x3ff) << 10) | (str.charCodeAt(i) & 0x3ff));\n      view.setUint8(offset++, 0xf0 | (c >> 18));\n      view.setUint8(offset++, 0x80 | (c >> 12) & 0x3f);\n      view.setUint8(offset++, 0x80 | (c >> 6) & 0x3f);\n      view.setUint8(offset++, 0x80 | (c & 0x3f));\n    }\n  }\n}\n\nfunction utf8Length(str) {\n  var c = 0, length = 0;\n  for (var i = 0, l = str.length; i < l; i++) {\n    c = str.charCodeAt(i);\n    if (c < 0x80) {\n      length += 1;\n    }\n    else if (c < 0x800) {\n      length += 2;\n    }\n    else if (c < 0xd800 || c >= 0xe000) {\n      length += 3;\n    }\n    else {\n      i++;\n      length += 4;\n    }\n  }\n  return length;\n}\n\nfunction _encode(bytes, defers, value) {\n  var type = typeof value, i = 0, l = 0, hi = 0, lo = 0, length = 0, size = 0;\n\n  if (type === 'string') {\n    length = utf8Length(value);\n\n    // fixstr\n    if (length < 0x20) {\n      bytes.push(length | 0xa0);\n      size = 1;\n    }\n    // str 8\n    else if (length < 0x100) {\n      bytes.push(0xd9, length);\n      size = 2;\n    }\n    // str 16\n    else if (length < 0x10000) {\n      bytes.push(0xda, length >> 8, length);\n      size = 3;\n    }\n    // str 32\n    else if (length < 0x100000000) {\n      bytes.push(0xdb, length >> 24, length >> 16, length >> 8, length);\n      size = 5;\n    } else {\n      throw new Error('String too long');\n    }\n    defers.push({ _str: value, _length: length, _offset: bytes.length });\n    return size + length;\n  }\n  if (type === 'number') {\n    // TODO: encode to float 32?\n\n    // float 64\n    if (Math.floor(value) !== value || !isFinite(value)) {\n      bytes.push(0xcb);\n      defers.push({ _float: value, _length: 8, _offset: bytes.length });\n      return 9;\n    }\n\n    if (value >= 0) {\n      // positive fixnum\n      if (value < 0x80) {\n        bytes.push(value);\n        return 1;\n      }\n      // uint 8\n      if (value < 0x100) {\n        bytes.push(0xcc, value);\n        return 2;\n      }\n      // uint 16\n      if (value < 0x10000) {\n        bytes.push(0xcd, value >> 8, value);\n        return 3;\n      }\n      // uint 32\n      if (value < 0x100000000) {\n        bytes.push(0xce, value >> 24, value >> 16, value >> 8, value);\n        return 5;\n      }\n      // uint 64\n      hi = (value / Math.pow(2, 32)) >> 0;\n      lo = value >>> 0;\n      bytes.push(0xcf, hi >> 24, hi >> 16, hi >> 8, hi, lo >> 24, lo >> 16, lo >> 8, lo);\n      return 9;\n    } else {\n      // negative fixnum\n      if (value >= -0x20) {\n        bytes.push(value);\n        return 1;\n      }\n      // int 8\n      if (value >= -0x80) {\n        bytes.push(0xd0, value);\n        return 2;\n      }\n      // int 16\n      if (value >= -0x8000) {\n        bytes.push(0xd1, value >> 8, value);\n        return 3;\n      }\n      // int 32\n      if (value >= -0x80000000) {\n        bytes.push(0xd2, value >> 24, value >> 16, value >> 8, value);\n        return 5;\n      }\n      // int 64\n      hi = Math.floor(value / Math.pow(2, 32));\n      lo = value >>> 0;\n      bytes.push(0xd3, hi >> 24, hi >> 16, hi >> 8, hi, lo >> 24, lo >> 16, lo >> 8, lo);\n      return 9;\n    }\n  }\n  if (type === 'object') {\n    // nil\n    if (value === null) {\n      bytes.push(0xc0);\n      return 1;\n    }\n\n    if (Array.isArray(value)) {\n      length = value.length;\n\n      // fixarray\n      if (length < 0x10) {\n        bytes.push(length | 0x90);\n        size = 1;\n      }\n      // array 16\n      else if (length < 0x10000) {\n        bytes.push(0xdc, length >> 8, length);\n        size = 3;\n      }\n      // array 32\n      else if (length < 0x100000000) {\n        bytes.push(0xdd, length >> 24, length >> 16, length >> 8, length);\n        size = 5;\n      } else {\n        throw new Error('Array too large');\n      }\n      for (i = 0; i < length; i++) {\n        size += _encode(bytes, defers, value[i]);\n      }\n      return size;\n    }\n\n    // fixext 8 / Date\n    if (value instanceof Date) {\n      var time = value.getTime();\n      hi = Math.floor(time / Math.pow(2, 32));\n      lo = time >>> 0;\n      bytes.push(0xd7, 0, hi >> 24, hi >> 16, hi >> 8, hi, lo >> 24, lo >> 16, lo >> 8, lo);\n      return 10;\n    }\n\n    if (value instanceof ArrayBuffer) {\n      length = value.byteLength;\n\n      // bin 8\n      if (length < 0x100) {\n        bytes.push(0xc4, length);\n        size = 2;\n      } else\n      // bin 16\n      if (length < 0x10000) {\n        bytes.push(0xc5, length >> 8, length);\n        size = 3;\n      } else\n      // bin 32\n      if (length < 0x100000000) {\n        bytes.push(0xc6, length >> 24, length >> 16, length >> 8, length);\n        size = 5;\n      } else {\n        throw new Error('Buffer too large');\n      }\n      defers.push({ _bin: value, _length: length, _offset: bytes.length });\n      return size + length;\n    }\n\n    if (typeof value.toJSON === 'function') {\n      return _encode(bytes, defers, value.toJSON());\n    }\n\n    var keys = [], key = '';\n\n    var allKeys = Object.keys(value);\n    for (i = 0, l = allKeys.length; i < l; i++) {\n      key = allKeys[i];\n      if (typeof value[key] !== 'function') {\n        keys.push(key);\n      }\n    }\n    length = keys.length;\n\n    // fixmap\n    if (length < 0x10) {\n      bytes.push(length | 0x80);\n      size = 1;\n    }\n    // map 16\n    else if (length < 0x10000) {\n      bytes.push(0xde, length >> 8, length);\n      size = 3;\n    }\n    // map 32\n    else if (length < 0x100000000) {\n      bytes.push(0xdf, length >> 24, length >> 16, length >> 8, length);\n      size = 5;\n    } else {\n      throw new Error('Object too large');\n    }\n\n    for (i = 0; i < length; i++) {\n      key = keys[i];\n      size += _encode(bytes, defers, key);\n      size += _encode(bytes, defers, value[key]);\n    }\n    return size;\n  }\n  // false/true\n  if (type === 'boolean') {\n    bytes.push(value ? 0xc3 : 0xc2);\n    return 1;\n  }\n  // fixext 1 / undefined\n  if (type === 'undefined') {\n    bytes.push(0xd4, 0, 0);\n    return 3;\n  }\n  throw new Error('Could not encode');\n}\n\nfunction encode(value) {\n  var bytes = [];\n  var defers = [];\n  var size = _encode(bytes, defers, value);\n  var buf = new ArrayBuffer(size);\n  var view = new DataView(buf);\n\n  var deferIndex = 0;\n  var deferWritten = 0;\n  var nextOffset = -1;\n  if (defers.length > 0) {\n    nextOffset = defers[0]._offset;\n  }\n\n  var defer, deferLength = 0, offset = 0;\n  for (var i = 0, l = bytes.length; i < l; i++) {\n    view.setUint8(deferWritten + i, bytes[i]);\n    if (i + 1 !== nextOffset) { continue; }\n    defer = defers[deferIndex];\n    deferLength = defer._length;\n    offset = deferWritten + nextOffset;\n    if (defer._bin) {\n      var bin = new Uint8Array(defer._bin);\n      for (var j = 0; j < deferLength; j++) {\n        view.setUint8(offset + j, bin[j]);\n      }\n    } else if (defer._str) {\n      utf8Write(view, offset, defer._str);\n    } else if (defer._float !== undefined) {\n      view.setFloat64(offset, defer._float);\n    }\n    deferIndex++;\n    deferWritten += deferLength;\n    if (defers[deferIndex]) {\n      nextOffset = defers[deferIndex]._offset;\n    }\n  }\n  return buf;\n}\n\nmodule.exports = encode;\n", "'use strict';\n\nfunction Decoder(buffer) {\n  this._offset = 0;\n  if (buffer instanceof ArrayBuffer) {\n    this._buffer = buffer;\n    this._view = new DataView(this._buffer);\n  } else if (ArrayBuffer.isView(buffer)) {\n    this._buffer = buffer.buffer;\n    this._view = new DataView(this._buffer, buffer.byteOffset, buffer.byteLength);\n  } else {\n    throw new Error('Invalid argument');\n  }\n}\n\nfunction utf8Read(view, offset, length) {\n  var string = '', chr = 0;\n  for (var i = offset, end = offset + length; i < end; i++) {\n    var byte = view.getUint8(i);\n    if ((byte & 0x80) === 0x00) {\n      string += String.fromCharCode(byte);\n      continue;\n    }\n    if ((byte & 0xe0) === 0xc0) {\n      string += String.fromCharCode(\n        ((byte & 0x1f) << 6) |\n        (view.getUint8(++i) & 0x3f)\n      );\n      continue;\n    }\n    if ((byte & 0xf0) === 0xe0) {\n      string += String.fromCharCode(\n        ((byte & 0x0f) << 12) |\n        ((view.getUint8(++i) & 0x3f) << 6) |\n        ((view.getUint8(++i) & 0x3f) << 0)\n      );\n      continue;\n    }\n    if ((byte & 0xf8) === 0xf0) {\n      chr = ((byte & 0x07) << 18) |\n        ((view.getUint8(++i) & 0x3f) << 12) |\n        ((view.getUint8(++i) & 0x3f) << 6) |\n        ((view.getUint8(++i) & 0x3f) << 0);\n      if (chr >= 0x010000) { // surrogate pair\n        chr -= 0x010000;\n        string += String.fromCharCode((chr >>> 10) + 0xD800, (chr & 0x3FF) + 0xDC00);\n      } else {\n        string += String.fromCharCode(chr);\n      }\n      continue;\n    }\n    throw new Error('Invalid byte ' + byte.toString(16));\n  }\n  return string;\n}\n\nDecoder.prototype._array = function (length) {\n  var value = new Array(length);\n  for (var i = 0; i < length; i++) {\n    value[i] = this._parse();\n  }\n  return value;\n};\n\nDecoder.prototype._map = function (length) {\n  var key = '', value = {};\n  for (var i = 0; i < length; i++) {\n    key = this._parse();\n    value[key] = this._parse();\n  }\n  return value;\n};\n\nDecoder.prototype._str = function (length) {\n  var value = utf8Read(this._view, this._offset, length);\n  this._offset += length;\n  return value;\n};\n\nDecoder.prototype._bin = function (length) {\n  var value = this._buffer.slice(this._offset, this._offset + length);\n  this._offset += length;\n  return value;\n};\n\nDecoder.prototype._parse = function () {\n  var prefix = this._view.getUint8(this._offset++);\n  var value, length = 0, type = 0, hi = 0, lo = 0;\n\n  if (prefix < 0xc0) {\n    // positive fixint\n    if (prefix < 0x80) {\n      return prefix;\n    }\n    // fixmap\n    if (prefix < 0x90) {\n      return this._map(prefix & 0x0f);\n    }\n    // fixarray\n    if (prefix < 0xa0) {\n      return this._array(prefix & 0x0f);\n    }\n    // fixstr\n    return this._str(prefix & 0x1f);\n  }\n\n  // negative fixint\n  if (prefix > 0xdf) {\n    return (0xff - prefix + 1) * -1;\n  }\n\n  switch (prefix) {\n    // nil\n    case 0xc0:\n      return null;\n    // false\n    case 0xc2:\n      return false;\n    // true\n    case 0xc3:\n      return true;\n\n    // bin\n    case 0xc4:\n      length = this._view.getUint8(this._offset);\n      this._offset += 1;\n      return this._bin(length);\n    case 0xc5:\n      length = this._view.getUint16(this._offset);\n      this._offset += 2;\n      return this._bin(length);\n    case 0xc6:\n      length = this._view.getUint32(this._offset);\n      this._offset += 4;\n      return this._bin(length);\n\n    // ext\n    case 0xc7:\n      length = this._view.getUint8(this._offset);\n      type = this._view.getInt8(this._offset + 1);\n      this._offset += 2;\n      return [type, this._bin(length)];\n    case 0xc8:\n      length = this._view.getUint16(this._offset);\n      type = this._view.getInt8(this._offset + 2);\n      this._offset += 3;\n      return [type, this._bin(length)];\n    case 0xc9:\n      length = this._view.getUint32(this._offset);\n      type = this._view.getInt8(this._offset + 4);\n      this._offset += 5;\n      return [type, this._bin(length)];\n\n    // float\n    case 0xca:\n      value = this._view.getFloat32(this._offset);\n      this._offset += 4;\n      return value;\n    case 0xcb:\n      value = this._view.getFloat64(this._offset);\n      this._offset += 8;\n      return value;\n\n    // uint\n    case 0xcc:\n      value = this._view.getUint8(this._offset);\n      this._offset += 1;\n      return value;\n    case 0xcd:\n      value = this._view.getUint16(this._offset);\n      this._offset += 2;\n      return value;\n    case 0xce:\n      value = this._view.getUint32(this._offset);\n      this._offset += 4;\n      return value;\n    case 0xcf:\n      hi = this._view.getUint32(this._offset) * Math.pow(2, 32);\n      lo = this._view.getUint32(this._offset + 4);\n      this._offset += 8;\n      return hi + lo;\n\n    // int\n    case 0xd0:\n      value = this._view.getInt8(this._offset);\n      this._offset += 1;\n      return value;\n    case 0xd1:\n      value = this._view.getInt16(this._offset);\n      this._offset += 2;\n      return value;\n    case 0xd2:\n      value = this._view.getInt32(this._offset);\n      this._offset += 4;\n      return value;\n    case 0xd3:\n      hi = this._view.getInt32(this._offset) * Math.pow(2, 32);\n      lo = this._view.getUint32(this._offset + 4);\n      this._offset += 8;\n      return hi + lo;\n\n    // fixext\n    case 0xd4:\n      type = this._view.getInt8(this._offset);\n      this._offset += 1;\n      if (type === 0x00) {\n        this._offset += 1;\n        return void 0;\n      }\n      return [type, this._bin(1)];\n    case 0xd5:\n      type = this._view.getInt8(this._offset);\n      this._offset += 1;\n      return [type, this._bin(2)];\n    case 0xd6:\n      type = this._view.getInt8(this._offset);\n      this._offset += 1;\n      return [type, this._bin(4)];\n    case 0xd7:\n      type = this._view.getInt8(this._offset);\n      this._offset += 1;\n      if (type === 0x00) {\n        hi = this._view.getInt32(this._offset) * Math.pow(2, 32);\n        lo = this._view.getUint32(this._offset + 4);\n        this._offset += 8;\n        return new Date(hi + lo);\n      }\n      return [type, this._bin(8)];\n    case 0xd8:\n      type = this._view.getInt8(this._offset);\n      this._offset += 1;\n      return [type, this._bin(16)];\n\n    // str\n    case 0xd9:\n      length = this._view.getUint8(this._offset);\n      this._offset += 1;\n      return this._str(length);\n    case 0xda:\n      length = this._view.getUint16(this._offset);\n      this._offset += 2;\n      return this._str(length);\n    case 0xdb:\n      length = this._view.getUint32(this._offset);\n      this._offset += 4;\n      return this._str(length);\n\n    // array\n    case 0xdc:\n      length = this._view.getUint16(this._offset);\n      this._offset += 2;\n      return this._array(length);\n    case 0xdd:\n      length = this._view.getUint32(this._offset);\n      this._offset += 4;\n      return this._array(length);\n\n    // map\n    case 0xde:\n      length = this._view.getUint16(this._offset);\n      this._offset += 2;\n      return this._map(length);\n    case 0xdf:\n      length = this._view.getUint32(this._offset);\n      this._offset += 4;\n      return this._map(length);\n  }\n\n  throw new Error('Could not parse');\n};\n\nfunction decode(buffer) {\n  var decoder = new Decoder(buffer);\n  var value = decoder._parse();\n  if (decoder._offset !== buffer.byteLength) {\n    throw new Error((buffer.byteLength - decoder._offset) + ' trailing bytes');\n  }\n  return value;\n}\n\nmodule.exports = decode;\n", "\n/**\n * Expose `Backoff`.\n */\n\nmodule.exports = Backoff;\n\n/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */\n\nfunction Backoff(opts) {\n  opts = opts || {};\n  this.ms = opts.min || 100;\n  this.max = opts.max || 10000;\n  this.factor = opts.factor || 2;\n  this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n  this.attempts = 0;\n}\n\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */\n\nBackoff.prototype.duration = function(){\n  var ms = this.ms * Math.pow(this.factor, this.attempts++);\n  if (this.jitter) {\n    var rand =  Math.random();\n    var deviation = Math.floor(rand * this.jitter * ms);\n    ms = (Math.floor(rand * 10) & 1) == 0  ? ms - deviation : ms + deviation;\n  }\n  return Math.min(ms, this.max) | 0;\n};\n\n/**\n * Reset the number of attempts.\n *\n * @api public\n */\n\nBackoff.prototype.reset = function(){\n  this.attempts = 0;\n};\n\n/**\n * Set the minimum duration\n *\n * @api public\n */\n\nBackoff.prototype.setMin = function(min){\n  this.ms = min;\n};\n\n/**\n * Set the maximum duration\n *\n * @api public\n */\n\nBackoff.prototype.setMax = function(max){\n  this.max = max;\n};\n\n/**\n * Set the jitter\n *\n * @api public\n */\n\nBackoff.prototype.setJitter = function(jitter){\n  this.jitter = jitter;\n};\n\n"], "sourceRoot": ""}