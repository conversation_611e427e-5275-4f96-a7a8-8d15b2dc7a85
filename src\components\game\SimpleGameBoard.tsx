import React from 'react';
import { Canvas } from '@react-three/fiber';
import { GameState, Player, TileType } from '../../types/gameTypes';
import { Ctx } from 'boardgame.io';
import { BrawlPartyScene } from './BrawlPartyScene';
import './SimpleGameBoard.scss';

interface SimpleGameBoardProps {
  G: GameState;
  ctx: Ctx;
  moves: any;
  playerID: string | null;
  isActive: boolean;
}

const getTileIcon = (type: TileType): string => {
  switch (type) {
    case TileType.BASIC:
      return '⬜';
    case TileType.HEALING:
      return '❤️';
    case TileType.DAMAGE:
      return '💀';
    case TileType.KEY:
      return '🗝️';
    case TileType.TREASURE_CHEST:
      return '🏆';
    default:
      return '⬜';
  }
};

const getTileColor = (type: TileType): string => {
  switch (type) {
    case TileType.BASIC:
      return '#f0f0f0';
    case TileType.HEALING:
      return '#90EE90';
    case TileType.DAMAGE:
      return '#FFB6C1';
    case TileType.KEY:
      return '#FFD700';
    case TileType.TREASURE_CHEST:
      return '#FF6347';
    default:
      return '#f0f0f0';
  }
};

export const SimpleGameBoard: React.FC<SimpleGameBoardProps> = ({
  G,
  ctx,
  moves,
  playerID,
  isActive,
}) => {
  const currentPlayer = playerID ? G.players[playerID] : null;
  const isMyTurn = isActive && ctx.currentPlayer === playerID;

  const handleRollDice = () => {
    if (isMyTurn && moves.rollDice) {
      moves.rollDice();
    }
  };

  const handleOpenChest = () => {
    if (isMyTurn && moves.openTreasureChest) {
      moves.openTreasureChest();
    }
  };

  const canOpenChest = currentPlayer &&
    currentPlayer.position === G.gameConfig.treasureChestPosition &&
    currentPlayer.keys >= G.gameConfig.keysToWin;

  return (
    <div className="simple-game-board">
      <div className="game-header">
        <h1>🎲 Brawl Party Board Game</h1>
        {ctx.gameover ? (
          <div className="game-over">
            <h2>🎉 Game Over!</h2>
            <p>Winner: {G.players[ctx.gameover.winner]?.name || 'Unknown'}</p>
          </div>
        ) : (
          <div className="turn-info">
            <p>Current Turn: <strong>{G.players[ctx.currentPlayer]?.name}</strong></p>
            {G.currentDiceRoll > 0 && (
              <p>Last Roll: <span className="dice-result">🎲 {G.currentDiceRoll}</span></p>
            )}
          </div>
        )}
      </div>

      <div className="game-content">
        <div className="players-panel">
          <h3>Players</h3>
          {Object.values(G.players).map((player: Player) => (
            <div
              key={player.ID}
              className={`player-card ${player.ID === ctx.currentPlayer ? 'current-player' : ''}`}
              style={{ borderColor: player.color }}
            >
              <div className="player-name">{player.name}</div>
              <div className="player-stats">
                <div className="stat">
                  <span className="stat-icon">❤️</span>
                  <span className="stat-value">{player.health}/100</span>
                </div>
                <div className="stat">
                  <span className="stat-icon">🗝️</span>
                  <span className="stat-value">{player.keys}</span>
                </div>
                <div className="stat">
                  <span className="stat-icon">📍</span>
                  <span className="stat-value">Tile {player.position}</span>
                </div>
              </div>
              {!player.isAlive && <div className="player-status">💀 Respawning...</div>}
            </div>
          ))}
        </div>

        <div className="board-panel">
          <h3>Game Board</h3>
          <div className="board-3d-container">
            <Canvas
              camera={{
                position: [0, 25, 25],
                fov: 60,
                near: 0.1,
                far: 1000
              }}
              style={{ height: '500px', width: '100%' }}
            >
              <BrawlPartyScene
                G={G}
                ctx={ctx}
                moves={moves}
                playerID={playerID}
                isActive={isActive}
              />
            </Canvas>
          </div>
        </div>

        <div className="controls-panel">
          <h3>Game Controls</h3>
          {isMyTurn ? (
            <div className="controls">
              <button
                onClick={handleRollDice}
                className="roll-dice-btn"
                disabled={ctx.gameover}
              >
                🎲 Roll Dice
              </button>

              {canOpenChest && (
                <button
                  onClick={handleOpenChest}
                  className="open-chest-btn"
                >
                  🏆 Open Treasure Chest
                </button>
              )}
            </div>
          ) : (
            <div className="waiting">
              <p>Waiting for {G.players[ctx.currentPlayer]?.name} to play...</p>
            </div>
          )}

          <div className="game-info">
            <h4>Game Rules:</h4>
            <ul>
              <li>🎲 Roll dice to move around the board</li>
              <li>❤️ Healing tiles restore 20 health</li>
              <li>💀 Damage tiles deal 30 damage</li>
              <li>🗝️ Key tiles give you 10 keys</li>
              <li>🏆 Reach the treasure chest with 40+ keys to win!</li>
              <li>💀 If health reaches 0, you respawn at start</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="game-log">
        <h3>Game Log</h3>
        <div className="log-entries">
          {G.gameLog.slice(-10).reverse().map((entry, index) => (
            <div key={index} className="log-entry">
              <span className="log-time">
                {new Date(entry.timestamp).toLocaleTimeString()}
              </span>
              <span className="log-details">{entry.details}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
