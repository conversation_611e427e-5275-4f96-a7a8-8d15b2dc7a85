{"name": "boardgame.io", "version": "0.50.2", "description": "library for turn-based games", "repository": "https://github.com/boardgameio/boardgame.io", "scripts": {"prestart": "run-p examples:install build", "start": "run-p dev:server dev:client", "predev": "npm run examples:install", "dev": "run-p build:watch dev:server dev:client", "dev:client": "node scripts/dev-client.js", "dev:server": "cross-env NODE_ENV=development nodemon -w src -w examples -e js,ts --exec babel-node --extensions \".ts,.js\" --ignore \"src/**/*.test.ts\" --presets @babel/preset-env examples/react-web/server.js", "build": "cross-env BABEL_ENV=rollup rollup --config rollup.config.js  --silent", "build:watch": "cross-env BABEL_ENV=rollup rollup -w --config rollup.config.js", "benchmark": "babel-node --extensions .ts,.js --presets @babel/preset-env,@babel/preset-typescript benchmark/index.js", "docs": "docsify serve docs", "examples": "npm run start", "examples:install": "node scripts/install-examples.js", "pretest": "npm run lint", "test": "cross-env NODE_ENV=test jest", "test:watch": "npm test -- --watch", "test:coverage": "npm test -- --coverage --collectCoverageFrom=\"src/**\"", "test:coveralls": "coveralls < coverage/lcov.info", "test:integration": "node ./scripts/integration.js", "ts": "tsc --noEmit", "ts:watch": "tsc --noEmit --watch", "lint": "eslint .", "lint:fix": "eslint --fix .", "prepublishOnly": "npm run clean", "proxydirs": "node scripts/proxy-dirs.js", "prepack": "run-s build proxydirs", "postpack": "npm run clean", "prettier": "prettier --write \"{examples,src,benchmark}/**/*.{ts,tsx,js,jsx,css,md}\"", "changelog": "node ./scripts/changelog.js", "clean": "node ./scripts/clean.js"}, "sideEffects": false, "main": "dist/boardgameio.js", "unpkg": "dist/boardgameio.min.js", "module": "dist/boardgameio.es.js", "types": "dist/types/src/types.d.ts", "files": ["src", "dist/boardgameio.js", "dist/boardgameio.min.js", "dist/boardgameio.es.js", "dist/esm", "dist/cjs", "dist/types", "client", "core", "debug", "react", "react-native", "server", "ai", "plugins", "master", "multiplayer", "internal", "testing"], "keywords": ["board games", "card games", "tabletop games", "game engine"], "engines": {"node": ">=10.0", "npm": ">=6.0"}, "author": "<EMAIL>", "license": "MIT", "funding": ["https://github.com/boardgameio/boardgame.io?sponsor=1", {"type": "opencollective", "url": "https://opencollective.com/boardgameio"}], "devDependencies": {"@babel/cli": "^7.16.0", "@babel/core": "^7.16.0", "@babel/node": "^7.16.0", "@babel/plugin-proposal-class-properties": "^7.16.0", "@babel/plugin-proposal-object-rest-spread": "^7.16.0", "@babel/plugin-transform-modules-commonjs": "^7.16.0", "@babel/preset-env": "^7.16.0", "@babel/preset-react": "^7.16.0", "@babel/preset-typescript": "^7.16.0", "@testing-library/jest-dom": "^5.14.1", "@testing-library/svelte": "^3.0.3", "@types/enzyme": "^3.10.9", "@types/jest": "^27.0.1", "@types/koa__cors": "^3.0.3", "@types/node": "^14.0.24", "@types/react": "^16.14.11", "@types/react-dom": "^16.9.14", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^4.29.3", "@typescript-eslint/parser": "^4.29.3", "babel-plugin-module-resolver": "^4.1.0", "benchmark": "^2.1.4", "cross-env": "^7.0.3", "docsify-cli": "^4.4.4", "enzyme": "^3.11.0", "enzyme-adapter-react-16": "^1.15.6", "eslint": "^7.31.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-jest": "^24.4.0", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-react": "^7.24.0", "eslint-plugin-unicorn": "^30.0.0", "husky": "^4.3.8", "identity-obj-proxy": "^3.0.0", "jest": "^27.1.0", "jest-date-mock": "^1.0.8", "jest-transform-svelte": "^2.1.1", "lint-staged": "^10.5.4", "node-persist": "^3.1.0", "nodemon": "^2.0.12", "npm-run-all": "^4.1.5", "prettier": "^2.3.2", "raf": "^3.4.1", "react": "^16.14.0", "react-dom": "^16.14.0", "rollup": "^1.0.2", "rollup-plugin-babel": "^4.2.0", "rollup-plugin-commonjs": "^9.2.0", "rollup-plugin-filesize": "^6.0.0", "rollup-plugin-node-builtins": "^2.1.2", "rollup-plugin-node-resolve": "^4.0.0", "rollup-plugin-replace": "^2.1.0", "rollup-plugin-svelte": "^6.1.1", "rollup-plugin-terser": "^5.3.0", "rollup-plugin-typescript2": "^0.22.0", "shelljs": "^0.8.4", "supertest": "^3.1.0", "svelte-icons": "^2.1.0", "tempy": "^1.0.1", "ts-jest": "^27.0.5", "ts-transformer-imports": "^0.4.3", "ttypescript": "^1.5.12", "typescript": "^3.8.2"}, "dependencies": {"@koa/cors": "^3.1.0", "@koa/router": "^10.1.1", "@types/koa": "^2.13.4", "@types/koa__router": "^8.0.8", "flatted": "^3.2.1", "immer": "^9.0.5", "koa": "^2.13.3", "koa-body": "^5.0.0", "koa-socket-2": "^2.0.0", "lodash.isplainobject": "^4.0.6", "nanoid": "^3.1.30", "p-queue": "^6.6.2", "prop-types": "^15.5.10", "react-cookies": "^0.1.0", "redux": "^4.1.0", "rfc6902": "^5.0.0", "setimmediate": "^1.0.5", "socket.io": "^4.5.0", "socket.io-client": "^4.1.3", "svelte": "^3.41.0", "svelte-json-tree-auto": "^0.1.0", "ts-toolbelt": "^6.3.6"}, "jest": {"preset": "ts-jest/presets/js-with-babel", "testEnvironment": "jsdom", "moduleNameMapper": {"\\.(css)$": "identity-obj-proxy", "\\.(svg)$": "<rootDir>/.empty_module.js", "svelte-json-tree-auto": "<rootDir>/src/client/debug/tests/JSONTree.mock.svelte"}, "coveragePathIgnorePatterns": ["/node_modules/", "src/client/transport/dummy", "src/client/debug/.*", "src/types.ts"], "setupFiles": ["raf/polyfill", "jest-date-mock"], "setupFilesAfterEnv": ["@testing-library/jest-dom/extend-expect"], "transform": {"^.+\\.svelte$": "jest-transform-svelte"}, "transformIgnorePatterns": ["node_modules/(?!(boardgame.io|flatted|svelte-icons)/)"], "testPathIgnorePatterns": ["examples/", "integration/", "node_modules/", ".npm/"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run test:coverage"}}}