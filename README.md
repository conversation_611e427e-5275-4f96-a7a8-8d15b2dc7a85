# 🎲 Brawl Party AI - 3D Board Game

### What is Brawl Party AI

A high-quality 3D multiplayer board game that runs in web browsers. Players take turns rolling dice and moving around a themed game board with various effect tiles. Collect keys, manage health, and compete to open the treasure chest first!

**Current Status**: ✅ 2D prototype complete, 🚧 3D implementation in progress

### Game Features

- **2-6 Players**: Multiplayer support for 2-6 players
- **3D Graphics**: AAA-quality 3D visuals using Three.js
- **Dynamic Board**: 40-120 interconnected tiles in a themed environment
- **Player Mechanics**: Health (0-100), Keys collection, Position tracking
- **Tile Effects**:
  - Basic Tiles (no effect)
  - Healing Tiles (+20 health)
  - Damage Tiles (-30 health)
  - Key Tiles (+10 keys)
  - Treasure Chest (win with 40+ keys)
- **Real-time Multiplayer**: WebSocket-based multiplayer with boardgame.io

### Tech stack

- Node.js
- Typescript
- React
- [boardgame.io](https://boardgame.io/)
- [three.js](https://threejs.org/) / [react-three-fiber](https://github.com/pmndrs/react-three-fiber)
- PostgreSQL

### How to Play

1. **Join a Game**: Create or join a multiplayer game lobby
2. **Roll the Dice**: On your turn, roll a 6-sided die
3. **Move**: Automatically move forward the number of spaces shown
4. **Tile Effects**: Experience the effect of the tile you land on
5. **Collect Keys**: Land on Key tiles to collect keys
6. **Win**: Be the first to reach the Treasure Chest tile with 40+ keys
7. **Respawn**: If your health reaches 0, respawn at the starting tile

### Development

```bash
# Install dependencies
npm install

# Start development server (client + server)
npm start

# Start only client
npm run client

# Start only server
npm run server

# Build for production
npm run build
```

### Recent Updates

- ✅ **Rebranded** from Santorini to Brawl Party AI
- ✅ **New Logo**: Gaming dice icon 🎲
- ✅ **Clean UI**: Removed debug panels and GitHub links
- ✅ **Simplified Navigation**: Streamlined game creation flow
- ✅ **Updated Branding**: All meta tags, titles, and descriptions updated

### Hosting

The game can be deployed to any Node.js hosting platform. The original Santorini project used render.com and supabase for hosting.
