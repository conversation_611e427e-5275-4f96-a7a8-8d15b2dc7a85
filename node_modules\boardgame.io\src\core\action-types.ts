/*
 * Copyright 2017 The boardgame.io Authors
 *
 * Use of this source code is governed by a MIT-style
 * license that can be found in the LICENSE file or at
 * https://opensource.org/licenses/MIT.
 */

export const MAKE_MOVE = 'MAKE_MOVE';
export const GAME_EVENT = 'GAME_EVENT';
export const REDO = 'REDO';
export const RESET = 'RESET';
export const SYNC = 'SYNC';
export const UNDO = 'UNDO';
export const UPDATE = 'UPDATE';
export const PATCH = 'PATCH';
export const PLUGIN = 'PLUGIN';
export const STRIP_TRANSIENTS = 'STRIP_TRANSIENTS';
