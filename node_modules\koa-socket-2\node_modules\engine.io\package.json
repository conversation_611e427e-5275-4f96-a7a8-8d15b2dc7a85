{"name": "engine.io", "version": "4.1.2", "description": "The realtime engine behind Socket.IO. Provides the foundation of a bidirectional connection between client and server", "main": "lib/engine.io.js", "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/socketio/engine.io", "contributors": [{"name": "<PERSON><PERSON>", "web": "https://github.com/EugenD<PERSON>ck"}, {"name": "<PERSON><PERSON><PERSON>", "web": "https://github.com/afshinm"}, {"name": "<PERSON>", "web": "https://github.com/cadorn"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"accepts": "~1.3.4", "base64id": "2.0.0", "cookie": "~0.4.1", "cors": "~2.8.5", "debug": "~4.3.1", "engine.io-parser": "~4.0.0", "ws": "~7.4.2"}, "devDependencies": {"babel-eslint": "^8.0.2", "eiows": "^3.3.0", "engine.io-client": "4.1.1", "engine.io-client-v3": "npm:engine.io-client@3.5.0", "eslint": "^4.19.1", "eslint-config-prettier": "^6.9.0", "expect.js": "^0.3.1", "mocha": "^4.0.1", "prettier": "^1.19.1", "s": "0.1.1", "superagent": "^3.8.1"}, "scripts": {"lint": "eslint lib/ test/ *.js", "test": "npm run lint && npm run format:check && mocha && EIO_CLIENT=3 mocha && EIO_WS_ENGINE=eiows mocha", "format:check": "prettier --check 'lib/**/*.js' 'test/**/*.js'", "format:fix": "prettier --write 'lib/**/*.js' 'test/**/*.js'"}, "repository": {"type": "git", "url": "**************:socketio/engine.io.git"}, "files": ["lib/"], "engines": {"node": ">=10.0.0"}}