import { _ClientImpl } from 'boardgame.io/dist/types/src/client/client';
import { Local } from 'boardgame.io/multiplayer';
import { Client as Client2 } from 'boardgame.io/client';
import { useEffect, useState } from 'react';
import { BrawlPartyGame } from '../../game';
import { SimpleGameBoard } from '../game/SimpleGameBoard';
import { GameState, GameType } from '../../types/gameTypes';

const LocalGameWrapper = ({
  p0,
  p1,
}: {
  p0: _ClientImpl<GameState>;
  p1: _ClientImpl<GameState>;
}): JSX.Element => {
  const [currentClient, setCurrentClient] = useState<_ClientImpl<GameState>>(p0);
  const [gameState, setGameState] = useState(p0.getState());

  useEffect(() => {
    const unsubscribeP0 = p0.subscribe((newState) => {
      if (newState) {
        setGameState(newState);
        if (newState.ctx.currentPlayer === '0') {
          setCurrentClient(p0);
        }
      }
    });

    const unsubscribeP1 = p1.subscribe((newState) => {
      if (newState) {
        setGameState(newState);
        if (newState.ctx.currentPlayer === '1') {
          setCurrentClient(p1);
        }
      }
    });

    return () => {
      unsubscribeP0();
      unsubscribeP1();
    };
  }, [p0, p1]);

  if (!gameState) return <div>Loading...</div>;

  return (
    <SimpleGameBoard
      G={gameState.G}
      ctx={gameState.ctx}
      moves={currentClient.moves}
      playerID={currentClient.playerID}
      isActive={currentClient.playerID === gameState.ctx.currentPlayer}
    />
  );
};

export const GameLocal = (): JSX.Element => {
  const game = { ...BrawlPartyGame };
  const p0 = Client2({ game, multiplayer: Local(), playerID: '0' });
  const p1 = Client2({ game, multiplayer: Local(), playerID: '1' });

  p0.start();
  p1.start();

  return <LocalGameWrapper p0={p0} p1={p1} />;
};
